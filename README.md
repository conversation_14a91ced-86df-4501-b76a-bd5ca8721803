# yieldHUB 2.0 UI

The UI for yieldHUB 2.0 application.

## Installation

```bash
$ git clone ssh://**************************:7999/yiel/yh2-ui.git
$ cd yh2-ui
$ npm install
$ npm run execute-husky
$ cp .env.example .env.local # Edit .env.local for the environment variables and/or database
```

## Run Development mode

```bash
$ npm run dev
```

## Run Production mode

```bash
$ npm run start
```

## Build

```bash
$ npm run build
```
