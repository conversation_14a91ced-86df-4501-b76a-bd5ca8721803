import path from "node:path";
import { fileURLToPath } from "node:url";
import react from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";
import eslintPluginImport from "eslint-plugin-import";
import globals from "globals";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

const esLintConfig = [
  {
    ignores: [
      "public/libarchive.js/**/*",
      "**/.git",
      "**/.svn",
      "**/.hg",
      "**/.husky",
      "**/.next",
      "**/node_modules",
      "**/README.md",
      "**/package.json",
      "**/package-lock.json",
      "**/.eslint*",
      "**/.stylelint*",
      "**/.prettier*",
    ],
  },
  ...compat.extends(
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:prettier/recommended",
    "plugin:@tanstack/eslint-plugin-query/recommended",
    "plugin:@next/next/recommended",
  ),
  {
    plugins: {
      react,
      "react-hooks": reactHooks,
      import: eslintPluginImport,
    },

    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
      },

      ecmaVersion: "latest",
      sourceType: "module",
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },

    files: ["**/*.{js,jsx,mjs,cjs,ts,tsx}"],

    rules: {
      // React Rules
      "react/prop-types": "off",
      "react/react-in-jsx-scope": "off",
      "react/jsx-uses-react": "error",
      "react/jsx-uses-vars": "error",

      // React Hooks Best Practices
      // "react-hooks/rules-of-hooks": "error",
      "react-hooks/exhaustive-deps": "off",

      // Import Order Rules
      "import/order": ["error", { "newlines-between": "never" }],

      // TanStack Query Best Practices
      "@tanstack/query/exhaustive-deps": "error",
      "@tanstack/query/no-rest-destructuring": "warn",
      "@tanstack/query/stable-query-client": "error",
    },
  },
];

export default esLintConfig;
