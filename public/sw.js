self.addEventListener("push", function (event) {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: "/favicon-48x48.png",
      badge: "/favicon-48x48.png",
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: "2",
        url: data?.url ?? null,
      },
    };
    event.waitUntil(self.registration.showNotification(data.title, options));
  }
});

/* global clients */
self.addEventListener("notificationclick", function (event) {
  event.notification.close();
  event.waitUntil(clients.openWindow(event.notification.data?.url));
});
