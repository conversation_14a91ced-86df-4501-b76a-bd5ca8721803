export declare enum ArchiveFormat {
    SEVEN_ZIP = "7zip",
    AR = "ar",
    ARBSD = "arbsd",
    ARGNU = "argnu",
    ARSVR4 = "arsvr4",
    BIN = "bin",
    BSDTAR = "bsdtar",
    CD9660 = "cd9660",
    CPIO = "cpio",
    GNUTAR = "gnutar",
    ISO = "iso",
    ISO9660 = "iso9660",
    MTREE = "mtree",
    MTREE_CLASSIC = "mtree-classic",
    NEWC = "newc",
    ODC = "odc",
    OLDTAR = "oldtar",
    PAX = "pax",
    PAXR = "paxr",
    POSIX = "posix",
    PWB = "pwb",
    RAW = "raw",
    RPAX = "rpax",
    SHAR = "shar",
    SHARDUMP = "shardump",
    USTAR = "ustar",
    V7TAR = "v7tar",
    V7 = "v7",
    WARC = "warc",
    XAR = "xar",
    ZIP = "zip"
}
export declare enum ArchiveCompression {
    B64ENCODE = "b64encode",
    BZIP2 = "bzip2",
    COMPRESS = "compress",
    GRZIP = "grzip",
    GZIP = "gzip",
    LRZIP = "lrzip",
    LZ4 = "lz4",
    LZIP = "lzip",
    LZMA = "lzma",
    LZOP = "lzop",
    UUENCODE = "uuencode",
    XZ = "xz",
    ZSTD = "zstd",
    NONE = "none"
}
