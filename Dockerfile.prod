FROM node:lts-alpine

LABEL maintainer="<PERSON>"

ARG WWWUSER=888
ARG WWWGROUP=888
ARG APP_PM_USER=yieldhub
ARG DOCKER_UI_DIR='/app/ui'

RUN apk --no-cache add bash

RUN addgroup -S -g ${WWWGROUP} ${APP_PM_USER} || true && \
    adduser -S -u ${WWWUSER} -h /home/<USER>
    -s /bin/bash -G ${APP_PM_USER} ${APP_PM_USER} || true

RUN mkdir -p ${DOCKER_UI_DIR} \
    && chown ${APP_PM_USER}:${APP_PM_USER} ${DOCKER_UI_DIR}

WORKDIR ${DOCKER_UI_DIR}

COPY --chown=${APP_PM_USER}:${APP_PM_USER} package*.json ./
COPY --chown=${APP_PM_USER}:${APP_PM_USER} . .

RUN rm -rf node_modules .next || true

USER ${APP_PM_USER}

RUN npm install

RUN npm run build

EXPOSE 3000

CMD ["npm", "run", "start"]
