"use client";

import { <PERSON><PERSON>, Result } from "antd";
import { useEffect, useState } from "react";
// import Api from "../../src/utils/api";
// import Helper from "../../src/utils/helper";
// const Api = dynamic(() => import("../../src/utils/api").then((mod) => mod), {
//   ssr: false,
// });
// const Helper = dynamic(
//   () => import("../../src/utils/helper").then((mod) => mod),
//   {
//     ssr: false,
//   },
// );

/**
 * The system maintenance page UI component
 *
 * @returns {JSX.Element}
 */
export default function Maintenance() {
  const [Api, setApi] = useState(null);
  const [Helper, setHelper] = useState(null);

  useEffect(() => {
    // dynamically import Api and Helper only on client
    Promise.all([
      import("../../src/utils/api").then((mod) => mod.default),
      import("../../src/utils/helper").then((mod) => mod.default),
    ])
      .then(([ApiModule, HelperModule]) => {
        setApi(ApiModule);
        setHelper(HelperModule);
      })
      .catch((error) => console.error("Error loading modules:", error));
  }, []);

  useEffect(() => {
    if (!Api || !Helper) return;

    let interval;
    if (window) {
      interval = setInterval(() => {
        Api.checkMaintenance(
          () => {
            clearInterval(interval);
            Helper.setSiteMaintenance(false, true, "/page");
          },
          (err) => {
            if (err.status === 503) {
              Helper.setSiteMaintenance(true, false);
            }
          },
        );
      }, 60000);
    }

    return () => clearInterval(interval);
  }, [Api, Helper]);

  return (
    <Result
      status="500"
      subTitle="Sorry for the inconvenience! Our site is currently undergoing maintenance to enhance your experience. We'll be back shortly. Thank you for your patience!"
      extra={
        <Button
          type="primary"
          onClick={() => {
            if (window) {
              window.location.href = "/page";
            }
          }}
        >
          Back Home
        </Button>
      }
    />
  );
}
