"use client";

import "animate.css";
import "@ant-design/v5-patch-for-react-19";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-balham.css";
import "./globals.css";
import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { App } from "antd";
import { useBoundStore } from "../src/store/store";
import Header from "./header";

/**
 * Site app layout
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
export default function AppLayout({ children }) {
  // Create a react query client
  const router = useRouter();
  const pathname = usePathname();
  const loginData = useBoundStore((state) => state.loginData);
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            refetchOnWindowFocus: false,
          },
        },
      }),
  );

  // middleware should be used for this but it is not yet supported for app directory in beta version
  useEffect(() => {
    if (
      window.localStorage.getItem("isLoggedIn") !== "true" &&
      pathname !== "/login" &&
      pathname !== "/login/otp" &&
      pathname !== "/login/forgot-password" &&
      pathname.indexOf("/login/reset-password") === -1
    ) {
      router.push("/login");
    } else if (
      pathname === "/" ||
      (pathname === "/login/otp" && loginData.jwt === undefined)
    ) {
      // redirect to home if from otp page since encountering error when redirecting it back to login page
      // the issue is with dynamic loading for nextjs and is still to be resolved on their side
      router.push("/page");
    }
  }, [pathname]);

  return (
    <html lang="en">
      <Header />
      <body>
        <App className="overflow-hidden">
          <QueryClientProvider client={queryClient}>
            {children}
          </QueryClientProvider>
        </App>
      </body>
    </html>
  );
}
