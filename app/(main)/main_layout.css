.main-layout {
  height: 100vh;
}

.main-layout .logo .text {
  font-size: 1.8rem;
  color: #ffffff;
}

.main-layout .header-center-wrapper {
  flex: 1 1 0%;
}

.main-layout .header-right-wrapper {
  padding-right: 24px;
}

.main-layout .header {
  padding: 0;
  display: flex;
  align-items: center;
  height: 48px;
  overflow: hidden;
}

.main-layout .header-right-menu {
  display: flex;
  align-items: stretch;
}

.main-layout .header-right-menu .menu-item {
  box-shadow: none;
  height: 100%;
}

.main-layout .header-right-menu .menu-item-btn {
  padding: 0px 20px;
  width: 100%;
  height: 48px;
}

.main-layout .header-right-menu .menu-item-badge {
  z-index: 1;
  height: 48px;
}

.main-layout .header-right-menu .menu-item-user {
  margin-left: 10px;
}

.main-layout .expandable-search-wrapper {
  align-items: stretch;
}

.main-layout .footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: auto;
  min-height: 25px;
  padding: 0;
}

/* .main-layout .footer::after {
  content: "";
  flex: 1;
} */

.main-layout .footer .footer-logo {
  height: 22px;
  background-image: url("/images/yieldHUB-icon.svg");
  background-size: 22px auto;
  background-repeat: no-repeat;
  background-position-x: 11px;
  justify-self: start;
  /* flex: 1; */
}

.main-layout .footer .footer-logo > .footer-text {
  font-size: 18px;
  padding-left: 36px;
}

.main-layout .footer .copyright {
  text-align: center;
}

.main-layout .footer .version {
  text-align: right;
  padding-right: 8px;
}

.main-layout .footer > div {
  width: 33.33%;
}

.main-layout .side-menu-collapse-toogle {
  padding: 0 24px;
  font-size: 1rem;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

.main-layout .side-menu-collapse-toogle:hover {
  color: #1890ff;
}

.main-layout .menu-item-btn {
  border-radius: 0;
}

.main-layout .breadcrumb .active {
  color: #021d4d;
}

/***** ANT DESIGN OVERRIDES *****/

.main-layout .ant-menu .ant-menu-sub {
  color: #021d4d;
}
