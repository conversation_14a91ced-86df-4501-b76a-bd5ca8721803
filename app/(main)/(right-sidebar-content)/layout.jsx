"use client";

import { FilterFilled, CloseOutlined } from "@ant-design/icons";
import { theme, ConfigProvider, Layout, Button, Tooltip } from "antd";
import { useState } from "react";
import SearchFiltersForm from "../../../src/utils/forms/search_filters_form";
import Helper from "../../../src/utils/helper";
import { UserSettingsKeys } from "../../../src/utils/user_settings_keys";

const { Sider, Content } = Layout;
const { useToken } = theme;

const defaultData = {
  colorPrimary: "#154495",
  colorPrimaryBg: "#154495",
  colorBgTextHover: "#446AAA",
  controlItemBgActive: "#000000",
  colorSecondaryBg: "#EF6322",
};

/**
 * Layout component with right sidebar
 *
 * @param {string} pageKey
 * @param {Form} searchFilterForm
 * @param {function} setSearchFilterFields
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const RightSidebarContentLayout = ({
  pageKey,
  searchFilterForm,
  setSearchFilterFields,
  children,
}) => {
  const [collapsed, setCollapsed] = useState(
    Helper.getUserSettings(UserSettingsKeys.search_filters_is_collapsed),
  );
  const [data] = useState(defaultData);
  const { token } = useToken();

  /**
   * Set sidebar collapsed state
   */
  const onCollapse = () => {
    Helper.setUserSettings(
      UserSettingsKeys.search_filters_is_collapsed,
      !collapsed,
    );
    setCollapsed(!collapsed);
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: [theme.compactAlgorithm],
        token: {
          colorPrimary: data.colorPrimary,
          colorPrimaryBg: data.colorPrimaryBg,
          colorBgTextHover: data.colorBgTextHover,
          controlItemBgActive: token.controlItemBgActiveHover,
        },
        components: {
          Button: {
            colorPrimary: data.colorPrimaryBg,
          },
          Collapse: {
            colorFillAlter: data.colorPrimaryBg,
            colorTextHeading: token.colorWhite,
          },
          borderRadius: 0,
        },
      }}
    >
      <Layout className="h-full">
        <Content className="flex flex-col">{children}</Content>
        <Sider
          className="p-2"
          style={{
            background: token.yhLightBlue,
          }}
          width={340}
          collapsible
          collapsed={collapsed}
          collapsedWidth={0}
          trigger={null}
        >
          <Tooltip title={collapsed ? "Open Filters" : "Close Filters"}>
            <Button
              type="primary"
              className="absolute! w-9! h-9! -left-9! top-14! shadow-none!"
              style={{
                background: data.colorSecondaryBg,
              }}
              onClick={onCollapse}
            >
              {collapsed ? <FilterFilled /> : <CloseOutlined />}
            </Button>
          </Tooltip>
          <SearchFiltersForm
            pageKey={pageKey}
            searchFilterForm={searchFilterForm}
            setSearchFilterFields={setSearchFilterFields}
          />
        </Sider>
      </Layout>
    </ConfigProvider>
  );
};
export default RightSidebarContentLayout;
