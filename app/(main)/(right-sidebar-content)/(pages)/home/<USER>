"use client";

import { theme, ConfigProvider } from "antd";

const { useToken } = theme;

/**
 * Layout component of home page
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const HomeLayout = ({ children }) => {
  const { token } = useToken();

  return (
    <ConfigProvider
      theme={{
        components: {
          Collapse: {
            borderRadiusLG: 0,
            colorBorder: token.colorWhite,
          },
        },
      }}
    >
      {children}
    </ConfigProvider>
  );
};
export default HomeLayout;
