"use client";

import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { AgGridReact } from "ag-grid-react";
import {
  ReloadOutlined,
  SearchOutlined,
  DownOutlined,
} from "@ant-design/icons";
import { Button, notification, Space, Input, Dropdown, Select } from "antd";
import { handleStreamData } from "../../../../src/utils/stream_fetch";
import Api from "../../../../src/utils/api";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-balham.css";
import GridHelper from "../../../../src/utils/grid/grid_helper";
import { useBoundStore } from "../../../../src/store/store";
import { useEffectApiFetch } from "../../../../src/hooks";
import Helper from "../../../../src/utils/helper";

/**
 * NOT BEING USED ANYMORE SINCE WE ARE NOW USING COMMON GRID COMPONENT
 * Search grid component
 *
 * @param {object} params
 * @returns {JSX.Element}
 */
const Grid = ({ params }) => {
  const stream = params.get("stream");
  const [rowData, setRowData] = useState();
  const [curData, setCurData] = useState([]);
  const [downloadMenuItems, setDownloadMenuItems] = useState([]);
  const [downloadMenuProps, setDownloadMenuProps] = useState({});
  const [loadingDownload, setLoadingDownload] = useState(false);
  const [pageSizeOptions, setPageSizeOptions] = useState([]);
  const [pageSize, setPageSize] = useState(100);
  const setGridSelectionData = useBoundStore(
    (state) => state.setGridSelectionData,
  );
  const [columnDefs] = useState([
    {
      field: "checkbox",
      headerName: "",
      headerCheckboxSelection: true,
      checkboxSelection: true,
      headerCheckboxSelectionFilteredOnly: true,
      hide: false,
      lockPosition: true,
      resizable: false,
      sortable: false,
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      width: 30,
    },
    { field: "start_date", headerName: "Start Date" },
    { field: "lot_id", headerName: "Lot Id" },
    { field: "program", headerName: "Program" },
    {
      field: "file_name",
      headerName: "Filename",
      width: 300,
    },
    { field: "wir_wafer_id", headerName: "Wafer ID" },
    {
      field: "datalogged_parts",
      headerName: "Dlog Units",
      type: "numericColumn",
      valueFormatter: GridHelper.numberFormatter,
    },
    {
      field: "datalogged_good",
      headerName: "Dlog Good Units",
      type: "numericColumn",
      valueFormatter: GridHelper.numberFormatter,
    },
    {
      field: "datalogged_yield",
      headerName: "Dlog Yield",
      type: "numericColumn",
      valueFormatter: GridHelper.percentageFormatter,
    },
    {
      field: "datalogged_fail",
      headerName: "Dlog Failed Quantity",
      type: "numericColumn",
      valueFormatter: GridHelper.numberFormatter,
    },
    { field: "tester_node", headerName: "Tester Node" },
    { field: "manufacturing_process", headerName: "Manufacturing Process" },
    { field: "run_type", headerName: "Run Type" },
    { field: "subcon", headerName: "Subcon" },
    {
      field: "average_test_time",
      headerName: "Avg Test Time",
      type: "numericColumn",
    },
  ]);
  const defaultColDef = useMemo(() => ({
    sortable: true,
    resizable: true,
    sortingOrder: ["asc", "desc"],
  }));

  const gridRef = useRef();
  const tableParams = {};

  useEffectApiFetch(
    () => {
      const abortCtl =
        stream === "1"
          ? handleStreamData(setCurData, tableParams)
          : getGridData(tableParams);

      return abortCtl;
    },
    () => {
      setRowData([]);
      if (stream === "1") {
        setCurData([]);
      }
    },
    [],
  );

  useEffect(() => {
    let _rowData = rowData ? [...rowData] : [];
    if (curData.length) {
      _rowData.push(...curData);
      setRowData(_rowData);
    }
  }, [curData]);

  useEffect(() => {
    const menuItems = getDownloadMenuItems();
    setDownloadMenuItems(menuItems);
    const sizeOptions = getPageSizeOptions();
    setPageSizeOptions(sizeOptions);
  }, []);

  useEffect(() => {
    const menuProps = getDownloadMenuProps();
    setDownloadMenuProps(menuProps);
  }, [downloadMenuItems]);

  useEffect(() => {
    if (gridRef.current.api) {
      gridRef.current.api.paginationSetPageSize(pageSize);
    }
  }, [pageSize]);

  /**
   * Get download menu props
   *
   * @returns {object} menuProps
   */
  const getDownloadMenuProps = () => {
    const menuProps = {
      items: downloadMenuItems,
      onClick: handleDownloadMenuClick,
    };

    return menuProps;
  };

  /**
   * Get download menu items
   *
   * @returns {array} menuItems
   */
  const getDownloadMenuItems = () => {
    const menuItems = [
      {
        label: "Datalogs",
        key: "download_datalogs",
        disabled: true,
      },
      {
        label: "Table",
        key: "download_table",
      },
      {
        label: "Advanced",
        key: "download_advanced",
        disabled: true,
      },
    ];

    return menuItems;
  };

  /**
   * Get page size options
   *
   * @returns {array} sizeOptions
   */
  const getPageSizeOptions = () => {
    const sizes = [10, 25, 50, 100, 200, 400, 500];
    const sizeOptions = [];
    sizes.forEach((size) => {
      sizeOptions.push({
        value: size,
        label: size,
      });
    });

    return sizeOptions;
  };

  /**
   * Handler when clicking download menu
   *
   * @param {Event} e
   */
  const handleDownloadMenuClick = (e) => {
    switch (e.key) {
      case "download_table":
        downloadSearchTable();
        break;
    }
  };

  /**
   * Download search table data as CSV
   */
  const downloadSearchTable = () => {
    setLoadingDownload(true);
    Api.downloadSearchTable(
      (res) => {
        const filename = `search-table-data-${+new Date()}.csv`;
        Helper.downloadBlobAsFile(res, filename);
        setLoadingDownload(false);
      },
      (err) => {
        notification.error({
          message: "Download Table",
          description: err,
        });
        setLoadingDownload(false);
      },
      tableParams,
    );
  };

  /**
   * The grid has initialised and is ready for most api calls,
   * but may not be fully rendered yet
   */
  const onGridReady = () => {
    // move paging panel from the bottom to the top
    const pagingPanel =
      gridRef.current.api.gridOptionsWrapper.eGridDiv.getElementsByClassName(
        "ag-paging-panel",
      )[0];
    document.getElementsByClassName("paging-panel")[0].appendChild(pagingPanel);
  };

  /**
   * Fired the first time data is rendered into the grid
   */
  const onFirstDataRendered = () => {
    let columns = gridRef.current.columnApi.getColumns().filter((column) => {
      return column.getId() !== "file_name";
    });
    gridRef.current.columnApi.autoSizeColumns(columns);
  };

  /**
   * Fired when grid selection will change
   * Set grid selection data global state
   *
   * @param {SelectionChangedEvent} event
   */
  const onSelectionChanged = (event) => {
    const selectedRows = event.api.getSelectedRows();
    setGridSelectionData(selectedRows);
  };

  /**
   * Search for grid data
   *
   * @param {string} searched
   */
  const searchData = useCallback((searched) => {
    gridRef.current.api.setQuickFilter(searched);
  }, []);

  /**
   * Reload grid data
   */
  const reloadData = () => {
    setRowData(undefined);
    if (stream === "1") {
      handleStreamData(setCurData, tableParams);
    } else {
      getGridData(tableParams);
    }
  };

  /**
   * Get grid data
   *
   * @param {object} tableParams
   * @returns {AbortController} abortCtl
   */
  const getGridData = (tableParams) => {
    const abortCtl = Api.search(
      (res) => {
        if (res.success) {
          setRowData(res.data);
        } else {
          notification.warning({
            message: "Search",
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: "Search",
          description: err,
        });
      },
      {
        stream: stream === "1" ? 1 : 0,
        ...tableParams,
      },
    );

    return abortCtl;
  };

  return (
    <div>
      <div className="ag-toolbar ag-theme-balham">
        <Space>
          <Input
            placeholder=""
            allowClear
            onChange={(e) => {
              searchData(e.target.value);
            }}
            suffix={<SearchOutlined />}
          />
          <Button icon={<ReloadOutlined />} onClick={reloadData}>
            Reload Data
          </Button>
          <Dropdown menu={downloadMenuProps}>
            <Button loading={loadingDownload}>
              <Space>
                Download
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </Space>
        <Space className="float-right">
          <div>
            <span>Show:&nbsp;</span>
            <Select
              defaultValue={pageSize}
              onChange={setPageSize}
              options={pageSizeOptions}
              dropdownMatchSelectWidth={false}
            />
          </div>
          <div className="paging-panel float-right"></div>
        </Space>
      </div>
      <div
        className="ag-theme-balham"
        style={{ width: "100%", height: 560, marginTop: 5 }}
      >
        <AgGridReact
          ref={gridRef}
          rowData={rowData}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          paginationPageSize={pageSize}
          animateRows={true}
          pagination={true}
          rowSelection="multiple"
          onGridReady={onGridReady}
          onFirstDataRendered={onFirstDataRendered}
          onSelectionChanged={onSelectionChanged}
        />
      </div>
    </div>
  );
};

export default Grid;
