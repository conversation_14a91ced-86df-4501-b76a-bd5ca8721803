"use client";

import {
  App,
  But<PERSON>,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  Form,
  Modal,
  notification,
  Radio,
  Result,
  Row,
  Space,
  theme,
  Tooltip,
  Typography,
} from "antd";
import { RightOutlined, DownOutlined } from "@ant-design/icons";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Helper from "../../../../../src/utils/helper";
import yhLogo from "../../../../../public/images/yieldHUB-icon.svg";
import ResetPasswordForm from "../../../../../src/utils/forms/reset_password_form";
import Api from "../../../../../src/utils/api";
import { useBoundStore } from "../../../../../src/store/store";
import YHGrid from "../../../../../src/utils/grid/yh_grid";
import { useEffectApiFetch } from "../../../../../src/hooks";
import RightSidebarContentLayout from "../../layout";
import TraceabilityMenu from "../../../(full-width-content)/(pages)/traceability/traceability_menu";
import MetadataMenu from "../../../(full-width-content)/(pages)/metadata/metadata_menu";
import NPIMenu from "../../../(full-width-content)/(pages)/npi/npi_menu";
import CalculatedTestsMenu from "../../../(full-width-content)/(pages)/calculated_tests/calculated_tests_menu";
import DataCleansingMenu from "../../../(full-width-content)/(pages)/datacleansing/data_cleansing_menu";
import GenealogyLinkingMenu from "../../../(full-width-content)/(pages)/genealogy_linking/genealogy_linking_menu";
import { ComponentNameMapper } from "../../../../../src/utils/grid/component_name_mapper";
import { UserSettingsKeys } from "../../../../../src/utils/user_settings_keys";
import DataIntegrityGrid from "../../../../../src/utils/grid/components/data_integrity_grid";
import { SearchFilterFieldsMapper } from "../../../../../src/utils/forms/mappers/search_filter_fields_mapper";
import TestSelectionForm from "../../../../../src/utils/forms/test_selection_form";
import GridHelper from "../../../../../src/utils/grid/grid_helper";
import HomeLayout from "./layout";
import styles from "./styles.module.css";

const { Title, Text } = Typography;
const { useToken } = theme;

/**
 * Home page component
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function Home({ pageKey }) {
  // commented out for now to be used later on
  // const [isNewUser, setIsNewUser] = useState(false);
  const [isTestSelectionModalOpen, setIsTestSelectionModalOpen] =
    useState(false);
  const [initialPasswordChange, setInitialPasswordChange] = useState(true);
  const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] =
    useState(false);
  const [showChangePasswordForm, setShowChangePasswordForm] = useState(true);
  const [isPasswordChanged, setIsPasswordChanged] = useState(false);
  const [
    isApplyTopSearchFilterBtnDisabled,
    setIsApplyTopSearchFilterBtnDisabled,
  ] = useState(true);
  const [
    isProceedSelectedTestBtnDisabled,
    setIsProceedSelectedTestBtnDisabled,
  ] = useState(true);
  const [loading, setLoading] = useState(false);
  const [validateStatus, setValidateStatus] = useState();
  const [validateMessage, setValidateMessage] = useState();
  const [allowSubmit, setAllowSubmit] = useState(false);
  const [manufacturingProcessOptions, setManufacturingProcessOptions] =
    useState([]);
  const [topSearchFilterInitialValues, setTopSearchFilterInitialValues] =
    useState({});
  const [rowGroups, setRowGroups] = useState([]);
  const [analyseItems, setAnalyseItems] = useState([]);
  const [searchGridComponent, setSearchGridComponent] = useState();
  const [presetAnalysisTemplates, setPresetAnalysisTemplates] = useState([]);
  const [searchFilterFields, setSearchFilterFields] = useState([]);
  const [analysisInput, setAnalysisInput] = useState({});
  const [templateKey, setTemplateKey] = useState();
  const urlParams = useBoundStore((state) => state.urlParams);
  const setUrlParams = useBoundStore((state) => state.setUrlParams);
  const userAnalysisTemplates = useBoundStore(
    (state) => state.userAnalysisTemplates,
  );
  const setUserAnalysisTemplates = useBoundStore(
    (state) => state.setUserAnalysisTemplates,
  );
  const userData = useBoundStore((state) => state.userData);
  const filters = useBoundStore((state) => state.filters);
  const setUserData = useBoundStore((state) => state.setUserData);
  const setMfgProcess = useBoundStore((state) => state.setMfgProcess);
  const setFilters = useBoundStore((state) => state.setFilters);
  const currentPageData = useBoundStore((state) => state.currentPageData);
  const consolidationConfig = useBoundStore(
    (state) => state.consolidationConfig,
  );
  const setConsolidationConfig = useBoundStore(
    (state) => state.setConsolidationConfig,
  );
  const isDataIntegrityModalOpen = useBoundStore(
    (state) => state.isDataIntegrityModalOpen,
  );
  const setIsDataIntegrityModalOpen = useBoundStore(
    (state) => state.setIsDataIntegrityModalOpen,
  );
  const gridComponentRefs = useBoundStore((state) => state.gridComponentRefs);

  const [changePasswordForm] = Form.useForm();
  const [topSearchFilterForm] = Form.useForm();
  const [searchFilterForm] = Form.useForm();
  const [testSelectionForm] = Form.useForm();
  const { message } = App.useApp();
  const searchGridWrapperRef = useRef();
  const searchGridRef = useRef();
  const searchGridId = ComponentNameMapper.home_search_table;
  const { token } = useToken();
  const queryClient = useQueryClient();

  useEffect(() => {
    setUserData(Helper.getUserData());
    if (filters[pageKey] === undefined) {
      filters[pageKey] = {};
    }
  }, []);

  useEffect(() => {
    // commented out for now to be used later on
    // setIsNewUser(userData.new_user);
    setInitialPasswordChange(userData?.initial_password_change);
  }, [userData]);

  useEffect(() => {
    setIsChangePasswordModalOpen(!initialPasswordChange);
  }, [initialPasswordChange]);

  useEffect(() => {
    // set filters from search filter fields
    const searchFilters = Helper.getFilterValues(
      searchFilterForm.getFieldsValue(),
      filters,
      pageKey,
    );
    Object.keys(searchFilters).forEach((fieldKey) => {
      if (searchFilters[fieldKey] !== undefined) {
        filters[pageKey][fieldKey] = searchFilters[fieldKey];
      }
    });

    const initialFilters = {
      manufacturing_process: manufacturingProcessOptions.map((mfgProcess) => {
        return mfgProcess.value;
      }),
      show: ["production_data"],
      grouping: "lot_id",
    };
    const savedUserFilters =
      Helper.getUserSettings(UserSettingsKeys.top_search_filter_values) ?? {};
    Object.keys(initialFilters).forEach((key) => {
      if (savedUserFilters[key]) {
        initialFilters[key] = savedUserFilters[key];
      }
    });
    setTopSearchFilterInitialValues(initialFilters);
  }, [manufacturingProcessOptions]);

  useEffect(() => {
    if (
      topSearchFilterInitialValues.manufacturing_process &&
      topSearchFilterInitialValues.manufacturing_process.length > 0
    ) {
      topSearchFilterForm.resetFields();
      topSearchFilterForm.submit();
    }
  }, [topSearchFilterInitialValues]);

  useEffect(() => {
    if (pageKey === currentPageData.key) {
      if (searchGridRef?.current?.api) {
        searchGridRef.current.api.deselectAll();
      }
      setMfgProcess(filters[pageKey].manufacturing_process);
      setRowGrouping(filters[pageKey].row_group);

      // update topSearchFilterForm 'show' field value if it differs from the filters value
      handleShowFieldUpdate();
    }
  }, [filters[pageKey]]);

  useEffect(() => {
    if (pageKey === currentPageData.key && urlParams[pageKey]) {
      delete urlParams[pageKey].template_key;
      delete urlParams[pageKey].surl;
      filters[pageKey] = merge(filters[pageKey], urlParams[pageKey]);
    }
  }, [urlParams[pageKey]]);

  useEffectApiFetch(
    () => {
      let urlParameters = Helper.getUrlParameters();
      if (urlParameters["surl"] !== undefined) {
        const surl = urlParameters["surl"];
        delete urlParameters["surl"];
        return Helper.getLongUrlParams(
          surl,
          urlParameters,
          setUrlParameters,
          {},
          message,
        );
      } else {
        setUrlParameters(urlParameters);
      }
    },
    () => {
      let urlParamsCopy = Helper.cloneObject(urlParams);
      urlParamsCopy[pageKey] = {};
      setUrlParams(urlParamsCopy);
    },
  );

  useEffectApiFetch(
    () => {
      if (userData?.initial_password_change) {
        return getSearchGridComponent();
      }
    },
    () => {
      setSearchGridComponent();
    },
  );

  useEffectApiFetch(
    () => {
      if (userData?.initial_password_change) {
        return Helper.getPresetAnalysisTemplates(setPresetAnalysisTemplates);
      }
    },
    () => {
      setPresetAnalysisTemplates([]);
    },
  );

  useEffectApiFetch(
    () => {
      if (userData?.initial_password_change) {
        return Helper.getUserAnalysisTemplates(setUserAnalysisTemplates);
      }
    },
    () => {
      setUserAnalysisTemplates([]);
    },
  );

  useEffectApiFetch(
    () => {
      if (userData?.initial_password_change) {
        return Helper.getManufacturingProcessOptions(
          setManufacturingProcessOptions,
        );
      }
    },
    () => {
      setManufacturingProcessOptions([]);
    },
  );

  useEffectApiFetch(
    () => {
      if (userData?.initial_password_change) {
        return getConsolidationConfig();
      }
    },
    () => {
      setConsolidationConfig();
    },
  );

  /**
   * Open test selection modal for generating Selected Test Report
   *
   * @param {string} key
   */
  const openTestSelection = (key) => {
    setTemplateKey(key);
    setIsTestSelectionModalOpen(true);
  };

  /**
   * Action mapper for preprocess when generating analysis
   */
  const preprocesses = {
    openTestSelection,
  };

  /**
   * Grid event handlers
   */
  const eventHandlers = {
    onSelectionChanged: () => {
      const input = Helper.getAnalysisInput(
        searchGridRef.current,
        searchGridComponent,
      );
      setAnalysisInput(input);
    },
  };

  /**
   * Updates the "show" field value in the topSearchFilterForm if it differs from filters value
   */
  const handleShowFieldUpdate = () => {
    if (
      filters[pageKey].show &&
      filters[pageKey].show !==
        topSearchFilterForm.getFieldValue("show")?.join()
    ) {
      topSearchFilterForm.setFieldValue(
        "show",
        filters[pageKey].show?.split(","),
      );
      topSearchFilterForm.submit();
    }
  };

  /**
   * Set url parameters global state used for fetching data
   *
   * @param {object} urlParameters
   * @param {object} surlParams
   */
  const setUrlParameters = (urlParameters, surlParams) => {
    if (surlParams !== undefined) {
      urlParameters = { ...urlParameters, ...surlParams };
    }
    let urlParamsCopy = Helper.cloneObject(urlParams);
    urlParamsCopy[pageKey] = urlParameters;
    setUrlParams(urlParamsCopy);
  };

  /**
   * Get consolidation grouping field list
   *
   * @returns {AbortController} abortCtl
   */
  const getConsolidationConfig = () => {
    const abortCtl = Api.getConsolidationFieldOptions(
      (res) => {
        if (res.success) {
          setConsolidationConfig(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
    );

    return abortCtl;
  };

  /**
   * Set row grouping
   *
   * @param {string} rowGroup
   */
  const setRowGrouping = (rowGroup) => {
    if (rowGroup) {
      topSearchFilterForm.setFieldValue("grouping", rowGroup);
    }
    setRowGroups([topSearchFilterForm.getFieldValue("grouping")]);
    // remove row group filter since it is only used for setting initial row grouping
    delete filters[pageKey].row_group;
  };

  /**
   * Generate 'Analyse As' menu items
   *
   * @param {boolean} open
   */
  const generateAnalyseItems = (open) => {
    if (open) {
      const items = presetAnalysisTemplates
        .filter((template) => {
          const templateData = JSON.parse(template.template_data);
          return templateData.has_datalog_selection === true;
        })
        .map((template) => {
          const invalidMessages = Helper.validateAnalysisInput(
            analysisInput,
            template,
          );
          const isValid = invalidMessages.length === 0;
          const selectedNodes = GridHelper.getSelectedNodes(
            searchGridRef.current,
            true,
          );
          let dskCount = 0;
          selectedNodes.forEach((node) => {
            if (node.group) {
              dskCount += node.data.dlog_count ?? 0;
            } else if (node.parent && !node.parent.isSelected()) {
              dskCount++;
            }
          });
          const menuItemChildren = Helper.validateChildrenRulesByCondition(
            { dsk: dskCount },
            template,
          );
          return {
            key: `preset_${template.id}`,
            label: isValid ? (
              template.name
            ) : (
              <Tooltip title={invalidMessages} placement="right">
                {template.name}
              </Tooltip>
            ),
            children: menuItemChildren.length > 0 ? menuItemChildren : null,
            disabled: !isValid,
          };
        });

      // Grouping is not needed since we only support preset templates for now
      // // preset templates
      // const presetTemplatesGroup = {
      //   key: "preset-templates",
      //   type: "group",
      //   label: "Preset",
      //   children: presetAnalysisTemplates
      //     .filter((template) => {
      //       return template.key.indexOf(hiddenAnalysisTemplateKeys) === -1;
      //     })
      //     .map((template) => {
      //       return {
      //         key: `preset_${template.id}`,
      //         label: template.name,
      //       };
      //     }),
      // };
      // items.push(presetTemplatesGroup);

      // Commented out since we don't support user templates for now
      // // user templates
      // const userTemplatesGroup = {
      //   key: "user-templates",
      //   type: "group",
      //   label: "User",
      //   children: userAnalysisTemplates.map((template) => {
      //     return {
      //       key: `user_${template.id}`,
      //       label: template.name,
      //     };
      //   }),
      // };
      // items.push(userTemplatesGroup);

      setAnalyseItems(items);
    }
  };

  /**
   * Get and set search grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getSearchGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setSearchGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: ComponentNameMapper.home_search_table,
      },
    );

    return abortCtl;
  };

  /**
   * Initialize analysis
   *
   * @param {string} key
   * @param {array} keyPath
   */
  const initAnalysis = ({ key, keyPath }) => {
    const additionalInput =
      key === "grouped" ? { group_by: "lot_id", group_by_label: "Lot Id" } : {};
    key = keyPath[keyPath.length - 1];
    const template = Helper.getTemplateByKeyId(
      key,
      presetAnalysisTemplates,
      userAnalysisTemplates,
    );
    const templateData = JSON.parse(template.template_data);

    Helper.getAnalysisInput(
      searchGridRef.current,
      searchGridComponent,
      templateData.selection_type,
      generateAnalysis,
      { key, template, templateData, additionalInput },
      message.warning,
      message.error,
    );
  };

  /**
   * Generate analysis
   *
   * @param {object} input
   * @param {object} params
   */
  const generateAnalysis = (input, params) => {
    setAnalysisInput(input);
    const { key, template, templateData, additionalInput } = params;
    const invalidMessages = Helper.validateAnalysisInput(
      input,
      template,
      message,
    );

    if (invalidMessages.length === 0) {
      if (templateData.preprocess) {
        preprocesses[templateData.preprocess](key);
      } else {
        Helper.generateAnalysis(
          template.key,
          merge(input, additionalInput ?? {}),
          presetAnalysisTemplates,
          queryClient,
          message,
        );
      }
    }
  };

  /**
   * Change user password
   *
   * @param {object} values
   */
  const changePassword = (values) => {
    setLoading(true);
    Api.changePassword(
      (res) => {
        if (res.success) {
          setValidateStatus("success");
          setValidateMessage(res.message);
          onCloseChangePasswordModal();
          showPasswordChangedModal();
          Helper.logoutUser(false);
        } else {
          setValidateStatus("error");
          setValidateMessage(res.message);
        }
        setLoading(false);
      },
      (err) => {
        setValidateStatus("error");
        setValidateMessage(err);
        setLoading(false);
      },
      {
        new_password: values.password,
        new_password_confirmation: values.password_confirmation,
      },
    );
  };

  /**
   * Show password changed modal and close change password modal
   */
  const showPasswordChangedModal = () => {
    setIsChangePasswordModalOpen(false);
    setIsPasswordChanged(true);
  };

  /**
   * Triggered when change password modal is closed
   */
  const onCloseChangePasswordModal = () => {
    Helper.updateUserDataStorage("new_user", false, setUserData);
  };

  /**
   * Show guided tour
   */
  const showGuidedTour = () => {
    setIsChangePasswordModalOpen(false);
    onCloseChangePasswordModal();
    notification.warning({
      message: "Guided Tour",
      description: "Coming Soon!",
    });
  };

  /**
   * Triggers when top search filter field updated
   * Enable apply selection button for top search filter
   */
  const onTopSearchFilterFieldsChange = () => {
    setIsApplyTopSearchFilterBtnDisabled(false);
  };

  /**
   * Triggers when 'manufacturing process' field changes
   * Do not allow no selection and always set all manufacturing process as default value
   *
   * @param {array} checkedValues
   */
  const onMfgProcessFieldChange = (checkedValues) => {
    if (checkedValues.length === 0) {
      topSearchFilterForm.setFieldValue(
        "manufacturing_process",
        topSearchFilterInitialValues["manufacturing_process"],
      );
    }
  };

  /**
   * Apply top search filter
   *
   * @param {object} values
   */
  const applyTopSearchFilter = (values) => {
    setIsApplyTopSearchFilterBtnDisabled(true);
    if (values.manufacturing_process.length > 0) {
      const filtersCopy = Helper.cloneObject(filters);
      if (filtersCopy[pageKey] === undefined) {
        filtersCopy[pageKey] = {};
      }
      if (Array.isArray(values.manufacturing_process)) {
        filtersCopy[pageKey].manufacturing_process =
          values.manufacturing_process.join();
      }
      if (Array.isArray(values.show)) {
        filtersCopy[pageKey].show = values.show.join();
      }
      setFilters(filtersCopy);
      gridComponentRefs[searchGridId]?.current?.reloadGridData();

      Helper.setUserSettings(UserSettingsKeys.top_search_filter_values, values);
    }
  };

  /**
   * Revert to default top search filter values
   */
  const revertToDefaultTopSearchFilter = () => {
    topSearchFilterForm.resetFields();
    topSearchFilterForm.submit();
  };

  /**
   * Triggers when 'show' field changes
   * Do not allow no selection and always set 'Production Data' as default value
   *
   * @param {array} checkedValues
   */
  const onShowFieldChange = (checkedValues) => {
    if (checkedValues.length === 0) {
      message.error("This filter should not be empty.");
      topSearchFilterForm.setFieldValue("show", ["production_data"]);
    }
  };

  /**
   * Initialize Selected Test Report
   *
   * @param {object} values
   */
  const initSelectedTestReport = (values) => {
    setIsTestSelectionModalOpen(false);
    const additionalInput = {
      tnum: values.tnum.value.split("|")[1],
      tnum_dsk: values.tnum.value.split("|")[3],
    };
    const template = Helper.getTemplateByKeyId(
      templateKey,
      presetAnalysisTemplates,
      userAnalysisTemplates,
    );
    if (values.group_by) {
      additionalInput.group_by = values.group_by;
    }
    Helper.generateAnalysis(
      template.key,
      merge(analysisInput, additionalInput),
      presetAnalysisTemplates,
      queryClient,
      message,
    );

    testSelectionForm.resetFields();
    setIsProceedSelectedTestBtnDisabled(true);
  };

  /**
   * Close test selection modal and reset form
   */
  const handleCancelTestSelection = () => {
    setIsTestSelectionModalOpen(false);
    testSelectionForm.resetFields();
    setIsProceedSelectedTestBtnDisabled(true);
  };

  return (
    <RightSidebarContentLayout
      pageKey={pageKey}
      searchFilterForm={searchFilterForm}
      setSearchFilterFields={setSearchFilterFields}
    >
      <HomeLayout>
        <div className="flex flex-col h-full">
          {isTestSelectionModalOpen && (
            <Modal
              key="test_selection_modal"
              width={450}
              centered
              destroyOnClose
              open={isTestSelectionModalOpen}
              title="Select Test"
              okText="Proceed"
              cancelText="Close"
              onCancel={handleCancelTestSelection}
              onOk={() => testSelectionForm.submit()}
              okButtonProps={{
                disabled: isProceedSelectedTestBtnDisabled,
              }}
            >
              <TestSelectionForm
                pageKey={pageKey}
                form={testSelectionForm}
                onFinish={initSelectedTestReport}
                input={analysisInput}
                setIsProceedSelectedTestBtnDisabled={
                  setIsProceedSelectedTestBtnDisabled
                }
              />
            </Modal>
          )}
          {isDataIntegrityModalOpen && (
            <Modal
              key="data_integrity_index_modal"
              width={"45%"}
              centered
              open={isDataIntegrityModalOpen}
              title="Lot Integrity Index Breakdown"
              onCancel={() => setIsDataIntegrityModalOpen(false)}
              footer={[
                <Button
                  key="close"
                  onClick={() => setIsDataIntegrityModalOpen(false)}
                >
                  Close
                </Button>,
              ]}
            >
              <DataIntegrityGrid pageKey={pageKey} />
            </Modal>
          )}
          {isChangePasswordModalOpen && (
            <Modal
              title=""
              className={styles.changePasswordModal}
              open={isChangePasswordModalOpen}
              onCancel={onCloseChangePasswordModal}
              footer={null}
              centered
              closable={false}
              maskClosable={false}
            >
              <div className={styles.changePasswordModalContent}>
                <Title
                  className="mb-0!"
                  style={{ color: token.yhColorSubmenu }}
                >
                  Welcome to yieldHub,{" "}
                  {userData?.full_name?.replace(/\s.*/g, "")}!
                </Title>
                <Title
                  level={3}
                  className="font-normal! mt-2!"
                  style={{ color: token.colorPrimaryBg }}
                >
                  Start turning data into actionable insights.
                </Title>
                <Image
                  src={yhLogo}
                  className={`${styles.yhLogo} my-4`}
                  alt="yieldHUB"
                ></Image>
                <Title
                  level={4}
                  className="font-normal! mb-5!"
                  type="secondary"
                >
                  {initialPasswordChange
                    ? "Let's find out how we can help you in less than a minute."
                    : "Before we start, let's protect your account by changing your temporary password."}
                </Title>
                {showChangePasswordForm ? (
                  <ResetPasswordForm
                    form={changePasswordForm}
                    onFinish={changePassword}
                    validateMessage={validateMessage}
                    validateStatus={validateStatus}
                    allowSubmit={allowSubmit}
                    setAllowSubmit={setAllowSubmit}
                    loading={loading}
                  />
                ) : (
                  <Button
                    type="primary"
                    icon={<RightOutlined />}
                    className="mb-2"
                    onClick={() =>
                      initialPasswordChange
                        ? showGuidedTour()
                        : setShowChangePasswordForm(true)
                    }
                  >
                    Let&apos;s Go!
                  </Button>
                )}
                {!initialPasswordChange && (
                  <Button
                    className="hidden"
                    type="link"
                    block
                    onClick={showGuidedTour}
                  >
                    Skip to Guided Tour
                  </Button>
                )}
              </div>
            </Modal>
          )}
          {isPasswordChanged && (
            <Modal
              title=""
              className={styles.changePasswordModal}
              open={isPasswordChanged}
              onCancel={() => setIsPasswordChanged(false)}
              footer={null}
              centered
              closable={false}
              maskClosable={false}
            >
              <Result
                className="py-16"
                status="success"
                title="Your password has been reset successfully!"
                subTitle="Use your new password to login"
                extra={[
                  <Button
                    type="primary"
                    key="relogin_btn"
                    icon={<RightOutlined />}
                    onClick={() => Helper.logoutUser()}
                  >
                    Relogin
                  </Button>,
                ]}
              />
            </Modal>
          )}
          {userData?.initial_password_change && (
            <>
              <Row
                className="px-4 py-3"
                style={{
                  backgroundColor: token.colorBgContainer,
                }}
              >
                <Col span={24}>
                  <Form
                    form={topSearchFilterForm}
                    layout="inline"
                    initialValues={topSearchFilterInitialValues}
                    onFieldsChange={onTopSearchFilterFieldsChange}
                    onFinish={(values) => applyTopSearchFilter(values)}
                  >
                    <Space>
                      <Form.Item
                        name="manufacturing_process"
                        label={
                          <Text className="mr-1" strong>
                            Manufacturing Process:
                          </Text>
                        }
                        colon={false}
                      >
                        <Checkbox.Group
                          onChange={onMfgProcessFieldChange}
                          options={manufacturingProcessOptions}
                        />
                      </Form.Item>
                      <Form.Item name="grouping" className="me-0!">
                        <Radio.Group>
                          <Radio value="lot_id">Lot ID</Radio>
                          <Radio value="datalog">Datalog</Radio>
                        </Radio.Group>
                      </Form.Item>
                      <Divider type="vertical" className="ml-0" />
                      <Form.Item name="show" noStyle>
                        <Checkbox.Group onChange={onShowFieldChange}>
                          <Space>
                            <Checkbox value="production_data">
                              {SearchFilterFieldsMapper.production_data.label}
                            </Checkbox>
                            <Checkbox value="personal_data">
                              {SearchFilterFieldsMapper.personal_data.label}
                            </Checkbox>
                            <Checkbox value="other_personal_data">
                              {
                                SearchFilterFieldsMapper.other_personal_data
                                  .label
                              }
                            </Checkbox>
                          </Space>
                        </Checkbox.Group>
                      </Form.Item>
                      <Button
                        type="primary"
                        disabled={isApplyTopSearchFilterBtnDisabled}
                        htmlType="submit"
                      >
                        Apply Selection
                      </Button>
                      <Button onClick={() => revertToDefaultTopSearchFilter()}>
                        Revert to Default Selections
                      </Button>
                    </Space>
                  </Form>
                </Col>
              </Row>
              <Row
                className={`px-4 py-2 border-solid border-x-0	border-y`}
                style={{
                  backgroundColor: token.colorBgContainer,
                  borderColor: `${token.yhLightBlue}`,
                }}
              >
                <Col span={24}>
                  <Space size="large">
                    <Dropdown
                      menu={{
                        items: analyseItems,
                        onClick: initAnalysis,
                      }}
                      trigger="click"
                      onOpenChange={generateAnalyseItems}
                    >
                      <Button type="primary">
                        <Space>
                          Analyze
                          <DownOutlined />
                        </Space>
                      </Button>
                    </Dropdown>
                    <TraceabilityMenu
                      gridRef={searchGridRef}
                      pageKey={pageKey}
                      queryClient={queryClient}
                      getSearchTableSelection={() =>
                        Helper.getAnalysisInput(
                          searchGridRef.current,
                          searchGridComponent,
                        )
                      }
                    ></TraceabilityMenu>
                    {consolidationConfig?.consolidation && (
                      <DataCleansingMenu
                        searchGridComponent={searchGridComponent}
                        pageKey={pageKey}
                      ></DataCleansingMenu>
                    )}
                    <MetadataMenu
                      queryClient={queryClient}
                      getSearchTableSelection={() =>
                        Helper.getAnalysisInput(
                          searchGridRef.current,
                          searchGridComponent,
                        )
                      }
                      searchGridRef={searchGridRef}
                    ></MetadataMenu>
                    <NPIMenu
                      searchGridRef={searchGridRef}
                      getSearchTableSelection={() =>
                        Helper.getAnalysisInput(
                          searchGridRef.current,
                          searchGridComponent,
                        )
                      }
                    ></NPIMenu>
                    <GenealogyLinkingMenu
                      pageKey={pageKey}
                      analysisInput={analysisInput}
                    />
                    <CalculatedTestsMenu
                      pageKey={pageKey}
                      getSearchTableSelection={() =>
                        Helper.getAnalysisInput(
                          searchGridRef.current,
                          searchGridComponent,
                        )
                      }
                    />
                  </Space>
                </Col>
              </Row>
              <Row
                className="m-2 px-2 py-4 flex grow"
                style={{
                  backgroundColor: token.colorBgContainer,
                  height: "100%",
                }}
              >
                <Col span={24}>
                  {searchGridComponent &&
                    filters?.[pageKey]?.manufacturing_process && (
                      <YHGrid
                        ref={searchGridWrapperRef}
                        gridRef={searchGridRef}
                        gridId={searchGridId}
                        gridOptions={{ rowModelType: "serverSide" }}
                        gridFilterForm={searchFilterForm}
                        gridFilterFields={searchFilterFields}
                        component={searchGridComponent}
                        pageKey={pageKey}
                        filters={filters}
                        setFilters={setFilters}
                        rowGroups={rowGroups}
                        wrapperClassName="flex grow flex-col h-full"
                        eventHandlers={eventHandlers}
                      />
                    )}
                </Col>
              </Row>
            </>
          )}
        </div>
      </HomeLayout>
    </RightSidebarContentLayout>
  );
}
