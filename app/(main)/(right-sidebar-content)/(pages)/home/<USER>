"use client";

import { Form, notification } from "antd";
import { useRouter } from "next/navigation";
import { useBoundStore } from "../../../../../src/store/store";
import TemplatesDropdown from "../../../../../src/utils/components/templates_dropdown";
import Helper from "../../../../../src/utils/helper";

/**
 * Page options component of search page
 *
 * @returns {JSX.Element}
 */
export default function SearchPageOptions() {
  const gridSelectionData = useBoundStore((state) => state.gridSelectionData);
  const [form] = Form.useForm();
  const router = useRouter();

  /**
   * Generate analysis
   *
   * @param {string} template
   */
  const generateAnalysis = (template) => {
    form.resetFields();
    if (gridSelectionData.selectionStoreKey.length) {
      const dsks = gridSelectionData.selectionStoreKey.map((data) => {
        return data.data_struc_key;
      });

      if (dsks.length > 1) {
        const dsksParam = dsks.join(",");
        const surlParams = {
          dsk: dsksParam,
        };
        const urlParams = {
          template: template,
        };
        Helper.getShortUrl(surlParams, urlParams, goToAnalysis);
      } else {
        const urlParams = {
          dsk: dsks[0],
          template: template,
        };
        goToAnalysis(urlParams);
      }
    } else {
      notification.warning({
        message: "Generate Analysis",
        description: "Please select a datalog first.",
        placement: "top",
      });
    }
    // TODO
  };

  /**
   * Redirect to analysis page
   *
   * @param {object} urlParams - url parameters
   * @param {string} surl - short url value
   */
  const goToAnalysis = (urlParams, surl) => {
    if (surl !== undefined) {
      urlParams = { ...urlParams, ...{ surl: surl } };
    }
    const queryString = Helper.createQueryString(urlParams);
    router.push(`/analysis?${queryString}`);
  };

  return (
    <div>
      <Form form={form}>
        <Form.Item name="analysisTemplate">
          <TemplatesDropdown onChange={generateAnalysis} />
        </Form.Item>
      </Form>
    </div>
  );
}
