"use client";

import { useEffect, useState } from "react";
import { ConfigProvider, theme } from "antd";
import dynamic from "next/dynamic";
import PageLoading from "../loading";
import WebSockets from "../../src/utils/websockets";

const MainLayout = dynamic(() => import("./main_layout"), {
  ssr: false,
  loading: () => <PageLoading />,
});

const defaultData = {
  colorPrimary: "#EF6322",
  colorPrimaryBg: "#154495",
  colorBgTextHover: "#446AAA",
  yhColorSubmenu: "#021D4D",
  yhColorLightOrange: "#F7C19B",
  yhColorSafetyOrange: "#FF6700",
  yhGrey: "#2F3340",
  yhMidnight: "#003366",
  yhLightBlue: "#E8EDFC",
  yhHeaderColorBg: "#446AAA",
  yhPageColorBg: "#F9FAFF", // Background color for the radio button
  buttonCheckedBg: "#AAC7F6", // Background color when selected
};

const defaultInputToken = {
  colorPrimary: "#1677ff",
  colorPrimaryBorder: "#91caff",
  colorPrimaryHover: "#4096ff",
};

/**
 * Root layout
 *
 * @param {JSX.Element} children
 * @returns
 */
const Layout = ({ children }) => {
  const [data] = useState(defaultData);

  useEffect(() => {
    setTimeout(() => {
      WebSockets.connect();
    }, 200);
    return () => {
      setTimeout(() => {
        WebSockets.disconnect();
      }, 200);
    };
  }, []);

  return (
    <ConfigProvider
      theme={{
        algorithm: [theme.compactAlgorithm],
        token: {
          colorPrimary: data.colorPrimary,
          colorPrimaryBg: data.colorPrimaryBg,
          colorBgTextHover: data.colorBgTextHover,
          yhColorSubmenu: data.yhColorSubmenu,
          yhColorLightOrange: data.yhColorLightOrange,
          yhColorSafetyOrange: data.yhColorSafetyOrange,
          yhGrey: data.yhGrey,
          yhMidnight: data.yhMidnight,
          yhLightBlue: data.yhLightBlue,
          yhHeaderColorBg: data.yhHeaderColorBg,
          yhPageColorBg: data.yhPageColorBg,
          buttonBg: data.buttonBg,
          buttonColor: data.buttonColor,
        },
        components: {
          Layout: {
            bodyBg: data.yhPageColorBg,
          },
          Button: {
            colorPrimary: data.colorPrimaryBg,
            defaultHoverBorderColor: defaultInputToken.colorPrimary,
          },
          Checkbox: defaultInputToken,
          Radio: {
            buttonSolidCheckedBg: data.buttonCheckedBg,
            buttonSolidCheckedColor: data.yhMidnight,
          },
          Tag: {
            colorTextLightSolid: "rgba(0, 0, 0, 0.45)",
          },
          Input: {
            activeBorderColor: defaultInputToken.colorPrimary,
            activeShadow: "0 0 0 2px rgba(5, 145, 255, 0.1)",
            colorPrimaryHover: defaultInputToken.colorPrimaryHover,
          },
          InputNumber: {
            activeBorderColor: defaultInputToken.colorPrimary,
            activeShadow: "0 0 0 2px rgba(5, 145, 255, 0.1)",
            colorPrimaryHover: defaultInputToken.colorPrimaryHover,
          },
          Select: {
            activeBorderColor: defaultInputToken.colorPrimary,
            activeShadow: "0 0 0 2px rgba(5, 145, 255, 0.1)",
            colorPrimaryHover: defaultInputToken.colorPrimaryHover,
            colorPrimary: defaultInputToken.colorPrimary,
            controlOutline: "rgba(5, 145, 255, 0.1)",
            optionActiveBg: data.yhColorLightOrange,
          },
          Dropdown: {
            colorPrimaryBorder: defaultInputToken.colorPrimary,
            activeShadow: "0 0 0 2px rgba(5, 145, 255, 0.1)",
            controlOutline: "rgba(5, 145, 255, 0.1)",
            controlItemBgHover: data.yhColorLightOrange,
          },
          Drawer: {
            colorBgElevated: "rgba(233, 237, 251)",
          },
        },
      }}
    >
      <MainLayout>{children}</MainLayout>
    </ConfigProvider>
  );
};
export default Layout;
