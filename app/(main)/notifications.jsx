import { Avatar, List, message } from "antd";
import VirtualList from "rc-virtual-list";
import { useEffect, useState } from "react";
const fakeDataUrl = "/json/notifications.json";
const ContainerHeight = 300;

/**
 * Notifications list component
 *
 * @returns {JSX.Element}
 */
const Notifications = () => {
  const [data, setData] = useState([]);

  /**
   * Set notifications list data
   */
  const appendData = () => {
    fetch(fakeDataUrl)
      .then((res) => res.json())
      .then((body) => {
        setData(data.concat(body.results));
        message.success(`${body.results.length} more items loaded!`);
      });
  };

  useEffect(() => {
    appendData();
  }, []);

  /**
   * Trigger when scrolling notifications list
   *
   * @param {Event} e
   */
  const onScroll = (e) => {
    if (
      e.currentTarget.scrollHeight - e.currentTarget.scrollTop ===
      ContainerHeight
    ) {
      appendData();
    }
  };

  return (
    <List>
      <VirtualList
        data={data}
        height={ContainerHeight}
        itemHeight={47}
        itemKey="email"
        onScroll={onScroll}
      >
        {(item) => (
          <List.Item key={item.email}>
            <List.Item.Meta
              avatar={<Avatar src={item.picture.large} />}
              title={<a href="https://ant.design">{item.name.last}</a>}
              description={item.email}
            />
            <div>Content</div>
          </List.Item>
        )}
      </VirtualList>
    </List>
  );
};
export default Notifications;
