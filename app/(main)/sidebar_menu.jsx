"use client";

import {
  HomeOutlined,
  // DashboardOutlined,
  UploadOutlined,
  UserOutlined,
  // QuestionCircleOutlined,
  SettingOutlined,
  // PicLeftOutlined,
  // FieldTimeOutlined,
  BookOutlined,
  ToolOutlined,
} from "@ant-design/icons";
import { theme, Menu, ConfigProvider, App } from "antd";
import {
  useSelectedLayoutSegment,
  useSelectedLayoutSegments,
} from "next/navigation";
import { useEffect, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useBoundStore } from "../../src/store/store";
import Helper from "../../src/utils/helper";

const { useToken } = theme;
const adminMenuItemKeys = ["user_management", "template"];

/**
 * Sidebar menu of site page
 *
 * @returns {JSX.Element}
 */
const SidebarMenu = () => {
  const [menuItems, setMenuItems] = useState([]);
  const setPageSelectedKeys = useBoundStore(
    (state) => state.setPageSelectedKeys,
  );
  const currentPageData = useBoundStore((state) => state.currentPageData);
  const userData = useBoundStore((state) => state.userData);
  const activeSegments = useSelectedLayoutSegments();
  const selectedLayoutSegment = useSelectedLayoutSegment();
  const { message } = App.useApp();
  const { token } = useToken();
  const [selectedKey, setSelectedKey] = useState(currentPageData.key);
  const queryClient = useQueryClient();

  const sourceMenuItems = [
    {
      key: "home",
      icon: <HomeOutlined />,
      label: "Home",
    },
    // {
    //   key: "dashboard",
    //   icon: <DashboardOutlined />,
    //   label: "Dashboard",
    // },
    // {
    //   key: "oee",
    //   icon: <FieldTimeOutlined />,
    //   label: "OEE",
    // },
    {
      key: "uploads",
      icon: <UploadOutlined />,
      label: "Uploads",
      children: [
        {
          label: "Engineering & Production Files",
          key: "engineering_production_files_upload",
          style: {
            color: token.colorWhite,
          },
        },
        {
          label: "Datalog Processing Updates",
          key: "datalog_processing_updates",
          style: {
            color: token.colorWhite,
          },
        },
      ],
    },
    {
      key: "recipes_menu",
      icon: <BookOutlined />,
      label: "Recipes",
      children: [
        {
          label: "Production Recipes",
          key: "recipes",
          style: {
            color: token.colorWhite,
          },
        },
        {
          label: "My Drafts",
          key: "recipe_drafts",
          style: {
            color: token.colorWhite,
          },
        },
      ],
    },
    // {
    //   key: "template",
    //   icon: <PicLeftOutlined />,
    //   label: "Analysis Template",
    // },
    {
      key: "user_management",
      icon: <UserOutlined />,
      label: "User Management",
    },
    {
      key: "my_profile",
      icon: <SettingOutlined />,
      label: "Settings",
    },
    {
      key: "admin_settings",
      icon: <ToolOutlined />,
      label: "Admin Settings",
      children: [
        {
          key: "roles_permissions_teams",
          label: "Roles, Permissions, & Teams",
        },
      ],
    },
    // {
    //   key: "help",
    //   icon: <QuestionCircleOutlined />,
    //   label: "Help",
    // },
  ];

  useEffect(() => {
    const activeSegment = activeSegments.length
      ? activeSegments.indexOf("settings") !== -1
        ? "settings"
        : activeSegments.pop()
      : selectedLayoutSegment;
    setPageSelectedKeys([activeSegment]);
    setSelectedKey(currentPageData.key);
  }, [currentPageData.key]);

  useEffect(() => {
    if (userData?.is_admin) {
      setMenuItems(sourceMenuItems);
    } else {
      setMenuItems(
        sourceMenuItems.filter((item) => {
          return adminMenuItemKeys.indexOf(item.key) === -1;
        }),
      );
    }
  }, [userData]);

  return (
    <ConfigProvider
      theme={{
        components: {
          Menu: {
            darkItemColor: token.colorWhite,
            darkItemBg: token.yhMidnight,
            darkItemHoverBg: token.colorPrimary,
            darkItemSelectedBg: token.colorPrimary,
            darkSubMenuItemBg: token.yhColorSubmenu,
          },
        },
      }}
    >
      <Menu
        theme="dark"
        mode="inline"
        className="sidebar-menu"
        inlineIndent={16}
        selectedKeys={[selectedKey]}
        onSelect={({ key }) => {
          Helper.navigatePage(key, queryClient, message);
          setPageSelectedKeys([key]);
          setSelectedKey(key);
        }}
        items={menuItems}
      />
    </ConfigProvider>
  );
};
export default SidebarMenu;
