import { useBoundStore } from "../../src/store/store";
import TemplatePageOptions from "./(full-width-content)/(pages)/template/page_options";

const pageOptionsComponent = {
  template: <TemplatePageOptions />,
};

/**
 * Page options component
 *
 * @returns {JSX.Element}
 */
export default function PageOptions() {
  const currentPageData = useBoundStore((state) => state.currentPageData);

  return (
    <div className="inline-block">
      {pageOptionsComponent[currentPageData.key]}
    </div>
  );
}
