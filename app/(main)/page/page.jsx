"use client";

import React, { useEffect, useState } from "react";
import { Layout, notification } from "antd";
import Error from "next/error";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useBoundStore } from "../../../src/store/store";
import MainContentLayout from "../(full-width-content)/layout";
import Helper from "../../../src/utils/helper";
import Api from "../../../src/utils/api";
import { QueryKeys } from "../../../src/utils/query_keys";
import { PageMapper } from "../page_mapper";

const { Content } = Layout;

/**
 * Render page by current URL hash value
 *
 * @param {QueryClient} queryClient
 * @param {function} setErrorCode
 * @param {boolean} shouldSaveHistory
 */
const renderPageByCurrentURLHash = (
  queryClient,
  setErrorCode,
  shouldSaveHistory,
) => {
  const hash = window.location.hash ? window.location.hash : "#home";
  const urlParams = Helper.getUrlParameters();
  const pageMap = urlParams.template
    ? Helper.getPageMapByKeyValue("pageName", urlParams.template)
    : Helper.getPageMapFromHash(hash);

  if (Object.keys(pageMap).length > 0) {
    Object.keys(pageMap).forEach((key) => {
      Helper.renderPage(
        pageMap[key].pageKey ?? hash,
        pageMap[key].pageName,
        pageMap[key].pageUrl ?? hash,
        queryClient,
        shouldSaveHistory,
      );
    });
  } else {
    setErrorCode(404);
  }
};

/**
 * Page component that serves as entry point when navigating pages
 * Serves as page wrapper for SPA behavior
 *
 * @returns {JSX.Element}
 */
export default function Page() {
  const [errorCode, setErrorCode] = useState();
  const [historyState, setHistoryState] = useState();
  const renderedPages = useBoundStore((state) => state.renderedPages);
  const currentPageData = useBoundStore((state) => state.currentPageData);
  const pageTitle = useBoundStore((state) => state.pageTitle);
  const [notificationApi, contextHolder] = notification.useNotification();
  const queryClient = useQueryClient();
  const userData = Helper.getUserData();

  /**
   * Set history state from pop state event of window.history
   *
   * @param {PopStateEvent} event
   */
  const setHistoryStateFromPopStateEvent = (event) => {
    if (event.state) {
      setHistoryState(event.state);
    }
  };

  useEffect(() => {
    if (historyState) {
      renderPageByCurrentURLHash(queryClient, setErrorCode, false);
    }
  }, [historyState]);

  useEffect(() => {
    // Handle forward/back browser button
    window.addEventListener("popstate", setHistoryStateFromPopStateEvent);
    return () => {
      window.removeEventListener("popstate", setHistoryStateFromPopStateEvent);
    };
  }, []);

  useEffect(() => {
    if (Object.keys(renderedPages).length === 0) {
      renderPageByCurrentURLHash(queryClient, setErrorCode, true);
    }
  }, []);

  useEffect(() => {
    if (currentPageData.key) {
      setErrorCode();
    }
  }, [currentPageData.key]);

  useEffect(() => {
    setBrowserTabTitle();
  }, [currentPageData.key, pageTitle]);

  /**
   * Set browser tab title
   */
  const setBrowserTabTitle = () => {
    document.title = currentPageData.title ?? pageTitle;
  };

  /**
   * Fetch user settings data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchUserSettings = async ({ signal }) => {
    const response = await Api.fetchData(
      "/api/v1/internal/user_settings/get",
      "get",
      {},
      {},
      signal,
    );

    return response;
  };

  /**
   * Query to fetch user settings
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.user_settings(),
    queryFn: fetchUserSettings,
    enabled: userData?.initial_password_change === true,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        Helper.storeUserSettings(response.data);
      } else {
        notificationApi.warning({
          message: "User Settings",
          description: response.message,
        });
      }
    }
  }, [chartDataQuery.dataUpdatedAt]);

  return (
    <>
      {contextHolder}
      {errorCode && <Error statusCode={errorCode} />}
      <Layout>
        <Content>
          <MainContentLayout>
            {currentPageData.name &&
              renderedPages[currentPageData.key] === undefined &&
              React.createElement(PageMapper[currentPageData.name]?.component, {
                pageKey: currentPageData.key,
                key: currentPageData.key,
              })}

            {Object.keys(renderedPages).map((pageKey) => {
              const displayClass =
                renderedPages[pageKey].active === true ? "flex" : "hidden!";
              return (
                <div
                  key={`${pageKey}_container`}
                  className={`${displayClass} h-full`}
                >
                  {React.createElement(
                    PageMapper[renderedPages[pageKey].name]?.component,
                    {
                      pageKey: pageKey,
                      key: pageKey,
                    },
                  )}
                </div>
              );
            })}
          </MainContentLayout>
        </Content>
      </Layout>
    </>
  );
}
