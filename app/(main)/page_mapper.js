import Analysis from "./(full-width-content)/(pages)/analysis/analysis_page";
import Dashboard from "./(full-width-content)/(pages)/dashboard/dashboard_page";
import MetadataPage from "./(full-width-content)/(pages)/metadata/metadata_page";
import AnalysisTemplate from "./(full-width-content)/(pages)/template/template_page";
import TraceabilityPage from "./(full-width-content)/(pages)/traceability/traceability_page";
import DatalogProcessingUpdates from "./(full-width-content)/(pages)/upload/files/datalog_processing_updates";
import FilesUpload from "./(full-width-content)/(pages)/upload/files/upload_files_page";
import ChangePassword from "./(full-width-content)/settings/account/change-password/change_password_page";
import AccountProfile from "./(full-width-content)/settings/account/profile/profile_page";
import AppearanceSettings from "./(full-width-content)/settings/preferences/appearance/appearance_page";
import GeneralSettings from "./(full-width-content)/settings/preferences/general/general_page";
import NotificationsSettings from "./(full-width-content)/settings/preferences/notifications/notifications_page";
import RolesPermissionsTeamsManagement from "./(full-width-content)/(pages)/roles_permissions_teams/roles_permissions_teams";
import Home from "./(right-sidebar-content)/(pages)/home/<USER>";

/**
 * Mapper for page
 *
 * For breadcrumb data, empty "" value means that breadcrumb segment should be the previous opened page
 */
export const PageMapper = {
  home: {
    pageKey: "home",
    pageName: "home",
    pageTitle: "Home",
    pageUrl: "#home",
    pageSegment: "#home",
    component: Home,
    isLanding: true,
    cache: true,
  },
  dashboard: {
    pageKey: "dashboard",
    pageName: "dashboard",
    pageTitle: "Dashboard",
    pageUrl: "#dashboard",
    pageSegment: "#dashboard",
    component: Dashboard,
    isLanding: true,
    cache: false,
  },
  engineering_production_files_upload: {
    pageKey: "engineering_production_files_upload",
    pageName: "engineering_production_files_upload",
    pageTitle: "Engineering & Production Files",
    pageUrl: "#upload/files",
    pageSegment: "#upload/files",
    component: FilesUpload,
    isLanding: true,
    cache: true,
  },
  template: {
    pageKey: "template",
    pageName: "template",
    pageTitle: "Analysis Template",
    pageUrl: "#template",
    pageSegment: "#template",
    component: AnalysisTemplate,
    isLanding: true,
    cache: true,
  },
  my_profile: {
    pageKey: "my_profile",
    pageName: "my_profile",
    pageTitle: "My Profile",
    pageUrl: "#settings/account/profile",
    pageSegment: "#settings/account/profile",
    component: AccountProfile,
    isLanding: true,
    cache: true,
  },
  change_password: {
    pageKey: "change_password",
    pageName: "change_password",
    pageTitle: "Change Password",
    pageUrl: "#settings/account/change-password",
    pageSegment: "#settings/account/change-password",
    component: ChangePassword,
    cache: true,
  },
  settings: {
    pageKey: "my_profile",
    pageName: "my_profile",
    pageTitle: "My Profile",
    pageUrl: "#settings/account/profile",
    pageSegment: "#settings/account/profile",
    component: AccountProfile,
    isLanding: true,
    cache: true,
  },
  roles_permissions_teams: {
    pageKey: "roles_permissions_teams",
    pageName: "roles_permissions_teams",
    pageTitle: "Roles, Permissions, & Teams Management",
    pageUrl: "#roles_permissions_teams",
    pageSegment: "#roles_permissions_teams",
    component: RolesPermissionsTeamsManagement,
    isLanding: true,
    cache: true,
  },
  account: {
    pageKey: "my_profile",
    pageName: "my_profile",
    pageTitle: "My Profile",
    pageUrl: "#settings/account/profile",
    pageSegment: "#settings/account/profile",
    component: AccountProfile,
    isLanding: true,
    cache: true,
  },
  general: {
    pageKey: "general",
    pageName: "general",
    pageTitle: "General Settings",
    pageUrl: "#settings/preferences/general",
    pageSegment: "#settings/preferences/general",
    component: GeneralSettings,
    isLanding: true,
    cache: true,
  },
  notifications: {
    pageKey: "notifications",
    pageName: "notifications",
    pageTitle: "Notifications Settings",
    pageUrl: "#settings/preferences/notifications",
    pageSegment: "#settings/preferences/notifications",
    component: NotificationsSettings,
    isLanding: true,
    cache: true,
  },
  appearance: {
    pageKey: "appearance",
    pageName: "appearance",
    pageTitle: "Appearance Settings",
    pageUrl: "#settings/preferences/appearance",
    pageSegment: "#settings/preferences/appearance",
    component: AppearanceSettings,
    isLanding: true,
    cache: true,
  },
  analysis: {
    // no page key and url set since those are dynamic for analysis page
    pageName: "analysis",
    pageSegment: "#analysis",
    component: Analysis,
    cache: false,
  },
  metadata_header: {
    pageKey: "metadata",
    pageName: "metadata_header",
    pageTitle: "Metadata",
    pageUrl: "#metadata",
    pageSegment: "#metadata",
    component: MetadataPage,
    breadcrumbData: ["", "metadata_header"],
    cache: true,
  },
  oee: {
    pageKey: "oee",
    pageName: "oee",
    pageTitle: "OEE",
    pageUrl: "#analysis?template_key=oee",
    pageSegment: "#analysis?template_key=oee",
    component: Analysis,
    isLanding: true,
    cache: false,
  },
  recipes: {
    pageKey: "recipes",
    pageName: "recipes",
    pageTitle: "Production Recipes",
    pageUrl: "#recipes/analysis?template_key=recipes",
    pageSegment: "#recipes/analysis",
    component: Analysis,
    showTitle: false,
    isLanding: true,
    cache: true,
  },
  recipe_drafts: {
    pageKey: "recipe_drafts",
    pageName: "recipe_drafts",
    pageTitle: "Draft Recipes",
    pageUrl: "#recipes/analysis?template_key=recipe_drafts",
    pageSegment: "#recipes/analysis",
    component: Analysis,
    isLanding: true,
    cache: true,
  },
  user_management: {
    pageKey: "user_management",
    pageName: "user_management",
    pageTitle: "User Management",
    pageUrl: "#analysis?template_key=user_management",
    pageSegment: "#analysis?template_key=user_management",
    component: Analysis,
    isLanding: true,
    cache: true,
  },
  wafer_map_gallery: {
    pageName: "wafer_map_gallery",
    pageTitle: "All Wafer IDs",
    pageSegment: "#lot-analysis/wafer-map-gallery",
    component: Analysis,
    breadcrumbData: ["home", "lot_analysis", "wafer_map_gallery"],
    cache: false,
  },
  pin_yield_datalog_breakdown: {
    pageName: "pin_yield_datalog_breakdown",
    pageTitle: "MPR Maps: Pin Yield | Datalog Breakdown",
    pageSegment: "#selected-test/pin-yield-datalog-breakdown",
    component: Analysis,
    breadcrumbData: ["home", "selected_test", "pin_yield_datalog_breakdown"],
    cache: false,
  },
  mean_parametric_pin_substrate_datalog_breakdown: {
    pageName: "mean_parametric_pin_substrate_datalog_breakdown",
    pageTitle: "MPR Maps: Mean Parametric Pin Substrate | Datalog Breakdown",
    pageSegment:
      "#selected-test/mean-parametric-pin-substrate-datalog-breakdown",
    component: Analysis,
    breadcrumbData: [
      "home",
      "selected_test",
      "mean_parametric_pin_substrate_datalog_breakdown",
    ],
    cache: false,
  },
  pin_yield_group_breakdown: {
    pageName: "pin_yield_group_breakdown",
    pageTitle: "MPR Maps: Pin Yield | Group Breakdown",
    pageSegment: "#selected-test/pin-yield-group-breakdown",
    component: Analysis,
    breadcrumbData: ["home", "selected_test", "pin_yield_group_breakdown"],
    cache: false,
  },
  mean_parametric_pin_substrate_group_breakdown: {
    pageName: "mean_parametric_pin_substrate_group_breakdown",
    pageTitle: "MPR Maps: Mean Parametric Pin Substrate | Group Breakdown",
    pageSegment: "#selected-test/mean-parametric-pin-substrate-group-breakdown",
    component: Analysis,
    breadcrumbData: [
      "home",
      "selected_test",
      "mean_parametric_pin_substrate_group_breakdown",
    ],
    cache: false,
  },
  datalog_processing_updates: {
    pageKey: "datalog_processing_updates",
    pageName: "datalog_processing_updates",
    pageTitle: "Datalog Processing Updates",
    pageUrl: "#datalog-processing-updates",
    pageSegment: "#datalog-processing-updates",
    component: DatalogProcessingUpdates,
    isLanding: true,
    cache: true,
  },
  lot_analysis: {
    // no page key and url set since those are dynamic for analysis page
    pageName: "lot_analysis",
    pageTitle: "Lot Analysis",
    pageSegment: "#analysis",
    component: Analysis,
    breadcrumbData: ["home", "lot_analysis"],
    cache: false,
  },
  binning_analysis: {
    // no page key and url set since those are dynamic for analysis page
    pageName: "binning_analysis",
    pageTitle: "Binning Analysis",
    pageSegment: "#analysis",
    component: Analysis,
    breadcrumbData: ["home", "binning_analysis"],
    cache: false,
  },
  all_tests: {
    // no page key and url set since those are dynamic for analysis page
    pageName: "all_tests",
    pageTitle: "All Tests",
    pageSegment: "#analysis",
    component: Analysis,
    breadcrumbData: ["home", "all_tests"],
    cache: false,
  },
  raw_data: {
    // no page key and url set since those are dynamic for analysis page
    pageName: "raw_data",
    pageTitle: "Raw Data",
    pageSegment: "#analysis",
    component: Analysis,
    breadcrumbData: ["home", "raw_data"],
    cache: false,
  },
  selected_test: {
    // no page key and url set since those are dynamic for analysis page
    pageName: "selected_test",
    pageTitle: "Selected Test",
    pageSegment: "#analysis",
    component: Analysis,
    breadcrumbData: ["home", "selected_test"],
    cache: false,
  },
  dice_analysis: {
    pageName: "dice_analysis",
    pageTitle: "Dice Analysis",
    pageSegment: "#analysis",
    component: Analysis,
    breadcrumbData: ["home", "dice_analysis"],
    cache: false,
  },
  traceability: {
    pageKey: "traceability",
    pageName: "traceability",
    pageTitle: "Traceability",
    pageUrl: "#traceability",
    pageSegment: "#traceability",
    component: TraceabilityPage,
    breadcrumbData: ["", "traceability"],
    cache: true,
  },
  create_calculated_test: {
    pageName: "create_calculated_test",
    pageTitle: "Create Calculated Test",
    pageSegment: "#create_calculated_test",
    component: Analysis,
    breadcrumbData: ["home", "create_calculated_test"],
  },
  npi_recipe: {
    pageName: "npi_recipe",
    pageTitle: "NPI Recipe",
    pageSegment: "#analysis",
    component: Analysis,
    breadcrumbData: ["home", "npi_recipe"],
  },
  simulate_npi_report: {
    pageName: "simulate_npi_report",
    pageTitle: "Simulate Report",
    pageSegment: "#analysis",
    component: Analysis,
    breadcrumbData: ["home", "npi_recipe", "simulate_npi_report"],
  },
  generate_npi_report: {
    pageName: "generate_npi_report",
    pageTitle: "Generate Report",
    pageSegment: "#analysis",
    component: Analysis,
    breadcrumbData: [
      "home",
      "npi_recipe",
      "simulate_npi_report",
      "generate_npi_report",
    ],
  },
  characterization_report: {
    pageName: "characterization_report",
    pageTitle: "Characterization Report",
    pageSegment: "#analysis",
    component: Analysis,
    breadcrumbData: [
      "home",
      "npi_recipe",
      "simulate_npi_report",
      "generate_npi_report",
      "characterization_report",
    ],
  },
  gage_rr_report: {
    pageName: "gage_rr_report",
    pageTitle: "Gage R+R Report",
    pageSegment: "#analysis",
    component: Analysis,
    breadcrumbData: [
      "home",
      "npi_recipe",
      "simulate_npi_report",
      "generate_npi_report",
      "gage_rr_report",
    ],
  },
  gage_charts_gallery: {
    pageName: "gage_charts_gallery",
    pageTitle: "Gage R+R Charts Gallery",
    pageSegment: "#analysis/gage-charts-gallery",
    component: Analysis,
    breadcrumbData: ["home", "gage_rr_report", "gage_charts_gallery"],
    cache: false,
  },
  die_drift_report: {
    pageName: "die_drift_report",
    pageTitle: "Die Drift Report",
    pageSegment: "#analysis",
    component: Analysis,
    breadcrumbData: [
      "home",
      "npi_recipe",
      "simulate_npi_report",
      "generate_npi_report",
      "die_drift_report",
    ],
  },
};
