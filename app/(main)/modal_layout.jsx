import { theme, ConfigProvider } from "antd";
import { useState } from "react";

const { useToken } = theme;

const buttonData = {
  colorBgContainerDisabled: "rgba(255, 255, 255, 1)",
};

/**
 * Layout component for modals
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const ModalLayout = ({ children }) => {
  const [btnData] = useState(buttonData);
  const { token } = useToken();

  return (
    <ConfigProvider
      theme={{
        algorithm: [theme.compactAlgorithm],
        components: {
          Button: {
            colorPrimary: token.colorPrimaryBg,
            colorBgContainerDisabled: btnData.colorBgContainerDisabled,
          },
        },
      }}
    >
      {children}
    </ConfigProvider>
  );
};
export default ModalLayout;
