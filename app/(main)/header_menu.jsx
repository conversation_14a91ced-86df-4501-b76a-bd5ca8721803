import {
  // QuestionCircleOutlined,
  // BellOutlined,
  UserOutlined,
  LogoutOutlined,
} from "@ant-design/icons";
import {
  Button,
  Space,
  Dropdown,
  Avatar,
  // Badge,
  ConfigProvider,
  theme,
} from "antd";
// import ExpandableSearch from "./expandable_search";
// import Notifications from "./notifications";
import { useEffect, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import Helper from "../../src/utils/helper";
import { useBoundStore } from "../../src/store/store";

const { useToken } = theme;

/**
 * Menu component in page header
 *
 * @returns {JSX.Element}
 */
const HeaderMenu = () => {
  const [userMenuItems, setUserMenuItems] = useState([]);
  const [userMenuProps, setUserMenuProps] = useState({});
  const userData = useBoundStore((state) => state.userData);
  const setUserData = useBoundStore((state) => state.setUserData);
  const { token } = useToken();
  const queryClient = useQueryClient();

  useEffect(() => {
    setUserData(Helper.getUserData());
    const menuItems = getUserMenuItems();
    setUserMenuItems(menuItems);
  }, []);

  useEffect(() => {
    const menuProps = getUserMenuProps();
    setUserMenuProps(menuProps);
  }, [userMenuItems]);

  /**
   * Get user menu props
   *
   * @returns {object} menuProps
   */
  const getUserMenuProps = () => {
    const menuProps = {
      items: userMenuItems,
      selectable: true,
      onClick: handleMenuClick,
    };

    return menuProps;
  };

  /**
   * Get user menu items
   *
   * @returns {array} menuItems
   */
  const getUserMenuItems = () => {
    const menuItems = [
      {
        label: "Account Settings",
        key: "account_settings",
        icon: <UserOutlined />,
      },
      {
        label: "Logout",
        key: "logout",
        icon: <LogoutOutlined />,
      },
    ];

    return menuItems;
  };

  /**
   * Handler when clicking a menu
   *
   * @param {object} selected menu item properties
   */
  const handleMenuClick = ({ key }) => {
    switch (key) {
      case "account_settings":
        Helper.navigatePage("account", queryClient);
        break;
      case "logout":
        Helper.logoutUser();
        break;
    }
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: [theme.compactAlgorithm],
        token: {
          colorPrimaryBg: token.yhColorLightOrange,
          colorLink: token.colorText,
        },
        components: {
          Button: {
            colorPrimary: token.yhHeaderColorBg,
          },
        },
      }}
    >
      <Space className="header-right-menu" size={1}>
        {/* <ExpandableSearch />
        <Button type="primary" className="menu-item menu-item-btn" size="large">
          <QuestionCircleOutlined />
        </Button>
        <Dropdown
          className="menu-item"
          dropdownRender={() => <Notifications />}
          overlayClassName="notifications-dropdown"
          disabled
        >
          <Badge
            count={100}
            overflowCount={99}
            offset={[-10, 15]}
            className="menu-item-badge"
          >
            <Button
              type="primary"
              className="menu-item menu-item-btn menu-item-btn-badge"
              size="large"
            >
              <BellOutlined />
            </Button>
          </Badge>
        </Dropdown> */}
        <Dropdown className="menu-item" menu={userMenuProps}>
          <Button
            type="primary"
            icon={
              <Avatar
                style={{
                  backgroundColor: "#87d068",
                  justifyContent: "center",
                  marginRight: 5,
                }}
                icon={<UserOutlined />}
                size="small"
              />
            }
            className="menu-item menu-item-btn"
            size="large"
          >
            <Space>{userData?.full_name}</Space>
          </Button>
        </Dropdown>
      </Space>
    </ConfigProvider>
  );
};

export default HeaderMenu;
