"use client";

import { App, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Config<PERSON><PERSON>ider, theme } from "antd";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useBoundStore } from "../../src/store/store";
import Helper from "../../src/utils/helper";
import { PageMapper } from "./page_mapper";

/**
 * Breakpoint segments to exclude parameter segments in breadcrumb
 */
const { useToken } = theme;

/**
 * Main breadcrumb component
 *
 * @returns {JSX.Element}
 */
export default function MainBreadcrumb() {
  const [breadcrumbItems, setBreadcrumbItems] = useState([]);
  const analysisName = useBoundStore((state) => state.analysisName);
  const currentPageData = useBoundStore((state) => state.currentPageData);
  const { message } = App.useApp();
  const pathname = usePathname();
  const { token } = useToken();
  const queryClient = useQueryClient();

  useEffect(() => {
    const items = generateBreadcrumbItems();
    setBreadcrumbItems(items);
  }, [pathname, analysisName, currentPageData.key]);

  /**
   * Generate breadcrumb items
   *
   * @returns {array} items
   */
  const generateBreadcrumbItems = () => {
    const items = [];
    const currentPageMap = PageMapper[currentPageData.name];
    if (currentPageMap?.breadcrumbData) {
      const breadcrumbData = currentPageMap.breadcrumbData;
      if (Array.isArray(breadcrumbData) && breadcrumbData.length > 1) {
        const pageHistory = JSON.parse(
          window.sessionStorage.getItem("pageHistory"),
        );
        breadcrumbData.forEach((pageName) => {
          if (pageName === "") {
            // use previously opened page if present, if not then use Home page
            pageName = pageHistory[pageHistory.length - 2] ?? "home";
          }
          if (pageName) {
            const pageMap = Helper.getPageMapByKeyValue("pageName", pageName);
            if (Object.keys(pageMap).length > 0) {
              Object.keys(pageMap).forEach((key) => {
                if (pageMap[key].pageTitle) {
                  items.push({
                    title:
                      pageMap[key].pageName !== currentPageMap.pageName ? (
                        <Button
                          type="link"
                          size="small"
                          onClick={() =>
                            Helper.navigatePage(
                              pageMap[key].pageName,
                              queryClient,
                              message,
                            )
                          }
                        >
                          {pageMap[key].pageTitle}
                        </Button>
                      ) : (
                        <Button
                          type="text"
                          size="small"
                          className="cursor-auto!"
                          style={{ color: token.colorWhite }}
                          disabled
                        >
                          {pageMap[key].pageTitle}
                        </Button>
                      ),
                  });
                }
              });
            }
          }
        });
      }
    }

    return items;
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: [theme.compactAlgorithm],
        token: {
          colorLink: token.colorText,
          colorLinkHover: token.colorWhite,
        },
      }}
    >
      <Breadcrumb className="breadcrumb" items={breadcrumbItems} />
    </ConfigProvider>
  );
}
