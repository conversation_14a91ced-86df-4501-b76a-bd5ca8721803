"use client";

import "./main_layout.css";
import { MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";
import { ConfigProvider, Layout, Space, Tooltip, theme, Popover } from "antd";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import yhLogo from "../../public/images/yieldHUB-icon.svg";
import Helper from "../../src/utils/helper";
import { UserSettingsKeys } from "../../src/utils/user_settings_keys";
import Api from "../../src/utils/api";
import { useEffectApiFetch } from "../../src/hooks";
import { useBoundStore } from "../../src/store/store";
import MainBreadcrumb from "./main_breadcrumb";
import HeaderMenu from "./header_menu";
import SidebarMenu from "./sidebar_menu";

const { useToken } = theme;
const { <PERSON><PERSON>, <PERSON>er, Sider } = Layout;

/**
 * Main layout component
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const MainLayout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(
    Helper.getUserSettings(UserSettingsKeys.sidebar_menu_is_collapsed),
  );
  const [showLogoText, setShowLogoText] = useState(true);
  const { token } = useToken();
  const userData = useBoundStore((state) => state.userData);
  const [appVersionData, setAppVersionData] = useState({});
  const [versionDisplay, setVersionDisplay] = useState("----");

  useEffect(() => {
    setShowLogoText(!collapsed);
  }, [collapsed]);

  useEffect(() => {
    setTimeout(() => {}, 300);
  }, [showLogoText]);

  useEffect(() => {
    if (appVersionData?.version) {
      const apps = Object.keys(appVersionData?.apps ?? {});
      const content = (
        <table>
          {apps.map((app, key) => {
            const appData = appVersionData.apps[app];
            return (
              <tr key={key}>
                <td style={{ width: 24 }}>
                  <b>{app.substring(4).toUpperCase()}</b>
                </td>
                <td>
                  {appData.version} ({appData.branch}) {appData.date}
                </td>
              </tr>
            );
          })}
        </table>
      );
      setVersionDisplay(
        <Popover content={content}>{appVersionData?.version}</Popover>,
      );
    } else {
      setVersionDisplay("----");
    }
  }, [appVersionData]);

  useEffectApiFetch(
    () => {
      let abortCtl;
      if (userData?.initial_password_change) {
        abortCtl = Api.getAppVersion(
          (res) => {
            if (res.success) {
              setAppVersionData(res.data);
            } else {
              console.log(res.message);
            }
          },
          (err) => {
            console.log(err);
          },
        );
      } else {
        abortCtl = Api.initAbortCtl();
      }
      return abortCtl;
    },
    () => {
      setAppVersionData({});
    },
    [userData?.initial_password_change],
  );

  return (
    <Layout className="main-layout">
      <ConfigProvider
        theme={{
          token: {
            colorPrimaryBg: token.yhMidnight,
          },
          components: {
            Menu: {
              subMenuItemBg: token.yhLightBlue,
            },
          },
        }}
      >
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          collapsedWidth={48}
          width={220}
          style={{
            backgroundColor: token.yhMidnight,
          }}
        >
          <Space className="logo h-12 m-1">
            <Image src={yhLogo} alt="yieldHUB" className="w-10 h-10"></Image>
            {showLogoText && <Space className="text">yieldHUB</Space>}
          </Space>
          <SidebarMenu />
        </Sider>
      </ConfigProvider>
      <Layout className="site-layout">
        <Header
          className="header"
          style={{
            background: token.yhHeaderColorBg,
          }}
        >
          <Space>
            <Tooltip title={collapsed ? "Show Sidebar" : "Hide Sidebar"}>
              {React.createElement(
                collapsed ? MenuUnfoldOutlined : MenuFoldOutlined,
                {
                  className: "side-menu-collapse-toogle",
                  onClick: () => {
                    Helper.setUserSettings(
                      UserSettingsKeys.sidebar_menu_is_collapsed,
                      !collapsed,
                    );
                    setCollapsed(!collapsed);
                  },
                  style: {
                    color: token.colorWhite,
                  },
                },
              )}
            </Tooltip>
            <MainBreadcrumb />
          </Space>
          <div className="header-center-wrapper"></div>
          <div className="header-right-wrapper">
            <HeaderMenu />
          </div>
        </Header>
        {children}
        <Footer
          className="footer"
          style={{
            background: token.yhMidnight,
            color: token.colorWhite,
          }}
        >
          <div className="footer-logo">
            <div className="footer-text">yieldHUB</div>
          </div>
          <div className="copyright">
            Copyright &#169; 2005-2024 yieldHUB. All Rights Reserved.
          </div>
          <div className="version">
            Version {versionDisplay} ({appVersionData?.branch ?? "----"}){" "}
            {appVersionData?.date ?? "----"}
          </div>
        </Footer>
      </Layout>
    </Layout>
  );
};
export default MainLayout;
