"use client";

import {
  UploadOutlined,
  LogoutOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import {
  App,
  Button,
  Col,
  Row,
  Space,
  Form,
  Spin,
  Tooltip,
  Progress,
  Flex,
} from "antd";
import { useRef, useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useEffectApiFetch } from "../../../../../../src/hooks";
import { useBoundStore } from "../../../../../../src/store/store";
import Api from "../../../../../../src/utils/api";
import YHGrid from "../../../../../../src/utils/grid/yh_grid";
import UploadForm from "../../../../../../src/utils/forms/upload_form";
import Helper from "../../../../../../src/utils/helper";

/**
 * Engineering Upload page component
 *
 * @param {string} pageKey
 * @param {string} tabKey
 * @returns {JSX.Element}
 */
export default function EngineeringUpload({ pageKey, tabKey }) {
  const [engineeringGridComponent, setEngineeringGridComponent] = useState();
  const [uploadFormDisabled, setUploadFormDisabled] = useState(false);
  const [uploadingIsComplete, setUploadingIsComplete] = useState(false);
  const [isUploadFormOpen, setIsUploadFormOpen] = useState(false);
  const userPdbStorageInfo = useBoundStore((state) => state.userPdbStorageInfo);
  const setUserPdbStorageInfo = useBoundStore(
    (state) => state.setUserPdbStorageInfo,
  );
  const filters = useBoundStore((state) => state.filters);
  const setFilters = useBoundStore((state) => state.setFilters);
  const reloadGridFilters = useBoundStore((state) => state.reloadGridFilters);
  const engineeringGridRef = useRef();
  const engineeringGridWrapperRef = useRef();
  const { message } = App.useApp();
  const [engineeringUploadForm] = Form.useForm();
  const queryClient = useQueryClient();
  const reloadGridKey = "engineering_upload";

  useEffect(() => {
    if (filters[pageKey] === undefined) {
      filters[pageKey] = {};
    }
    if (reloadGridFilters[reloadGridKey] === undefined) {
      reloadGridFilters[reloadGridKey] = {};
    }
  }, []);

  useEffectApiFetch(
    () => {
      return getEngineeringGridComponent();
    },
    () => {
      setEngineeringGridComponent();
    },
  );

  useEffectApiFetch(
    () => {
      return getPDBStorageInfo();
    },
    () => {
      setUserPdbStorageInfo();
    },
  );

  /**
   * Get and set engineering grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getEngineeringGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setEngineeringGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: "engineering_upload",
      },
    );

    return abortCtl;
  };

  /**
   * Get and set PDB storage info
   *
   * @returns {AbortController} abortCtl
   */
  const getPDBStorageInfo = () => {
    const abortCtl = Api.getPDBStorageInfo(
      (res) => {
        if (res.success) {
          setUserPdbStorageInfo(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
    );

    return abortCtl;
  };

  return (
    <div className="flex flex-col h-full">
      <Row className="pb-3.5" justify="space-between" align="middle">
        <Col>
          <Space>
            <Button
              type="primary"
              className="flex items-center justify-center"
              icon={
                uploadFormDisabled && !uploadingIsComplete ? (
                  <Spin
                    indicator={<LoadingOutlined spin className="text-base" />}
                  />
                ) : (
                  <UploadOutlined className="text-base" />
                )
              }
              disabled={
                (uploadFormDisabled && !uploadingIsComplete) ||
                Object.keys(userPdbStorageInfo ?? {}).length === 0
              }
              onClick={() => setIsUploadFormOpen(true)}
            >
              {uploadFormDisabled && !uploadingIsComplete
                ? "Uploading"
                : "Upload"}
            </Button>
            <Button
              icon={<LogoutOutlined />}
              onClick={() => {
                const key = "home";
                const filtersCopy = Helper.cloneObject(filters);
                if (!filtersCopy[key]) {
                  filtersCopy[key] = {};
                }
                filtersCopy[key].show = "personal_data";
                setFilters(filtersCopy);
                Helper.navigatePage(key, queryClient);
              }}
            >
              View My Eng Upload in Home
            </Button>
          </Space>
        </Col>
        <Col>
          <Space>
            <span>Total Storage Capacity</span>
            <Flex
              style={{
                width: 150,
              }}
            >
              <Tooltip
                title={`${Helper.formatFileSize(userPdbStorageInfo?.used)} of ${Helper.formatFileSize(userPdbStorageInfo?.capacity)} used`}
              >
                <Progress
                  percent={
                    (userPdbStorageInfo?.used / userPdbStorageInfo?.capacity) *
                    100
                  }
                  status={
                    userPdbStorageInfo?.capacity - userPdbStorageInfo?.used <
                    0.1 * userPdbStorageInfo?.capacity
                      ? "exception"
                      : "success"
                  }
                  showInfo={false}
                />
              </Tooltip>
            </Flex>
          </Space>
        </Col>
      </Row>
      <Row className="flex grow h-full">
        <Col span={24}>
          {engineeringGridComponent && (
            <YHGrid
              ref={engineeringGridWrapperRef}
              gridRef={engineeringGridRef}
              gridId="engineering_upload_grid"
              gridOptions={{ rowModelType: "serverSide" }}
              component={engineeringGridComponent}
              filters={filters}
              rowGroups={[]}
              pageKey={pageKey}
              wrapperClassName="flex grow flex-col h-full"
            />
          )}
        </Col>
        <Form
          form={engineeringUploadForm}
          className="flex flex-col h-full"
          layout="vertical"
        >
          <UploadForm
            tabKey={tabKey}
            uploadForm={engineeringUploadForm}
            isUploadFormOpen={isUploadFormOpen}
            setIsUploadFormOpen={setIsUploadFormOpen}
            uploadFormDisabled={uploadFormDisabled}
            setUploadFormDisabled={setUploadFormDisabled}
            uploadingIsComplete={uploadingIsComplete}
            setUploadingIsComplete={setUploadingIsComplete}
            userPdbStorageInfo={userPdbStorageInfo}
          />
        </Form>
      </Row>
    </div>
  );
}
