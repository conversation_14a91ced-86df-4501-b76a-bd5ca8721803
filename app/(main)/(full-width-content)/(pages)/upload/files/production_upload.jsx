"use client";

import { UploadOutlined, LoadingOutlined } from "@ant-design/icons";
import { App, Button, Col, Row, Space, Form, Spin } from "antd";
import { useRef, useState, useEffect } from "react";
import { useEffectApiFetch } from "../../../../../../src/hooks";
import { useBoundStore } from "../../../../../../src/store/store";
import Api from "../../../../../../src/utils/api";
import YHGrid from "../../../../../../src/utils/grid/yh_grid";
import UploadForm from "../../../../../../src/utils/forms/upload_form";

/**
 * Production Upload page component
 *
 * @param {string} pageKey
 * @param {string} tabKey
 * @returns {JSX.Element}
 */
export default function ProductionUpload({ pageKey, tabKey }) {
  const [isUploadFormOpen, setIsUploadFormOpen] = useState(false);
  const [productionUploadForm] = Form.useForm();
  const [productionGridComponent, setProductionGridComponent] = useState();
  const [uploadFormDisabled, setUploadFormDisabled] = useState(false);
  const [uploadingIsComplete, setUploadingIsComplete] = useState(false);
  const filters = useBoundStore((state) => state.filters);
  const productionGridRef = useRef();
  const reloadGridFilters = useBoundStore((state) => state.reloadGridFilters);
  const { message } = App.useApp();

  useEffect(() => {
    if (productionGridComponent) {
      reloadGridFilters[productionGridComponent.reload_grid_key] = {};
    }
  }, [productionGridComponent]);

  useEffectApiFetch(
    () => {
      return getProductionGridComponent();
    },
    () => {
      setProductionGridComponent();
    },
  );

  /**
   * Get and set production grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getProductionGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setProductionGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: "manual_production_upload",
      },
    );

    return abortCtl;
  };

  return (
    <div className="flex flex-col h-full">
      <Row className="pb-3.5">
        <Col>
          <Space>
            <Button
              type="primary"
              className="flex items-center justify-center"
              icon={
                uploadFormDisabled && !uploadingIsComplete ? (
                  <Spin
                    indicator={<LoadingOutlined spin className="text-base" />}
                  />
                ) : (
                  <UploadOutlined className="text-base" />
                )
              }
              disabled={uploadFormDisabled && !uploadingIsComplete}
              onClick={() => setIsUploadFormOpen(true)}
            >
              {uploadFormDisabled && !uploadingIsComplete
                ? "Uploading"
                : "Upload"}
            </Button>
          </Space>
        </Col>
      </Row>
      <Row className="flex grow h-full">
        <Col span={24}>
          {productionGridComponent && (
            <YHGrid
              gridRef={productionGridRef}
              gridId="production_upload_grid"
              gridOptions={{ rowModelType: "serverSide" }}
              component={productionGridComponent}
              filters={filters}
              rowGroups={[]}
              pageKey={pageKey}
              wrapperClassName="flex grow flex-col h-full"
            />
          )}
        </Col>
        <Form
          form={productionUploadForm}
          className="flex flex-col h-full"
          layout="vertical"
        >
          <UploadForm
            tabKey={tabKey}
            pageKey={pageKey}
            uploadForm={productionUploadForm}
            isUploadFormOpen={isUploadFormOpen}
            setIsUploadFormOpen={setIsUploadFormOpen}
            uploadFormDisabled={uploadFormDisabled}
            setUploadFormDisabled={setUploadFormDisabled}
            uploadingIsComplete={uploadingIsComplete}
            setUploadingIsComplete={setUploadingIsComplete}
          />
        </Form>
      </Row>
    </div>
  );
}
