import { Tabs } from "antd";
import PagesLayout from "../../layout";
import EngineeringUpload from "./engineering_upload";
import ProductionUpload from "./production_upload";
import "./override.css";

/**
 * Files upload page component
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function FilesUpload({ pageKey }) {
  const items = [
    {
      key: "engineering_upload",
      label: `Engineering`,
      children: (
        <EngineeringUpload pageKey={pageKey} tabKey={"engineering_upload"} />
      ),
    },
    {
      key: "production_upload",
      label: `Production`,
      children: (
        <ProductionUpload pageKey={pageKey} tabKey={"production_upload"} />
      ),
    },
  ];

  return (
    <PagesLayout>
      <Tabs
        className="upload-tabs h-full"
        defaultActiveKey="engineering_upload"
        items={items}
      />
    </PagesLayout>
  );
}
