import { <PERSON><PERSON>, Col, Row, Space, Typography, Input, Form, App } from "antd";
import { useRef, useState, useEffect } from "react";
import PagesLayout from "../../layout";
import { useEffectApiFetch } from "../../../../../../src/hooks";
import { useBoundStore } from "../../../../../../src/store/store";
import Api from "../../../../../../src/utils/api";
import YHGrid from "../../../../../../src/utils/grid/yh_grid";
import Helper from "../../../../../../src/utils/helper";

const { Text } = Typography;

/**
 * Datalog processing updates page component
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function DatalogProcessingUpdates({ pageKey }) {
  const reloadGridFilters = useBoundStore((state) => state.reloadGridFilters);
  const setReloadGridFilters = useBoundStore(
    (state) => state.setReloadGridFilters,
  );
  const selectedStatusOption = useBoundStore(
    (state) => state.selectedStatusOption,
  );
  const setSelectedStatusOption = useBoundStore(
    (state) => state.setSelectedStatusOption,
  );
  const setSelectedDatalogProcessingSubconOption = useBoundStore(
    (state) => state.setSelectedDatalogProcessingSubconOption,
  );
  const [
    datalogProcessingUpdatesGridComponent,
    setDatalogProcessingUpdatesGridComponent,
  ] = useState();
  const [
    datalogProcessingUpdatesGridComponentId,
    setDatalogProcessingUpdatesGridComponentId,
  ] = useState();
  const filters = useBoundStore((state) => state.filters);
  const { message } = App.useApp();
  const datalogUpdatesGridRef = useRef();
  const datalogUpdatesGridWrapperRef = useRef();
  const [topSearchFilterForm] = Form.useForm();
  const [topSearchFilterInitialValues] = useState({
    lot_id: "",
    wafer_id: "",
    file_name: "",
    program: "",
  });
  const setFilters = useBoundStore((state) => state.setFilters);
  const gridComponentRefs = useBoundStore((state) => state.gridComponentRefs);
  const reloadGridKey = "datalog_processing_report";

  useEffect(() => {
    if (filters[pageKey] === undefined) {
      filters[pageKey] = {};
    }
    if (reloadGridFilters[reloadGridKey] === undefined) {
      reloadGridFilters[reloadGridKey] = {};
    }
  }, []);

  useEffect(() => {
    if (datalogProcessingUpdatesGridComponent) {
      setDatalogProcessingUpdatesGridComponentId(
        pageKey + "_" + datalogProcessingUpdatesGridComponent.name,
      );
    }
  }, [datalogProcessingUpdatesGridComponent]);

  useEffectApiFetch(
    () => {
      return getDatalogProcessingUpdatesGridComponent();
    },
    () => {
      setDatalogProcessingUpdatesGridComponent();
    },
  );

  /**
   * Get and set datalog processing updates grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getDatalogProcessingUpdatesGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setDatalogProcessingUpdatesGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: "datalog_processing_report",
      },
    );

    return abortCtl;
  };

  /**
   * Revert to default top search filter and gridFilter values
   */
  const revertToDefaultTopSearchFilter = () => {
    topSearchFilterForm.resetFields();
    datalogUpdatesGridRef.current.api.setServerSideSelectionState({
      selectAll: false,
      toggledNodes: [],
    });
    const selectedStatusOptionCopy = Helper.cloneObject(selectedStatusOption);
    selectedStatusOptionCopy[datalogProcessingUpdatesGridComponentId] =
      undefined;
    setSelectedStatusOption(selectedStatusOptionCopy);
    setSelectedDatalogProcessingSubconOption(undefined);
    // update/remove grid filters
    const reloadGridFiltersCopy = Helper.cloneObject(reloadGridFilters);
    reloadGridFiltersCopy[reloadGridKey].file_status = undefined;
    reloadGridFiltersCopy[reloadGridKey].subcon = undefined;
    setReloadGridFilters(reloadGridFiltersCopy);
    topSearchFilterForm.submit();
  };

  /**
   * Apply top search filter
   *
   * @param {object} values
   */
  const applyTopSearchFilter = (values) => {
    // update filter state
    const filtersCopy = Helper.cloneObject(filters);
    filtersCopy[pageKey].lot_id = values.lot_id || undefined;
    filtersCopy[pageKey].wafer_id = values.wafer_id || undefined;
    filtersCopy[pageKey].file_name = values.file_name || undefined;
    filtersCopy[pageKey].program = values.program || undefined;
    setFilters(filtersCopy);
    gridComponentRefs[
      datalogProcessingUpdatesGridComponentId
    ]?.current?.reloadGridData();
  };

  return (
    <PagesLayout>
      <div className="flex flex-col h-full">
        <Row className="pb-4">
          <Col>
            <Form
              form={topSearchFilterForm}
              layout="inline"
              initialValues={topSearchFilterInitialValues}
              onFinish={applyTopSearchFilter}
            >
              <Space>
                <Form.Item
                  name="lot_id"
                  label={
                    <Text className="mr-1" strong>
                      Lot ID:
                    </Text>
                  }
                  colon={false}
                >
                  <Input placeholder="" allowClear />
                </Form.Item>
                <Form.Item
                  name="wafer_id"
                  label={
                    <Text className="mr-1" strong>
                      Wafer ID:
                    </Text>
                  }
                  colon={false}
                >
                  <Input placeholder="" allowClear />
                </Form.Item>
                <Form.Item
                  name="file_name"
                  label={
                    <Text className="mr-1" strong>
                      Filename:
                    </Text>
                  }
                  colon={false}
                >
                  <Input placeholder="" allowClear />
                </Form.Item>
                <Form.Item
                  name="program"
                  label={
                    <Text className="mr-1" strong>
                      Program:
                    </Text>
                  }
                  colon={false}
                >
                  <Input placeholder="" allowClear />
                </Form.Item>
                <Button type="primary" htmlType="submit">
                  Apply Filter
                </Button>
                <Button onClick={() => revertToDefaultTopSearchFilter()}>
                  Clear Filters
                </Button>
              </Space>
            </Form>
          </Col>
        </Row>
        <Row className="flex grow h-full">
          <Col span={24}>
            {datalogProcessingUpdatesGridComponent &&
              datalogProcessingUpdatesGridComponentId && (
                <YHGrid
                  ref={datalogUpdatesGridWrapperRef}
                  gridRef={datalogUpdatesGridRef}
                  gridId={datalogProcessingUpdatesGridComponentId}
                  component={datalogProcessingUpdatesGridComponent}
                  filters={filters}
                  setFilters={setFilters}
                  rowGroups={[]}
                  pageKey={pageKey}
                  wrapperClassName="flex grow flex-col h-full"
                />
              )}
          </Col>
        </Row>
      </div>
    </PagesLayout>
  );
}
