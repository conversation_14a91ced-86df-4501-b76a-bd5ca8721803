"use client";

import { Flex, Form, Steps, message } from "antd";
import { useEffect, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import RecipeInfo from "../npi/recipe_info";
import { useSaveRecipe } from "../../../../../src/hooks/useSaveRecipe";
import { useBoundStore } from "../../../../../src/store/store";
import { useUpdateNpiConditions } from "../../../../../src/hooks/useUpdateNpiConditions";
import ProgressStepButtons from "../../../../../src/utils/components/recipe_common/progress_step_buttons";
import Helper from "../../../../../src/utils/helper";
import { useEffectApiFetch } from "../../../../../src/hooks";
import { useUpdateNpiConditionsOrder } from "../../../../../src/hooks/useUpdateNpiConditionsOrder";
import CalculatedTestsHeader from "../calculated_tests/calculated_tests_header";
import StepComponent from "./step_components";

/**
 * Recipe template component
 *
 * @param {string} templateData
 * @param {object} urlParams
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
const RecipeTemplate = ({ templateData, urlParams, pageKey }) => {
  const [userPermissionsForm] = Form.useForm();
  const queryClient = useQueryClient();
  const selectedConditions = useBoundStore((state) => state.selectedConditions);
  const [currentStep, setCurrentStep] = useState(0);
  const [previousStep, setPreviousStep] = useState(currentStep);
  const [pageFilters, setPageFilters] = useState({});
  const [gridFilters, setGridFilters] = useState({});
  const [saveRecipeFilters, setSaveRecipeFilters] = useState({});
  const [updatedConditionFilters, setUpdateConditionFilters] = useState({});
  const [updateConditionsOrderFilters, setUpdateConditionsOrderFilters] =
    useState({});
  const [disableSkipToSimulateBtn, setDisableSkipToSimulateBtn] =
    useState(true);
  const [disableNextBtn, setDisableNextBtn] = useState(true);
  const [
    updatedConditionWithCallbackFilters,
    setUpdateConditionWithCallbackFilters,
  ] = useState({});
  const [presetAnalysisTemplates, setPresetAnalysisTemplates] = useState([]);
  const [messageApi, contextHolder] = message.useMessage();
  const recipeTemplateData = JSON.parse(templateData);

  useEffectApiFetch(
    () => {
      return Helper.getPresetAnalysisTemplates(setPresetAnalysisTemplates);
    },
    () => {
      setPresetAnalysisTemplates([]);
    },
  );

  /**
   * Generate simulate npi report page
   */
  const openSimulateNpiReportPage = () => {
    Helper.generateAnalysis(
      "simulate_npi_report",
      urlParams[pageKey],
      presetAnalysisTemplates,
      queryClient,
    );
  };

  /**
   * Event handlers
   */
  const eventHandlers = {
    npiOnChangeStepHandler: () => {
      setRecipeFilters();
      if (previousStep === 0) {
        updateCondition();
      }
      if (previousStep === 1) {
        updateConditionsOrder();
      }
    },
    npiSimulationHandler: () => {
      if (previousStep === 1) {
        updateConditionsOrder(openSimulateNpiReportPage);
      } else {
        openSimulateNpiReportPage();
      }
    },
    npiUpdatedConditionHandler: () => {
      updateCondition();
    },
    npiUpdatedConditionWithCallbackHandler: () => {
      setUpdateConditionWithCallbackFilters({
        ...pageFilters,
        recipe_data: {
          conditions: selectedConditions,
        },
        time_initiated: Date.now(),
      });
    },
    npiSaveRecipeHandler: () => {
      setRecipeFilters();
    },
    npiDisableSkipToSimulateBtnHandler: () => {
      setDisableSkipToSimulateBtn(selectedConditions?.length < 1);
    },
    npiDisableNextBtnHandler: () => {
      setDisableNextBtn(selectedConditions?.length < 1);
    },
  };

  useEffect(() => {
    setFilters();
  }, []);

  useEffect(() => {
    if (
      typeof eventHandlers[
        recipeTemplateData.event_handlers["on_change_step"]
      ] === "function"
    ) {
      eventHandlers[recipeTemplateData.event_handlers["on_change_step"]]();
    }
    setPreviousStep(currentStep);
  }, [currentStep]);

  useEffect(() => {
    if (recipeTemplateData?.event_handlers?.["disable_skip_to_simulate_btn"]) {
      eventHandlers[
        recipeTemplateData.event_handlers["disable_skip_to_simulate_btn"]
      ]();
    }
    if (recipeTemplateData?.event_handlers?.["disable_next_btn"]) {
      eventHandlers[recipeTemplateData.event_handlers["disable_next_btn"]]();
    }
  }, [selectedConditions]);

  useSaveRecipe(saveRecipeFilters, pageFilters?.recipe_type === "npi");

  useEffect(() => {
    setRecipeFilters();
  }, [pageFilters]);

  useUpdateNpiConditions(updatedConditionFilters);
  const conditionsOrderMutation = useUpdateNpiConditionsOrder(
    updateConditionsOrderFilters,
  );

  const {
    dataUpdatedAt: updateConditionWithCallbackUpdatedAt,
    data: updateConditionDataWithCallback,
  } = useUpdateNpiConditions(updatedConditionWithCallbackFilters);

  useEffect(() => {
    if (updateConditionDataWithCallback?.success) {
      eventHandlers.npiSimulationHandler();
    }
  }, [updateConditionWithCallbackUpdatedAt]);

  useEffect(() => {
    if (typeof updateConditionsOrderFilters.recipe_name !== "undefined") {
      conditionsOrderMutation.mutate(updateConditionsOrderFilters);
    }
  }, [updateConditionsOrderFilters]);

  /**
   * Set filters for both page and grid
   */
  const setFilters = async () => {
    const filters = {
      ...urlParams[pageKey],
      recipe_type: recipeTemplateData.recipe_type ?? "npi",
      show_simulate_buttons: recipeTemplateData.show_simulate_buttons ?? true,
      show_calculated_tests_header:
        recipeTemplateData.show_calculated_tests_header ?? false,
    };
    if (!filters.recipe_category) {
      const recipeData = await Helper.getRecipeDataByReportKey(
        filters.report_key,
        messageApi,
      );
      merge(filters, recipeData);
      merge(urlParams[pageKey], recipeData);
    }
    setPageFilters(filters);
    setGridFilters(urlParams);
  };

  /**
   * Set the recipe filters
   */
  const setRecipeFilters = () => {
    setSaveRecipeFilters({
      ...pageFilters,
      recipe_data: {
        conditions: selectedConditions,
      },
      notes: userPermissionsForm?.getFieldValue("notes"),
      time_initiated: Date.now(),
    });
  };

  /**
   * Update the condition query
   */
  const updateCondition = () => {
    setUpdateConditionFilters({
      ...pageFilters,
      recipe_data: {
        conditions: selectedConditions,
      },
      time_initiated: Date.now(),
    });
  };

  /**
   * Update the conditions order
   *
   * @param {function} successCbk
   */
  const updateConditionsOrder = (successCbk) => {
    setUpdateConditionsOrderFilters({
      ...pageFilters,
      recipe_data: {
        conditions: selectedConditions,
      },
      success_callback: successCbk,
      time_initiated: Date.now(),
    });
  };

  const steps = [],
    items = [];
  recipeTemplateData.items.forEach((item) => {
    items.push({
      key: item.key,
      title: item.title,
      disabled: pageFilters?.recipe_type === "npi" && disableNextBtn,
    });
    steps.push(
      <StepComponent
        name={item.key}
        pageKey={pageKey}
        pageFilters={pageFilters}
        gridFilters={gridFilters}
        userPermissionsForm={userPermissionsForm}
        setDisableSkipToSimulateBtn={setDisableSkipToSimulateBtn}
        setDisableNextBtn={setDisableNextBtn}
      />,
    );
  });

  return (
    <Flex vertical gap={30}>
      {contextHolder}
      {pageFilters?.show_calculated_tests_header ? (
        <CalculatedTestsHeader />
      ) : (
        <RecipeInfo params={pageFilters} />
      )}
      <Steps
        items={items}
        current={currentStep}
        onChange={(value) => {
          setCurrentStep(value);
        }}
      />
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        {steps[currentStep]}
      </div>
      <ProgressStepButtons
        pageFilters={pageFilters}
        steps={steps}
        currentStep={currentStep}
        setCurrentStep={setCurrentStep}
        simulationHandler={
          eventHandlers[recipeTemplateData.event_handlers["simulation"]]
        }
        saveRecipeHandler={
          eventHandlers[recipeTemplateData.event_handlers["save_recipe"]]
        }
        updateConditionHandler={
          eventHandlers[recipeTemplateData.event_handlers["update_condition"]]
        }
        updateConditionWithCallbackHandler={
          eventHandlers[
            recipeTemplateData.event_handlers["update_condition_with_callback"]
          ]
        }
        disableSkipToSimulateBtn={disableSkipToSimulateBtn}
        disableNextBtn={disableNextBtn}
      />
    </Flex>
  );
};

export default RecipeTemplate;
