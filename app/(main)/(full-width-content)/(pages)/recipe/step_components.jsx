"use client";

import { useState, memo } from "react";
import ReadCreateConditionsStep from "../npi/read_create_conditions_step";
import ReviewConditionsStep from "../npi/review_conditions_step";
import ReviewTestsStep from "../npi/review_tests_step";
import UserPermissionsStep from "../npi/user_permissions_step";
import FindUniquePartsStep from "../npi/find_unique_parts_step";
import FilterTestTable from "../calculated_tests/step1/filter_test_table";
import CreateCalculatedTest from "../calculated_tests/step2/create_calculated_test";

/**
 * Step component for recipe pages
 *
 * @param {string} name
 * @param {string} pageKey
 * @param {object} pageFilters
 * @param {object} gridFilters
 * @param {FormInstance} userPermissionsForm
 * @param {function} setDisableSkipToSimulateBtn
 * @param {function} setDisableNextBtn
 * @returns {JSX.Element}
 */
const StepComponent = ({
  name,
  pageKey,
  pageFilters,
  gridFilters,
  userPermissionsForm,
  setDisableSkipToSimulateBtn,
  setDisableNextBtn,
}) => {
  const [testTableFilters, setTestTableFilters] = useState([]);
  const [calcTestFormValues, setCalcTestFormValues] = useState({
    calcEq: {
      text: "",
      map: {},
    },
    testCfgForm: {
      // TODO: new_tnum value hardcoded for now, should come from api
      new_tnum: "33010",
      new_tname: "",
      tunit: "",
      tstep: "",
    },
    optSettingsForm: {
      within_range: "",
      sbin_number: "",
      sbin_name: "",
      hbin_number: "",
      hbin_name: "",
      private_label: "",
      private_label_color: "#0054A6",
    },
  });

  const component = {
    read_create_conditions: (
      <ReadCreateConditionsStep
        pageKey={pageKey}
        pageFilters={pageFilters}
        setDisableSkipToSimulateBtn={setDisableSkipToSimulateBtn}
        setDisableNextBtn={setDisableNextBtn}
      />
    ),
    review_selected_conditions: (
      <ReviewConditionsStep pageKey={pageKey} gridFilters={gridFilters} />
    ),
    review_tests: (
      <ReviewTestsStep
        pageFilters={pageFilters}
        pageKey={pageKey}
        gridFilters={gridFilters}
      />
    ),
    filter_test_table: (
      <FilterTestTable
        pageFilters={pageFilters}
        pageKey={pageKey}
        setDisableNextBtn={setDisableNextBtn}
        setTestTableFilters={setTestTableFilters}
      />
    ),
    create_calculated_test: (
      <CreateCalculatedTest
        pageKey={pageKey}
        pageFilters={pageFilters}
        setDisableNextBtn={setDisableNextBtn}
        testTableFilters={testTableFilters}
        calcTestFormValues={calcTestFormValues}
        setCalcTestFormValues={setCalcTestFormValues}
      />
    ),
    save_calculated_test: <h1>Save Calculated Test</h1>,
    find_unique_parts: (
      <FindUniquePartsStep
        pageFilters={pageFilters}
        pageKey={pageKey}
        gridFilters={gridFilters}
      />
    ),
    user_permissions: <UserPermissionsStep form={userPermissionsForm} />,
  };

  return component[name];
};

export default memo(StepComponent);
