import { useState, useRef, useEffect } from "react";
import { App, Form, Input, Modal } from "antd";
import { useEffectApiFetch } from "../../../../../../src/hooks";
import YHGrid from "../../../../../../src/utils/grid/yh_grid";
import Api from "../../../../../../src/utils/api";
import { useBoundStore } from "../../../../../../src/store/store";
import { ComponentNameMapper } from "../../../../../../src/utils/grid/component_name_mapper";

/**
 * Teams tab
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function TeamsTab({ pageKey }) {
  const { message } = App.useApp();
  const teamsGridRef = useRef();
  const [editTeamForm] = Form.useForm();

  const [isEditFormValuesValid, setIsEditFormValuesValid] = useState(false);
  const [gridComponent, setGridComponent] = useState();
  const filters = useBoundStore((state) => state.filters);
  const setFilters = useBoundStore((state) => state.setFilters);

  const isEditRolePermissionTeamModalOpen = useBoundStore(
    (state) => state.isEditRolePermissionTeamModalOpen,
  );
  const setIsEditRolePermissionTeamModalOpen = useBoundStore(
    (state) => state.setIsEditRolePermissionTeamModalOpen,
  );
  const rolePermissionTeamData = useBoundStore(
    (state) => state.rolePermissionTeamData,
  );

  useEffectApiFetch(
    () => {
      return getTeamsGridComponent();
    },
    () => {
      setGridComponent();
    },
  );

  useEffect(() => {
    setFilters({ [pageKey]: false });
  }, []);

  /**
   * Get and set teams grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getTeamsGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setGridComponent(res.data);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      { name: ComponentNameMapper.user_team_management },
    );
    return abortCtl;
  };

  /**
   * Submit form values
   *
   * @param {object} values
   */
  const handleEditTeam = (values) => {
    const teamId = rolePermissionTeamData.id;
    Api.getData(
      `/api/v1/internal/user_role_permission_team/team/${teamId}`,
      "put",
      () => {
        setIsEditRolePermissionTeamModalOpen(false);
        setIsEditFormValuesValid(false);
        message.success("Successfully edited Team", 5);
        setFilters({ [pageKey]: !filters[pageKey] });
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: values.name,
        display_name: values.name,
        description: values.description,
      },
    );
  };

  return (
    <>
      {gridComponent && (
        <YHGrid
          gridRef={teamsGridRef}
          gridId="teams_grid"
          gridOptions={{ rowModelType: "clientSide" }}
          component={gridComponent}
          filters={filters}
          rowGroups={[]}
          pageKey={pageKey}
          wrapperClassName="flex grow flex-col h-full"
        />
      )}
      <Modal
        title="Edit Team"
        destroyOnClose={true}
        open={isEditRolePermissionTeamModalOpen}
        okText="Edit"
        onOk={() => {
          editTeamForm.submit();
        }}
        okButtonProps={{
          disabled: !isEditFormValuesValid,
        }}
        onCancel={() => {
          setIsEditRolePermissionTeamModalOpen(false);
          setIsEditFormValuesValid(false);
        }}
      >
        <Form
          clearOnDestroy={true}
          form={editTeamForm}
          layout="vertical"
          requiredMark={false}
          onFinish={handleEditTeam}
          onValuesChange={() => {
            editTeamForm
              .validateFields()
              .then(() => setIsEditFormValuesValid(true))
              .catch((err) =>
                setIsEditFormValuesValid(err.errorFields.length === 0),
              );
          }}
          initialValues={rolePermissionTeamData}
        >
          <Form.Item
            name="name"
            label="Team Name"
            rules={[{ required: true, message: "Team Name is required." }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="Team Description"
            rules={[
              { required: true, message: "Team Description is required." },
            ]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
