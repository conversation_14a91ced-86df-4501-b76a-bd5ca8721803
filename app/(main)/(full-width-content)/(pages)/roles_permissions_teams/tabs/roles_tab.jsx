import { useState, useRef } from "react";
import { useEffectApiFetch } from "../../../../../../src/hooks";
import YHGrid from "../../../../../../src/utils/grid/yh_grid";
import Api from "../../../../../../src/utils/api";
import { useBoundStore } from "../../../../../../src/store/store";
import { ComponentNameMapper } from "../../../../../../src/utils/grid/component_name_mapper";

/**
 * Roles tab
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function RolesTab({ pageKey }) {
  const rolesGridRef = useRef();
  const [gridComponent, setGridComponent] = useState();
  const filters = useBoundStore((state) => state.filters);
  const setFilters = useBoundStore((state) => state.setFilters);

  useEffectApiFetch(
    () => {
      return getRolesGridComponent();
    },
    () => {
      setGridComponent();
    },
  );

  /**
   * Get and set roles grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getRolesGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setGridComponent(res.data);
        }
      },
      () => {},
      { name: ComponentNameMapper.user_role_management },
    );
    return abortCtl;
  };

  return (
    <>
      {gridComponent && (
        <YHGrid
          gridRef={rolesGridRef}
          gridId="roles_grid"
          gridOptions={{ rowModelType: "clientSide" }}
          component={gridComponent}
          filters={filters}
          setFilters={setFilters}
          rowGroups={[]}
          pageKey={pageKey}
          wrapperClassName="flex grow flex-col h-full"
        />
      )}
    </>
  );
}
