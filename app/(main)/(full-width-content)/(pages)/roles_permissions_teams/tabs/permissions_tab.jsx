import { useState, useRef } from "react";
import { App } from "antd";
import { useEffectApiFetch } from "../../../../../../src/hooks";
import YHGrid from "../../../../../../src/utils/grid/yh_grid";
import Api from "../../../../../../src/utils/api";
import { useBoundStore } from "../../../../../../src/store/store";
import { ComponentNameMapper } from "../../../../../../src/utils/grid/component_name_mapper";

/**
 * Permissions tab
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function PermissionsTab({ pageKey }) {
  const { message } = App.useApp();
  const permissionsGridRef = useRef();
  const [gridComponent, setGridComponent] = useState();
  const filters = useBoundStore((state) => state.filters);
  const setFilters = useBoundStore((state) => state.setFilters);

  useEffectApiFetch(
    () => {
      return getPermissionsGridComponent();
    },
    () => {
      setGridComponent();
    },
  );

  /**
   * Get and set permissions grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getPermissionsGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setGridComponent(res.data);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      { name: ComponentNameMapper.user_permission_management },
    );
    return abortCtl;
  };

  return (
    <>
      {gridComponent && (
        <YHGrid
          gridRef={permissionsGridRef}
          gridId="permissions_grid"
          gridOptions={{ rowModelType: "clientSide" }}
          component={gridComponent}
          filters={filters}
          setFilters={setFilters}
          rowGroups={[]}
          pageKey={pageKey}
          wrapperClassName="flex grow flex-col h-full"
        />
      )}
    </>
  );
}
