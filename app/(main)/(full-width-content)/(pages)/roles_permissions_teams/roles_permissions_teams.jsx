import { Tabs } from "antd";
import PagesLayout from "../layout";
import RolesTab from "./tabs/roles_tab";
import PermissionsTab from "./tabs/permissions_tab";
import TeamsTab from "./tabs/teams_tab";

/**
 * Roles, Permissions, & Teams management page
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function RolesPermissionsTeamsManagement({ pageKey }) {
  const items = [
    {
      key: "roles",
      label: "Roles",
      children: <RolesTab pageKey={pageKey} />,
    },
    {
      key: "permissions",
      label: "Permissions",
      children: <PermissionsTab pageKey={pageKey} />,
    },
    {
      key: "teams",
      label: "Teams",
      children: <TeamsTab pageKey={pageKey} />,
    },
  ];
  return (
    <PagesLayout>
      <Tabs defaultActiveKey="roles" items={items} />
    </PagesLayout>
  );
}
