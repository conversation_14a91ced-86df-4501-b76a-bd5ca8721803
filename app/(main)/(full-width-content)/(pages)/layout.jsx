"use client";

import dynamic from "next/dynamic";
import PageLoading from "../../loading";

const PageLayout = dynamic(() => import("./page_layout"), {
  ssr: false,
  loading: () => <PageLoading />,
});

/**
 * Layout component of pages
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const PagesLayout = ({ children }) => {
  return <PageLayout>{children}</PageLayout>;
};
export default PagesLayout;
