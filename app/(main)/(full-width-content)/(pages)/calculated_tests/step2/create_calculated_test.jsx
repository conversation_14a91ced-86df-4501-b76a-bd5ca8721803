import { useEffect, useRef, useReducer, useState } from "react";
import { Collapse, Form, Modal, Typography, message, theme } from "antd";
import DatalogInfoGrid from "../../../../../../src/utils/components/recipe_common/datalog_info_grid";
import YHGrid from "../../../../../../src/utils/grid/yh_grid";
import Api from "../../../../../../src/utils/api";
import { useEffectApiFetch } from "../../../../../../src/hooks";
import { ComponentNameMapper } from "../../../../../../src/utils/grid/component_name_mapper";
import CalculatedTestEquation from "./calculated_test_equation";
import CalculatedTestConfiguration from "./calculated_test_configuration";
import CalculatedTestOptionalSettings from "./calculated_test_optional_settings";

/*
 * Create Calculated Test component
 *
 * @param {string} pageKey
 * @param {object} pageFilters
 * @param {function} setDisableNextBtn
 * @param {object} testTableFilters
 * @param {object} calcTestFormValues
 * @param {function} setCalcTestFormValues
 * @returns {JSX.Element}
 * */
export default function CreateCalculatedTest({
  pageKey,
  pageFilters,
  setDisableNextBtn,
  testTableFilters,
  calcTestFormValues,
  setCalcTestFormValues,
}) {
  const { token } = theme.useToken();
  const [messageApi, messageContextHolder] = message.useMessage();
  const [modalApi, modalContextHolder] = Modal.useModal();
  const [variablesRowValueMapState, variablesRowValueMapDispatch] = useReducer(
    (state, action) => {
      const handlers = {
        delete_key: (s, a) => {
          const next = { ...s };
          delete next[a.key];
          return next;
        },
        set_value: (s, a) => {
          return {
            ...s,
            [a.key]: {
              ...(s[a.key] || {}),
              ...(typeof a.value === "object" ? a.value : {}),
            },
          };
        },
      };
      return handlers[action.type]
        ? handlers[action.type](state, action)
        : state;
    },
    calcTestFormValues.calcEq.map,
  );
  const [equationText, setEquationText] = useState("");
  const [gridComponent, setGridComponent] = useState();
  const [
    isEquationOutputPreviewModalOpen,
    setIsEquationOutputPreviewModalOpen,
  ] = useState(false);
  const [gridFilters, setGridFilters] = useState({});
  const testVariableNumberRef = useRef(0);
  const testTableGridRef = useRef();
  const [equationForm] = Form.useForm();
  const [configurationForm] = Form.useForm();
  const [optionalSettingsForm] = Form.useForm();
  const calculatedEquation = Form.useWatch("equation_textarea", equationForm);
  const configurationFormValues = Form.useWatch((values) => {
    return values;
  }, configurationForm);
  const optionalSettingsFormValues = Form.useWatch((values) => {
    return values;
  }, optionalSettingsForm);

  useEffect(() => {
    equationForm.setFieldValue(
      "equation_textarea",
      calcTestFormValues.calcEq.text,
    );
    configurationForm.setFieldsValue(calcTestFormValues.testCfgForm);
    optionalSettingsForm.setFieldsValue(calcTestFormValues.optSettingsForm);
  }, []);

  useEffect(() => {
    const newTestNumber = configurationFormValues?.new_tnum;
    const newTestName = configurationFormValues?.new_tname;
    const testStep = configurationFormValues?.tstep;
    setDisableNextBtn(
      calculatedEquation === "" ||
        newTestNumber === "" ||
        newTestName === "" ||
        testStep === "",
    );
    setCalcTestFormValues({
      calcEq: {
        text: calculatedEquation,
        map: variablesRowValueMapState,
      },
      testCfgForm: configurationFormValues,
      optSettingsForm: optionalSettingsFormValues,
    });
  }, [
    calculatedEquation,
    variablesRowValueMapState,
    configurationFormValues,
    optionalSettingsFormValues,
  ]);

  useEffect(() => {
    const filters = {};
    filters[pageKey] = pageFilters;
    setGridFilters(filters);
  }, [pageFilters]);

  useEffect(() => {
    const variables = Object.values(variablesRowValueMapState).filter(
      (variable) => !variable.removed,
    );
    const textVariables =
      variables.length === 0
        ? ""
        : variables.length === 1
          ? variables[0].name
          : `max(${variables.map((variable) => variable.name).join()})`;
    setEquationText(textVariables);
  }, [variablesRowValueMapState]);

  useEffect(() => {
    equationForm.setFieldValue("equation_textarea", equationText);
  }, [equationText]);

  useEffectApiFetch(
    () => {
      return getLinkGridComponent();
    },
    () => {
      setGridComponent();
    },
  );

  /*
   * Get calculated test grid component
   *
   * @returns {AbortController} abortCtl
   * */
  const getLinkGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setGridComponent(res.data);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      { name: ComponentNameMapper.calculated_tests_table },
    );
    return abortCtl;
  };

  /*
   * Handle cell editing stopped event
   *
   * @param {CellEditingStoppedEvent} event
   * */
  const handleCellEditingStopped = (event) => {
    const api = event.api;
    const node = event.node;
    const value = event.value;
    const rowId = node.id;
    const rowIndex = node.rowIndex;
    const colId = "variable";

    if (value === null || value === "") {
      variablesRowValueMapDispatch({
        type: "delete_key",
        key: rowId,
      });
    } else {
      if (
        Object.values(variablesRowValueMapState)
          .map((row) => row.name)
          .includes(value?.trim()) &&
        variablesRowValueMapState[rowId]?.name !== value?.trim()
      ) {
        modalApi.warning({
          title: "Duplicate Variable Detected",
          content: (
            <Typography.Text>
              The variable
              <Typography.Text strong>{` ${value} `}</Typography.Text> is
              already in use. Please rename it to create a unique variable.
            </Typography.Text>
          ),
          okText: "Rename Variable",
          onOk: () => {
            api.startEditingCell({
              rowIndex: rowIndex,
              colKey: colId,
            });
          },
          onCancel: () => {
            api.startEditingCell({
              rowIndex: rowIndex,
              colKey: colId,
            });
          },
          centered: true,
        });
      } else {
        if (value !== undefined && value !== null) {
          variablesRowValueMapDispatch({
            type: "set_value",
            key: rowId,
            value: {
              name: value.trim(),
              data: 1.8564, // TODO: value hardcoded for now, should come from api
              removed: false,
            },
          });
          node.setDataValue(colId, value.trim());
        }
      }
    }
  };

  /*
   * Handle row selected event
   *
   * @param {RowSelectedEvent} event
   * */
  const handleRowSelected = (event) => {
    const node = event.node;
    const rowId = node.id;
    const colId = "variable";

    if (node.isSelected()) {
      if (!variablesRowValueMapState[rowId]) {
        do {
          testVariableNumberRef.current += 1;
        } while (
          Object.values(variablesRowValueMapState)
            .map((row) => row.name)
            .includes(`T${testVariableNumberRef.current}`)
        );
        const variableName = `T${testVariableNumberRef.current}`;
        variablesRowValueMapDispatch({
          type: "set_value",
          key: rowId,
          value: {
            name: variableName,
            data: 1.8564, // TODO: value hardcoded for now, should come from api
            removed: false,
          },
        });
        node.setDataValue(colId, variableName);
      } else {
        variablesRowValueMapDispatch({
          type: "set_value",
          key: rowId,
          value: { removed: false },
        });
        node.setDataValue(colId, variablesRowValueMapState[rowId]?.name);
      }
    } else {
      node.setDataValue(colId, null);
      if (variablesRowValueMapState[rowId]) {
        variablesRowValueMapDispatch({
          type: "set_value",
          key: rowId,
          value: { removed: true },
        });
      }
    }
  };

  /*
   * Handle model updated event
   *
   * @param {ModelUpdatedEvent} event
   * */
  const handleModelUpdated = (event) => {
    const api = event.api;
    const colId = "variable";
    api.forEachNode((node) => {
      if (variablesRowValueMapState[node.id]) {
        const obj = variablesRowValueMapState[node.id];
        node.setDataValue(colId, obj.removed ? null : obj.name);
      }
    });
  };

  /**
   * Handle first data rendered event
   *
   * @param {FirstDataRenderedEvent} event
   */
  const handleFirstDataRendered = (event) => {
    setTimeout(() => {
      event.api.forEachNode((node) => {
        const variable = variablesRowValueMapState[node.id];
        if (variable && !variable.removed) {
          node.setSelected(true);
        }
      });
    }, 500);
  };

  /*
   * Handle remove tests click
   *
   * @param {Array} selectedNodes
   * @param {object} tableUniqueCols
   * @param {function} deleteTableRow
   * */
  const handleRemoveTest = (selectedNodes, tableUniqueCols, deleteTableRow) => {
    modalApi.confirm({
      title: "Remove Selection",
      content: (
        <Typography.Text>
          Are you sure you want to remove the selected test(s)?
        </Typography.Text>
      ),
      okText: "Remove",
      onOk: () => {
        selectedNodes.forEach((node) => {
          variablesRowValueMapDispatch({
            type: "delete_key",
            key: node.id,
          });
          deleteTableRow(tableUniqueCols, node.data);
        });
        testVariableNumberRef.current = 0;
      },
      centered: true,
    });
  };

  /*
   * Handle clear all variables click
   *
   * @param {Array} selectedNodes
   * */
  const handleClearAllVariables = (selectedNodes) => {
    selectedNodes.forEach((node) => {
      variablesRowValueMapDispatch({
        type: "delete_key",
        key: node.id,
      });
    });
    Object.entries(variablesRowValueMapState).forEach(([key, value]) => {
      if (value.removed) {
        variablesRowValueMapDispatch({
          type: "delete_key",
          key: key,
        });
      }
    });
    testVariableNumberRef.current = 0;
  };

  return (
    <>
      {messageContextHolder}
      {modalContextHolder}
      <Collapse
        style={{
          background: token.colorBgContainer,
        }}
        items={[
          {
            key: "calculated_test_dlog_info",
            label: "Selected Datalog Information",
            style: {
              marginBottom: 16,
              background: token.yhHeaderColorBg,
              borderRadius: token.borderRadius,
            },
            children: (
              <DatalogInfoGrid
                pageKey={pageKey}
                pageFilters={pageFilters}
                gridId="calculated_test_dlog_info"
              />
            ),
          },
        ]}
      />
      <div className="mt-4">
        <Typography.Title level={5}>Test Table</Typography.Title>
        {gridComponent && (
          <YHGrid
            gridRef={testTableGridRef}
            pageKey={pageKey}
            gridId="test_table_grid"
            component={gridComponent}
            gridOptions={{ rowModelType: "serverSide" }}
            filters={gridFilters}
            initialGridFilters={testTableFilters}
            rowGroups={[]}
            wrapperClassName="flex grow flex-col h-full"
            eventHandlers={{
              onCellEditingStopped: handleCellEditingStopped,
              onRowSelected: handleRowSelected,
              onModelUpdated: handleModelUpdated,
              onFirstDataRendered: handleFirstDataRendered,
              onRemoveTest: handleRemoveTest,
              onClearAllVariables: handleClearAllVariables,
            }}
          />
        )}
      </div>
      <div className="mt-4">
        <CalculatedTestEquation
          equationForm={equationForm}
          isEquationOutputPreviewModalOpen={isEquationOutputPreviewModalOpen}
          setIsEquationOutputPreviewModalOpen={
            setIsEquationOutputPreviewModalOpen
          }
          equationText={equationText}
          setEquationText={setEquationText}
          variablesRowValueMapState={variablesRowValueMapState}
        />
      </div>
      <div className="mt-4">
        <CalculatedTestConfiguration
          configurationForm={configurationForm}
          pageKey={pageKey}
          pageFilters={pageFilters}
          calcTestFormValues={calcTestFormValues}
        />
      </div>
      <div className="mt-4">
        <CalculatedTestOptionalSettings
          optionalSettingsForm={optionalSettingsForm}
        />
      </div>
    </>
  );
}
