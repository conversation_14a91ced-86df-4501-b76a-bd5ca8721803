import { Col, Form, Input, Row, Typography } from "antd";
import FilterSelect from "../../../../../../src/utils/grid/components/filter_select";

/*
 * Calculated test configuration component
 *
 * @param {FormInstance} configurationForm
 * @param {object} pageFilters
 * @param {object} calcTestFormValues
 * @returns {JSX.Element}
 * */
export default function CalculatedTestConfiguration({
  configurationForm,
  pageFilters,
  calcTestFormValues,
}) {
  const testStep = calcTestFormValues?.testCfgForm?.tstep;
  const hiLimFormItemRules = [
    ({ getFieldValue }) => {
      return {
        validator: (_, value) => {
          const floatLoLim = parseFloat(getFieldValue("lo_lim"));
          const floatHiLim = parseFloat(value);
          if (
            isNaN(floatLoLim) ||
            isNaN(floatHiLim) ||
            floatLoLim <= floatHiLim
          ) {
            return Promise.resolve();
          }
          return Promise.reject(new Error("Hi Lim must be ≥ to <PERSON> Lim"));
        },
      };
    },
  ];

  return (
    <>
      <Typography.Title level={5}>Test Configuration</Typography.Title>
      <Form
        layout="vertical"
        form={configurationForm}
        initialValues={{
          // TODO: new_tnum value hardcoded for now, should come from api
          new_tnum: "",
          new_tname: "",
          tunit: "",
          tstep: "",
        }}
      >
        <Row gutter={4}>
          <Col span={4}>
            <Form.Item name="new_tnum" label="New Test Number">
              <Input placeholder="Enter Number" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item name="new_tname" label="New Test Name">
              <Input placeholder="Enter Name" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item name="tunit" label="Unit">
              <Input placeholder="Enter Unit" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item name="lo_lim" label="Lo Limit">
              <Input type="number" placeholder="Enter" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item
              name="hi_lim"
              label="Hi Limit"
              dependencies={["lo_lim"]}
              rules={hiLimFormItemRules}
            >
              <Input type="number" placeholder="Enter" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item name="tstep" label="Test Step">
              <FilterSelect
                className="min-w-16"
                placeholder="Select"
                params={{
                  api: {
                    ...pageFilters,
                    field: "test_step",
                  },
                }}
                deps={[pageFilters]}
                selectFirstOption={testStep === undefined || testStep === ""}
                form={configurationForm}
                fieldName={"tstep"}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </>
  );
}
