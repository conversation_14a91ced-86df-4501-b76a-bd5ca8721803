import { useState } from "react";
import { Modal } from "antd";
import { FileAddOutlined } from "@ant-design/icons";
import AddTestsForm from "../../../../../../src/utils/forms/add_tests_form";

/*
 * Add tests form modal component
 *
 * @param {boolean} isAddTestsModalOpen
 * @param {function} setIsAddTestsModalOpen
 * @param {FormInstance} addTestsForm
 * @param {React.Ref} gridRef
 * @param {string} pageKey
 * @param {object} filters
 * @param {function} addUpdateTableRow
 * @param {object} tableUniqueCols
 * @returns {JSX.Element}
 * */
export default function AddTestsFormModal({
  isAddTestsModalOpen,
  setIsAddTestsModalOpen,
  addTestsForm,
  gridRef,
  pageKey,
  filters,
  addUpdateTableRow,
  tableUniqueCols,
}) {
  const [isModalButtonsDisabled, setIsModalButtonsDisabled] = useState(false);
  const [isAddTestsButtonLoading, setIsAddTestsButtonLoading] = useState(false);
  return (
    <Modal
      className="!w-164"
      title="Add Tests"
      destroyOnHidden={true}
      closable={false}
      open={isAddTestsModalOpen}
      onCancel={() => setIsAddTestsModalOpen(false)}
      onOk={() => {
        addTestsForm.submit();
        setIsAddTestsButtonLoading(true);
        setIsModalButtonsDisabled(true);
      }}
      okText="Add Tests"
      cancelText="Close"
      okButtonProps={{
        icon: <FileAddOutlined />,
        disabled: isModalButtonsDisabled,
        loading: isAddTestsButtonLoading,
      }}
      cancelButtonProps={{
        disabled: isModalButtonsDisabled,
      }}
    >
      <AddTestsForm
        gridRef={gridRef}
        form={addTestsForm}
        pageKey={pageKey}
        filters={filters}
        addUpdateTableRow={addUpdateTableRow}
        tableUniqueCols={tableUniqueCols}
        setIsAddTestsModalOpen={setIsAddTestsModalOpen}
        setIsAddTestsButtonLoading={setIsAddTestsButtonLoading}
        setIsModalButtonsDisabled={setIsModalButtonsDisabled}
      />
    </Modal>
  );
}
