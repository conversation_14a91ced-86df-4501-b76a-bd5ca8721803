import { Button, Modal } from "antd";
import TestTableFilterForm from "../../../../../../src/utils/forms/test_table_filter_form";

/*
 * Test table filters form modal component
 *
 * @param {boolean} isTestTableFilterModalOpen
 * @param {function} setIsTestTableFilterModalOpen
 * @param {FormInstance} testTableFilterForm
 * @param {object} gridFilters
 * @param {function} setGridFilters
 * @param {function} reloadData
 * @returns {JSX.Element}
 * */
export default function TestTableFilterFormModal({
  isTestTableFilterModalOpen,
  setIsTestTableFilterModalOpen,
  testTableFilterForm,
  gridFilters,
  setGridFilters,
  reloadData,
}) {
  return (
    <Modal
      className="!w-164"
      title="Table Filter"
      open={isTestTableFilterModalOpen}
      onCancel={() => setIsTestTableFilterModalOpen(false)}
      footer={[
        <Button
          key="cancel"
          onClick={() => {
            testTableFilterForm.resetFields();
            setIsTestTableFilterModalOpen(false);
          }}
        >
          Cancel
        </Button>,
        <Button
          key="clear_all"
          onClick={() => {
            testTableFilterForm.resetFields();
          }}
        >
          Clear All
        </Button>,
        <Button
          key="apply"
          type="primary"
          onClick={() => {
            testTableFilterForm.submit();
            setIsTestTableFilterModalOpen(false);
          }}
        >
          Apply
        </Button>,
      ]}
    >
      <TestTableFilterForm
        form={testTableFilterForm}
        gridFilters={gridFilters}
        setGridFilters={setGridFilters}
        reloadTestsData={reloadData}
      />
    </Modal>
  );
}
