import {
  But<PERSON>,
  Col,
  Form,
  Input,
  Popover,
  Row,
  Tooltip,
  Typography,
} from "antd";
import { ClearOutlined, InfoCircleTwoTone } from "@ant-design/icons";
import EquationOutputPreviewModal from "./equation_output_preview_modal";

/*
 * Calculated test equation component
 *
 * @param {FormInstance} equationForm
 * @param {boolean} isEquationOutputPreviewModalOpen
 * @param {function} setIsEquationOutputPreviewModalOpen
 * @param {string} equationText
 * @param {function} setEquationText
 * @param {object} variablesRowValueMapState
 * @returns {JSX.Element}
 * */
export default function CalculatedTestEquation({
  equationForm,
  isEquationOutputPreviewModalOpen,
  setIsEquationOutputPreviewModalOpen,
  equationText,
  setEquationText,
  variablesRowValueMapState,
}) {
  const equationCharCount = equationText?.length ?? 0;
  return (
    <>
      <EquationOutputPreviewModal
        isEquationOutputPreviewModalOpen={isEquationOutputPreviewModalOpen}
        setIsEquationOutputPreviewModalOpen={
          setIsEquationOutputPreviewModalOpen
        }
        equationText={equationText}
        variablesRowValueMapState={variablesRowValueMapState}
      />
      <Typography.Title level={5}>Calculated Equation</Typography.Title>
      <Form layout="vertical" form={equationForm}>
        <Row gutter={16} className="mb-0 pb-0">
          <Col span={20}>
            <Form.Item
              name="equation_textarea"
              label={
                <span>
                  Equation for Calculated Test
                  <Popover
                    placement="top"
                    color="#c4ac5a"
                    title="You can enter any math equation supported in PHP"
                    content={
                      <>
                        <p className="text-black">
                          (a) Simple equation: T1 - T2
                        </p>
                        <p className="text-black">
                          (b) Conditional: (T1 {">"} 400) ? T1 - T2 : T1
                        </p>
                        <p className="text-black">
                          (c) PHP Math function: log10(T1)
                        </p>
                      </>
                    }
                  >
                    <InfoCircleTwoTone className="ml-4" />
                  </Popover>
                  <Tooltip placement="top" title="Clear the entered equation">
                    <Button
                      className="ml-4"
                      type="text"
                      icon={<ClearOutlined style={{ color: "#1890ff" }} />}
                      onClick={() => {
                        setEquationText("");
                      }}
                    />
                  </Tooltip>
                </span>
              }
            >
              <Input.TextArea
                className="pb-8"
                autoSize={{ minRows: 4 }}
                onChange={({ target }) => {
                  setEquationText(target.value?.trim());
                }}
              />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Button
              className="mt-8"
              type="primary"
              disabled={equationCharCount === 0}
              onClick={() => {
                setIsEquationOutputPreviewModalOpen(true);
              }}
            >
              Check Equation Output
            </Button>
          </Col>
        </Row>
        <Row>
          <Col span={1} offset={19} className="grid">
            <Typography.Paragraph className="justify-self-end">
              {equationCharCount}
            </Typography.Paragraph>
          </Col>
        </Row>
      </Form>
    </>
  );
}
