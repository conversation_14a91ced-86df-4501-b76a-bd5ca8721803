import { useEffect } from "react";
import { Col, ColorPicker, Form, Input, Row, Typography } from "antd";
import { InfoCircleTwoTone } from "@ant-design/icons";

/*
 * Calculated test optional settings component
 *
 * @param {FormInstance} optionalSettingsForm
 * @returns {JSX.Element}
 * */
export default function CalculatedTestOptionalSettings({
  optionalSettingsForm,
}) {
  const sbinNumber = Form.useWatch("sbin_number", optionalSettingsForm);
  const hbinNumber = Form.useWatch("hbin_number", optionalSettingsForm);

  useEffect(() => {
    if (sbinNumber === "") {
      optionalSettingsForm.setFieldValue("sbin_name", "");
    }
    if (hbinNumber === "") {
      optionalSettingsForm.setFieldValue("hbin_name", "");
    }
  }, [sbinNumber, hbinNumber]);

  return (
    <>
      <Typography.Title level={5}>Optional Settings</Typography.Title>
      <Form
        layout="vertical"
        form={optionalSettingsForm}
        initialValues={{
          within_range: "",
          sbin_number: "",
          sbin_name: "",
          hbin_number: "",
          hbin_name: "",
          private_label: "",
          private_label_color: "#0054A6",
        }}
      >
        <Row gutter={4}>
          <Col span={4}>
            <Form.Item
              name="within_range"
              label="Within Range"
              tooltip={{
                placement: "right",
                icon: <InfoCircleTwoTone />,
                title: (
                  <>
                    <p>
                      Choose which test numbers to include when suggesting this
                      calculated test.
                    </p>
                    <p>Example: 100-150</p>
                  </>
                ),
              }}
            >
              <Input placeholder="(Optional)" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item
              name="sbin_number"
              label="SBin Number"
              tooltip={{
                placement: "right",
                icon: <InfoCircleTwoTone />,
                title: "Define unique SBin Number.",
              }}
            >
              <Input placeholder="SBin Number (Optional)" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item
              name="sbin_name"
              label="SBin Name"
              tooltip={{
                placement: "right",
                icon: <InfoCircleTwoTone />,
                title: "Enter SBin Number first to enable this field.",
              }}
            >
              <Input
                disabled={sbinNumber === ""}
                placeholder="(Enter Bin Name)"
              />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item
              name="hbin_number"
              label="HBin Number"
              tooltip={{
                placement: "right",
                icon: <InfoCircleTwoTone />,
                title: "Define unique HBin Number.",
              }}
            >
              <Input placeholder="HBin Number (Optional)" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item
              name="hbin_name"
              label="HBin Name"
              tooltip={{
                placement: "right",
                icon: <InfoCircleTwoTone />,
                title: "Enter HBin Number first to enable this field.",
              }}
            >
              <Input
                disabled={hbinNumber === ""}
                placeholder="(Enter Bin Name)"
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={4}>
          <Col span={4}>
            <Form.Item
              name="private_label"
              label="Private Label"
              tooltip={{
                placement: "right",
                icon: <InfoCircleTwoTone />,
                title:
                  "Add a private label that will be applied to the datalog if the data exceeds the test limits.",
              }}
            >
              <Input placeholder="Label Name" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item name="private_label_color" label="Select Label Color">
              <ColorPicker
                presets={[
                  {
                    key: "select_label_colors",
                    label: "Select Label Colors",
                    // TODO: color values hardcoded for now, should come from api
                    colors: [
                      "#0054A6",
                      "#00A1AF",
                      "#8FBB1C",
                      "#FFC700",
                      "#CE2903",
                      "#A91D9E",
                    ],
                  },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </>
  );
}
