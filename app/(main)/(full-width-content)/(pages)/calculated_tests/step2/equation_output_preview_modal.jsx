import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";

/*
 * Equation output preview modal component
 *
 * @param {boolean} isEquationOutputPreviewModalOpen,
 * @param {function} setIsEquationOutputPreviewModalOpen,
 * @param {string} equationText,
 * @param {object} variablesRowValueMapState,
 * @returns {JSX.Element}
 * */
export default function EquationOutputPreviewModal({
  isEquationOutputPreviewModalOpen,
  setIsEquationOutputPreviewModalOpen,
  equationText,
  variablesRowValueMapState,
}) {
  /*
   * Render the variables name and value
   *
   * @returns {JSX.Element}
   * */
  const renderVariablesRows = () => {
    return (
      <>
        {Object.entries(variablesRowValueMapState)
          .filter(([, value]) => {
            return !value.removed;
          })
          .map(([key, value]) => {
            return (
              <Row key={key} gutter={4}>
                <Col span={3}>{value.name}</Col>
                <Col span={1}>=</Col>
                <Col span={20}>{value.data}</Col>
              </Row>
            );
          })}
      </>
    );
  };

  return (
    <>
      <Modal
        destroyOnHidden
        title="Equation Output Preview"
        open={isEquationOutputPreviewModalOpen}
        onCancel={() => {
          setIsEquationOutputPreviewModalOpen(false);
        }}
        footer={[
          <Button
            key="close"
            type="primary"
            onClick={() => {
              setIsEquationOutputPreviewModalOpen(false);
            }}
          >
            Close
          </Button>,
        ]}
      >
        <>
          <div className="mt-4">
            <p className="text-blue-900 font-bold">Equation:</p>
            <p className="px-4">{equationText}</p>
          </div>
          <div className="mt-4">
            <p className="text-blue-900 font-bold">Where:</p>
            <p className="px-4">{renderVariablesRows()}</p>
          </div>
          <div className="mt-4">
            <p className="text-blue-900 font-bold">Result:</p>
            {/* TODO: value hardcoded for now, should come from api */}
            <p className="px-4">5.2948</p>
          </div>
        </>
      </Modal>
    </>
  );
}
