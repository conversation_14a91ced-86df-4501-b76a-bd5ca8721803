import { Button, <PERSON>lex, Toolt<PERSON> } from "antd";
import {
  DownloadOutlined,
  FileAddOutlined,
  FolderOpenOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
} from "@ant-design/icons";

export default function CalculatedTestsHeader() {
  return (
    <Flex justify="space-between">
      <Flex wrap="wrap" gap="middle" className="min-h-12">
        <Button icon={<FileAddOutlined />}>Create New Calculated Test</Button>
        <Button icon={<FolderOpenOutlined />}>Load a Calculated Test</Button>
        <Button icon={<SettingOutlined />}>Manage Calculated Tests</Button>
      </Flex>
      <Flex wrap="wrap" gap="middle" className="min-h-12">
        <Tooltip title="View this page in Zendesk">
          <Button icon={<QuestionCircleOutlined />} />
        </Tooltip>
        <Button icon={<DownloadOutlined />} />
      </Flex>
    </Flex>
  );
}
