import { useEffect, useState } from "react";
import { Checkbox, Col, Flex, Row } from "antd";
import TestListSelect from "../../../../../../src/utils/components/test_list_select";

const filterOptions = [
  {
    value: "followed_tests_show_only",
    label: "Followed Tests",
    disabled: true,
  },
  {
    value: "calculated_tests_show_only",
    label: "Calculated Tests",
    disabled: false,
  },
  {
    value: "calculated_related_tests_show_only",
    label: "Calculated & Related Tests",
    disabled: false,
  },
  {
    value: "summarize_by_pin_show_only",
    label: "Summarize by Pin",
    disabled: false,
  },
  {
    value: "select_tests_manually",
    label: "Select Tests Manually",
    disabled: false,
  },
];

/*
 * Filter Test Table component
 *
 * @param {object} pageFilters
 * @param {string} pageKey
 * @param {function} setDisableNextBtn
 * @param {function} setTestTableFilters
 * @returns {JSX.Element}
 * */
export default function FilterTestTable({
  pageFilters,
  pageKey,
  setDisableNextBtn,
  setTestTableFilters,
}) {
  const [selectedFilters, setSelectedFilters] = useState([
    "calculated_tests_show_only",
  ]);

  useEffect(() => {
    setDisableNextBtn(!(selectedFilters.length > 0));
    filterOptions.forEach((item) => {
      if (!item.disabled && item.value !== "select_tests_manually") {
        setTestTableFilters((prev) => {
          return {
            ...prev,
            [item.value]: selectedFilters.includes(item.value),
          };
        });
      }
    });
  }, [selectedFilters]);

  /*
   * Handle filter checkbox selection
   *
   * @param {array} selected
   * */
  const handleFilterSelection = (selected) => {
    setSelectedFilters(selected);
  };

  /*
   * Handle select all filters
   *
   * @param {HTMLInputElement} event.target
   * */
  const handleSelectAllFilters = ({ target }) => {
    if (target.checked) {
      setSelectedFilters(filterOptions.map((option) => option.value));
    } else {
      setSelectedFilters([]);
    }
  };

  return (
    <div className="mx-8">
      <p>
        Choose one or more filter options below to narrow down the test table
        before creating a test table.
      </p>
      <div className="mt-8 mx-4">
        <div className="mb-4">
          <Checkbox
            indeterminate={
              selectedFilters.length > 0 &&
              selectedFilters.length < filterOptions.length
            }
            checked={filterOptions.length === selectedFilters.length}
            onChange={handleSelectAllFilters}
          >
            Select All
          </Checkbox>
        </div>
        <div>
          <Checkbox.Group
            value={selectedFilters}
            onChange={handleFilterSelection}
          >
            <Flex vertical gap={4}>
              {filterOptions.map((item) => {
                return (
                  <Row key={item.value} className="w-200">
                    {item.value === "select_tests_manually" ? (
                      <>
                        <Col span={8}>
                          <Checkbox
                            key={item.value}
                            disabled={item.disabled}
                            value={item.value}
                          >
                            {item.label}
                          </Checkbox>
                        </Col>
                        <Col span={12}>
                          <TestListSelect
                            className="min-w-64 max-w-128"
                            id={pageKey}
                            mode={"multiple"}
                            disabled={
                              !selectedFilters.some(
                                (item) => item === "select_tests_manually",
                              )
                            }
                            apiParams={pageFilters}
                            onChange={(value) => {
                              setTestTableFilters((prev) => {
                                return {
                                  ...prev,
                                  tnum_show_only: value
                                    .map((testItem) => {
                                      const splitItem = testItem.split("|");
                                      return splitItem[1];
                                    })
                                    .join(),
                                };
                              });
                            }}
                          />
                        </Col>
                      </>
                    ) : (
                      <Col span={8}>
                        <Checkbox
                          key={item.value}
                          disabled={item.disabled}
                          value={item.value}
                        >
                          {item.label}
                        </Checkbox>
                      </Col>
                    )}
                  </Row>
                );
              })}
            </Flex>
          </Checkbox.Group>
        </div>
      </div>
    </div>
  );
}
