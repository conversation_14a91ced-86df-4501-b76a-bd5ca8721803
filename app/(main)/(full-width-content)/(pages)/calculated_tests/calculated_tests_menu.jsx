import { useState } from "react";
import { Dropdown, Space, Tooltip, Typography } from "antd";
import { DownOutlined } from "@ant-design/icons";
import { useQueryClient } from "@tanstack/react-query";
import Helper from "../../../../../src/utils/helper";

/*
 * Calculted Tests Menu component
 *
 * @param {string} pageKey
 * @param {object} analysisInput
 * @returns {JSX.Element}
 * */
export default function CalculatedTestsMenu({ getSearchTableSelection }) {
  const queryClient = useQueryClient();
  const [selectedDlogCount, setSelectedDlogCount] = useState(0);
  const [hasSameMfgProcess, setHasSameMfgProcess] = useState(true);

  /*
   * Handle menu item click
   * */
  const handleMenuItemClick = ({ key }) => {
    const selectedParams = getSearchTableSelection();
    const queryString = Helper.createQueryString({
      template_key: key,
      ...selectedParams,
    });
    Helper.renderPage(
      `#${key}?${queryString}`,
      key,
      `#${key}?${queryString}`,
      queryClient,
    );
  };

  /**
   * Enable/disable menu items based on dlog selection
   */
  const validateMenuItems = () => {
    const selectedParams = getSearchTableSelection();
    setSelectedDlogCount(selectedParams.dsk?.length ?? 0);
    setHasSameMfgProcess(selectedParams.mfg_process?.length === 1);
  };

  return (
    <>
      <Dropdown
        trigger="click"
        menu={{
          onClick: handleMenuItemClick,
          items: [
            {
              key: "create_calculated_test",
              disabled: selectedDlogCount === 0 || !hasSameMfgProcess,
              label:
                selectedDlogCount > 0 && hasSameMfgProcess ? (
                  "Create Calculated Tests"
                ) : (
                  <Tooltip
                    title={
                      <>
                        <p>Please select 1 or more datalogs.</p>
                        <p>
                          You can select up to 1 manufacturing process only.
                        </p>
                      </>
                    }
                  >
                    Create Calculated Tests
                  </Tooltip>
                ),
            },
          ],
        }}
        onClick={validateMenuItems}
      >
        <Typography.Text className="cursor-pointer">
          <Space>
            Calculated Tests
            <DownOutlined />
          </Space>
        </Typography.Text>
      </Dropdown>
    </>
  );
}
