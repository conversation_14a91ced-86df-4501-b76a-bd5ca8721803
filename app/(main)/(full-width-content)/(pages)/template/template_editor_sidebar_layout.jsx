import { theme, ConfigProvider } from "antd";
import { useState } from "react";

const { useToken } = theme;

const defaultData = {
  colorPrimary: "#EF6322",
  colorPrimaryBg: "#154495",
  colorBgTextHover: "#446AAA",
};

const buttonData = {
  colorBgContainerDisabled: "rgba(255, 255, 255, 1)",
};

/**
 * Sidebar layout of template editor
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const TemplateEditorSidebarLayout = ({ children }) => {
  const [data] = useState(defaultData);
  const [btnData] = useState(buttonData);
  const { token } = useToken();

  return (
    <ConfigProvider
      theme={{
        algorithm: [theme.compactAlgorithm],
        token: {
          colorPrimary: data.colorPrimary,
          colorPrimaryBg: data.colorPrimaryBg,
          colorBgTextHover: data.colorBgTextHover,
        },
        components: {
          Button: {
            colorPrimary: token.colorPrimaryHover,
            colorBgContainerDisabled: btnData.colorBgContainerDisabled,
          },
        },
      }}
    >
      {children}
    </ConfigProvider>
  );
};
export default TemplateEditorSidebarLayout;
