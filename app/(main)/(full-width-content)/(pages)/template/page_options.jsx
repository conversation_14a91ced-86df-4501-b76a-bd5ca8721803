"use client";

import { Divider, notification, Popconfirm, Select, Space } from "antd";
import { useEffect, useState } from "react";
import { useEffectApiFetch } from "../../../../../src/hooks";
import { useBoundStore } from "../../../../../src/store/store";
import Api from "../../../../../src/utils/api";
import TemplatesDropdown from "../../../../../src/utils/components/templates_dropdown";

/**
 * Page options component of a template
 *
 * @returns {JSX.Element}
 */
export default function TemplatePageOptions() {
  const [dataLevelOptions, setDataLevelOptions] = useState([]);
  const [templateData, setTemplateData] = useState({});
  const [previousSelectedTemplateId, setPreviousSelectedTemplateId] =
    useState();
  const [openLoadTemplateConfirmation, setOpenLoadTemplateConfirmation] =
    useState(false);
  const templateId = useBoundStore((state) => state.templateId);
  const templateType = useBoundStore((state) => state.templateType);
  const setTemplateId = useBoundStore((state) => state.setTemplateId);
  const setTemplateType = useBoundStore((state) => state.setTemplateType);
  const setTemplateName = useBoundStore((state) => state.setTemplateName);
  const setTemplateRows = useBoundStore((state) => state.setTemplateRows);
  const setPreviousTemplateRows = useBoundStore(
    (state) => state.setPreviousTemplateRows,
  );
  const setTemplateAnalysis = useBoundStore(
    (state) => state.setTemplateAnalysis,
  );
  const selectedTemplateId = useBoundStore((state) => state.selectedTemplateId);
  const setSelectedTemplateId = useBoundStore(
    (state) => state.setSelectedTemplateId,
  );
  const setDisableEditing = useBoundStore((state) => state.setDisableEditing);
  const setDisableDeletion = useBoundStore((state) => state.setDisableDeletion);
  const currentPageData = useBoundStore((state) => state.currentPageData);

  useEffectApiFetch(
    () => {
      return getDataLevels();
    },
    () => {
      setDataLevelOptions([]);
    },
  );

  useEffect(() => {
    setSelectedTemplateId();
  }, [currentPageData.key]);

  useEffect(() => {
    if (
      templateId !== null &&
      templateId !== undefined &&
      templateType !== "preset"
    ) {
      setDisableDeletion(false);
    } else {
      setDisableDeletion(true);
    }
  }, [templateId, templateType]);

  useEffect(() => {
    loadTemplateData(templateData);
  }, [templateData]);

  /**
   * Get data levels to be used for selection
   *
   * @returns {AbortController}
   */
  const getDataLevels = () => {
    return Api.getDataLevels(
      (res) => {
        if (res.success) {
          setDataLevelOptions(
            res.data.map((data) => {
              return {
                value: data.id,
                label: data.display_name,
              };
            }),
          );
        } else {
          notification.warning({
            message: "Data Levels",
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: "Data Levels",
          description: err,
        });
      },
    );
  };

  /**
   * Confirm loading of template
   *
   * @param {int} templateId
   */
  const confirmLoadTemplate = (templateId) => {
    setPreviousSelectedTemplateId(selectedTemplateId);
    setSelectedTemplateId(templateId);
    if (templateId) {
      setOpenLoadTemplateConfirmation(true);
    } else {
      clearTemplate(false);
    }
  };

  /**
   * Load existing template
   *
   * @param {int} templateId
   */
  const loadTemplate = () => {
    setOpenLoadTemplateConfirmation(false);
    getTemplate(selectedTemplateId);
  };

  /**
   * Cancel loading of template
   */
  const cancelLoadTemplate = () => {
    setSelectedTemplateId(previousSelectedTemplateId);
    setOpenLoadTemplateConfirmation(false);
  };

  /**
   * Get template data
   *
   * @param {string} selectedTemplateId
   */
  const getTemplate = (selectedTemplateId) => {
    const templateIdArr = selectedTemplateId.split("_");
    const type = templateIdArr[0];
    const id = templateIdArr[1];
    const getAnalysisTemplate =
      type === "preset"
        ? Api.getPresetAnalysisTemplate
        : Api.getUserAnalysisTemplate;

    setDisableEditing(type === "preset" ? true : false);
    setTemplateType(type);

    getAnalysisTemplate(
      (res) => {
        if (res.success) {
          setTemplateData(res.data);
        } else {
          notification.warning({
            message: "Analysis Template",
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: "Analysis Template",
          description: err,
        });
      },
      {
        templateId: id,
      },
    );
  };

  /**
   * Load template data
   *
   * @param {array} templateData
   */
  const loadTemplateData = (templateData) => {
    if (templateData && templateData.id) {
      setTemplateId(templateData.id);
      setTemplateName(templateData.name);
      const data = JSON.parse(templateData.template_data);
      setTemplateRows(data.template_rows ? data.template_rows : []);
      setPreviousTemplateRows(data.template_rows ? data.template_rows : []);
    } else {
      clearTemplate();
    }
  };

  /**
   * Clear template
   *
   * @param {bool} clearRows
   */
  const clearTemplate = (clearRows = true) => {
    setTemplateId();
    setTemplateName();
    if (clearRows) {
      setTemplateRows([]);
      setPreviousTemplateRows([]);
    }
  };

  return (
    <div>
      <Space split={<Divider type="vertical" />}>
        <Select
          className="w-32"
          showSearch
          allowClear
          placeholder="Select data level"
          optionFilterProp="children"
          onChange={setTemplateAnalysis}
          onSearch={setTemplateAnalysis}
          filterOption={(input, option) =>
            (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
          }
          options={dataLevelOptions}
        />
        <TemplatesDropdown
          selectedTemplateId={selectedTemplateId}
          onChange={confirmLoadTemplate}
          placeholder="Load a template"
        />
      </Space>
      <Popconfirm
        title="Load Template"
        description="Changes to currently loaded template will be lost. Do you want to proceed?"
        open={openLoadTemplateConfirmation}
        onConfirm={loadTemplate}
        onCancel={cancelLoadTemplate}
      ></Popconfirm>
    </div>
  );
}
