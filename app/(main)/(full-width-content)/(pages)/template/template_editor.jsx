import {
  LeftOutlined,
  RightOutlined,
  DeleteOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import {
  Button,
  Form,
  Input,
  Layout,
  Modal,
  notification,
  Popconfirm,
  Space,
  theme,
} from "antd";
import { memo, useEffect, useRef, useState } from "react";
import { useEffectApiFetch } from "../../../../../src/hooks";
import { useBoundStore } from "../../../../../src/store/store";
import Api from "../../../../../src/utils/api";
import Helper from "../../../../../src/utils/helper";
import ModalLayout from "../../../modal_layout";
import Components from "./component_list/components";
import { TemplateContent } from "./template_content/template_content";
import TemplateEditorSidebarLayout from "./template_editor_sidebar_layout";

const { Sider, Content } = Layout;
const { useToken } = theme;

/**
 * Template editor
 *
 * @returns {JSX.Element}
 */
const TemplateEditor = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [openSaveTemplateForm, setOpenSaveTemplateForm] = useState(false);
  const [disableSaveBtn, setDisableSaveBtn] = useState(true);
  const templateId = useBoundStore((state) => state.templateId);
  const templateType = useBoundStore((state) => state.templateType);
  const templateName = useBoundStore((state) => state.templateName);
  const templateRows = useBoundStore((state) => state.templateRows);
  const previousTemplateRows = useBoundStore(
    (state) => state.previousTemplateRows,
  );
  const userAnalysisTemplates = useBoundStore(
    (state) => state.userAnalysisTemplates,
  );
  const disableEditing = useBoundStore((state) => state.disableEditing);
  const disableDeletion = useBoundStore((state) => state.disableDeletion);
  const rerenderTemplateDropdown = useBoundStore(
    (state) => state.rerenderTemplateDropdown,
  );
  const setTemplateId = useBoundStore((state) => state.setTemplateId);
  const setTemplateType = useBoundStore((state) => state.setTemplateType);
  const setTemplateName = useBoundStore((state) => state.setTemplateName);
  const setTemplateRows = useBoundStore((state) => state.setTemplateRows);
  const setPreviousTemplateRows = useBoundStore(
    (state) => state.setPreviousTemplateRows,
  );
  const setSelectedTemplateId = useBoundStore(
    (state) => state.setSelectedTemplateId,
  );
  const setRerenderTemplateDropdown = useBoundStore(
    (state) => state.setRerenderTemplateDropdown,
  );
  const templateEditorSidebarRef = useRef();
  const { token } = useToken();
  const [form] = Form.useForm();

  useEffect(() => {
    setDisableSaveBtn(!templateRows.length);
    if (
      templateType !== "preset" &&
      templateRows.length &&
      JSON.stringify(templateRows) !== JSON.stringify(previousTemplateRows)
    ) {
      Helper.draftUserAnalysisTemplate(templateId, templateRows);
    }
    setPreviousTemplateRows(templateRows);
  }, [templateRows]);

  useEffect(() => {
    form.setFieldValue("name", templateName);
  }, [templateName]);

  useEffectApiFetch(
    () => {
      return getDraftUserAnalysisTemplate();
    },
    () => {
      clearTemplate();
    },
    [userAnalysisTemplates],
  );

  /**
   * Get draft user analysis template
   *
   * @returns {AbortController}
   */
  const getDraftUserAnalysisTemplate = () => {
    return Api.getDraftUserAnalysisTemplate(
      (res) => {
        if (res.success && Object.keys(res.data).length) {
          const templateType = "user";
          const selectedTemplateId =
            res.data.template_id !== null
              ? `${templateType}_${res.data.template_id}`
              : res.data.template_id;
          setTemplateType(templateType);
          setSelectedTemplateId(selectedTemplateId);
          setTemplateId(res.data.template_id);
          setTemplateName(getTemplateName(res.data.template_id, res.data.name));
          const data = JSON.parse(res.data.template_data);
          setTemplateRows(data.template_rows ? data.template_rows : []);
          setPreviousTemplateRows(data.template_rows ? data.template_rows : []);
        }
      },
      (err) => {
        notification.error({
          message: "Draft User Analysis Template",
          description: err,
        });
      },
    );
  };

  /**
   * Get template name
   *
   * @param {int|null} templateId
   * @param {string} defaultTemplateName
   * @returns {string} templateName
   */
  const getTemplateName = (templateId, defaultTemplateName) => {
    let templateName = defaultTemplateName;
    if (templateId !== null) {
      userAnalysisTemplates.forEach((analysisTemplate) => {
        if (analysisTemplate.id === templateId) {
          templateName = analysisTemplate.name;
        }
      });
    }

    return templateName;
  };

  const SaveTemplateForm = ({ form, open, onSubmit, onCancel }) => {
    return (
      <Modal
        forceRender
        open={open}
        title="Save Template"
        okText="Submit"
        cancelText="Cancel"
        onCancel={onCancel}
        onOk={() => {
          form
            .validateFields()
            .then((values) => {
              form.resetFields();
              onSubmit(values);
            })
            .catch((info) => {
              console.log("Validate Failed:", info);
            });
        }}
      >
        <Form form={form} layout="vertical" name="save_template_form">
          <Form.Item
            name="name"
            label="Name"
            initialValue={templateName}
            rules={[
              {
                required: true,
                message: "Please input the name of the template.",
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    );
  };

  /**
   * Set sidebar collapsed state
   *
   * @param {boolean} collapsed
   */
  const onCollapse = (collapsed) => {
    setCollapsed(collapsed);
  };

  /**
   * Handler when clicking save button on save template form
   *
   * @param {object} values
   */
  const onSubmit = (values) => {
    saveTemplate(values);
    setOpenSaveTemplateForm(false);
  };

  /**
   * Save/update analysis template
   *
   * @param {object} values
   */
  const saveTemplate = (values) => {
    if (templateId) {
      updateUserAnalysisTemplate(values, templateId);
    } else {
      saveUserAnalysisTemplate(values);
    }
  };

  /**
   * Save analysis template
   *
   * @param {object} values
   */
  const saveUserAnalysisTemplate = (values) => {
    const analysisTemplateName = values.name;
    const templateData = {
      template_rows: templateRows,
    };
    Api.saveUserAnalysisTemplate(
      (res) => {
        if (res.success) {
          const type = "user";
          setTemplateId(res.data.id);
          setTemplateName(res.data.name);
          const data = JSON.parse(res.data.template_data);
          setTemplateRows(data.template_rows ? data.template_rows : []);
          setSelectedTemplateId(`${type}_${res.data.id}`);
          setRerenderTemplateDropdown(!rerenderTemplateDropdown);

          notification.info({
            message: "Save Template",
            description: res.message,
          });
        } else {
          notification.warning({
            message: "Save Template",
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: "Save Template",
          description: err,
        });
      },
      {
        name: analysisTemplateName,
        template_data: templateData,
      },
    );
  };

  /**
   * Update analysis template
   *
   * @param {object} values
   * @param {int} templateId
   */
  const updateUserAnalysisTemplate = (values, analysisTemplateId) => {
    const analysisTemplateName = values.name;
    const templateData = {
      template_rows: templateRows,
    };
    Api.updateUserAnalysisTemplate(
      (res) => {
        if (res.success) {
          setTemplateId(res.data.id);
          setTemplateName(res.data.name);

          notification.info({
            message: "Update Template",
            description: res.message,
          });
        } else {
          notification.warning({
            message: "Update Template",
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: "Update Template",
          description: err,
        });
      },
      {
        id: analysisTemplateId,
        name: analysisTemplateName,
        template_data: templateData,
      },
    );
  };

  /**
   * Delete analysis template
   */
  const deleteUserAnalysisTemplate = () => {
    Api.deleteUserAnalysisTemplate(
      (res) => {
        if (res.success) {
          clearTemplate();
          setRerenderTemplateDropdown(!rerenderTemplateDropdown);

          notification.info({
            message: "Delete Template",
            description: res.message,
          });
        } else {
          notification.warning({
            message: "Delete Template",
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: "Delete Template",
          description: err,
        });
      },
      {
        id: templateId,
      },
    );
  };

  /**
   * Clear template
   *
   * @param {bool} clearRows
   */
  const clearTemplate = () => {
    setTemplateId();
    setTemplateName();
    setTemplateRows([]);
    setSelectedTemplateId();
  };

  return (
    <Layout className="h-full">
      <Content>
        <TemplateContent
          templateRows={templateRows}
          setTemplateRows={setTemplateRows}
        />
      </Content>
      <Sider
        ref={templateEditorSidebarRef}
        style={{ background: token.colorPrimaryBg }}
        collapsible
        defaultCollapsed={collapsed}
        collapsedWidth={0}
        reverseArrow={true}
        trigger={collapsed ? <LeftOutlined /> : <RightOutlined />}
        zeroWidthTriggerStyle={{
          bottom: 50,
          top: "auto",
          background: token.colorPrimaryBg,
        }}
        onCollapse={onCollapse}
      >
        <TemplateEditorSidebarLayout>
          <div className="flex flex-col h-full p-2">
            <Components />
            <Space className="p-4 pr-2 justify-end w-full">
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={setOpenSaveTemplateForm}
                disabled={disableSaveBtn || disableEditing}
              >
                Save
              </Button>
              <Popconfirm
                title="Delete template"
                description="Are you sure to delete this template?"
                placement="topRight"
                onConfirm={deleteUserAnalysisTemplate}
                okText="Yes"
                cancelText="No"
                disabled={disableDeletion}
              >
                <Button
                  type="primary"
                  icon={<DeleteOutlined />}
                  danger
                  disabled={disableDeletion}
                >
                  Delete
                </Button>
              </Popconfirm>
              <ModalLayout>
                <SaveTemplateForm
                  form={form}
                  open={openSaveTemplateForm}
                  onSubmit={onSubmit}
                  onCancel={() => {
                    setOpenSaveTemplateForm(false);
                  }}
                />
              </ModalLayout>
            </Space>
          </div>
        </TemplateEditorSidebarLayout>
      </Sider>
    </Layout>
  );
};

export default memo(TemplateEditor);
