"use client";

import "./override.css";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import PagesLayout from "../layout";
import TemplateEditor from "./template_editor";

/**
 * Page template wrapper
 *
 * @returns {JSX.Element}
 */
export default function AnalysisTemplate() {
  return (
    <PagesLayout>
      <DndProvider backend={HTML5Backend}>
        <TemplateEditor />
      </DndProvider>
    </PagesLayout>
  );
}
