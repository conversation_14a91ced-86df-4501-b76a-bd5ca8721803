.main-layout .content {
  height: 100%;
  overflow-y: auto;
}

/**
*
*Override ant-collapse (non-existent) Component Token
*
**/
.ant-collapse
  > .ant-collapse-item
  > .ant-collapse-header
  .ant-collapse-expand-icon {
  padding-inline-end: 14px;
}
.ant-collapse
  > .ant-collapse-item
  > .ant-collapse-content
  > .ant-collapse-content-box {
  padding: 4px !important;
}

/**
*
*Override ant-tabs (non-existent) Component Token
*
**/
.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab {
  margin-right: 32px;
  border: none;
  border-bottom: 1px solid #f0f0f0;
}
.ant-tabs-tab-active {
  background: #f9faff !important;
}
.ant-tabs-card > .ant-tabs-nav .ant-tabs-ink-bar,
.ant-tabs-nav .ant-tabs-ink-bar {
  visibility: visible;
}
