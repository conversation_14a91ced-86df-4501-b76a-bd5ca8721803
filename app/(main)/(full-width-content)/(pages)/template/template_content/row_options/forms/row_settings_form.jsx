import { Form } from "antd";
import { useBoundStore } from "../../../../../../../../src/store/store";
import FormHelper from "../../../../../../../../src/utils/form_helper";
import Helper from "../../../../../../../../src/utils/helper";

/**
 * Form to update row settings
 *
 * @param {FormInstance} form
 * @param {object} fields
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {string} colRowColKey
 * @param {string} colRowColRowKey
 * @param {string} colRowColRowColKey
 * @param {string} colRowColRowColRowKey
 * @param {string} colRowColRowColRowColKey
 * @param {string} tabKey
 * @param {string} subTabKey
 * @returns {JSX.Element}
 */
const RowSettingsForm = ({
  form,
  fields,
  rowKey,
  colKey,
  colRowKey,
  colRowCol<PERSON>ey,
  colRowColRowKey,
  colRowColRowColKey,
  colRowColRowColRowKey,
  colRowColRowColRowColKey,
  tabKey,
  subTabKey,
}) => {
  const templateRows = useBoundStore((state) => state.templateRows);
  const rowData = Helper.getTemplateRowByIndex(
    templateRows,
    rowKey,
    colKey,
    colRowKey,
    colRowColKey,
    colRowColRowKey,
    colRowColRowColKey,
    colRowColRowColRowKey,
    colRowColRowColRowColKey,
    tabKey,
    subTabKey,
  );

  return (
    <div>
      <Form form={form} size="small" labelWrap>
        {Object.keys(fields).reduce((result, key) => {
          if (fields[key].options) {
            const formItems = [
              <Form.Item
                key={key}
                className="font-bold"
                label={fields[key].label}
                colon={false}
                labelCol={{
                  span: 6,
                }}
              ></Form.Item>,
            ];
            formItems.push(
              Object.keys(fields[key].options).map((optionKey) => {
                const fieldValue =
                  rowData[key] && rowData[key][optionKey]
                    ? rowData[key][optionKey]
                    : "";
                return FormHelper.getFormItem(
                  fields[key].options[optionKey],
                  optionKey,
                  fieldValue,
                  [key],
                );
              }),
            );
            result.push(formItems);
          } else {
            result.push(FormHelper.getFormItem(fields[key], key, rowData[key]));
          }
          return result;
        }, [])}
      </Form>
    </div>
  );
};

export default RowSettingsForm;
