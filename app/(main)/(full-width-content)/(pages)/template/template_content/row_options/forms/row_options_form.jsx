import { Form } from "antd";
import { useBoundStore } from "../../../../../../../../src/store/store";
import FormHelper from "../../../../../../../../src/utils/form_helper";

/**
 * Form to set row options
 *
 * @param {FormInstance} form
 * @param {object} fields
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {string} colRowColKey
 * @param {string} colRowColRowKey
 * @param {string} colRowColRowColKey
 * @param {string} colRowColRowColRowKey
 * @param {string} tabKey
 * @returns {JSX.Element}
 */
const RowOptionsForm = ({
  form,
  fields,
  rowKey,
  colKey,
  colRowKey,
  colRowColKey,
  colRowColRowKey,
  colRowColRowColKey,
  colRowColRowColRowKey,
  tabKey,
}) => {
  const templateRows = useBoundStore((state) => state.templateRows);
  const options =
    tabKey !== undefined && colRowColRowColRowKey !== undefined
      ? templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
          colRowColKey
        ]["tabs"][tabKey]["rows"][colRowColRowKey]["columns"][
          colRowColRowColKey
        ]["rows"][colRowColRowColRowKey].options
      : tabKey !== undefined && colRowColRowKey !== undefined
        ? templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
            colRowColKey
          ]["tabs"][tabKey]["rows"][colRowColRowKey].options
        : colRowColRowKey !== undefined
          ? templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
              "columns"
            ][colRowColKey]["rows"][colRowColRowKey].options
          : colRowKey !== undefined
            ? templateRows[rowKey]["columns"][colKey]["rows"][colRowKey].options
            : templateRows[rowKey].options;

  return (
    <Form form={form} size="small">
      {Object.keys(fields).map((key) => {
        const value =
          options !== undefined
            ? options.filter((option) => {
                return option.key === key;
              }).length > 0
            : false;

        return FormHelper.getFormItem(fields[key], key, value);
      })}
    </Form>
  );
};

export default RowOptionsForm;
