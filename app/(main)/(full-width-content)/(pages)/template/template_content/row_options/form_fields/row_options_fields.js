import TopFailingTestSelectionForm from "../../../../../../../../src/utils/charts/components/top_failing_test_selection_form";
import AllTestsFilter from "../../../../../../../../src/utils/components/all_tests_filter";
import GroupingSelection from "../../../../../../../../src/utils/components/grouping_selection";
import LotAnalysisFilter from "../../../../../../../../src/utils/components/lot_analysis_filter";
import OEEFilter from "../../../../../../../../src/utils/components/oee_filter";
import SelectedTestAnalysisFilter from "../../../../../../../../src/utils/components/selected_test_analysis_filter";
import WaferMapGalleryOptions from "../../../../../../../../src/utils/components/wafer_map_gallery_options";
import YieldTrendLotCountForm from "../../../../../../../../src/utils/forms/yield_trend_lot_count_form";
import MoreWaferChartsOptions from "../../../../../../../../src/utils/charts/components/binning_more_wafer_charts_selection_form";
import BinningParetoChartsGroupFilter from "../../../../../../../../src/utils/charts/components/binning_pareto_charts_group_filter";
import PerGroupWaferChartOptions from "../../../../../../../../src/utils/charts/components/per_group_wafer_chart_options";
import SelectedTestTrendAnalysisFilter from "../../../../../../../../src/utils/components/selected_test_trend_analysis_filter";
import TrendChartsOptions from "../../../../../../../../src/utils/charts/components/trend_charts_options";
import MPRMapsOptions from "../../../../../../../../src/utils/charts/components/mpr_maps_options";
import ScatterChartOptions from "../../../../../../../../src/utils/charts/components/scatter_chart_options";
import PerTestDescription from "../../../../../../../../app/(main)/(full-width-content)/(pages)/npi/per_test_description";
import NpiReportOptions from "../../../../npi/npi_report_options";

/**
 * Form fields for row options
 */
export const RowOptionsFields = {
  topFailingTestSelection: {
    type: "checkbox",
    inputType: "form",
    label: "Top Failing Test Selection",
    component: TopFailingTestSelectionForm,
  },
  yieldTrendLotCount: {
    type: "checkbox",
    inputType: "form",
    label: "Yield Trend Lot Count",
    component: YieldTrendLotCountForm,
  },
  lotAnalysisFilter: {
    type: "checkbox",
    inputType: "form",
    label: "Lot Analysis Filter",
    component: LotAnalysisFilter,
  },
  allTestsFilter: {
    type: "checkbox",
    inputType: "form",
    label: "All Tests Filter",
    component: AllTestsFilter,
  },
  selectedTestAnalysisFilter: {
    type: "checkbox",
    inputType: "form",
    label: "Selected Test Analysis Filter",
    component: SelectedTestAnalysisFilter,
  },
  oeeFilter: {
    type: "checkbox",
    inputType: "form",
    label: "OEE Filter",
    component: OEEFilter,
  },
  waferMapGalleryOptions: {
    type: "checkbox",
    inputType: "form",
    label: "Wafer Map Gallery Options",
    component: WaferMapGalleryOptions,
  },
  zonalTypeToggle: {
    type: "checkbox",
    inputType: "radioButton",
    label: "Zonal Type Toggle",
    defaultValue: "percent_zonal",
    onChange: "toggleZonalType",
    position: "center",
    options: [
      {
        label: "Percent Zonal",
        value: "percent_zonal",
      },
      {
        label: "Number Zonal",
        value: "number_zonal",
      },
    ],
  },
  moreWaferChartsSelection: {
    type: "checkbox",
    inputType: "form",
    label: "wafer Chart Selection",
    component: MoreWaferChartsOptions,
  },
  binningParetoChartsGroupFilter: {
    type: "checkbox",
    inputType: "form",
    label: "Bin Distribution Charts Group Filter",
    component: BinningParetoChartsGroupFilter,
  },
  groupingSelection: {
    type: "checkbox",
    inputType: "form",
    label: "Grouping Selection",
    component: GroupingSelection,
  },
  perGroupWaferChartOptions: {
    type: "checkbox",
    inputType: "form",
    label: "More Wafer Chart Options",
    component: PerGroupWaferChartOptions,
  },
  MPRMapsOptions: {
    type: "checkbox",
    inputType: "form",
    component: MPRMapsOptions,
  },
  scatterChartOptions: {
    type: "checkbox",
    inputType: "form",
    label: "Scatter Chart Options",
    component: ScatterChartOptions,
  },
  npiReportOptions: {
    type: "checkbox",
    inputType: "form",
    label: "NPI Report Options",
    component: NpiReportOptions,
  },
  perTestDescription: {
    type: "checkbox",
    inputType: "form",
    label: "Per Test Description",
    component: PerTestDescription,
  },
  selectedTestTrendAnalysisFilter: {
    type: "checkbox",
    inputType: "form",
    label: "Selected Test Analysis Filter",
    component: SelectedTestTrendAnalysisFilter,
  },
  trendChartsOptions: {
    type: "checkbox",
    inputType: "form",
    label: "Trend Charts Options",
    component: TrendChartsOptions,
  },
};
