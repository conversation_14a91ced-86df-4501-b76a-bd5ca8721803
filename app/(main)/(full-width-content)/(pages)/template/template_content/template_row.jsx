import {
  CaretUpOutlined,
  CaretDownOutlined,
  SettingOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { <PERSON><PERSON>, Popconfirm, Row, Space } from "antd";
import React, { useState } from "react";
import Helper from "../../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../../src/store/store";
import { RowColumn } from "./row_column";
import styles from "./styles.module.css";
import RowOptions from "./row_options";

/**
 * Row in a template
 *
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {string} colRowColKey
 * @param {string} colRowColRowKey
 * @param {string} colRowColRowColKey
 * @param {string} colRowColRowColRowKey
 * @param {string} colRowColRowColRowColKey
 * @param {array} columns
 * @param {array} templateRows
 * @param {function} setTemplateRows
 * @param {string} tabKey
 * @returns {JSX.Element}
 */
export const TemplateRow = ({
  rowKey,
  colKey,
  colRowKey,
  colRowColKey,
  colRowColRowKey,
  colRowColRowColKey,
  colRowColRowColRowKey,
  colRowColRowColRowColKey,
  columns,
  templateRows,
  setTemplateRows,
  tabKey,
  subTabKey,
}) => {
  const [isRowSettingsOpen, setIsRowSettingsOpen] = useState(false);
  const disableEditing = useBoundStore((state) => state.disableEditing);

  /**
   * Generate row option button
   *
   * @param {object} props
   * @returns {React.DetailedReactHTMLElement}
   */
  const createRowOption = (props) => {
    return React.createElement(Button, {
      type: "primary",
      shape: "circle",
      ...props,
    });
  };

  /**
   * Move row up/down
   *
   * @param {int} offset
   */
  const moveRow = (offset) => {
    let rows = Helper.getTemplateRowsByIndex(
      templateRows,
      rowKey,
      colKey,
      colRowKey,
      colRowColKey,
      colRowColRowKey,
      colRowColRowColKey,
      colRowColRowColRowKey,
      colRowColRowColRowColKey,
      tabKey,
      subTabKey,
    );
    const rowsKey = Helper.getTemplateRowsKey(
      rowKey,
      colRowKey,
      colRowColRowKey,
      colRowColRowColRowKey,
    );
    if (rows[rowsKey + offset]) {
      const templateRowsCopy = Helper.cloneObject(templateRows);
      rows = Helper.getTemplateRowsByIndex(
        templateRowsCopy,
        rowKey,
        colKey,
        colRowKey,
        colRowColKey,
        colRowColRowKey,
        colRowColRowColKey,
        colRowColRowColRowKey,
        colRowColRowColRowColKey,
        tabKey,
        subTabKey,
      );
      const toRow = rows[rowsKey + offset];
      rows[rowsKey + offset] = rows[rowsKey];
      rows[rowsKey] = toRow;
      setTemplateRows(templateRowsCopy);
    }
  };

  /**
   * Remove row from template
   */
  const deleteRow = () => {
    const templateRowsCopy = Helper.cloneObject(templateRows);
    let rows = Helper.getTemplateRowsByIndex(
      templateRowsCopy,
      rowKey,
      colKey,
      colRowKey,
      colRowColKey,
      colRowColRowKey,
      colRowColRowColKey,
      colRowColRowColRowKey,
      colRowColRowColRowColKey,
      tabKey,
      subTabKey,
    );
    const rowsKey = Helper.getTemplateRowsKey(
      rowKey,
      colRowKey,
      colRowColRowKey,
      colRowColRowColRowKey,
    );
    rows.splice(rowsKey, 1);
    setTemplateRows(templateRowsCopy);
  };

  return (
    <div className="overflow-x-hidden group">
      <RowOptions
        isRowSettingsOpen={isRowSettingsOpen}
        setIsRowSettingsOpen={setIsRowSettingsOpen}
        rowKey={rowKey}
        colKey={colKey}
        colRowKey={colRowKey}
        colRowColKey={colRowColKey}
        colRowColRowKey={colRowColRowKey}
        colRowColRowColKey={colRowColRowColKey}
        colRowColRowColRowKey={colRowColRowColRowKey}
        tabKey={tabKey}
        subTabKey={subTabKey}
      />
      <div className="text-center z-10 invisible group-hover:visible">
        <Space>
          {createRowOption({
            icon: <CaretUpOutlined />,
            onClick: () => {
              moveRow(-1);
            },
            disabled: disableEditing ? true : false,
          })}
          {createRowOption({
            icon: <CaretDownOutlined />,
            onClick: () => {
              moveRow(1);
            },
            disabled: disableEditing ? true : false,
          })}
          {createRowOption({
            icon: <SettingOutlined />,
            onClick: () => {
              setIsRowSettingsOpen(true);
            },
          })}
          <Popconfirm
            title="Delete the row"
            description="Are you sure to delete this row?"
            onConfirm={deleteRow}
            okText="Yes"
            cancelText="No"
            disabled={disableEditing ? true : false}
          >
            {createRowOption({
              icon: <DeleteOutlined />,
              danger: true,
              disabled: disableEditing ? true : false,
            })}
          </Popconfirm>
        </Space>
      </div>
      <Row className={`${styles.rowLayout} h-full mt-2 mb-4`} gutter={[8, 8]}>
        {columns.map((column, key) => {
          return colRowColRowColRowKey !== undefined ? (
            <RowColumn
              key={key}
              rowKey={rowKey}
              colKey={colKey}
              colRowKey={colRowKey}
              colRowColKey={colRowColKey}
              colRowColRowKey={colRowColRowKey}
              colRowColRowColKey={colRowColRowColKey}
              colRowColRowColRowKey={colRowColRowColRowKey}
              colRowColRowColRowColKey={key}
              colspan={column.colspan}
              templateRows={templateRows}
              setTemplateRows={setTemplateRows}
              tabKey={tabKey}
              subTabKey={subTabKey}
            />
          ) : colRowColRowKey !== undefined ? (
            <RowColumn
              key={key}
              rowKey={rowKey}
              colKey={colKey}
              colRowKey={colRowKey}
              colRowColKey={colRowColKey}
              colRowColRowKey={colRowColRowKey}
              colRowColRowColKey={key}
              colRowColRowColRowKey={colRowColRowColRowKey}
              colspan={column.colspan}
              templateRows={templateRows}
              setTemplateRows={setTemplateRows}
              tabKey={tabKey}
              subTabKey={subTabKey}
            />
          ) : colRowKey !== undefined ? (
            <RowColumn
              key={key}
              rowKey={rowKey}
              colKey={colKey}
              colRowKey={colRowKey}
              colRowColKey={key}
              colRowColRowKey={colRowColRowKey}
              colRowColRowColKey={colRowColRowColKey}
              colRowColRowColRowKey={colRowColRowColRowKey}
              colspan={column.colspan}
              templateRows={templateRows}
              setTemplateRows={setTemplateRows}
              tabKey={tabKey}
              subTabKey={subTabKey}
            />
          ) : (
            <RowColumn
              key={key}
              rowKey={rowKey}
              colKey={key}
              colRowKey={colRowKey}
              colRowColKey={colRowColKey}
              colRowColRowKey={colRowColRowKey}
              colRowColRowColKey={colRowColRowColKey}
              colRowColRowColRowKey={colRowColRowColRowKey}
              colspan={column.colspan}
              templateRows={templateRows}
              setTemplateRows={setTemplateRows}
              tabKey={tabKey}
              subTabKey={subTabKey}
            />
          );
        })}
      </Row>
    </div>
  );
};
