import { <PERSON><PERSON>, <PERSON>, Row, Typography } from "antd";
import React, { memo, useEffect } from "react";
import Helper from "../../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../../src/store/store";
import styles from "./styles.module.css";

const { Title } = Typography;

/**
 * Layout options to be used when selecting row layout to be added in template
 *
 * @param {object}
 * @returns {JSX.Element}
 */
const LayoutOptions = ({
  templateRows,
  setTemplateRows,
  templateContentBottomRef,
}) => {
  const previousTemplateRows = useBoundStore(
    (state) => state.previousTemplateRows,
  );
  const setPreviousTemplateRows = useBoundStore(
    (state) => state.setPreviousTemplateRows,
  );

  useEffect(() => {
    if (templateRows.length > previousTemplateRows.length) {
      templateContentBottomRef.current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
    setPreviousTemplateRows(templateRows);
  }, [templateRows]);

  /**
   * Generate row layout option
   *
   * @param {array} colspans
   * @returns {React.DetailedReactHTMLElement}
   */
  const createRowLayoutOption = (colspans) => {
    return React.createElement(
      Button,
      {
        className: "border-0",
        onClick: () => {
          Helper.addTemplateRow(
            {
              colspans: colspans,
            },
            templateRows,
            setTemplateRows,
          );
        },
        block: true,
      },
      createRowLayout(colspans),
    );
  };

  /**
   * Generate row layout
   *
   * @param {array} colspans
   * @returns {JSX.Element}
   */
  const createRowLayout = (colspans) => {
    return (
      <Row className={styles.layoutOption} gutter={4}>
        {colspans.map((colspan, key) => {
          return (
            <Col key={key} span={colspan}>
              <div className="w-full h-full bg-green-600 ">&nbsp;</div>
            </Col>
          );
        })}
      </Row>
    );
  };

  return (
    <div className={styles.layoutOptionsWrapper}>
      <Title level={5}>Add Row</Title>
      <Row gutter={8}>
        <Col span={8}>{createRowLayoutOption([24])}</Col>
        <Col span={8}>{createRowLayoutOption([12, 12])}</Col>
        <Col span={8}>{createRowLayoutOption([8, 8, 8])}</Col>
      </Row>
      <Row gutter={8}>
        <Col span={8}>{createRowLayoutOption([6, 6, 6, 6])}</Col>
        <Col span={8}>{createRowLayoutOption([8, 16])}</Col>
        <Col span={8}>{createRowLayoutOption([16, 8])}</Col>
      </Row>
    </div>
  );
};

export default memo(LayoutOptions);
