.templateContent {
  height: 100%;
  border: 1px dashed;
  position: relative;
  overflow-y: auto;
}

.templateComponent {
  display: inline-block;
  border: 1px dashed gray;
  cursor: move;
  padding: 12px;
  text-align: center;
}

.layoutOptionsWrapper {
  width: 300px;
  padding: 12px;
  background: white;
}

.layoutOption {
  opacity: 0.7;
}

.layoutOption:hover {
  opacity: 1;
}

.rowLayout {
  min-height: 300px;
}

.columnLayout {
  min-height: 300px;
}
