import {
  PlusOutlined,
  ClearOutlined,
  FontSizeOutlined,
} from "@ant-design/icons";
import { Button, Popconfirm, Popover, Space, theme } from "antd";
import React, { useRef, useState } from "react";
import Helper from "../../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../../src/store/store";
import LayoutOptions from "./layout_options";
import TextOptions from "./text_options";
import { TemplateRow } from "./template_row";
import styles from "./styles.module.css";

const { useToken } = theme;

/**
 * Template content component
 *
 * @param {array} templateRows
 * @param {function} setTemplateRows
 * @returns {JSX.Element}
 */
export const TemplateContent = ({ templateRows, setTemplateRows }) => {
  const [isTextOptionsOpen, setIsTextOptionsOpen] = useState(false);
  const disableEditing = useBoundStore((state) => state.disableEditing);
  const { token } = useToken();
  const templateContentBottomRef = useRef();

  /**
   * Generate template option button
   *
   * @param {object} props
   * @returns {React.DetailedReactHTMLElement}
   */
  const createTemplateOption = (props) => {
    return React.createElement(Button, {
      type: "primary",
      shape: "circle",
      ...props,
    });
  };

  /**
   * Get layout options
   *
   * @returns {JSX.Element}
   */
  const layoutOptions = () => {
    return (
      <LayoutOptions
        templateRows={templateRows}
        setTemplateRows={setTemplateRows}
        templateContentBottomRef={templateContentBottomRef}
      />
    );
  };

  /**
   * Remove existing template rows
   */
  const clearTemplateRows = () => {
    setTemplateRows([]);
  };

  return (
    <div
      className={styles.templateContent}
      style={{ backgroundColor: token.colorBgContainer }}
      data-testid="content"
    >
      <div className="sticky top-0 text-right p-1 pr-2 z-20">
        {!disableEditing ? (
          <Space>
            <TextOptions
              templateRows={templateRows}
              setTemplateRows={setTemplateRows}
              templateContentBottomRef={templateContentBottomRef}
              isTextOptionsOpen={isTextOptionsOpen}
              setIsTextOptionsOpen={setIsTextOptionsOpen}
            />
            <Popover
              placement="bottomRight"
              content={layoutOptions}
              trigger="click"
            >
              <Button type="primary" shape="circle" icon={<PlusOutlined />} />
            </Popover>

            {createTemplateOption({
              icon: <FontSizeOutlined />,
              onClick: () => {
                setIsTextOptionsOpen(true);
              },
              disabled: disableEditing ? true : false,
            })}

            <Popconfirm
              title="Clear template"
              description="Are you sure to clear this template?"
              placement="topRight"
              onConfirm={clearTemplateRows}
              okText="Yes"
              cancelText="No"
            >
              <Button
                type="primary"
                shape="circle"
                icon={<ClearOutlined />}
                danger
              />
            </Popconfirm>
          </Space>
        ) : (
          <Space>
            <Button
              type="primary"
              shape="circle"
              icon={<PlusOutlined />}
              disabled
            />
            <Button
              type="primary"
              shape="circle"
              icon={<ClearOutlined />}
              danger
              disabled
            />
          </Space>
        )}
      </div>

      {templateRows &&
        templateRows.map((templateRow, index) => {
          // copy template row to avoid mutation to original row layout
          const templateRowCopy = Helper.cloneObject(templateRow);
          const { columns } = templateRowCopy;
          return (
            <TemplateRow
              key={index}
              rowKey={index}
              templateRow={templateRow}
              columns={columns}
              templateRows={templateRows}
              setTemplateRows={setTemplateRows}
            />
          );
        })}

      <div ref={templateContentBottomRef}></div>
    </div>
  );
};
