import { <PERSON><PERSON>, <PERSON>, <PERSON>, Typo<PERSON> } from "antd";
import React, { memo } from "react";
import Helper from "../../../../../../src/utils/helper";
import styles from "./styles.module.css";

const { Title } = Typography;

/**
 * Layout options to be used when selecting row layout to be added in collapsible component
 *
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {string} colRowColKey
 * @param {string} colRowColRowKey
 * @param {string} colRowColRowColKey
 * @param {array} templateRows
 * @param {function} setTemplateRows
 * @param {string} tabKey
 * @param {string} subTabKey
 * @returns {JSX.Element}
 */
const ColumnLayoutOptions = ({
  rowKey,
  colKey,
  colRowKey,
  colRowColKey,
  colRowColRowKey,
  colRowColRowColKey,
  templateRows,
  setTemplateR<PERSON>,
  tab<PERSON>ey,
  subTab<PERSON>ey,
}) => {
  /**
   * Generate row layout option
   *
   * @param {array} colspans
   * @returns {React.DetailedReactHTMLElement}
   */
  const createRowLayoutOption = (colspans) => {
    return React.createElement(
      Button,
      {
        className: "border-0",
        onClick: () => {
          Helper.addColumnRow(
            rowKey,
            colKey,
            colRowKey,
            colRowColKey,
            colRowColRowKey,
            colRowColRowColKey,
            {
              colspans: colspans,
            },
            templateRows,
            setTemplateRows,
            tabKey,
            subTabKey,
          );
        },
        block: true,
      },
      createRowLayout(colspans),
    );
  };

  /**
   * Generate row layout
   *
   * @param {array} colspans
   * @returns {JSX.Element}
   */
  const createRowLayout = (colspans) => {
    return (
      <Row className={styles.layoutOption} gutter={4}>
        {colspans.map((colspan, key) => {
          return (
            <Col key={key} span={colspan}>
              <div className="w-full h-full bg-green-600 ">&nbsp;</div>
            </Col>
          );
        })}
      </Row>
    );
  };

  return (
    <div className={styles.layoutOptionsWrapper}>
      <Title level={5}>Add Row</Title>
      <Row gutter={8}>
        <Col span={8}>{createRowLayoutOption([24])}</Col>
        <Col span={8}>{createRowLayoutOption([12, 12])}</Col>
        <Col span={8}>{createRowLayoutOption([8, 8, 8])}</Col>
      </Row>
      <Row gutter={8}>
        <Col span={8}>{createRowLayoutOption([6, 6, 6, 6])}</Col>
        <Col span={8}>{createRowLayoutOption([8, 16])}</Col>
        <Col span={8}>{createRowLayoutOption([16, 8])}</Col>
      </Row>
    </div>
  );
};

export default memo(ColumnLayoutOptions);
