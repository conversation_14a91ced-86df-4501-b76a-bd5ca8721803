import { Form, Input, Radio, Space, Typography } from "antd";
import React, { memo, useState } from "react";
import Image from "next/image";
import styles from "./styles.module.css";

const { Title } = Typography;

/**
 * Layout options to be used when selecting row layout to be added in collapsible component
 *
 * @param {object} form
 * @param {string} containerType
 * @param {string} containerTitle
 * @returns {JSX.Element}
 */
const ColumnContainerOptions = ({ form, containerType, containerTitle }) => {
  const [type, setType] = useState(containerType);

  /**
   * Triggers when selecting container type option
   *
   * @param {Event} e
   */
  const onChange = (e) => {
    setType(e.target.value);
  };

  return (
    <div className={styles.layoutOptionsWrapper}>
      <Form form={form}>
        <Title level={5}>Container Type</Title>
        <Form.Item name="type" initialValue={type}>
          <Radio.Group onChange={onChange} value={type}>
            <Space>
              <Space direction="vertical">
                <Radio value="none">None</Radio>
                <Radio value="card">Card</Radio>
                <Radio value="collapse">Collapse</Radio>
                <Radio value="tabs">Tabs</Radio>
              </Space>
              <Image
                src={`/images/${type}-preview.png`}
                width={150}
                height={100}
                alt={`Preview of ${type} container`}
              />
            </Space>
          </Radio.Group>
        </Form.Item>
        <Title level={5}>Header</Title>
        <Form.Item name="title" label="Title" initialValue={containerTitle}>
          <Input />
        </Form.Item>
      </Form>
    </div>
  );
};

export default memo(ColumnContainerOptions);
