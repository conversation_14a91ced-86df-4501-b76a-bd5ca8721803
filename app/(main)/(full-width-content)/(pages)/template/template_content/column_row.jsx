import {
  CaretUpOutlined,
  CaretDownOutlined,
  SettingOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { But<PERSON>, Popconfirm, Row, Space } from "antd";
import React, { useState } from "react";
import Helper from "../../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../../src/store/store";
import styles from "./styles.module.css";
import { ColumnRowColumn } from "./column_row_column";
import RowOptions from "./row_options";

/**
 * Row component inside a column
 *
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {array} columns
 * @param {array} templateRows
 * @param {function} setTemplateRows
 * @returns
 */
export const ColumnRow = ({
  rowKey,
  colKey,
  colRowKey,
  columns,
  templateRows,
  setTemplateRows,
}) => {
  const [isRowSettingsOpen, setIsRowSettingsOpen] = useState(false);
  const disableEditing = useBoundStore((state) => state.disableEditing);

  /**
   * Generate row option button
   *
   * @param {object} props
   * @returns {React.DetailedReactHTMLElement}
   */
  const createRowOption = (props) => {
    return React.createElement(Button, {
      type: "primary",
      shape: "circle",
      ...props,
    });
  };

  /**
   * Move row up/down
   *
   * @param {int} offset
   */
  const moveRow = (offset) => {
    if (templateRows[rowKey]["columns"][colKey]["rows"][colRowKey + offset]) {
      const templateRowsCopy = Helper.cloneObject(templateRows);
      const toRow =
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey + offset];
      templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey + offset] =
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey];
      templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey] = toRow;
      setTemplateRows(templateRowsCopy);
    }
  };

  /**
   * Remove row from template
   */
  const deleteRow = () => {
    const templateRowsCopy = Helper.cloneObject(templateRows);
    templateRowsCopy[rowKey]["columns"][colKey]["rows"].splice(colRowKey, 1);
    setTemplateRows(templateRowsCopy);
  };

  return (
    <div className="overflow-x-hidden group/column-row">
      <RowOptions
        isRowSettingsOpen={isRowSettingsOpen}
        setIsRowSettingsOpen={setIsRowSettingsOpen}
        rowKey={rowKey}
        colKey={colKey}
        colRowKey={colRowKey}
      />
      <div className="text-center z-10 invisible group-hover/column-row:visible">
        <Space>
          {createRowOption({
            icon: <CaretUpOutlined />,
            onClick: () => {
              moveRow(-1);
            },
            disabled: disableEditing ? true : false,
          })}
          {createRowOption({
            icon: <CaretDownOutlined />,
            onClick: () => {
              moveRow(1);
            },
            disabled: disableEditing ? true : false,
          })}
          {createRowOption({
            icon: <SettingOutlined />,
            onClick: () => {
              setIsRowSettingsOpen(true);
            },
          })}
          <Popconfirm
            title="Delete the row"
            description="Are you sure to delete this row?"
            onConfirm={deleteRow}
            okText="Yes"
            cancelText="No"
            disabled={disableEditing ? true : false}
          >
            {createRowOption({
              icon: <DeleteOutlined />,
              danger: true,
              disabled: disableEditing ? true : false,
            })}
          </Popconfirm>
        </Space>
      </div>
      <Row
        className={`${
          templateRows[rowKey]["columns"][colKey]["rows"][colRowKey].type !==
          "text"
            ? styles.rowLayout
            : ""
        } h-full mt-2 mb-4`}
        gutter={[8, 8]}
      >
        {columns.map((column, key) => {
          return (
            <ColumnRowColumn
              key={`${rowKey}-${colKey}-${colRowKey}-${key}`}
              rowKey={rowKey}
              colKey={colKey}
              colRowKey={colRowKey}
              colRowColKey={key}
              colspan={column.colspan}
              templateRows={templateRows}
              setTemplateRows={setTemplateRows}
            />
          );
        })}
      </Row>
    </div>
  );
};
