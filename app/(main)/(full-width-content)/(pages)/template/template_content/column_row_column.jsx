import {
  SettingOutlined,
  CloseOutlined,
  BorderOuterOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Col,
  Collapse,
  Form,
  Image,
  Input,
  Popconfirm,
  Popover,
  Space,
  Tabs,
} from "antd";
import { useDrop } from "react-dnd";
import React, { useCallback, useEffect, useRef, useState } from "react";
import Helper from "../../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../../src/store/store";
import { TemplateComponent } from "./template_component";
import { ItemTypes } from "./itemtypes";
import ComponentOptions from "./component_options/component_options";
import styles from "./styles.module.css";
import ColumnContainerOptions from "./column_container_options";

/**
 * Column component inside column row
 *
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {string} colRowColKey
 * @param {int} colspan
 * @param {array} templateRows
 * @param {function} setTemplateRows
 * @returns {JSX.Element}
 */
export const ColumnRowColumn = ({
  rowKey,
  colKey,
  colRowKey,
  colRowColKey,
  colspan,
  templateRows,
  setTemplateRows,
}) => {
  const [tabActiveKey, setTabActiveKey] = useState();
  const [tabItems, setTabItems] = useState([]);
  const newTabIndex = useRef(0);
  const [isComponentOptionsOpen, setIsComponentOptionsOpen] = useState(false);
  const disableEditing = useBoundStore((state) => state.disableEditing);
  const [containerOptionsForm] = Form.useForm();

  useEffect(() => {
    if (
      templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
        colRowColKey
      ]["tabs"] !== undefined
    ) {
      const tabs = [];
      Object.keys(
        templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
          colRowColKey
        ]["tabs"],
      ).forEach((tabKey) => {
        tabs.push({
          label: (
            <Input
              defaultValue={
                templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
                  "columns"
                ][colRowColKey]["tabs"][tabKey].label
              }
              onKeyDown={(e) => e.stopPropagation()}
              onChange={(e) => setTabLabel(e.target.value, tabKey)}
            />
          ),
          key: tabKey,
        });
        setTabActiveKey(tabKey);
        newTabIndex.current++;
      });
      setTabItems(tabs);
    }
  }, [templateRows]);

  /**
   * Generate component node to be rendered
   *
   * @param {object} item
   * @returns {JSX.Element}
   */
  const createComponent = (item) => {
    const { props } = item;

    return (
      <TemplateComponent
        key={`${rowKey}-${colKey}-${colRowKey}-${colRowColKey}`}
        id={`${rowKey}-${colKey}-${colRowKey}-${colRowColKey}`}
        index={`${rowKey}-${colKey}-${colRowKey}-${colRowColKey}`}
        props={props}
      >
        {item.type === "text" ? (
          Helper.getTextElement(item.value, item.text_type)
        ) : (
          <Card
            className="w-full h-full"
            title={
              item.props
                ? item.props.settings.title
                  ? item.props.settings.title
                  : item.display_name
                : ""
            }
          >
            {item.type === "text" ? (
              Helper.getTextElement(item.value, item.text_type)
            ) : (
              <Image src={item.preview_image} width={200} preview={false} />
            )}
          </Card>
        )}
      </TemplateComponent>
    );
  };

  /**
   * Move/Swap template component
   *
   * @param {object} component
   */
  const moveTemplateComponent = useCallback(
    (component) => {
      Helper.moveTemplateComponent(
        component,
        templateRows,
        setTemplateRows,
        rowKey,
        colKey,
        colRowKey,
        colRowColKey,
      );
    },
    [templateRows],
  );

  /**
   * Add component to template
   *
   * @param {object} component
   */
  const addTemplateComponent = useCallback(
    (component, tabKey) => {
      Helper.addTemplateComponent(
        undefined,
        component,
        templateRows,
        setTemplateRows,
        rowKey,
        colKey,
        colRowKey,
        colRowColKey,
        tabKey,
      );
    },
    [templateRows],
  );

  /**
   * Hook to wire an element to be a drop target
   * https://react-dnd.github.io/react-dnd/docs/api/use-drop
   *
   * @params {object} spec - specification object
   * @params {array} deps - dependency array used for memoization
   * @returns {array} [collectedProps, dropTargetRef]
   */
  const [, drop] = useDrop(
    () => ({
      accept: ItemTypes.COMPONENT,
      drop: (item) => {
        if (!item.index) {
          // add component to row column
          // copy item component to avoid mutation to original component list
          const component = Helper.cloneObject(item);
          addTemplateComponent(component, tabActiveKey);
        } else {
          // move or swap template component
          moveTemplateComponent(item);
        }
      },
      collect: (monitor) => ({
        isOver: monitor.isOver(),
        canDrop: monitor.canDrop(),
      }),
    }),
    [templateRows, tabActiveKey],
  );

  /**
   * Generate component option button
   *
   * @param {object} props
   * @returns {React.DetailedReactHTMLElement}
   */
  const createComponentOption = (props) => {
    return React.createElement(Button, {
      type: "primary",
      shape: "circle",
      ...props,
    });
  };

  /**
   * Remove component from column
   */
  const clearColumnComponent = () => {
    if (
      templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
        colRowColKey
      ]["component"]
    ) {
      const templateRowsCopy = Helper.cloneObject(templateRows);
      if (
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["component"]["type"] === "text"
      ) {
        // remove row
        templateRowsCopy[rowKey]["columns"][colKey]["rows"].splice(
          colRowKey,
          1,
        );
      } else {
        // clear component but retain column
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["component"] = null;
      }
      setTemplateRows(templateRowsCopy);
    }
  };

  /**
   * Triggers when visibility of container options popover change
   *
   * @param {boolean} isOpen
   */
  const onToggleContainerOptions = (isOpen) => {
    if (!isOpen) {
      const values = containerOptionsForm.getFieldsValue();
      const templateRowsCopy = Helper.cloneObject(templateRows);
      templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
        colRowColKey
      ]["container"] = {};
      templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
        colRowColKey
      ]["container"]["type"] = values.type;
      templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
        colRowColKey
      ]["container"]["title"] = values.title;
      if (
        values.type === "tabs" &&
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["tabs"] === undefined
      ) {
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["tabs"] = {};
      }
      setTemplateRows(templateRowsCopy);
    }
  };

  /**
   * Set active key when changing tab
   *
   * @param {string} newTabActiveKey
   */
  const onTabChange = (newTabActiveKey) => {
    setTabActiveKey(newTabActiveKey);
  };

  /**
   * Set tab label
   *
   * @param {string} label
   * @param {string} tabKey
   */
  const setTabLabel = (label, tabKey) => {
    const templateRowsCopy = Helper.cloneObject(templateRows);
    if (
      templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
        colRowColKey
      ]["tabs"][tabKey] === undefined
    ) {
      templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
        colRowColKey
      ]["tabs"][tabKey] = {};
    }
    templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
      colRowColKey
    ]["tabs"][tabKey]["label"] = label;
    setTemplateRows(templateRowsCopy);
  };

  /**
   * Add new tab
   */
  const addTab = () => {
    const newTabActiveKey = `newTab${newTabIndex.current++}`;
    const newPanes = [...tabItems];
    newPanes.push({
      label: (
        <Input
          onKeyDown={(e) => e.stopPropagation()}
          onChange={(e) => setTabLabel(e.target.value, newTabActiveKey)}
        />
      ),
      key: newTabActiveKey,
    });
    setTabItems(newPanes);
    setTabActiveKey(newTabActiveKey);

    const templateRowsCopy = Helper.cloneObject(templateRows);
    templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
      colRowColKey
    ]["tabs"][newTabActiveKey] = {};
    setTemplateRows(templateRowsCopy);
  };

  /**
   * Remove tab
   *
   * @param {string} targetKey
   */
  const removeTab = (targetKey) => {
    let newTabActiveKey = tabActiveKey;
    let lastIndex = -1;
    tabItems.forEach((item, i) => {
      if (item.key === targetKey) {
        lastIndex = i - 1;
      }
    });
    const newPanes = tabItems.filter((item) => item.key !== targetKey);
    if (newPanes.length && newTabActiveKey === targetKey) {
      if (lastIndex >= 0) {
        newTabActiveKey = newPanes[lastIndex].key;
      } else {
        newTabActiveKey = newPanes[0].key;
      }
    }
    setTabItems(newPanes);
    setTabActiveKey(newTabActiveKey);
  };

  /**
   * Callback function when adding or removing a tab
   *
   * @param {string} targetKey
   * @param {string} action
   */
  const onTabEdit = (targetKey, action) => {
    if (action === "add") {
      addTab();
    } else {
      removeTab(targetKey);
    }
  };

  /**
   * Generate content of column
   *
   * @param {string} tabKey
   * @returns {JSX.Element}
   */
  const createColumnContent = (tabKey) => {
    return (
      <>
        {templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
          colRowColKey
        ].component &&
          templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
            colRowColKey
          ].component.type !== "text" && (
            <div>
              <ComponentOptions
                component={
                  templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
                    "columns"
                  ][colRowColKey]["component"]
                }
                isComponentOptionsOpen={isComponentOptionsOpen}
                setIsComponentOptionsOpen={setIsComponentOptionsOpen}
              />
              <div className="absolute right-0 mt-2 mr-4 z-10 invisible group-hover/column-row-column:visible">
                <Space>
                  {!disableEditing ? (
                    <Popover
                      placement="bottomRight"
                      content={
                        <ColumnContainerOptions
                          form={containerOptionsForm}
                          containerType={
                            templateRows[rowKey]["columns"][colKey]["rows"][
                              colRowKey
                            ]["columns"][colRowColKey]["container"]
                              ? templateRows[rowKey]["columns"][colKey]["rows"][
                                  colRowKey
                                ]["columns"][colRowColKey]["container"]["type"]
                              : "none"
                          }
                          containerTitle={
                            templateRows[rowKey]["columns"][colKey]["rows"][
                              colRowKey
                            ]["columns"][colRowColKey]["container"]
                              ? templateRows[rowKey]["columns"][colKey]["rows"][
                                  colRowKey
                                ]["columns"][colRowColKey]["container"]["title"]
                              : ""
                          }
                        />
                      }
                      trigger="click"
                      onOpenChange={onToggleContainerOptions}
                    >
                      <Button
                        type="primary"
                        shape="circle"
                        icon={<BorderOuterOutlined />}
                      />
                    </Popover>
                  ) : (
                    <Button
                      type="primary"
                      shape="circle"
                      icon={<BorderOuterOutlined />}
                      disabled
                    />
                  )}
                  {createComponentOption({
                    icon: <SettingOutlined />,
                    onClick: () => {
                      setIsComponentOptionsOpen(true);
                    },
                  })}
                  <Popconfirm
                    title="Remove the component"
                    description="Are you sure to remove this component?"
                    placement="topRight"
                    onConfirm={clearColumnComponent}
                    okText="Yes"
                    cancelText="No"
                    disabled={disableEditing ? true : false}
                  >
                    {createComponentOption({
                      icon: <CloseOutlined />,
                      danger: true,
                      disabled: disableEditing ? true : false,
                    })}
                  </Popconfirm>
                </Space>
              </div>
            </div>
          )}

        {/* <div className={`h-full p-2 border-dashed`}>
          {templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
            colRowColKey
          ].component &&
            createComponent(
              templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
                "columns"
              ][colRowColKey].component
            )}
        </div> */}

        {tabKey !== undefined ? (
          <div className={`${styles.columnLayout} h-full p-2 border-dashed`}>
            {templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
              "columns"
            ][colRowColKey]["tabs"][tabKey] &&
              templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
                "columns"
              ][colRowColKey]["tabs"][tabKey].component &&
              createComponent(
                templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
                  "columns"
                ][colRowColKey]["tabs"][tabKey].component,
              )}
          </div>
        ) : (
          <div className={`${styles.columnLayout} h-full p-2 border-dashed`}>
            {templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
              "columns"
            ][colRowColKey].component &&
              createComponent(
                templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
                  "columns"
                ][colRowColKey].component,
              )}
          </div>
        )}
      </>
    );
  };

  /**
   * Generate tabs
   *
   * @returns {JSX.Element}
   */
  const createTabs = () => {
    let tabs = [];
    tabItems.forEach((item) => {
      tabs.push({
        label: item.label,
        children: createColumnContent(item.key),
        key: item.key,
      });
    });
    return (
      <Tabs
        type="editable-card"
        onChange={onTabChange}
        activeKey={tabActiveKey}
        onEdit={onTabEdit}
        items={tabs}
      />
    );
  };

  return (
    <Col
      className="group/column-row-column"
      ref={drop}
      key={`${rowKey}-${colKey}-${colRowKey}-${colRowColKey}`}
      span={colspan}
      data-testid="content"
    >
      {templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
        colRowColKey
      ]["container"] &&
      templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
        colRowColKey
      ]["container"]["type"] === "collapse" ? (
        <Collapse
          defaultActiveKey={
            templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
              "columns"
            ][colRowColKey]["container"]["collapsed"] !== true
              ? ["1"]
              : []
          }
          items={[
            {
              key: "1",
              label:
                templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
                  "columns"
                ][colRowColKey]["container"]["title"],
              children: createColumnContent(),
            },
          ]}
        />
      ) : templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
          colRowColKey
        ]["container"] &&
        templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
          colRowColKey
        ]["container"]["type"] === "card" ? (
        <Card
          title={
            templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
              "columns"
            ][colRowColKey]["container"]["title"]
              ? templateRows[rowKey]["columns"][colKey]["rows"][colRowKey][
                  "columns"
                ][colRowColKey]["container"]["title"]
              : null
          }
        >
          {createColumnContent()}
        </Card>
      ) : templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
          colRowColKey
        ]["container"] &&
        templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
          colRowColKey
        ]["container"]["type"] === "tabs" ? (
        createTabs()
      ) : (
        createColumnContent()
      )}
    </Col>
  );
};
