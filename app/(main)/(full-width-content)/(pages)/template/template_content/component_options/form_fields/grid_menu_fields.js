import { EyeOutlined } from "@ant-design/icons";
import GenerateRawDataTableForm from "../../../../../../../../src/utils/grid/components/generate_raw_data_table_form";

/**
 * Form fields for Grid Menu component option
 */
export const GridMenuFields = {
  showWaferIdBreakdownTable: {
    type: "checkbox",
    inputType: "button",
    label: "Show Wafer ID Breakdown Table",
    icon: <EyeOutlined />,
    position: "center",
    onClick: "toggleShowWaferIdBreakdownTable",
  },
  generateRawDataTable: {
    type: "checkbox",
    inputType: "form",
    label: "Generate Raw Data Table",
    component: GenerateRawDataTableForm,
  },
};
