/**
 * Form fields for Settings component option
 */
export const ComponentSettingsFields = {
  table: {
    title: {
      label: "Title",
      required: false,
    },
    width: {
      label: "Width",
      required: true,
    },
    height: {
      label: "Height",
      required: true,
    },
    column_defs: {
      exclude: true,
    },
    is_visible: {
      label: "Visible",
      type: "boolean",
      default: true,
    },
    checkbox_selection: {
      label: "Checkbox Selection",
      type: "boolean",
    },
    suppress_row_virtualisation: {
      label: "Suppress Row Virtualisation",
      type: "boolean",
    },
    is_server_side: {
      label: "Server Side",
      type: "boolean",
    },
  },
  chart: {
    title: {
      label: "Title",
      required: false,
    },
    width: {
      label: "Width",
      min: 0,
      type: "integer",
    },
    height: {
      label: "Height",
      min: 0,
      type: "integer",
    },
    testLimits: {
      label: "Test Limits",
      type: "boolean",
    },
    robustLimits: {
      label: "Robust Limits",
      type: "boolean",
    },
    meanMark: {
      label: "Mean Mark",
      type: "boolean",
    },
    bellCurve: {
      label: "Bell Curve",
      type: "boolean",
    },
    x: {
      label: "X Axis",
      options: {
        title: {
          label: "Title",
        },
        labelRotation: {
          label: "Label Rotation",
          type: "integer",
        },
        gridLines: {
          label: "Grid Lines",
          type: "boolean",
        },
      },
    },
    y: {
      label: "Y Axis",
      options: {
        title: {
          label: "Title",
        },
        axisRange: {
          label: "Axis Range",
          type: "custom",
        },
        gridLines: {
          label: "Grid Lines",
          type: "boolean",
        },
        logScale: {
          label: "Log Scale",
          type: "boolean",
        },
      },
    },
    y2: {
      includeIn: ["pareto", "pareto_with_line"],
      label: "Y2 Axis",
      options: {
        title: {
          label: "Title",
        },
        minRange: {
          label: "Min Range",
          type: "integer",
        },
        maxRange: {
          label: "Max Range",
          type: "integer",
        },
        gridLines: {
          label: "Grid Lines",
          type: "boolean",
        },
      },
    },
    bar: {
      includeIn: ["pareto", "pareto_with_line"],
      label: "Bar Series",
      options: {
        title: {
          label: "Title",
        },
      },
    },
    line: {
      includeIn: ["pareto", "pareto_with_line"],
      label: "Line Series",
      options: {
        title: {
          label: "Title",
        },
      },
    },
    legend: {
      label: "Legend",
      options: {
        color: {
          label: "Color",
          type: "custom",
        },
      },
    },
  },
};
