/**
 * Form fields for Grid Columns component option
 */
export const GridColumnProps = {
  field: {
    required: true,
    editable: false,
  },
  // header
  headerName: {
    editable: false,
  },
  headerTooltip: {},
  headerClass: {},
  wrapHeaderText: {
    type: "boolean",
    default: false,
  },
  autoHeaderHeight: {
    type: "boolean",
    default: false,
  },
  headerCheckboxSelection: {
    type: "boolean",
    default: false,
  },
  // width
  width: {
    type: "integer",
  },
  resizable: {
    type: "boolean",
    default: true,
  },
  suppressSizeToFit: {
    type: "boolean",
    default: false,
  },
  suppressAutoSize: {
    type: "boolean",
    default: false,
  },
  type: {},
  valueGetter: {
    type: "function",
  },
  valueFormatter: {},
  checkboxSelection: {
    type: "boolean",
    default: false,
  },
  suppressColumnsToolPanel: {
    type: "boolean",
    default: false,
  },
  // display props
  hide: {
    type: "boolean",
    default: false,
  },
  lockPosition: {
    type: "boolean",
    default: false,
  },
  suppressMovable: {
    type: "boolean",
    default: false,
  },
  // editing props
  editable: {
    type: "boolean",
    default: false,
  },
  // pinned
  pinned: {
    type: "boolean",
    default: false,
  },
  // rendering and styling
  cellClass: {},
  cellRenderer: {
    type: "function",
  },
  cellRendererParams: {},
  autoHeight: {
    type: "boolean",
    default: false,
  },
  wrapText: {
    type: "boolean",
    default: false,
  },
  // row grouping
  rowGroup: {
    type: "boolean",
    default: false,
  },
  enableRowGroup: {
    type: "boolean",
    default: false,
  },
  // sort
  sortable: {
    type: "boolean",
    default: true,
  },
  comparator: {
    type: "function",
  },
  // events
  onCellValueChanged: {
    type: "function",
  },
  onCellClicked: {
    type: "function",
  },
};
