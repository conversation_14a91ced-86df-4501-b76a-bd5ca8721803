import {
  ReloadOutlined,
  <PERSON><PERSON>extOutlined,
  <PERSON>AddOutlined,
  MinusSquareOutlined,
  DownloadOutlined,
  EditOutlined,
  Check<PERSON>ircleOutlined,
  EyeOutlined,
  <PERSON>umnWidthOutlined,
  FilterOutlined,
  DeleteOutlined,
  CopyOutlined,
  SaveOutlined,
  PlusSquareOutlined,
  ClearOutlined,
  User<PERSON>ddOutlined,
  <PERSON>ginOutlined,
  MinusCircleOutlined,
  Plus<PERSON>ircleOutlined,
  UndoOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { Tooltip } from "antd";
import ViewMoreRunTypeForm from "../../../../../../../../src/utils/grid/components/view_more_run_type_form";
import AssignAnalysisSetForm from "../../../../npi/assign_analysis_set_form";
import FilterStatusSelect from "../../../../../../../../src/utils/grid/components/filter_status_select";
import FilterSubconSelect from "../../../../../../../../src/utils/grid/components/filter_subcon_select";
import FilterSelect from "../../../../../../../../src/utils/grid/components/filter_select";
import { UserSettingsKeys } from "../../../../../../../../src/utils/user_settings_keys";
import AssignConditionValuesForm from "../../../../npi/assign_condition_values_form";

/**
 * Generate percent drift options
 *
 * @returns {array} options
 */
const getPercentDriftOptions = () => {
  const options = [];
  for (let i = 1; i <= 25; i++) {
    options.push({
      value: i,
      label: i,
    });
  }

  return options;
};

/**
 * Form fields for Grid Toolbar component option
 */
export const GridToolbarFields = {
  search: {
    type: "checkbox",
    inputType: "search",
    label: "Search",
  },
  downloadCSV: {
    type: "checkbox",
    inputType: "button",
    label: "Download CSV",
    icon: <FileTextOutlined />,
    onClick: "notifyForDevelopment",
  },
  edit: {
    type: "checkbox",
    inputType: "button",
    label: "Edit",
    icon: <EditOutlined />,
    onClick: "notifyForDevelopment",
    disabled: true,
    // tooltip: "MVP 2",
  },
  validateSelection: {
    type: "checkbox",
    inputType: "button",
    label: "Validate Selection",
    icon: <CheckCircleOutlined />,
    onClick: "validateSelection",
    disabled: true,
  },
  showInvalid: {
    type: "checkbox",
    inputType: "button",
    label: "Show Invalid",
    icon: <EyeOutlined />,
    onClick: "toggleShowInvalid",
  },
  selectOptions: {
    type: "checkbox",
    inputType: "dropdown",
    label: "Select Options",
    onClick: "onMenuItemClick",
    items: [
      {
        key: "entire_page",
        label: "Entire Page",
      },
      {
        key: "range",
        label: "Range",
      },
      {
        key: "invert_selection",
        label: "Invert Selection",
      },
      {
        key: "clear_selection",
        label: "Clear Selection",
      },
    ],
  },
  downloadSelectedAs: {
    type: "checkbox",
    inputType: "dropdown",
    label: "Download Selected as",
    onClick: "onMenuItemClick",
    hasLoading: true,
    items: [
      {
        key: "download_table",
        label: "Table",
      },
      {
        key: "download_datalogs",
        label: "Datalogs",
      },
      // {
      //   key: "download_advanced",
      //   label: "Advanced",
      //   disabled: true,
      // },
    ],
  },
  downloadDatalogs: {
    type: "checkbox",
    inputType: "button",
    label: "Download Selected",
    icon: <DownloadOutlined />,
    onClick: "downloadDatalogs",
    loadingStatusKey: "downloadDatalogs",
    hasLoading: true,
  },
  label: {
    type: "checkbox",
    inputType: "dropdown",
    label: "Label",
    onClick: "onMenuItemClick",
    disabled: true,
    // tooltip: "MVP 2",
    items: [
      {
        key: "add_label",
        label: "Add Label",
      },
      {
        key: "remove_label",
        label: "Remove Label",
      },
      {
        key: "create_new_label",
        label: "Create New Label",
      },
      {
        key: "filter_by_label",
        label: "Filter by Label",
      },
    ],
  },
  sortBy: {
    type: "checkbox",
    inputType: "dropdown",
    label: "Sort By",
    onClick: "notifyForDevelopment",
    disabled: true,
    // tooltip: "MVP 2",
  },
  manageTable: {
    type: "checkbox",
    inputType: "dropdown",
    label: "Manage Table",
    icon: <ColumnWidthOutlined />,
    position: "right",
    onClick: "onMenuItemClick",
    hasLoading: true,
    hasDownArrow: false,
    items: [
      {
        key: "reload_table",
        label: (
          <Tooltip
            title="Refreshing the data displayed in the table"
            placement="right"
          >
            <div className="w-full">Reload Table</div>
          </Tooltip>
        ),
      },
      {
        key: "reset_table",
        label: (
          <Tooltip
            title="Filters and sorting will reset to default"
            placement="right"
          >
            <div className="w-full">Reset Table</div>
          </Tooltip>
        ),
      },
      {
        key: "add_remove_column",
        label: "Add/Remove Column",
      },
      {
        key: "save_table_settings",
        label: "Save Table Settings",
      },
      {
        key: "load_saved_columns",
        label: "Load Saved Columns",
      },
      {
        key: "reset_columns",
        label: (
          <Tooltip
            title="Restoring default column settings & clearing customizations"
            placement="right"
          >
            <div className="w-full">Reset Columns</div>
          </Tooltip>
        ),
      },
      {
        key: "adjust_columns_evenly",
        label: (
          <Tooltip
            title="Adjusts the size of columns to fit content"
            placement="right"
          >
            <div className="w-full">Adjust Columns Evenly</div>
          </Tooltip>
        ),
      },
      // {
      //   key: "add_parametric_column",
      //   label: "Add Parametric Column",
      //   disabled: true,
      //   // title: "MVP 2",
      // },
      // {
      //   key: "add_bin_column",
      //   label: "Add Bin Column",
      //   disabled: true,
      //   // title: "MVP 2",
      // },
    ],
  },
  manageEngineeringTable: {
    type: "checkbox",
    inputType: "dropdown",
    label: "Table Options",
    position: "right",
    onClick: "onMenuItemClick",
    hasLoading: true,
    hasDownArrow: true,
    items: [
      {
        key: "reload_table",
        label: "Reload Table",
        title: "Refreshing the data displayed in the table",
      },
      {
        key: "reset_table",
        label: "Reset Table",
        title: "Filters and sorting will reset to default",
      },
    ],
  },
  reloadData: {
    type: "checkbox",
    inputType: "button",
    label: "Reload Table",
    icon: <ReloadOutlined />,
    position: "right",
    onClick: "reloadData",
    tooltip: "Refreshing the data displayed in the table",
  },
  resetTable: {
    type: "checkbox",
    inputType: "button",
    label: "Reset Table",
    icon: <UndoOutlined />,
    position: "right",
    onClick: "resetTable",
    tooltip: "Filters and Sorting will reset to default",
  },
  adjustColumnsEvenly: {
    type: "checkbox",
    inputType: "button",
    label: "Adjust Columns Evenly",
    icon: <ColumnWidthOutlined />,
    position: "right",
    onClick: "adjustColumnsEvenly",
    tooltip: "Adjusts the size of columns to fit content",
  },
  viewMoreRunType: {
    type: "checkbox",
    inputType: "form",
    label: "View More Run Type",
    component: ViewMoreRunTypeForm,
  },
  filterTable: {
    type: "checkbox",
    inputType: "button",
    label: "Filter Table",
    icon: <FilterOutlined />,
    buttonType: "primary",
    onClick: "showFilterTableOptions",
  },
  advanceActions: {
    type: "checkbox",
    inputType: "dropdown",
    label: "Advance Actions",
    onClick: "notifyForDevelopment",
    items: [],
  },
  reprocessDatalog: {
    type: "checkbox",
    inputType: "button",
    label: "Reprocess Datalog",
    icon: <ReloadOutlined />,
    onClick: "reprocessDatalog",
  },
  viewFileInfo: {
    type: "checkbox",
    inputType: "button",
    label: "View File Info",
    icon: <FileTextOutlined />,
    onClick: "notifyForDevelopment",
    disabled: true,
  },
  editDatalog: {
    type: "checkbox",
    inputType: "dropdown",
    label: "Edit",
    onClick: "onMenuItemClick",
    hasLoading: true,
    hasDownArrow: true,
    items: [
      {
        key: "edit_table_data",
        label: "Edit",
      },
      {
        key: "save_table_data",
        label: "Save Changes",
      },
      {
        key: "cancel_edit_table",
        label: "Cancel Editing",
      },
    ],
  },
  deleteDatalog: {
    type: "checkbox",
    inputType: "button",
    label: "Delete",
    icon: <DeleteOutlined />,
    onClick: "deleteSelectedRow",
    disabled: true,
    loadingStatusKey: "delete_datalog",
    apiParam: "file_name",
    keyword: "datalog",
    deleteApiCallback: "deleteDatalog",
  },
  selectFileType: {
    inputType: "select",
    allowClear: false,
    showSearch: false,
    onChange: "filterByFileType",
    defaultValue: "",
    options: [
      {
        value: "",
        label: "All File Types",
      },
      {
        value: "datalog",
        label: "Datalogs",
      },
      {
        value: "non_datalog",
        label: "Non-Datalog Files",
      },
    ],
  },
  selectDiceType: {
    inputType: "select",
    allowClear: false,
    showSearch: false,
    onChange: "filterByDiceType",
    defaultValue: "show_all_dice",
    options: [
      {
        value: "show_all_dice",
        label: "Show All Dice",
      },
      {
        value: "dice_retested",
        label: "Dice Re-tested",
      },
    ],
  },
  openWith: {
    type: "checkbox",
    inputType: "select",
    label: "Open With",
    placeholder: "-Select-",
    options: [
      {
        key: "lot_analysis",
        label: "Lot Analysis",
      },
      {
        key: "all_bins",
        label: "All Bins",
      },
      {
        key: "all_tests",
        label: "All Tests",
      },
    ],
  },
  selectStatus: {
    type: "checkbox",
    inputType: "select",
    label: "Select Status",
    placeholder: "All Status",
    mode: "multiple",
    onChange: "filterTableStatus",
    component: FilterStatusSelect,
  },
  selectSubcon: {
    type: "checkbox",
    inputType: "select",
    label: "Select Subcon",
    placeholder: "All Subcon",
    mode: "multiple",
    onChange: "filterTableSubcon",
    component: FilterSubconSelect,
  },
  selectRecipeType: {
    type: "checkbox",
    inputType: "select",
    label: "Select Recipe Type",
    onChange: "filterByRecipeType",
    component: FilterSelect,
    allowClear: false,
    className: "w-48",
    labelInValue: true,
    defaultValue: "",
    params: {
      api: {
        url: "api/v1/recipe/types",
        cache_it: 0,
      },
      allOption: {
        label: "All Recipes",
        value: "",
      },
      userSettings: {
        key: UserSettingsKeys.recipe_type_filter,
      },
    },
  },
  selectRecipeOwner: {
    type: "checkbox",
    inputType: "select",
    label: "Select Recipe Owner",
    onChange: "filterByRecipeOwner",
    component: FilterSelect,
    allowClear: false,
    className: "w-40",
    labelInValue: true,
    defaultValue: {
      label: "All Users",
      value: "",
    },
    params: {
      api: {
        field: "user",
        cache_it: 0,
      },
      allOption: {
        label: "All Users",
        value: "",
      },
      userSettings: {
        key: UserSettingsKeys.recipe_owner_filter,
      },
    },
  },
  selectAutoTriggerStatus: {
    type: "checkbox",
    inputType: "select",
    label: "",
    placeholder: "-Select-",
    allowClear: false,
    defaultValue: "",
    showSearch: false,
    onChange: "filterByAutoTriggerStatus",
    options: [
      {
        value: "",
        label: "All Trigger Status",
      },
      {
        value: 1,
        label: "Auto-Trigger On",
      },
      {
        value: 0,
        label: "Auto-Trigger Off",
      },
    ],
    userSettings: {
      key: UserSettingsKeys.recipe_auto_trigger_status_filter,
    },
  },
  editRecipe: {
    type: "checkbox",
    inputType: "button",
    label: "Edit",
    icon: <EditOutlined />,
    onClick: "editSelectedRecipe",
    disabled: true,
  },
  duplicateRecipe: {
    type: "checkbox",
    inputType: "button",
    label: "Duplicate",
    icon: <CopyOutlined />,
    onClick: "duplicateSelectedRecipe",
    disabled: true,
  },
  deleteRecipe: {
    type: "checkbox",
    inputType: "button",
    label: "Delete",
    icon: <DeleteOutlined />,
    onClick: "deleteSelectedRow",
    disabled: true,
    loadingStatusKey: "delete_recipe",
    apiParam: "recipe_key",
    keyword: "recipe",
    deleteApiCallback: "deleteRecipe",
  },
  saveRecipe: {
    type: "checkbox",
    inputType: "button",
    label: "Save as Recipe",
    icon: <SaveOutlined />,
    onClick: "saveSelectedRecipe",
    disabled: true,
  },
  copySelectedToPDB: {
    type: "checkbox",
    inputType: "button",
    label: "Copy Selected to PDB",
    icon: <DownloadOutlined />,
    onClick: "notifyForDevelopment",
    disabled: true,
  },
  statisticType: {
    type: "checkbox",
    inputType: "dropdown",
    label: "Statistic Type",
    onClick: "notifyForDevelopment",
    disabled: true,
    items: [],
  },
  followedTestsOnly: {
    type: "checkbox",
    inputType: "checkbox",
    label: "Followed Tests Only",
    onChange: "showFollowedTestsOnly",
    disabled: true,
  },
  showBreakdownPerPin: {
    type: "checkbox",
    inputType: "checkbox",
    label: "Show Breakdown Per Pin",
    onChange: "showBreakdownPerPin",
    disabled: true,
  },
  viewSelectedIn: {
    type: "checkbox",
    inputType: "dropdown",
    label: "View Selected in",
    onClick: "onMenuItemClick",
    destroyPopupOnHide: true,
    items: [
      {
        key: "view_test_raw_data",
        label: "Raw Data",
        disabled: true,
        title: "You must select a test to view raw data.",
      },
    ],
  },
  exportAs: {
    type: "checkbox",
    inputType: "dropdown",
    label: "Export as",
    onClick: "onMenuItemClick",
    hasLoading: true,
    items: [
      // {
      //   key: "pdf",
      //   label: "PDF",
      //   disabled: true,
      // },
      {
        key: "export_csv",
        label: "CSV",
      },
    ],
  },
  filterByTestType: {
    inputType: "select",
    label: "Test Type",
    placeholder: "All Test Types",
    onChange: "filterByTestType",
    options: [
      {
        key: "all",
        value: "all",
        label: "All Test Types",
      },
      {
        key: "f",
        value: "f",
        label: "FTR",
      },
      {
        key: "m",
        value: "m",
        label: "MPR",
      },
      {
        key: "p",
        value: "p",
        label: "PTR",
      },
    ],
  },
  filterByTestTypes: {
    inputType: "select",
    placeholder: "Filter By Test Type",
    mode: "multiple",
    className: "w-36",
    maxTagCount: "responsive",
    onChange: "filterByTestTypes",
    options: [
      {
        key: "m",
        value: "m",
        label: "MPR",
      },
      {
        key: "p",
        value: "p",
        label: "PTR",
      },
      {
        key: "f",
        value: "f",
        label: "FTR",
      },
    ],
  },
  analyseParts: {
    inputType: "button",
    buttonType: "primary",
    label: "Analyse Parts",
    icon: <PlusCircleOutlined />,
    onClick: "analyseParts",
    position: "right",
  },
  addSingleCondition: {
    inputType: "button",
    buttonType: "primary",
    label: "Add this Condition",
    icon: <PlusCircleOutlined />,
    onClick: "addSingleCondition",
    position: "right",
  },
  addMultiCondition: {
    inputType: "button",
    buttonType: "primary",
    label: "Add Selection as Condition",
    icon: <PlusCircleOutlined />,
    onClick: "addMultiCondition",
    position: "right",
  },
  conditionName: {
    inputType: "inputText",
    defaultValue: "MyCondition",
    placeholder: "Condition Name",
    position: "left",
    id: "conditionName",
  },
  addOpenButton: {
    inputType: "button",
    buttonType: "primary",
    label: "Open",
    onClick: "openSimulationTab",
    position: "left",
  },
  assignAnalysisSet: {
    type: "checkbox",
    inputType: "form",
    component: AssignAnalysisSetForm,
  },
  applyConditionToPartId: {
    type: "checkbox",
    inputType: "form",
    component: AssignConditionValuesForm,
  },
  addTestsToTraceability: {
    inputType: "button",
    label: "Add Tests",
    icon: <FileAddOutlined />,
    onClick: "addTestsToTraceability",
  },
  deleteTestsFromTraceability: {
    inputType: "button",
    label: "Remove Tests",
    icon: <MinusSquareOutlined />,
    onClick: "deleteTestsFromTraceability",
  },
  addTraceabilityVariables: {
    inputType: "button",
    buttonType: "primary",
    label: "Append Selected to Recipe",
    icon: <PlusSquareOutlined />,
    onClick: "addSelectedRowsToTraceabilityRecipe",
  },
  clearTraceabilityVariables: {
    inputType: "button",
    label: "Clear all Variables",
    icon: <ClearOutlined />,
    onClick: "clearTraceabilityVariables",
  },
  editMetadataOutputValue: {
    inputType: "button",
    label: "Edit Output Value",
    icon: <EditOutlined />,
    onClick: "editMetadataOutputValue",
  },
  deleteMetadataOutputValue: {
    inputType: "button",
    label: "Delete Output Value",
    icon: <DeleteOutlined />,
    onClick: "deleteMetadataOutputValue",
  },
  simulateMetadataRecipe: {
    inputType: "select",
    label: "Simulate Recipe By",
    placeholder: "-Select-",
    // placeholder: "Simulate Recipe By:",
    // hideLabel: true,
    onChange: "simulateMetadataRecipe",
    options: [
      {
        key: "datalog",
        value: "datalog",
        label: "Datalog",
      },
      {
        key: "program",
        value: "program",
        label: "Program",
      },
    ],
  },
  saveMetadataRecipe: {
    inputType: "button",
    buttonType: "primary",
    label: "Save Recipe",
    icon: <SaveOutlined />,
    onClick: "saveMetadataRecipe",
  },
  addNewUser: {
    type: "checkbox",
    inputType: "button",
    buttonType: "primary",
    label: "Add New User",
    icon: <UserAddOutlined />,
    onClick: "showAddNewUserForm",
  },
  selectUserType: {
    type: "checkbox",
    inputType: "select",
    label: "Select User Type",
    onChange: "filterByUserType",
    component: FilterSelect,
    className: "w-32",
    params: {
      api: {
        url: "api/v1/internal/options/list/user/types",
        cache_it: 0,
      },
      allOption: {
        label: "All User Types",
        value: "",
      },
    },
  },
  selectOnlineStatus: {
    type: "checkbox",
    inputType: "select",
    label: "Select Online Status",
    onChange: "filterByOnlineStatus",
    component: FilterSelect,
    className: "w-32",
    params: {
      api: {
        url: "api/v1/internal/options/list/user/presence_status",
        cache_it: 0,
      },
      allOption: {
        label: "All Online Status",
        value: "",
      },
    },
  },
  viewInHomepage: {
    type: "checkbox",
    inputType: "button",
    label: "View in Homepage",
    icon: <LoginOutlined />,
    onClick: "viewInHomepage",
    disabled: true,
  },
  excludeDatalog: {
    type: "checkbox",
    inputType: "button",
    label: "Exclude Datalog",
    icon: <MinusCircleOutlined />,
    onClick: "excludeDatalog",
    disabled: true,
    tooltip: "Excludes selected datalog and creates a new report",
  },
  downloadTable: {
    type: "checkbox",
    inputType: "button",
    label: "Download Table",
    icon: <DownloadOutlined />,
    onClick: "downloadTable",
    hasLoading: true,
  },
  generateMPRMaps: {
    type: "checkbox",
    inputType: "button",
    label: "Generate MPR Maps",
    onClick: "generateMPRMaps",
    tooltip: "Complete X Y data to Generate",
  },
  addNewRole: {
    type: "checkbox",
    position: "left",
    inputType: "button",
    buttonType: "primary",
    disabled: true,
    label: "Add New Role",
    icon: <PlusOutlined />,
  },
  addNewPermission: {
    type: "checkbox",
    position: "left",
    inputType: "button",
    buttonType: "primary",
    disabled: true,
    label: "Add New Permission",
    icon: <PlusOutlined />,
  },
  addNewTeam: {
    type: "checkbox",
    inputType: "button",
    buttonType: "primary",
    disabled: true,
    label: "Add New Team",
    icon: <PlusOutlined />,
    onClick: "showAddNewTeamForm",
  },
  testsFilter: {
    type: "checkbox",
    inputType: "button",
    buttonType: "primary",
    label: "Table Filter",
    icon: <FilterOutlined />,
    onClick: "showTestsFilterForm",
  },
  selectPercentDrift: {
    inputType: "select",
    label: "% Drift",
    allowClear: false,
    showSearch: false,
    onChange: "filterByPercentDrift",
    defaultValue: 15,
    options: getPercentDriftOptions(),
  },
  testTableFilter: {
    type: "checkbox",
    inputType: "button",
    buttonType: "primary",
    label: "Table Filter",
    icon: <FilterOutlined />,
    onClick: "showTestTableFilterForm",
  },
  addTests: {
    type: "checkbox",
    inputType: "button",
    label: "Add Tests",
    icon: <FileAddOutlined />,
    onClick: "showAddTestsForm",
  },
  removeTest: {
    type: "checkbox",
    inputType: "button",
    label: "Remove Test",
    icon: <MinusSquareOutlined />,
    onClick: "removeTest",
  },
  clearAllVariables: {
    type: "checkbox",
    inputType: "button",
    label: "Clear All Variables",
    icon: <ClearOutlined />,
    onClick: "clearAllVariables",
  },
};
