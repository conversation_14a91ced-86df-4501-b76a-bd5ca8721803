import {
  ApiOutlined,
  SettingOutlined,
  TableOutlined,
  InsertRowAboveOutlined,
  ControlOutlined,
} from "@ant-design/icons";
import { Form, Modal, Tabs } from "antd";
import React from "react";
import { useBoundStore } from "../../../../../../../src/store/store";
import Helper from "../../../../../../../src/utils/helper";
import ApiForm from "./forms/api_form";
import ChartControlsForm from "./forms/chart_controls_form";
import ColumnsForm from "./forms/columns_form";
import GridMenuForm from "./forms/grid_menu_form";
import GridToolbarForm from "./forms/grid_toolbar_form";
import SettingsForm from "./forms/settings_form";
import { ChartControlsFields } from "./form_fields/chart_controls_fields";
import { ComponentApiFields } from "./form_fields/component_api_fields";
import { ComponentSettingsFields } from "./form_fields/component_settings_fields";
import { GridColumnProps } from "./form_fields/grid_column_props";
import { GridMenuFields } from "./form_fields/grid_menu_fields";
import { GridToolbarFields } from "./form_fields/grid_toolbar_fields";

/**
 * Component options component to set component settings
 *
 * @param {object} component
 * @param {boolean} isComponentOptionsOpen
 * @param {function} setIsComponentOptionsOpen
 * @returns {JSX.Element}
 */
const ComponentOptions = ({
  component,
  isComponentOptionsOpen,
  setIsComponentOptionsOpen,
}) => {
  const { props } = component;
  const [apiForm] = Form.useForm();
  const [settingsForm] = Form.useForm();
  const [columnsForm] = Form.useForm();
  const [gridToolbarForm] = Form.useForm();
  const [gridMenuForm] = Form.useForm();
  const [chartControlsForm] = Form.useForm();
  const disableEditing = useBoundStore((state) => state.disableEditing);
  const templateId = useBoundStore((state) => state.templateId);
  const templateType = useBoundStore((state) => state.templateType);
  const templateRows = useBoundStore((state) => state.templateRows);

  const items = [];
  if (props !== undefined) {
    if (
      props.settings !== undefined &&
      ComponentSettingsFields[component.type] !== undefined
    ) {
      items.push({
        key: "settings",
        label: (
          <span>
            <SettingOutlined />
            Settings
          </span>
        ),
        children: (
          <SettingsForm
            form={settingsForm}
            fields={ComponentSettingsFields[component.type]}
            settings={props.settings}
            component={component}
          />
        ),
        className: "mt-2",
      });
    }

    if (props.params !== undefined && ComponentApiFields !== undefined) {
      items.push({
        key: "params",
        label: (
          <span>
            <ApiOutlined />
            API
          </span>
        ),
        children: (
          <ApiForm
            form={apiForm}
            fields={ComponentApiFields}
            params={props.params}
          />
        ),
        className: "mt-2",
      });
    }
  }

  if (component.type === "table") {
    items.push(
      {
        key: "columns",
        label: (
          <span>
            <TableOutlined />
            Columns
          </span>
        ),
        children: (
          <ColumnsForm
            form={columnsForm}
            fields={GridColumnProps}
            settings={props.settings}
          />
        ),
      },
      {
        key: "toolbar",
        label: (
          <span>
            <InsertRowAboveOutlined />
            Toolbar
          </span>
        ),
        children: (
          <GridToolbarForm
            form={gridToolbarForm}
            fields={GridToolbarFields}
            toolbar={props.toolbar}
            component={component}
          />
        ),
      },
      {
        key: "menu",
        label: (
          <span>
            <InsertRowAboveOutlined />
            Menu
          </span>
        ),
        children: (
          <GridMenuForm
            form={gridMenuForm}
            fields={GridMenuFields}
            toolbar={props.toolbar}
            component={component}
          />
        ),
      },
    );
  } else if (component.type === "chart") {
    items.push({
      key: "controls",
      label: (
        <span>
          <ControlOutlined />
          Controls
        </span>
      ),
      children: (
        <ChartControlsForm
          form={chartControlsForm}
          fields={ChartControlsFields}
          toolbar={props.toolbar}
          component={component}
        />
      ),
    });
  }

  /**
   * Set component options and update component props
   *
   */
  const setComponentOptions = () => {
    updateComponentSettings();
    updateComponentColumns();
    updateComponentGridToolbar();
    updateComponentGridMenu();
    updateComponentChartControls();
    setIsComponentOptionsOpen(false);
    if (templateType !== "preset") {
      Helper.draftUserAnalysisTemplate(templateId, templateRows);
    }
  };

  /**
   * Update component settings excluding column definitions for tables
   */
  const updateComponentSettings = () => {
    const settingsValues = settingsForm.getFieldsValue();

    Object.keys(settingsValues).forEach((settingsKey) => {
      const settingsValue = settingsValues[settingsKey];
      if (component.props.settings[settingsKey] !== settingsValue) {
        component.props.settings[settingsKey] = settingsValue;
      }
    });
  };

  /**
   * Update component column definitions for tables
   */
  const updateComponentColumns = () => {
    const columnsValues = columnsForm.getFieldsValue();
    const columns = component.props.settings.column_defs;
    Object.keys(columnsValues).forEach((valueKey) => {
      const columnValues = columnsValues[valueKey];
      const editedColumnArr = columns.filter((column) => {
        return column.field === valueKey;
      });
      const editedColumn = editedColumnArr.shift();
      if (editedColumn) {
        Object.keys(GridColumnProps).forEach((propKey) => {
          if (columnValues[propKey] !== editedColumn[propKey]) {
            if (
              columnValues[propKey] !== GridColumnProps[propKey].default &&
              columnValues[propKey] !== null
            ) {
              // update column property
              editedColumn[propKey] = columnValues[propKey];
            } else {
              // remove column property if same with default value or is null
              delete editedColumn[propKey];
            }
          }
        });
      }
    });
  };

  /**
   * Update component grid toolbar for tables
   */
  const updateComponentGridToolbar = () => {
    const toolbarOptions = gridToolbarForm.getFieldsValue();
    component.props.toolbar = [];
    Object.keys(toolbarOptions).forEach((optionKey) => {
      if (toolbarOptions[optionKey]) {
        component.props.toolbar.push({
          key: optionKey,
        });
      }
    });
  };

  /**
   * Update component grid menu for tables
   */
  const updateComponentGridMenu = () => {
    const menuOptions = gridMenuForm.getFieldsValue();
    component.props.menu = [];
    Object.keys(menuOptions).forEach((optionKey) => {
      if (menuOptions[optionKey]) {
        component.props.menu.push({
          key: optionKey,
        });
      }
    });
  };

  /**
   * Update component chart controls for charts
   */
  const updateComponentChartControls = () => {
    const controlsOptions = chartControlsForm.getFieldsValue();
    component.props.chart_controls = [];
    Object.keys(controlsOptions).forEach((optionKey) => {
      if (controlsOptions[optionKey]) {
        component.props.chart_controls.push({
          key: optionKey,
        });
      }
    });
  };

  /**
   * Cancel component options changes
   */
  const cancelComponentOptions = () => {
    setIsComponentOptionsOpen(false);
  };

  return (
    <Modal
      title="Component Options"
      open={isComponentOptionsOpen}
      forceRender
      onOk={setComponentOptions}
      onCancel={cancelComponentOptions}
      okButtonProps={{
        disabled: disableEditing ? true : false,
      }}
    >
      <Tabs size="small" defaultActiveKey="1" items={items} />
    </Modal>
  );
};

export default ComponentOptions;
