import { Form } from "antd";
import FormHelper from "../../../../../../../../src/utils/form_helper";

/**
 * Form to set grid toolbar
 *
 * @param {FormInstance} form
 * @param {object} fields
 * @param {object} toolbar
 * @param {object} component
 * @returns {JSX.Element}
 */
const GridToolbarForm = ({ form, fields, toolbar, component }) => {
  return (
    <Form form={form} size="small">
      {Object.keys(fields).reduce((result, key) => {
        if (
          !fields[key].includeIn ||
          (fields[key].includeIn &&
            fields[key].includeIn.indexOf(component.component) !== -1)
        ) {
          const value =
            toolbar !== undefined
              ? toolbar.filter((toolbarOption) => {
                  return toolbarOption.key === key;
                }).length > 0
              : false;

          result.push(FormHelper.getFormItem(fields[key], key, value));
        }
        return result;
      }, [])}
    </Form>
  );
};

export default GridToolbarForm;
