import { Form } from "antd";
import FormHelper from "../../../../../../../../src/utils/form_helper";

/**
 * Form to view API request parameters
 *
 * @param {FormInstance} form
 * @param {object} fields
 * @param {object} params
 * @returns {JSX.Element}
 */
const ApiForm = ({ form, fields, params }) => {
  return (
    <Form form={form} size="small">
      {Object.keys(fields).map((key) => {
        return FormHelper.getFormItem(fields[key], key, params[key]);
      })}
    </Form>
  );
};

export default ApiForm;
