import { Form, Tabs } from "antd";
import FormHelper from "../../../../../../../../src/utils/form_helper";

/**
 * Form to update column definitions for tables
 *
 * @param {FormInstance} form
 * @param {object} fields
 * @param {object} settings
 * @returns {JSX.Element}
 */
const ColumnsForm = ({ form, fields, settings }) => {
  /**
   * Get columns form tab items
   *
   * @returns {array} tabItems
   */
  const getColumnTabItems = () => {
    const columns = settings.column_defs;
    const tabItems = columns.map((column) => {
      return {
        // TODO: Find a way to avoid fixed pixel sizes
        className: "h-[calc(100vh-400px)] overflow-auto",
        label: column.headerName,
        key: column.field,
        children: createColumnFormItems(column),
      };
    });

    return tabItems;
  };

  /**
   * Generate column form items
   *
   * @param {object} column
   * @returns {JSX.Element}
   */
  const createColumnFormItems = (column) => {
    return Object.keys(fields).map((key) => {
      return FormHelper.getFormItem(
        fields[key],
        key,
        column[key],
        `${column.field}`,
        8,
        16,
      );
    });
  };

  return (
    <div>
      <Form form={form} size="small">
        <Tabs defaultActiveKey="1" items={getColumnTabItems()} />
      </Form>
    </div>
  );
};

export default ColumnsForm;
