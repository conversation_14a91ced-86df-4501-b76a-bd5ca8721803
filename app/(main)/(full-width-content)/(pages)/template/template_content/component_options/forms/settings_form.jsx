import { Form } from "antd";
import ChartOptionsForm from "../../../../../../../../src/utils/forms/chart_options_form";
import FormHelper from "../../../../../../../../src/utils/form_helper";

/**
 * Form to update component settings excluding column definitions for tables
 *
 * @param {FormInstance} form
 * @param {object} fields
 * @param {object} settings
 * @param {object} component
 * @returns {JSX.Element}
 */
const SettingsForm = ({ form, fields, settings, component }) => {
  return (
    <div>
      {component.type === "chart" ? (
        <ChartOptionsForm
          form={form}
          fields={fields}
          settings={settings}
          component={component}
          excludeFields={["legend"]}
        />
      ) : (
        <Form form={form} size="small" labelWrap>
          {Object.keys(fields).reduce((result, key) => {
            if (
              !fields[key].includeIn ||
              (fields[key].includeIn &&
                fields[key].includeIn.indexOf(component.component) !== -1)
            ) {
              if (fields[key].options) {
                const formItems = [
                  <Form.Item
                    key={key}
                    className="font-bold"
                    label={fields[key].label}
                    colon={false}
                    labelCol={{
                      span: 6,
                    }}
                  ></Form.Item>,
                ];
                formItems.push(
                  Object.keys(fields[key].options).map((optionKey) => {
                    const fieldValue =
                      settings[key] && settings[key][optionKey]
                        ? settings[key][optionKey]
                        : "";
                    return FormHelper.getFormItem(
                      fields[key].options[optionKey],
                      optionKey,
                      fieldValue,
                      [key],
                    );
                  }),
                );
                result.push(formItems);
              } else {
                result.push(
                  FormHelper.getFormItem(fields[key], key, settings[key]),
                );
              }
            }
            return result;
          }, [])}
        </Form>
      )}
    </div>
  );
};

export default SettingsForm;
