import {
  SettingOutlined,
  CloseOutlined,
  PlusOutlined,
  BorderOuterOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Col,
  Collapse,
  Form,
  Image,
  Input,
  Popconfirm,
  Popover,
  Space,
  Tabs,
} from "antd";
import { useDrop } from "react-dnd";
import React, { useCallback, useEffect, useRef, useState } from "react";
import Helper from "../../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../../src/store/store";
import { TemplateComponent } from "./template_component";
import { ItemTypes } from "./itemtypes";
import ComponentOptions from "./component_options/component_options";
import styles from "./styles.module.css";
import ColumnLayoutOptions from "./column_layout_options";
import ColumnContainerOptions from "./column_container_options";
import { TemplateRow } from "./template_row";

/**
 * Template column of a row
 *
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {string} colRowColKey
 * @param {string} colRowColRowKey
 * @param {string} colRowColRowColKey
 * @param {string} colRowColRowColRowKey
 * @param {string} colRowColRowColRowColKey
 * @param {int} colspan
 * @param {array} templateRows
 * @param {function} setTemplateRows
 * @param {string} tabKey
 * @param {string} subTabKey
 * @returns {JSX.Element}
 */
export const RowColumn = ({
  rowKey,
  colKey,
  colRowKey,
  colRowColKey,
  colRowColRowKey,
  colRowColRowColKey,
  colRowColRowColRowKey,
  colRowColRowColRowColKey,
  colspan,
  templateRows,
  setTemplateRows,
  tabKey,
  subTabKey,
}) => {
  const [isComponentOptionsOpen, setIsComponentOptionsOpen] = useState(false);
  const [tabActiveKey, setTabActiveKey] = useState(tabKey);
  const [tabItems, setTabItems] = useState([]);
  const newTabIndex = useRef(0);
  const disableEditing = useBoundStore((state) => state.disableEditing);
  const [containerOptionsForm] = Form.useForm();

  /**
   * Get column object
   *
   * @param {array} rows - template rows
   * @returns {object} column
   */
  const getColumn = (rows) => {
    const column = Helper.getTemplateColumnByIndex(
      rows,
      rowKey,
      colKey,
      colRowKey,
      colRowColKey,
      colRowColRowKey,
      colRowColRowColKey,
      colRowColRowColRowKey,
      colRowColRowColRowColKey,
      tabKey,
      subTabKey,
    );

    return column;
  };

  useEffect(() => {
    let column = getColumn(templateRows);
    if (column["tabs"] !== undefined) {
      const tabs = [];
      Object.keys(column["tabs"]).forEach((tabKey) => {
        tabs.push({
          label: (
            <Input
              defaultValue={column["tabs"][tabKey].label}
              onKeyDown={(e) => e.stopPropagation()}
              onChange={(e) => setTabLabel(e.target.value, tabKey)}
            />
          ),
          key: tabKey,
        });
        setTabActiveKey(tabKey);
        newTabIndex.current++;
      });
      setTabItems(tabs);
    }
  }, [templateRows]);

  /**
   * Generate component node to be rendered
   *
   * @param {object} item
   * @returns {JSX.Element}
   */
  const createComponent = (item) => {
    const { props } = item;
    const templateComponentKey =
      colRowColRowColRowColKey !== undefined
        ? `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}-${colRowColRowColRowKey}-${colRowColRowColRowColKey}`
        : colRowColRowColKey !== undefined
          ? `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}`
          : colRowColKey !== undefined
            ? `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}`
            : `${rowKey}-${colKey}`;

    return (
      <TemplateComponent
        key={templateComponentKey}
        id={templateComponentKey}
        index={templateComponentKey}
        props={props}
      >
        {item.type === "text" ? (
          Helper.getTextElement(item.value, item.text_type)
        ) : (
          <Card
            className="w-full h-full"
            title={
              item.props
                ? item.props.settings.title
                  ? item.props.settings.title
                  : item.display_name
                : ""
            }
          >
            {item.type === "text" ? (
              Helper.getTextElement(item.value, item.text_type)
            ) : (
              <Image src={item.preview_image} width={200} preview={false} />
            )}
          </Card>
        )}
      </TemplateComponent>
    );
  };

  /**
   * Move/Swap template component
   *
   * @param {object} component
   */
  const moveTemplateComponent = useCallback(
    (component) => {
      Helper.moveTemplateComponent(
        component,
        templateRows,
        setTemplateRows,
        rowKey,
        colKey,
        colRowKey,
        colRowColKey,
        colRowColRowKey,
        colRowColRowColKey,
        colRowColRowColRowKey,
        colRowColRowColRowColKey,
      );
    },
    [templateRows],
  );

  /**
   * Add component to template
   *
   * @param {object} component
   * @param {string} tabKey
   */
  const addTemplateComponent = useCallback(
    (component, tabKey, subTabKey) => {
      Helper.addTemplateComponent(
        undefined,
        component,
        templateRows,
        setTemplateRows,
        rowKey,
        colKey,
        colRowKey,
        colRowColKey,
        colRowColRowKey,
        colRowColRowColKey,
        colRowColRowColRowKey,
        colRowColRowColRowColKey,
        tabKey,
        subTabKey,
      );
    },
    [templateRows],
  );

  /**
   * Hook to wire an element to be a drop target
   * https://react-dnd.github.io/react-dnd/docs/api/use-drop
   *
   * @params {object} spec - specification object
   * @params {array} deps - dependency array used for memoization
   * @returns {array} [collectedProps, dropTargetRef]
   */
  const [, drop] = useDrop(
    () => ({
      accept: ItemTypes.COMPONENT,
      drop: (item, monitor) => {
        if (!item.index) {
          // add component to row column
          // copy item component to avoid mutation to original component list
          if (monitor.isOver({ shallow: true })) {
            const component = Helper.cloneObject(item);
            addTemplateComponent(component, tabKey, subTabKey);
          }
        } else {
          // move or swap template component
          if (monitor.isOver({ shallow: true })) {
            moveTemplateComponent(item);
          }
        }
      },
      collect: (monitor) => ({
        isOver: monitor.isOver(),
        isOverCurrent: monitor.isOver({ shallow: true }),
        canDrop: monitor.canDrop(),
      }),
    }),
    [templateRows, tabKey],
  );

  /**
   * Generate component option button
   *
   * @param {object} props
   * @returns {React.DetailedReactHTMLElement}
   */
  const createComponentOption = (props) => {
    return React.createElement(Button, {
      type: "primary",
      shape: "circle",
      ...props,
    });
  };

  /**
   * Generate content of column
   *
   * @param {string} contentTabKey
   * @returns {JSX.Element}
   */
  const createColumnContent = (contentTabKey) => {
    let column = getColumn(templateRows);
    return (
      <>
        <div className="absolute right-0 mt-2 mr-4 z-10 invisible group-hover/column:visible">
          <Space>
            {!disableEditing ? (
              <>
                <Popover
                  placement="bottomRight"
                  content={
                    <ColumnLayoutOptions
                      rowKey={rowKey}
                      colKey={colKey}
                      colRowKey={colRowKey}
                      colRowColKey={colRowColKey}
                      colRowColRowKey={colRowColRowKey}
                      colRowColRowColKey={colRowColRowColKey}
                      colRowColRowColRowKey={colRowColRowColRowKey}
                      colRowColRowColRowColKey={colRowColRowColRowColKey}
                      templateRows={templateRows}
                      setTemplateRows={setTemplateRows}
                      tabKey={Helper.getTabKey(tabKey, contentTabKey)}
                      subTabKey={Helper.getSubTabKey(
                        tabKey,
                        subTabKey,
                        contentTabKey,
                      )}
                    />
                  }
                  trigger="click"
                >
                  <Button
                    type="primary"
                    shape="circle"
                    icon={<PlusOutlined />}
                  />
                </Popover>
                <Popover
                  placement="bottomRight"
                  content={
                    <ColumnContainerOptions
                      form={containerOptionsForm}
                      containerType={
                        column["container"]
                          ? column["container"]["type"]
                          : "none"
                      }
                      containerTitle={
                        column["container"] ? column["container"]["title"] : ""
                      }
                    />
                  }
                  trigger="click"
                  onOpenChange={onToggleContainerOptions}
                >
                  <Button
                    type="primary"
                    shape="circle"
                    icon={<BorderOuterOutlined />}
                  />
                </Popover>
              </>
            ) : (
              <>
                <Button
                  type="primary"
                  shape="circle"
                  icon={<PlusOutlined />}
                  disabled
                />
                <Button
                  type="primary"
                  shape="circle"
                  icon={<BorderOuterOutlined />}
                  disabled
                />
              </>
            )}
            {column.component && column.component.type !== "text" && (
              <div>
                <ComponentOptions
                  component={column["component"]}
                  isComponentOptionsOpen={isComponentOptionsOpen}
                  setIsComponentOptionsOpen={setIsComponentOptionsOpen}
                />
                <Space>
                  {createComponentOption({
                    icon: <SettingOutlined />,
                    onClick: () => {
                      setIsComponentOptionsOpen(true);
                    },
                  })}
                  <Popconfirm
                    title="Remove the component"
                    description="Are you sure to remove this component?"
                    placement="topRight"
                    onConfirm={clearColumnComponent}
                    okText="Yes"
                    cancelText="No"
                    disabled={disableEditing ? true : false}
                  >
                    {createComponentOption({
                      icon: <CloseOutlined />,
                      danger: true,
                      disabled: disableEditing ? true : false,
                    })}
                  </Popconfirm>
                </Space>
              </div>
            )}
          </Space>
        </div>
        {contentTabKey !== undefined && column["tabs"] !== undefined ? (
          <div className={`${styles.columnLayout} p-2 border-dashed`}>
            {/* tab component */}
            {column["tabs"][contentTabKey] &&
              column["tabs"][contentTabKey].component &&
              createComponent(column["tabs"][contentTabKey].component)}

            {/* tab rows */}
            {/* TODO: To add additional rows? */}
            {column["tabs"][contentTabKey].rows &&
              column["tabs"][contentTabKey].rows.map(
                (columnRowColumnRow, index) => {
                  // copy column row to avoid mutation to original column row layout
                  const columnRowColumnRowCopy =
                    Helper.cloneObject(columnRowColumnRow);
                  const { columns } = columnRowColumnRowCopy;
                  return (
                    <TemplateRow
                      key={`${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${index}`}
                      templateRow={columnRowColumnRow}
                      rowKey={rowKey}
                      colKey={colKey}
                      colRowKey={colRowKey}
                      colRowColKey={colRowColKey}
                      colRowColRowKey={index}
                      colRowColRowColKey={colRowColRowColKey}
                      colRowColRowColRowKey={colRowColRowColRowKey}
                      colRowColRowColRowColKey={colRowColRowColRowColKey}
                      columns={columns}
                      templateRows={templateRows}
                      setTemplateRows={setTemplateRows}
                      tabKey={Helper.getTabKey(tabKey, contentTabKey)}
                      subTabKey={Helper.getSubTabKey(
                        tabKey,
                        subTabKey,
                        contentTabKey,
                      )}
                    />
                  );
                },
              )}
          </div>
        ) : (
          <div className={`${styles.columnLayout} p-2 border-dashed`}>
            {column.component && createComponent(column.component)}
            {column.rows &&
              column.rows.map((columnRow, index) => {
                // copy column row to avoid mutation to original column row layout
                const columnRowCopy = Helper.cloneObject(columnRow);
                const { columns } = columnRowCopy;
                return colRowColRowColRowColKey !== undefined ? (
                  // adding row on 4rd level is not supported
                  <></>
                ) : colRowColRowColKey !== undefined ? (
                  <TemplateRow
                    key={`${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}-${index}-${colRowColRowColRowColKey}`}
                    templateRow={columnRow}
                    rowKey={rowKey}
                    colKey={colKey}
                    colRowKey={colRowKey}
                    colRowColKey={colRowColKey}
                    colRowColRowKey={colRowColRowKey}
                    colRowColRowColKey={colRowColRowColKey}
                    colRowColRowColRowKey={index}
                    colRowColRowColRowColKey={colRowColRowColRowColKey}
                    columns={columns}
                    templateRows={templateRows}
                    setTemplateRows={setTemplateRows}
                    tabKey={Helper.getTabKey(tabKey, contentTabKey)}
                    subTabKey={Helper.getSubTabKey(
                      tabKey,
                      subTabKey,
                      contentTabKey,
                    )}
                  />
                ) : colRowColKey !== undefined ? (
                  <TemplateRow
                    key={`${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${index}-${colRowColRowColKey}-${colRowColRowColRowKey}-${colRowColRowColRowColKey}`}
                    templateRow={columnRow}
                    rowKey={rowKey}
                    colKey={colKey}
                    colRowKey={colRowKey}
                    colRowColKey={colRowColKey}
                    colRowColRowKey={index}
                    colRowColRowColKey={colRowColRowColKey}
                    colRowColRowColRowKey={colRowColRowColRowKey}
                    colRowColRowColRowColKey={colRowColRowColRowColKey}
                    columns={columns}
                    templateRows={templateRows}
                    setTemplateRows={setTemplateRows}
                    tabKey={Helper.getTabKey(tabKey, contentTabKey)}
                    subTabKey={Helper.getSubTabKey(
                      tabKey,
                      subTabKey,
                      contentTabKey,
                    )}
                  />
                ) : (
                  <TemplateRow
                    key={`${rowKey}-${colKey}-${index}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}-${colRowColRowColRowKey}-${colRowColRowColRowColKey}`}
                    templateRow={columnRow}
                    rowKey={rowKey}
                    colKey={colKey}
                    colRowKey={index}
                    colRowColKey={colRowColKey}
                    colRowColRowKey={colRowColRowKey}
                    colRowColRowColKey={colRowColRowColKey}
                    colRowColRowColRowKey={colRowColRowColRowKey}
                    colRowColRowColRowColKey={colRowColRowColRowColKey}
                    columns={columns}
                    templateRows={templateRows}
                    setTemplateRows={setTemplateRows}
                    tabKey={Helper.getTabKey(tabKey, contentTabKey)}
                    subTabKey={Helper.getSubTabKey(
                      tabKey,
                      subTabKey,
                      contentTabKey,
                    )}
                  />
                );
              })}
          </div>
        )}
      </>
    );
  };

  /**
   * Generate tabs
   *
   * @returns {JSX.Element}
   */
  const createTabs = () => {
    let tabs = [];
    tabItems.forEach((item) => {
      tabs.push({
        label: item.label,
        children: createColumnContent(item.key),
        key: item.key,
      });
    });
    return (
      <Tabs
        type="editable-card"
        onChange={onTabChange}
        activeKey={tabActiveKey}
        onEdit={onTabEdit}
        items={tabs}
      />
    );
  };

  /**
   * Remove component from column
   */
  const clearColumnComponent = () => {
    let column = getColumn(templateRows);
    if (column["component"]) {
      let templateRowsCopy = Helper.cloneObject(templateRows);
      column = getColumn(templateRowsCopy);
      if (column["component"]["type"] === "text") {
        // remove row
        let rows =
          tabKey !== undefined && colRowColRowKey !== undefined
            ? templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
                "columns"
              ][colRowColKey]["tabs"][tabKey]["rows"]
            : colRowColRowKey !== undefined
              ? templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
                  "columns"
                ][colRowColKey]["rows"]
              : colRowKey !== undefined
                ? templateRowsCopy[rowKey]["columns"][colKey]["rows"]
                : templateRowsCopy;
        const rowsKey =
          colRowColRowKey !== undefined
            ? colRowColRowKey
            : colRowKey !== undefined
              ? colRowKey
              : rowKey;
        rows.splice(rowsKey, 1);
      } else {
        // clear component but retain column
        column["component"] = null;
      }
      setTemplateRows(templateRowsCopy);
    }
  };

  /**
   * Triggers when visibility of container options popover change
   *
   * @param {boolean} isOpen
   */
  const onToggleContainerOptions = (isOpen) => {
    if (!isOpen) {
      const values = containerOptionsForm.getFieldsValue();
      const templateRowsCopy = Helper.cloneObject(templateRows);
      let column = getColumn(templateRowsCopy);
      column["container"] = {};
      column["container"]["type"] = values.type;
      column["container"]["title"] = values.title;
      if (values.type === "tabs" && column["tabs"] === undefined) {
        column["tabs"] = {};
      }
      setTemplateRows(templateRowsCopy);
    }
  };

  /**
   * Set active key when changing tab
   *
   * @param {string} newTabActiveKey
   */
  const onTabChange = (newTabActiveKey) => {
    setTabActiveKey(newTabActiveKey);
  };

  /**
   * Set tab label
   *
   * @param {string} label
   * @param {string} tabKey
   */
  const setTabLabel = (label, tabKey) => {
    const templateRowsCopy = Helper.cloneObject(templateRows);
    let column = getColumn(templateRowsCopy);
    if (column["tabs"][tabKey] === undefined) {
      column["tabs"][tabKey] = {};
    }
    column["tabs"][tabKey]["label"] = label;
    setTemplateRows(templateRowsCopy);
  };

  /**
   * Add new tab
   */
  const addTab = () => {
    const newTabActiveKey = `newTab${newTabIndex.current++}`;
    const newPanes = [...tabItems];
    newPanes.push({
      label: (
        <Input
          onKeyDown={(e) => e.stopPropagation()}
          onChange={(e) => setTabLabel(e.target.value, newTabActiveKey)}
        />
      ),
      key: newTabActiveKey,
    });
    setTabItems(newPanes);
    setTabActiveKey(newTabActiveKey);

    const templateRowsCopy = Helper.cloneObject(templateRows);
    let column = getColumn(templateRowsCopy);
    column["tabs"][newTabActiveKey] = {};
    setTemplateRows(templateRowsCopy);
  };

  /**
   * Remove tab
   *
   * @param {string} targetKey
   */
  const removeTab = (targetKey) => {
    let newTabActiveKey = tabActiveKey;
    let lastIndex = -1;
    tabItems.forEach((item, i) => {
      if (item.key === targetKey) {
        lastIndex = i - 1;
      }
    });
    const newPanes = tabItems.filter((item) => item.key !== targetKey);
    if (newPanes.length && newTabActiveKey === targetKey) {
      if (lastIndex >= 0) {
        newTabActiveKey = newPanes[lastIndex].key;
      } else {
        newTabActiveKey = newPanes[0].key;
      }
    }
    setTabItems(newPanes);
    setTabActiveKey(newTabActiveKey);
  };

  /**
   * Callback function when adding or removing a tab
   *
   * @param {string} targetKey
   * @param {string} action
   */
  const onTabEdit = (targetKey, action) => {
    if (action === "add") {
      addTab();
    } else {
      removeTab(targetKey);
    }
  };

  const columnKey =
    colRowColRowColKey !== undefined
      ? `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}`
      : colRowColKey !== undefined
        ? `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}`
        : `${rowKey}-${colKey}`;
  let column = getColumn(templateRows);

  return (
    <Col
      className="group/column"
      ref={drop}
      key={columnKey}
      span={colspan}
      data-testid="content"
    >
      {column["container"] && column["container"]["type"] === "collapse" ? (
        <Collapse
          defaultActiveKey={
            column["container"]["collapsed"] !== true ? ["1"] : []
          }
          items={[
            {
              key: "1",
              label: column["container"]["title"],
              children: createColumnContent(tabKey),
            },
          ]}
        />
      ) : column["container"] && column["container"]["type"] === "card" ? (
        <Card
          title={
            column["container"]["title"] ? column["container"]["title"] : null
          }
        >
          {createColumnContent(tabKey)}
        </Card>
      ) : column["container"] && column["container"]["type"] === "tabs" ? (
        createTabs()
      ) : (
        createColumnContent(tabKey)
      )}
    </Col>
  );
};
