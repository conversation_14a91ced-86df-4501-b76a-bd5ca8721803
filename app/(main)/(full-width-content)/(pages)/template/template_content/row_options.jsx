import { SettingOutlined, FilterOutlined } from "@ant-design/icons";
import { Form, Modal, Tabs } from "antd";
import React from "react";
import { useBoundStore } from "../../../../../../src/store/store";
import Helper from "../../../../../../src/utils/helper";
import RowSettingsForm from "./row_options/forms/row_settings_form";
import { RowSettingsFields } from "./row_options/form_fields/row_settings_fields";
import RowOptionsForm from "./row_options/forms/row_options_form";
import { RowOptionsFields } from "./row_options/form_fields/row_options_fields";

/**
 * Row options to set row data filters and other settings
 *
 * @param {boolean} isRowSettingsOpen
 * @param {function} setIsRowSettingsOpen
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {string} colRowColKey
 * @param {string} colRowColRowKey
 * @param {string} colRowColRowColKey
 * @param {string} colRowColRowColRowKey
 * @param {string} tabKey
 * @param {string} subTabKey
 * @returns {JSX.Element}
 */
const RowOptions = ({
  isRowSettingsOpen,
  setIsRowSettingsOpen,
  rowKey,
  colKey,
  colRowKey,
  colRowColKey,
  colRowColRowKey,
  colRowColRowColKey,
  colRowColRowColRowKey,
  tabKey,
  subTabKey,
}) => {
  const [settingsForm] = Form.useForm();
  const [rowOptionsForm] = Form.useForm();
  const disableEditing = useBoundStore((state) => state.disableEditing);
  const templateId = useBoundStore((state) => state.templateId);
  const templateType = useBoundStore((state) => state.templateType);
  const templateRows = useBoundStore((state) => state.templateRows);
  const setTemplateRows = useBoundStore((state) => state.setTemplateRows);

  const items = [];
  items.push({
    key: "settings",
    label: (
      <span>
        <SettingOutlined />
        Settings
      </span>
    ),
    children: (
      <RowSettingsForm
        form={settingsForm}
        fields={RowSettingsFields}
        rowKey={rowKey}
        colKey={colKey}
        colRowKey={colRowKey}
        colRowColKey={colRowColKey}
        colRowColRowKey={colRowColRowKey}
        colRowColRowColKey={colRowColRowColKey}
        colRowColRowColRowKey={colRowColRowColRowKey}
        tabKey={tabKey}
        subTabKey={subTabKey}
      />
    ),
    className: "mt-2",
  });

  items.push({
    key: "options",
    label: (
      <span>
        <FilterOutlined />
        Options
      </span>
    ),
    children: (
      <RowOptionsForm
        form={rowOptionsForm}
        fields={RowOptionsFields}
        rowKey={rowKey}
        colKey={colKey}
        colRowKey={colRowKey}
        colRowColKey={colRowColKey}
        colRowColRowKey={colRowColRowKey}
        colRowColRowColKey={colRowColRowColKey}
        colRowColRowColRowKey={colRowColRowColRowKey}
        tabKey={tabKey}
      />
    ),
  });

  /**
   * Set row options and update template rows and draft template
   */
  const setRowOptions = () => {
    updateRowSettings();
    updateRowOptions();
    setIsRowSettingsOpen(false);
    if (templateType !== "preset") {
      Helper.draftUserAnalysisTemplate(templateId, templateRows);
    }
  };

  /**
   * Update row settings
   */
  const updateRowSettings = () => {
    const values = rowOptionsForm.getFieldsValue();
    const templateRowsCopy = Helper.cloneObject(templateRows);
    const rowData =
      tabKey !== undefined && colRowColRowColRowKey !== undefined
        ? templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
            "columns"
          ][colRowColKey]["tabs"][tabKey]["rows"][colRowColRowKey]["columns"][
            colRowColRowColKey
          ]["rows"][colRowColRowColRowKey]
        : tabKey !== undefined && colRowColRowKey !== undefined
          ? templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
              "columns"
            ][colRowColKey]["tabs"][tabKey]["rows"][colRowColRowKey]
          : colRowColRowKey !== undefined
            ? templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
                "columns"
              ][colRowColKey]["rows"][colRowColRowKey]
            : colRowKey !== undefined
              ? templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey]
              : templateRowsCopy[rowKey];
    rowData["title"] = values.title;
    rowData["info_message"] = values.info_message;
    setTemplateRows(templateRowsCopy);
  };

  /**
   * Update row options
   */
  const updateRowOptions = () => {
    const rowOptions = rowOptionsForm.getFieldsValue();
    const templateRowsCopy = Helper.cloneObject(templateRows);
    const rowData =
      tabKey !== undefined && colRowColRowColRowKey !== undefined
        ? templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
            "columns"
          ][colRowColKey]["tabs"][tabKey]["rows"][colRowColRowKey]["columns"][
            colRowColRowColKey
          ]["rows"][colRowColRowColRowKey]
        : tabKey !== undefined && colRowColRowKey !== undefined
          ? templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
              "columns"
            ][colRowColKey]["tabs"][tabKey]["rows"][colRowColRowKey]
          : colRowColRowKey !== undefined
            ? templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
                "columns"
              ][colRowColKey]["rows"][colRowColRowKey]
            : colRowKey !== undefined
              ? templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey]
              : templateRowsCopy[rowKey];
    rowData.options = [];
    Object.keys(rowOptions).forEach((optionKey) => {
      if (rowOptions[optionKey]) {
        rowData.options.push({
          key: optionKey,
        });
      }
    });
    setTemplateRows(templateRowsCopy);
  };

  /**
   * Cancel row options changes
   */
  const cancelRowOptions = () => {
    setIsRowSettingsOpen(false);
  };

  return (
    <Modal
      title="Row Options"
      open={isRowSettingsOpen}
      forceRender
      onOk={setRowOptions}
      onCancel={cancelRowOptions}
      okButtonProps={{
        disabled: disableEditing ? true : false,
      }}
    >
      <Tabs size="small" defaultActiveKey="1" items={items} />
    </Modal>
  );
};

export default RowOptions;
