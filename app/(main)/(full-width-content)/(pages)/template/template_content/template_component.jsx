import { useDrag } from "react-dnd";
import { useBoundStore } from "../../../../../../src/store/store";
import styles from "./styles.module.css";
import { ItemTypes } from "./itemtypes";

/**
 * Template component
 *
 * @param {string} id
 * @param {string} index
 * @param {object} props
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
export const TemplateComponent = ({ id, index, props, children }) => {
  const disableEditing = useBoundStore((state) => state.disableEditing);

  const [, drag] = useDrag(
    () => ({
      type: ItemTypes.COMPONENT,
      item: { id, index, props },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    }),
    [id, index, props],
  );

  return (
    <div
      ref={!disableEditing ? drag : null}
      className={`${styles.templateComponent} ${
        disableEditing ? "cursor-auto" : ""
      }`}
      style={
        props !== undefined
          ? {
              ...props.settings,
              width: props.settings.width ? props.settings.width : "100%",
              height: props.settings.height ? props.settings.height : "auto",
            }
          : {}
      }
      data-testid="box"
    >
      {children}
    </div>
  );
};
