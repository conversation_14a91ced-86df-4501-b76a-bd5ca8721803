import { Form, Input, Modal, Select, Typography } from "antd";
import React, { memo, useEffect } from "react";
import Helper from "../../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../../src/store/store";
import { ComponentBlueprint } from "../../../../../../src/utils/components/component_blueprint";

const { Option } = Select;
const { Title } = Typography;

const layout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

/**
 * Text options to be used when adding text in template
 *
 * @param {object}
 * @returns {JSX.Element}
 */
const TextOptions = ({
  templateRows,
  setTemplateRows,
  templateContentBottomRef,
  isTextOptionsOpen,
  setIsTextOptionsOpen,
}) => {
  const previousTemplateRows = useBoundStore(
    (state) => state.previousTemplateRows,
  );
  const setPreviousTemplateRows = useBoundStore(
    (state) => state.setPreviousTemplateRows,
  );
  const disableEditing = useBoundStore((state) => state.disableEditing);
  const [textOptionsForm] = Form.useForm();

  useEffect(() => {
    if (templateRows.length > previousTemplateRows.length) {
      templateContentBottomRef.current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
    setPreviousTemplateRows(templateRows);
  }, [templateRows]);

  /**
   * Open text options to be inserted on template row
   */
  const insertText = () => {
    const values = textOptionsForm.getFieldsValue();
    const component = ComponentBlueprint.text;

    component.value = values.text;
    component.text_type = values.text_type;

    const colspans = [24];
    Helper.addTemplateRow(
      {
        colspans: colspans,
        component: component,
        type: "text",
      },
      templateRows,
      setTemplateRows,
    );
    setIsTextOptionsOpen(false);
  };

  /**
   * Cancel adding of text
   */
  const cancelAddText = () => {
    textOptionsForm.resetFields();
    setIsTextOptionsOpen(false);
  };

  return (
    <Modal
      title="Add Text"
      open={isTextOptionsOpen}
      onOk={insertText}
      onCancel={cancelAddText}
      okButtonProps={{
        disabled: disableEditing ? true : false,
      }}
    >
      <Form {...layout} form={textOptionsForm} name="text-options-form">
        <Form.Item
          name="text"
          label="Text"
          rules={[
            {
              required: true,
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item name="text_type" label="Type">
          <Select optionLabelProp="label">
            <Option value="h1" label="Heading 1">
              <Title level={1}>Heading 1</Title>
            </Option>
            <Option value="h2" label="Heading 2">
              <Title level={2}>Heading 2</Title>
            </Option>
            <Option value="h3" label="Heading 3">
              <Title level={3}>Heading 3</Title>
            </Option>
            <Option value="h4" label="Heading 4">
              <Title level={4}>Heading 4</Title>
            </Option>
            <Option value="h5" label="Heading 5">
              <Title level={5}>Heading 5</Title>
            </Option>
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default memo(TextOptions);
