import { Collapse, notification } from "antd";
import { memo, useCallback, useState } from "react";
import Helper from "../../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../../src/store/store";
import Api from "../../../../../../src/utils/api";
import { useEffectApiFetch } from "../../../../../../src/hooks";
import { Component } from "./component";
import styles from "./styles.module.css";

/**
 * Page template components list
 *
 * @returns {JSX.Element}
 */
const Components = () => {
  const [components, setComponents] = useState({});
  const templateAnalysis = useBoundStore((state) => state.templateAnalysis);

  useEffectApiFetch(
    () => {
      return getAnalysisTemplateComponents();
    },
    () => {
      setComponents({});
    },
    [templateAnalysis],
  );

  /**
   * Get analysis template components
   *
   * @returns {AbortController}
   */
  const getAnalysisTemplateComponents = () => {
    return Api.getAnalysisTemplateComponents(
      (res) => {
        if (res.success) {
          groupAnalysisComponentsByType(res.data);
        } else {
          notification.warning({
            message: "Analysis Template Components",
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: "Analysis Template Components",
          description: err,
        });
      },
      templateAnalysis,
    );
  };

  /**
   * Group analysis components by type
   */
  const groupAnalysisComponentsByType = useCallback(
    (analysisComponents) => {
      let componentsByType = {};
      Object.keys(analysisComponents).forEach((analysis) => {
        componentsByType[analysis] = {};
        const types = analysisComponents[analysis]
          .map((component) => {
            return component.type;
          })
          .filter((type, index, types) => {
            return types.indexOf(type) === index;
          });
        types.forEach((type) => {
          componentsByType[analysis][type] = analysisComponents[
            analysis
          ].filter((component) => {
            return component.type === type;
          });
        });
      });
      setComponents(componentsByType);
    },
    [templateAnalysis],
  );

  /**
   * Generate component list collapsible items
   *
   * @param {string} analysis
   * @param {string} analysisIndex
   * @returns {array} items
   */
  const getComponentList = (analysis, analysisIndex) => {
    const items = [
      {
        key: analysisIndex,
        label: Helper.titlelize(analysis),
        children: Object.keys(components[analysis]).map((type, typeIndex) => {
          return (
            <Collapse
              ghost
              key={typeIndex}
              items={[
                {
                  key: `${analysisIndex}-${typeIndex}`,
                  label: Helper.titlelize(type),
                  children: Object.values(components[analysis][type]).map(
                    (component) => (
                      <Component
                        key={component.name}
                        id={component.name}
                        {...component}
                      />
                    ),
                  ),
                },
              ]}
            />
          );
        }),
      },
    ];

    return items;
  };

  return (
    <div className={styles.componentList}>
      {Object.keys(components).map((analysis, analysisIndex) => {
        return (
          <Collapse
            ghost
            key={analysisIndex}
            defaultActiveKey={0}
            items={getComponentList(analysis, analysisIndex)}
          />
        );
      })}
    </div>
  );
};

export default memo(Components);
