import { HolderOutlined } from "@ant-design/icons";
import { useDrag } from "react-dnd";
import { useBoundStore } from "../../../../../../src/store/store";
import { ItemTypes } from "../template_content/itemtypes";

/**
 * Draggable component in component list
 *
 * @param {object} component
 * @returns {JSX.Element}
 */
export const Component = (component) => {
  const disableEditing = useBoundStore((state) => state.disableEditing);

  /**
   * Hook to wire an element to be a drag source or draggable
   * https://react-dnd.github.io/react-dnd/docs/api/use-drag
   *
   * @params {object} spec - specification object
   * @returns {array} [collectedProps, dragSourceRef]
   */
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.COMPONENT,
    item: component,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
      handlerId: monitor.getHandlerId(),
    }),
  }));
  const opacity = isDragging ? 0.4 : 1;

  return (
    <div
      ref={!disableEditing ? drag : null}
      className={`my-1 p-1 border border-solid border-gray-600 bg-white ${
        disableEditing ? "cursor-auto" : "cursor-move"
      }`}
      style={{ opacity }}
      data-testid={`box`}
    >
      <HolderOutlined /> {component.display_name}
    </div>
  );
};
