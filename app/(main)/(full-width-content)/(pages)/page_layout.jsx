import "./page_layout.css";
import { Col, Config<PERSON><PERSON><PERSON>, Float<PERSON>utton, Layout, Row, theme } from "antd";
import { ArrowUpOutlined } from "@ant-design/icons";
import { useEffect, useMemo, useState } from "react";
import PageOptions from "../../page_options";
import PageTitle from "../../page_title";
import { useBoundStore } from "../../../../src/store/store";
import ChartOptionsDrawer from "../../../../src/utils/forms/chart_options/chart_options_drawer";
import { PageMapper } from "../../page_mapper";

const { Content, Sider } = Layout;
const { useToken } = theme;

/**
 * Layout component of page
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const PageLayout = ({ children }) => {
  const [pageTitle, setPageTitle] = useState();
  const [visibilityHeight, setVisibilityHeight] = useState(400);
  const mainContentRef = useBoundStore((state) => state.mainContentRef);
  const setMainContentRef = useBoundStore((state) => state.setMainContentRef);
  const currentPageData = useBoundStore((state) => state.currentPageData);
  const isChartOptionsOpen = useBoundStore((state) => state.isChartOptionsOpen);
  const pageMapper = useMemo(() => PageMapper, [currentPageData]);
  const { token } = useToken();

  useEffect(() => {
    const title =
      pageMapper[currentPageData.key] &&
      pageMapper[currentPageData.key].pageTitle &&
      pageMapper[currentPageData.key].showTitle !== false
        ? pageMapper[currentPageData.key].pageTitle
        : null;
    setPageTitle(title);
  }, [currentPageData.key]);

  // Hide the float button when chart options drawer is open
  useEffect(() => {
    setVisibilityHeight(isChartOptionsOpen ? 10000 : 400);
  }, [isChartOptionsOpen]);

  return (
    <ConfigProvider
      theme={{
        components: {
          Layout: {
            siderBg: "rgba(233, 237, 251)",
          },
        },
      }}
    >
      <Layout className="page-layout h-full">
        <Content className="main-content">
          {pageTitle && (
            <Row
              className="page-nav-wrapper"
              style={{
                backgroundColor: token.colorBgContainer,
              }}
            >
              <Col span={12}>
                <PageTitle pageTitle={pageTitle} />
              </Col>
              <Col span={12} className="text-right pr-8">
                <PageOptions />
              </Col>
            </Row>
          )}
          <div ref={setMainContentRef} className="content">
            {children}
            <FloatButton.BackTop
              type="primary"
              icon={<ArrowUpOutlined />}
              target={() => mainContentRef}
              visibilityHeight={visibilityHeight}
            />
          </div>
        </Content>
        <Sider
          className="flex"
          collapsible
          defaultCollapsed={false}
          collapsed={!isChartOptionsOpen}
          collapsedWidth={0}
          width={330}
        >
          <ChartOptionsDrawer open={isChartOptionsOpen}></ChartOptionsDrawer>
        </Sider>
      </Layout>
    </ConfigProvider>
  );
};
export default PageLayout;
