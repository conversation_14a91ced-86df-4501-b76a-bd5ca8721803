"use client";

import { ExclamationCircleOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { App, Button, Flex, Form, Input, Modal } from "antd";
import { reportNameRules } from "../../../../../src/utils/antd_validation_rules";
import { useSaveReport } from "../../../../../src/hooks/useSaveReport";
import { usePublishNpiReport } from "../../../../../src/hooks/usePublishNpiReport";
import { useGetReportData } from "../../../../../src/hooks/useGetReportData";
import { getPageParams } from "../../../../../src/utils/components/recipe_common/helpers";

const filterNames = [
  "recipe_category",
  "dsk",
  "recipe_key",
  "recipe_name",
  "src_type",
  "src_value",
];

const { TextArea } = Input;

/**
 * NPI report options component
 *
 * @returns {JSX.Element}
 */
const NpiReportOptions = () => {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const [saveReportFilters, setSaveReportFilters] = useState({
    recipe_type: "",
  });
  const [currentReportKey, setCurrentReportKey] = useState();
  const [pageFilters, setPageFilters] = useState({});
  const [reportDataFilters, setReportDataFilters] = useState({});
  const [reportDetails, setReportDetails] = useState({});
  const [reportIsPublished, setReportIsPublished] = useState(false);
  const [{ confirm }, contextHolder] = Modal.useModal();
  const { data: reportData, dataUpdatedAt: reportDataUpdatedAt } =
    useGetReportData(reportDataFilters);
  const reportOptions = reportData?.data?.report_options ?? {};
  const reportKey = reportData?.data?.npi_recipe_report_key;

  useEffect(() => {
    const { pageFilters } = getPageParams();
    setPageFilters(pageFilters);
    setReportDataFilters({ ...pageFilters, recipe_type: "npi" });
  }, []);

  useEffect(() => {
    if (reportData?.data?.report_name) {
      setReportDetails({
        report_name: reportData.data.report_name,
        report_description: reportData.data.report_description,
      });

      const filters = { ...pageFilters };
      filterNames.forEach((filter) => {
        if (reportData.data[filter]) {
          filters[filter] = reportData.data[filter];
        }
      });
      setPageFilters(filters);
      setReportIsPublished(reportData.data.is_published === 1);
    }
  }, [reportDataUpdatedAt]);

  useEffect(() => {
    form.setFieldsValue(reportDetails);
  }, [reportDetails]);

  const { data: saveReportData, dataUpdatedAt: saveReportDataUpdatedAt } =
    useSaveReport(saveReportFilters);

  useEffect(() => {
    if (saveReportData?.success) {
      message.success("Report was saved successfully.");
    }
  }, [saveReportDataUpdatedAt]);

  /**
   * Save the report
   */
  const handleSaveReport = () => {
    const values = form.getFieldsValue();
    setSaveReportFilters({
      recipe_type: "npi",
      ...pageFilters,
      ...values,
      report_options: { ...reportOptions, ...values },
    });
  };

  const publishSuccessful = usePublishNpiReport(currentReportKey);

  useEffect(() => {
    if (publishSuccessful) {
      setReportIsPublished(true);
    }
  }, [publishSuccessful]);

  /**
   * Confirm publishing of report
   */
  const confirmPublishReport = () => {
    confirm({
      title: "Publish Report",
      content: (
        <>
          Are you sure you want to publish the report?
          <br />
          <br />
          <ExclamationCircleOutlined /> The page will be reloaded.
        </>
      ),
      okText: "Publish",
      cancelText: "Cancel",
      onOk() {
        setCurrentReportKey(reportKey);
      },
    });
  };

  return (
    <div className="w-full h-full">
      {contextHolder}
      <Form form={form}>
        <Flex gap={"middle"}>
          <Form.Item
            name="report_name"
            className="w-64"
            rules={reportNameRules}
          >
            <Input placeholder="Report Name" disabled />
          </Form.Item>
          <Button
            onClick={confirmPublishReport}
            type="primary"
            disabled={reportIsPublished}
          >
            Publish Report
          </Button>
        </Flex>
        <Form.Item name="report_description" className="w-1/2">
          <TextArea
            placeholder="Report Description"
            rows={4}
            onBlur={handleSaveReport}
            disabled={reportIsPublished}
          />
        </Form.Item>
      </Form>
    </div>
  );
};

export default NpiReportOptions;
