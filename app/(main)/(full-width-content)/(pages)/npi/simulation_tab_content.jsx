"use client";

import { Collapse, Form, theme } from "antd";
import { useCallback, useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import Api from "../../../../../src/utils/api";
import { useEffectApiFetch } from "../../../../../src/hooks";
import SimulatedItems from "./simulated_items";
import SimulationOptions from "./simulation_options";
import AnalysisSetOptions from "./analysis_set_options";

/**
 * Simulation tab component
 *
 * @param {string} pageKey
 * @param {object} pageFilters
 * @param {string} uniqueTnum
 * @param {array} conditionOptions
 * @param {object} requiredParams
 * @param {boolean} isNextBtnDisabled
 * @returns {JSX.Element}
 */
const SimulationTabContent = ({
  pageKey,
  pageFilters,
  uniqueTnum,
  conditionOptions = [],
  requiredParams = {},
  isNextBtnDisabled,
}) => {
  const { token } = theme.useToken();
  const [simulateOptionsForm] = Form.useForm();
  const panelStyle = {
    marginBottom: 16,
    background: token.yhHeaderColorBg,
    borderRadius: token.borderRadius,
  };
  const [activeCollapseKey, setActiveCollapseKey] = useState([
    "simulate_options",
  ]);
  const [allFormValues, setAllFormValues] = useState([]);
  const [simulationOptions, setSimulationOptions] = useState();
  const [selectedAnalysisSet, setSelectedAnalysisSet] = useState("");
  const [loadAnalysisSetTime, setLoadAnalysisSetTime] = useState(Date.now());

  useEffectApiFetch(
    async () => {
      const response = await Api.fetchData(
        `/api/v1/npi/simulation/options/${pageFilters.recipe_category}`,
        "get",
        {},
      );
      setSimulationOptions(response.data);
    },
    () => {
      setSimulationOptions();
    },
    [],
  );

  /**
   * Generate button handler
   *
   * @param {object} form
   */
  const generateTab = useCallback((form) => {
    const values = form.getFieldsValue();
    if (values.append_new_items) {
      setAllFormValues((prevState) => {
        return [...prevState, values];
      });
    } else {
      setAllFormValues([values]);
    }
  }, []);

  const { data: analysisSetToLoad, dataUpdatedAt: analysisSetToLoadUpdatedAt } =
    useQuery({
      queryKey: [
        "get_analysis_set",
        selectedAnalysisSet,
        pageFilters,
        loadAnalysisSetTime,
      ],
      queryFn: async () => {
        return await Api.fetchData(`/api/v1/npi/analysis_set/load`, "POST", {
          ...pageFilters,
          analysis_set_name: selectedAnalysisSet,
        });
      },
      enabled: selectedAnalysisSet !== "",
    });

  useEffect(() => {
    if (analysisSetToLoad?.success) {
      setAllFormValues(analysisSetToLoad.data);
    }
  }, [analysisSetToLoadUpdatedAt]);

  /**
   * Load an analysis set
   *
   * @param {object} form
   */
  const loadAnalysisSet = (form) => {
    const analysisSet = form.getFieldValue("analysis_set");
    if (analysisSet) {
      setSelectedAnalysisSet(analysisSet);
      setLoadAnalysisSetTime(Date.now());
    }
  };

  return (
    simulationOptions && (
      <>
        <Collapse
          activeKey={activeCollapseKey}
          onChange={(key) => {
            setActiveCollapseKey(key);
          }}
          style={{
            background: token.colorBgContainer,
          }}
          items={[
            {
              key: "simulate_options",
              label: "Simulate Options",
              style: panelStyle,
              children: (
                <SimulationOptions
                  generateBtnHandler={generateTab}
                  conditionOptions={conditionOptions}
                  pageFilters={pageFilters}
                  form={simulateOptionsForm}
                  simulationOptions={simulationOptions}
                />
              ),
            },
          ]}
        />
        <AnalysisSetOptions
          pageKey={pageKey}
          analysisSetValues={allFormValues}
          pageFilters={pageFilters}
          loadAnalysisSetBtnHandler={loadAnalysisSet}
          applyFilters={requiredParams}
          isNextBtnDisabled={isNextBtnDisabled}
        />
        <SimulatedItems
          pageKey={pageKey}
          pageFilters={pageFilters}
          uniqueTnum={uniqueTnum}
          allFormValues={allFormValues}
          setAllFormValues={setAllFormValues}
          requiredParams={requiredParams}
        />
      </>
    )
  );
};

export default SimulationTabContent;
