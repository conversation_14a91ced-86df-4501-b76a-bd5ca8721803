"use client";

import { Form, Input } from "antd";

const { TextArea } = Input;

/**
 * Per test description
 *
 * @returns {JSX.Element}
 */
const PerTestDescription = () => {
  const [form] = Form.useForm();
  return (
    <div className="w-full h-full">
      <Form form={form} className="!mt-1">
        <Form.Item name="test_description">
          <TextArea placeholder="Test Description" rows={4} />
        </Form.Item>
      </Form>
    </div>
  );
};

export default PerTestDescription;
