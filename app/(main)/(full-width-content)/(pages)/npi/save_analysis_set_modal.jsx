"use client";

import { Button, Form, Input, Modal, Popconfirm } from "antd";
import { useEffect, useState } from "react";
import { reportNameRules } from "../../../../../src/utils/antd_validation_rules";
import { useSaveAnalysisSet } from "../../../../../src/hooks/useSaveAnalysisSet";
import { useCheckAnalysisSet } from "../../../../../src/hooks/useCheckAnalysisSet";

/**
 * Save analysis set modal component
 *
 * @param {boolean} open
 * @param {function} setModalOpen
 * @param {array} analysisSetValues
 * @param {object} pageFilters
 * @param {object} applyFilters
 * @param {function} successCallback
 * @returns {JSX.Element}
 */
const SaveAnalysisSetModal = ({
  open,
  setModalOpen,
  analysisSetValues,
  pageFilters,
  applyFilters,
  successCallback,
}) => {
  const [form] = Form.useForm();
  const [isSaveBtnDisabled, setIsSaveBtnDisabled] = useState(true);
  const [saveFilters, setSaveFilters] = useState({ recipe_type: "" });
  const [saveApplyFilters, setSaveApplyFilters] = useState({ recipe_type: "" });
  const [checkAnalysisSetFilters, setCheckAnalysisSetFilters] = useState({});
  const [
    checkAnalysisSetBeforeApplyFilters,
    setCheckAnalysisSetBeforeApplyFilters,
  ] = useState({});
  const [isOverwritePopconfirmOpen, setIsOverwritePopconfirmOpen] =
    useState(false);
  const [
    isOverwriteBeforeApplyPopconfirmOpen,
    setIsOverwriteBeforeApplyPopconfirmOpen,
  ] = useState(false);

  const { success: saveAnalysisSetSuccess } = useSaveAnalysisSet(saveFilters);
  const { success: saveApplyAnalysisSetSuccess } =
    useSaveAnalysisSet(saveApplyFilters);

  useEffect(() => {
    if (saveAnalysisSetSuccess || saveApplyAnalysisSetSuccess) {
      successCallback(
        saveApplyAnalysisSetSuccess,
        form.getFieldValue("analysis_set_name"),
      );
    }
  }, [saveAnalysisSetSuccess, saveApplyAnalysisSetSuccess]);

  /**
   * Handle the saving and applying of analysis set
   */
  const handleSaveAndApply = () => {
    setSaveApplyFilters({
      recipe_type: "npi",
      analysis_set_data: analysisSetValues,
      ...form.getFieldsValue(),
      ...pageFilters,
      ...applyFilters,
    });
    setIsOverwriteBeforeApplyPopconfirmOpen(false);
    setModalOpen(false);
  };

  /**
   * Handle the saving of the analysis set
   */
  const handleSave = () => {
    setSaveFilters({
      recipe_type: "npi",
      analysis_set_data: analysisSetValues,
      ...form.getFieldsValue(),
      ...pageFilters,
    });
    setIsOverwritePopconfirmOpen(false);
    setModalOpen(false);
  };

  const {
    dataUpdatedAt: checkAnalysisSetUpdatedAt,
    data: checkAnalysisSetData,
  } = useCheckAnalysisSet(checkAnalysisSetFilters);

  useEffect(() => {
    if (checkAnalysisSetData?.success) {
      if (checkAnalysisSetData.data) {
        setIsOverwritePopconfirmOpen(true);
      } else {
        handleSave();
      }
    }
  }, [checkAnalysisSetUpdatedAt]);

  /**
   * Check the analysis set name if already exists
   */
  const checkAnalysisSetName = () => {
    setCheckAnalysisSetFilters({
      ...form.getFieldsValue(),
      ...pageFilters,
      time_initiated: Date.now(),
    });
  };

  const {
    dataUpdatedAt: checkAnalysisSetBeforeApplyUpdatedAt,
    data: checkAnalysisSetBeforeApplyData,
  } = useCheckAnalysisSet(checkAnalysisSetBeforeApplyFilters);

  useEffect(() => {
    if (checkAnalysisSetBeforeApplyData?.success) {
      if (checkAnalysisSetBeforeApplyData.data) {
        setIsOverwriteBeforeApplyPopconfirmOpen(true);
      } else {
        handleSaveAndApply();
      }
    }
  }, [checkAnalysisSetBeforeApplyUpdatedAt]);

  /**
   * Check the analysis set name if already exists
   */
  const checkAnalysisSetNameBeforeApply = () => {
    setCheckAnalysisSetBeforeApplyFilters({
      ...form.getFieldsValue(),
      ...pageFilters,
      time_initiated: Date.now(),
    });
  };

  /**
   * When user types in the analysis set name input
   */
  const handleAnalysisSetChange = () => {
    const name = form.getFieldValue("analysis_set_name");
    setIsSaveBtnDisabled(name === "");
  };

  /**
   * When user cancels the modal
   */
  const handleCancel = () => {
    setIsOverwriteBeforeApplyPopconfirmOpen(false);
    setModalOpen(false);
  };

  return (
    <>
      <Modal
        title="Save Analysis Set"
        open={open}
        onCancel={handleCancel}
        destroyOnHidden
        footer={[
          <Button key="back" onClick={handleCancel}>
            Cancel
          </Button>,
          <Popconfirm
            key="submit"
            title="Name Already Exists!"
            description="What do you want to do?"
            okText="Overwrite Existing"
            cancelText="Rename"
            open={isOverwritePopconfirmOpen}
            onConfirm={handleSave}
            onCancel={() => {
              setIsOverwritePopconfirmOpen(false);
            }}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
          >
            <Button
              type="primary"
              onClick={checkAnalysisSetName}
              disabled={isSaveBtnDisabled}
            >
              Save
            </Button>
          </Popconfirm>,
          <Popconfirm
            key="submit_apply"
            title="Name Already Exists!"
            description="What do you want to do?"
            okText="Overwrite Existing"
            cancelText="Rename Analysis Set"
            open={isOverwriteBeforeApplyPopconfirmOpen}
            onConfirm={handleSaveAndApply}
            onCancel={() => {
              setIsOverwriteBeforeApplyPopconfirmOpen(false);
            }}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
          >
            <Button
              onClick={checkAnalysisSetNameBeforeApply}
              disabled={isSaveBtnDisabled}
            >
              Save and Apply
            </Button>
          </Popconfirm>,
        ]}
      >
        <Form form={form}>
          <Form.Item name="analysis_set_name" rules={reportNameRules} required>
            <Input
              placeholder="Analysis Set Name"
              onChange={handleAnalysisSetChange}
            ></Input>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default SaveAnalysisSetModal;
