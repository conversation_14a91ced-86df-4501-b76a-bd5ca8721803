"use client";

import { useEffect, useRef, useState } from "react";
import {
  Button,
  Checkbox,
  Flex,
  Form,
  Input,
  InputNumber,
  Select,
  Space,
  Typography,
} from "antd";
import {
  generateDateNowHash,
  maxTagPlaceholder,
} from "../../../../../src/utils/components/recipe_common/helpers";
import { useGetAnalysisSetList } from "../../../../../src/hooks/useGetAnalysisSetList";
import { reportNameRules } from "../../../../../src/utils/antd_validation_rules";
import { useEffectApiFetch } from "../../../../../src/hooks";
import Api from "../../../../../src/utils/api";
import HighlightOptionColorPicker from "../../../../../src/utils/components/highlight_option_color_picker";
import LoadSavedReportModal from "./load_saved_report_modal";

const { TextArea } = Input;
const { Text } = Typography;
/**
 * Report options component
 *
 * @param {object} form
 * @param {object} pageFilters
 * @param {function} setIsNextBtnDisabled
 * @returns {JSX.Element}
 */
const ReportOptions = ({ form, pageFilters, setIsNextBtnDisabled }) => {
  const [loadSavedReportModalOpen, setLoadSavedReportModalOpen] =
    useState(false);
  const [reportOptions, setReportOptions] = useState();
  const [enabledHighlightOptions, setEnabledHighlightOptions] = useState({});
  const defaultTestSummaryType = useRef(null);

  useEffectApiFetch(
    async () => {
      const response = await Api.fetchData(
        `/api/v1/npi/report/options/${pageFilters.recipe_category}`,
        "get",
        {},
      );
      setReportOptions(response.data);
    },
    () => {
      setReportOptions();
    },
    [],
  );

  useEffect(() => {
    let recipeCategoryLabel = "NPI";
    switch (pageFilters?.recipe_category) {
      case "npi_for_char":
        recipeCategoryLabel = "Characterization";
        break;
      case "npi_for_gage":
        recipeCategoryLabel = "Gage-RR";
        break;
      case "npi_for_die_drift":
        recipeCategoryLabel = "Die-Drift";
        break;
    }
    form.setFieldsValue({
      report_name: `${recipeCategoryLabel}-Report_${generateDateNowHash()}`,
    });
  }, []);

  const { data: analysisSetList } = useGetAnalysisSetList(pageFilters);

  useEffect(() => {
    if (analysisSetList?.data?.length) {
      form.setFieldValue(
        "analysis_set",
        analysisSetList.data.map((option) => option.value),
      );
      setIsNextBtnDisabled(false);
    }
  }, [analysisSetList?.data]);

  useEffect(() => {
    if (reportOptions?.test_summary_tables?.length > 0) {
      defaultTestSummaryType.current =
        pageFilters?.recipe_category === "npi_for_die_drift"
          ? reportOptions.test_summary_tables[1].value
          : reportOptions.test_summary_tables[0].value;
      form.setFieldValue("test_summary_types", [
        defaultTestSummaryType.current,
      ]);
    }
  }, [reportOptions]);

  /**
   * Called when any field is changed in the form
   *
   * @param {array} changedFields
   */
  const handleFieldsChange = (changedFields) => {
    // Do not allow unchecking of all test summary checkboxes
    if (
      changedFields[0].name.includes("test_summary_types") &&
      changedFields[0].value.length === 0
    ) {
      form.setFieldValue("test_summary_types", [
        defaultTestSummaryType.current ??
          reportOptions.test_summary_tables[0].value,
      ]);
    }

    const values = form.getFieldsValue();
    setIsNextBtnDisabled(
      values.report_name === "" || values.analysis_set.length === 0,
    );
  };

  /**
   * Toggle highlight options enabled state
   *
   * @param {string} key
   * @param {boolean} checked
   */
  const toggleHighlightOption = (key, checked) => {
    setEnabledHighlightOptions((prev) => ({
      ...prev,
      [key]: checked,
    }));
  };

  /**
   * Checkbox to enable/disable highlight option
   *
   * @param {object} props
   * @returns {JSX.Element}
   */
  const HighlightOptionCheckbox = (props) => {
    return (
      <Form.Item name={props.option.value} valuePropName="checked">
        <Checkbox
          checked={enabledHighlightOptions[props.option.value]}
          onChange={(e) =>
            toggleHighlightOption(props.option.value, e.target.checked)
          }
        >
          {props.label ?? ""}
        </Checkbox>
      </Form.Item>
    );
  };

  /**
   * Options for highlighting items
   *
   * @returns {JSX.Element}
   */
  const HighlightOptions = () => {
    const highlightOptions = [];
    reportOptions?.highlights?.forEach((option) => {
      const optionField = [];
      switch (option.input_type) {
        case "inputNumber":
          optionField.push(
            <HighlightOptionCheckbox option={option} />,
            <Form.Item name={option.input_name} label={option.label}>
              <InputNumber disabled={!enabledHighlightOptions[option.value]} />
            </Form.Item>,
          );
          break;
        default:
          optionField.push(
            <HighlightOptionCheckbox option={option} label={option.label} />,
          );
      }
      if (option.color_name) {
        optionField.push(
          <HighlightOptionColorPicker
            inputName={option.color_name}
            disabled={!enabledHighlightOptions[option.value]}
          />,
        );
      }
      highlightOptions.push(<Space>{optionField}</Space>);
    });

    return <Space size="large">{highlightOptions}</Space>;
  };

  return (
    <>
      <LoadSavedReportModal
        open={loadSavedReportModalOpen}
        setModalOpen={setLoadSavedReportModalOpen}
        pageFilters={pageFilters}
        loadReportCallback={(values) => {
          form.resetFields();
          const highlightOptionCheckboxes = Array.isArray(
            reportOptions?.highlights,
          )
            ? reportOptions.highlights.map((option) => option.value)
            : [];
          highlightOptionCheckboxes.forEach((key) => {
            toggleHighlightOption(key, values[key] ?? false);
          });
          form.setFieldsValue(values);
        }}
      />
      <Form
        form={form}
        className="mt-3 mb-3"
        onFieldsChange={handleFieldsChange}
      >
        <Flex gap="middle">
          <Form.Item
            name="report_name"
            className="w-48"
            rules={reportNameRules}
          >
            <Input placeholder="Report Name" />
          </Form.Item>
          <Button
            onClick={() => {
              setLoadSavedReportModalOpen(true);
            }}
          >
            Load Report
          </Button>
        </Flex>
        <Form.Item name="report_description" className="w-1/2">
          <TextArea placeholder="Report Description" rows={4} />
        </Form.Item>
        <Flex gap={"middle"} align="center">
          <Form.Item name="analysis_set" className="w-56">
            <Select
              mode="multiple"
              placeholder="Select Analysis set"
              options={analysisSetList?.data ?? []}
              allowClear={true}
              maxTagCount="responsive"
              maxTagPlaceholder={maxTagPlaceholder}
              showSearch
            ></Select>
          </Form.Item>
          <Flex vertical>
            <Text>Include Test Summary</Text>
            <Form.Item name="test_summary_types">
              <Checkbox.Group
                options={reportOptions?.test_summary_tables ?? []}
              ></Checkbox.Group>
            </Form.Item>
          </Flex>
        </Flex>
        <HighlightOptions />
      </Form>
    </>
  );
};

export default ReportOptions;
