"use client";

import { useEffect, useState, useRef } from "react";
import { useBoundStore } from "../../../../../src/store/store";
import { useGetGridBlueprint } from "../../../../../src/hooks/useGetGridBlueprint";
import YHGrid from "../../../../../src/utils/grid/yh_grid";
import TestsBasedOnSelectForm from "./tests_based_on_select_form";

/**
 * Review tests step component
 *
 * @param {string} pageKey
 * @param {object} gridFilters
 * @returns {JSX.Element}
 */
const ReviewTestsStep = ({ pageKey, gridFilters }) => {
  const [gridKey, setGridKey] = useState(pageKey);
  const [reviewStepGridFilters, setReviewStepGridFilters] =
    useState(gridFilters);
  const npiReviewTestsGridRef = useRef();
  const [npiReviewTestsGridComponent, setNpiReviewTestsGridComponent] =
    useState();
  const npiRecipeData = useBoundStore((state) => state.npiRecipeData);
  const { data: gridBlueprintData, dataUpdatedAt: gridBlueprintDataUpdatedAt } =
    useGetGridBlueprint(pageKey, "npi_review_tests");

  useEffect(() => {
    if (gridBlueprintData?.success) {
      setNpiReviewTestsGridComponent(gridBlueprintData.data);
    }
  }, [gridBlueprintDataUpdatedAt]);

  useEffect(() => {
    if (npiRecipeData.recipe_data?.linked_by) {
      const filters = {};
      filters[pageKey] = {
        ...gridFilters[pageKey],
        linked_by: npiRecipeData.recipe_data.linked_by,
      };
      setReviewStepGridFilters(filters);
    }
  }, [npiRecipeData.recipe_data?.linked_by]);

  return (
    <div>
      <TestsBasedOnSelectForm pageKey={pageKey} setGridKey={setGridKey} />
      {npiReviewTestsGridComponent && (
        <YHGrid
          key={gridKey}
          wrapperClassName="flex grow flex-col h-full"
          gridRef={npiReviewTestsGridRef}
          gridId={`${pageKey}_${npiReviewTestsGridComponent.name}`}
          pageKey={pageKey}
          component={npiReviewTestsGridComponent}
          filters={reviewStepGridFilters}
        />
      )}
    </div>
  );
};

export default ReviewTestsStep;
