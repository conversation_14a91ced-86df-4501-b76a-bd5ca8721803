"use client";

import { <PERSON>, <PERSON>lapse, Row, theme } from "antd";
import DatalogInfoGrid from "../../../../../src/utils/components/recipe_common/datalog_info_grid";
import SelectedConditions from "./selected_conditions";
import AvailableConditions from "./available_conditions";
import { NpiMapper } from "./npi_mapper";

/**
 * Read and create conditions step component
 *
 * @param {string} pageKey
 * @param {object} pageFilters
 * @param {function} setDisableSkipToSimulateBtn
 * @param {function} setDisableNextBtn
 * @returns {JSX.Element}
 */
const ReadCreateConditionsStep = ({
  pageKey,
  pageFilters,
  setDisableSkipToSimulateBtn,
  setDisableNextBtn,
}) => {
  const { token } = theme.useToken();
  const panelStyle = {
    marginBottom: 16,
    background: token.yhHeaderColorBg,
    borderRadius: token.borderRadius,
  };

  return (
    <>
      <Row gutter={16} className="mb-3">
        <Col span={24}>
          <Collapse
            style={{
              background: token.colorBgContainer,
            }}
            items={[
              {
                key: "dlog_info",
                label: `Datalog Information NPI Recipe: ${NpiMapper.recipe_category_label[pageFilters.recipe_category] ?? ""}`,
                style: panelStyle,
                children: (
                  <DatalogInfoGrid
                    pageKey={pageKey}
                    pageFilters={pageFilters}
                    gridId="metadata_dlog_info"
                  />
                ),
              },
            ]}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <AvailableConditions
            pageFilters={pageFilters}
            setDisableSkipToSimulateBtn={setDisableSkipToSimulateBtn}
            setDisableNextBtn={setDisableNextBtn}
          />
        </Col>
        <Col span={12}>
          <SelectedConditions
            pageFilters={pageFilters}
            setDisableSkipToSimulateBtn={setDisableSkipToSimulateBtn}
            setDisableNextBtn={setDisableNextBtn}
          />
        </Col>
      </Row>
    </>
  );
};

export default ReadCreateConditionsStep;
