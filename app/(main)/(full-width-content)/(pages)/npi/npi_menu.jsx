"use client";

import { useState } from "react";
import { Dropdown, Space, Tooltip, Typography } from "antd";
import { DownOutlined } from "@ant-design/icons";
import { useQueryClient } from "@tanstack/react-query";
import Helper from "../../../../../src/utils/helper";
import { useEffectApiFetch } from "../../../../../src/hooks";
import CreateRecipeModal from "./create_recipe_modal";
import LoadRecipeModal from "./load_recipe_modal";
import AddToRecipeModal from "./add_to_recipe_modal";

const createRecipeLabel = "Create Recipe";
const loadRecipeLabel = "Load Recipe";
const addToRecipeLabel = "Add to Recipe";
/**
 * NPI menu component
 *
 * @param {object} searchGridRef
 * @param {function} getSearchTableSelection
 * @returns {JSX.Element}
 */
const NPIMenu = ({ searchGridRef, getSearchTableSelection }) => {
  const queryClient = useQueryClient();
  const [selectedParams, setSelectedParams] = useState({});
  const [selectedDlogCount, setSelectedDlogCount] = useState(0);
  const [isCreateRecipeModalOpen, setIsCreateRecipeModalOpen] = useState(false);
  const [isLoadRecipeModalOpen, setIsLoadRecipeModalOpen] = useState(false);
  const [isAddToRecipeModalOpen, setIsAddToRecipeModalOpen] = useState(false);
  const [presetAnalysisTemplates, setPresetAnalysisTemplates] = useState([]);

  useEffectApiFetch(
    () => {
      return Helper.getPresetAnalysisTemplates(setPresetAnalysisTemplates);
    },
    () => {
      setPresetAnalysisTemplates([]);
    },
  );

  /**
   * Renders the create page
   *
   * @param {object} selectedParams
   */
  const renderCreatePage = (selectedParams) => {
    const key = "npi_recipe";
    Helper.generateAnalysis(
      key,
      selectedParams,
      presetAnalysisTemplates,
      queryClient,
    );
  };

  const menuItemHandlers = {
    create: () => {
      setIsCreateRecipeModalOpen(true);
    },
    load: () => {
      setIsLoadRecipeModalOpen(true);
    },
    addTo: () => {
      setIsAddToRecipeModalOpen(true);
    },
  };

  /**
   * Handle the metadata menu items
   *
   * @param {string} key
   */
  const handleMenuItem = ({ key }) => {
    const selectedParams = getSearchTableSelection();
    if (selectedParams.search_selection) {
      delete selectedParams.search_selection;
    }
    setSelectedParams({ ...selectedParams });
    menuItemHandlers[key](selectedParams);
  };

  const menuItems = [
    {
      key: "create",
      label:
        selectedDlogCount > 0 ? (
          createRecipeLabel
        ) : (
          <Tooltip title="Select at least one datalog">
            <span>{createRecipeLabel}</span>
          </Tooltip>
        ),
      disabled: selectedDlogCount === 0,
    },
    {
      key: "load",
      label: loadRecipeLabel,
    },
    {
      key: "addTo",
      label:
        selectedDlogCount > 0 ? (
          addToRecipeLabel
        ) : (
          <Tooltip title="Select at least one datalog">
            <span>{addToRecipeLabel}</span>
          </Tooltip>
        ),
      disabled: selectedDlogCount === 0,
    },
  ];

  /**
   * Enable/disable menu items based on dlog selection
   */
  const validateMenuItems = () => {
    const selectedParams = getSearchTableSelection();
    setSelectedDlogCount(selectedParams?.dsk?.length ?? 0);
  };

  return (
    <>
      <CreateRecipeModal
        isModalOpen={isCreateRecipeModalOpen}
        setIsModalOpen={setIsCreateRecipeModalOpen}
        selectedParams={selectedParams}
        renderCreatePage={renderCreatePage}
      />
      <LoadRecipeModal
        isModalOpen={isLoadRecipeModalOpen}
        setIsModalOpen={setIsLoadRecipeModalOpen}
        selectedParams={selectedParams}
        renderRecipePage={renderCreatePage}
      />
      <AddToRecipeModal
        isModalOpen={isAddToRecipeModalOpen}
        setIsModalOpen={setIsAddToRecipeModalOpen}
        selectedParams={selectedParams}
        renderRecipePage={renderCreatePage}
        searchGridRef={searchGridRef}
      />
      <Dropdown
        menu={{ items: menuItems, onClick: handleMenuItem }}
        trigger="click"
        onClick={validateMenuItems}
      >
        <Typography.Text className="cursor-pointer">
          <Space>
            NPI
            <DownOutlined />
          </Space>
        </Typography.Text>
      </Dropdown>
    </>
  );
};

export default NPIMenu;
