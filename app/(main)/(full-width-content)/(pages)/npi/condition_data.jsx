"use client";

import { useRef, useState, useEffect } from "react";
import { getPageParams } from "../../../../../src/utils/components/recipe_common/helpers";
import { useGetGridBlueprint } from "../../../../../src/hooks/useGetGridBlueprint";
import YHGrid from "../../../../../src/utils/grid/yh_grid";

/**
 * Map condition data keys to match with set request parameter key
 */
const conditionDataKeysMap = {
  condition_type: "cond_type",
};

/**
 * Retrieving of condition data
 *
 * @param {string} gridId
 * @param {object} conditionData
 * @returns {JSX.Element}
 */
const ConditionData = ({ gridId, conditionData }) => {
  const { pageKey, gridFilters } = getPageParams();
  const conditionDataGridRef = useRef();
  const [conditionDataGridComponent, setConditionDataGridComponent] =
    useState();
  const { data: gridBlueprintData, dataUpdatedAt: gridBlueprintDataUpdatedAt } =
    useGetGridBlueprint(pageKey, gridId);

  useEffect(() => {
    mapConditionData(conditionData);
  }, []);

  useEffect(() => {
    if (gridBlueprintData?.success) {
      setConditionDataGridComponent(gridBlueprintData.data);
    }
  }, [gridBlueprintDataUpdatedAt]);

  /**
   * Set condition data values based on key map
   *
   * @param {object} conditionData
   */
  const mapConditionData = (conditionData) => {
    Object.keys(conditionDataKeysMap).forEach((mapKey) => {
      if (conditionData[mapKey]) {
        conditionData[conditionDataKeysMap[mapKey]] = conditionData[mapKey];
      }
    });
  };

  return (
    <div className={"h-[25vh]"}>
      {conditionDataGridComponent && (
        <YHGrid
          wrapperClassName="flex grow flex-col h-full"
          gridRef={conditionDataGridRef}
          gridId={`${pageKey}_${conditionDataGridComponent.name}`}
          pageKey={pageKey}
          component={conditionDataGridComponent}
          filters={gridFilters}
          initialGridFilters={conditionData}
        />
      )}
    </div>
  );
};

export default ConditionData;
