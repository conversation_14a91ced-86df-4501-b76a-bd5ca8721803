"use_client";

import { <PERSON><PERSON>, <PERSON>lex, Mo<PERSON>, Tooltip, Typography } from "antd";
import { DeleteOutlined } from "@ant-design/icons";
import { useEffect, useRef, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import Helper from "../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../src/store/store";
import { useDeleteNpiReport } from "../../../../../src/hooks/useDeleteNpiReport";
import { useGetGridBlueprint } from "../../../../../src/hooks/useGetGridBlueprint";
import YHGrid from "../../../../../src/utils/grid/yh_grid";
import { NpiMapper } from "./npi_mapper";

const { Text } = Typography;
const pageKey = "recipes";

/**
 * NPI reports modal component
 *
 * @param {boolean} isModalOpen
 * @param {boolean} setIsModalOpen
 * @param {object} activeRecipeData
 * @returns {JSX.Element}
 */
const NpiReportsModal = ({ isModalOpen, setIsModalOpen, activeRecipeData }) => {
  const gridRef = useRef();
  const [gridFilters, setGridFilters] = useState({});
  const [reportKey, setReportKey] = useState();
  const presetAnalysisTemplates = useBoundStore(
    (state) => state.presetAnalysisTemplates,
  );
  const queryClient = useQueryClient();
  const npiRecipeReportsGridRef = useRef();
  const npiRecipeReportsGridWrapperRef = useRef();
  const [npiRecipeReportsGridComponent, setNpiRecipeReportsGridComponent] =
    useState();
  const { data: gridBlueprintData, dataUpdatedAt: gridBlueprintDataUpdatedAt } =
    useGetGridBlueprint(pageKey, "npi_recipe_reports_table");

  useEffect(() => {
    if (gridBlueprintData?.success) {
      setNpiRecipeReportsGridComponent(gridBlueprintData.data);
    }
  }, [gridBlueprintDataUpdatedAt]);

  useEffect(() => {
    if (activeRecipeData?.recipes?.recipe_name) {
      const filters = {};
      filters[pageKey] = {
        recipe_name: activeRecipeData.recipes.recipe_name,
        recipe_key: activeRecipeData.recipes.recipe_key,
      };
      setGridFilters(filters);
    }
  }, [activeRecipeData?.recipes?.recipe_name]);

  useEffect(() => {
    if (npiRecipeReportsGridWrapperRef.current?.isGridReady() === true) {
      npiRecipeReportsGridWrapperRef.current?.reloadGridData();
    }
  }, [gridFilters]);

  const { dataUpdatedAt: deleteNpiReportUpdatedAt, data: deleteNpiReportData } =
    useDeleteNpiReport(reportKey);

  // Remove the deleted report from the current row data
  useEffect(() => {
    if (deleteNpiReportData?.success) {
      const rowData = [];
      gridRef.current.api.forEachNode((rowNode) => {
        if (rowNode.data.npi_recipe_report_key !== reportKey) {
          rowData.push(rowNode.data);
        }
      });
      gridRef.current.api.setGridOption("rowData", rowData);
    }
  }, [deleteNpiReportUpdatedAt]);

  const cellRenderers = {
    clickableColumn: (params) => {
      return (
        <Tooltip title="View report.">
          <Button
            type="link"
            onClick={() => {
              Helper.generateAnalysis(
                NpiMapper.recipe_category_page_key[
                  activeRecipeData.recipes.recipe_category_value
                ],
                {
                  report_key: params.data.npi_recipe_report_key,
                },
                presetAnalysisTemplates,
                queryClient,
                null,
                false,
                true,
              );
            }}
          >
            {params.value}
          </Button>
        </Tooltip>
      );
    },
    customActionColumn: (params) => {
      return (
        params.data.is_published_timestamp === "" && (
          <Tooltip title="Delete report.">
            <DeleteOutlined
              onClick={() => {
                setReportKey(params.data.npi_recipe_report_key);
              }}
            />
          </Tooltip>
        )
      );
    },
  };

  /**
   * Event handler when users cancels/closes the modal
   */
  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal
      title="Recipe Reports"
      open={isModalOpen}
      onCancel={closeModal}
      width={"75vw"}
      destroyOnHidden
      footer={[
        <Button key="back" type="primary" onClick={closeModal}>
          Close
        </Button>,
      ]}
    >
      <Flex vertical className="mt-2 mb-2">
        <Text type="secondary">
          Recipe Name: {activeRecipeData?.recipes?.recipe_name}
        </Text>
        <Text type="secondary">
          Date Created: {activeRecipeData?.recipes?.date_created}
        </Text>
        <Text type="secondary">
          Last Modified: {activeRecipeData?.recipes?.date_updated}
        </Text>
      </Flex>
      <div className={"h-[25vh]"}>
        {npiRecipeReportsGridComponent && (
          <YHGrid
            wrapperClassName="flex grow flex-col h-full"
            ref={npiRecipeReportsGridWrapperRef}
            gridRef={npiRecipeReportsGridRef}
            gridId={`${pageKey}_${npiRecipeReportsGridComponent.name}`}
            pageKey={pageKey}
            component={npiRecipeReportsGridComponent}
            filters={gridFilters}
            cellRenderers={cellRenderers}
          />
        )}
      </div>
    </Modal>
  );
};

export default NpiReportsModal;
