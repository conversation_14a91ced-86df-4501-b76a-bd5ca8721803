"use client";

import { useEffect, useState, useRef } from "react";
import { Button, Form, Select } from "antd";
import { useGetGridBlueprint } from "../../../../../src/hooks/useGetGridBlueprint";
import YHGrid from "../../../../../src/utils/grid/yh_grid";
import { useEffectApiFetch } from "../../../../../src/hooks";
import Api from "../../../../../src/utils/api";
import { useBoundStore } from "../../../../../src/store/store";

/**
 * Find unique parts step component
 *
 * @param {string} pageKey
 * @param {object} gridFilters
 * @returns {JSX.Element}
 */
const FindUniquePartsStep = ({ pageKey, gridFilters }) => {
  const [gridKey, setGridKey] = useState(pageKey);
  const [npiUniquePartsGridComponent, setNpiUniquePartsGridComponent] =
    useState();
  const [uniquePartsOptions, setUniquePartsOptions] = useState({});
  const npiRecipeData = useBoundStore((state) => state.npiRecipeData);
  const setNpiRecipeData = useBoundStore((state) => state.setNpiRecipeData);
  const [uniquePartsGridFilters, setUniquePartsGridFilters] = useState({
    [pageKey]: { ...gridFilters[pageKey], ...npiRecipeData.recipe_data },
  });
  const npiUniquePartsGridRef = useRef();
  const [form] = Form.useForm();

  const { data: gridBlueprintData, dataUpdatedAt: gridBlueprintDataUpdatedAt } =
    useGetGridBlueprint(pageKey, "npi_unique_parts");

  useEffect(() => {
    if (gridBlueprintData?.success) {
      setNpiUniquePartsGridComponent(gridBlueprintData.data);
    }
  }, [gridBlueprintDataUpdatedAt]);

  useEffectApiFetch(
    async () => {
      const options = await Api.fetchData(
        "/api/v1/npi/unique_parts/options",
        "GET",
      );
      setUniquePartsOptions(options.data);
    },
    () => {
      setUniquePartsOptions({});
    },
    [],
  );

  useEffect(() => {
    form.setFieldsValue({
      part_unique:
        npiRecipeData.recipe_data?.part_unique ??
        uniquePartsOptions.part_unique?.[0]?.value,
      evaluate_within:
        npiRecipeData.recipe_data?.evaluate_within ??
        uniquePartsOptions.evaluate_within?.[0]?.value,
      instance:
        npiRecipeData.recipe_data?.instance ??
        uniquePartsOptions.instance?.[0]?.value,
    });
  }, [uniquePartsOptions]);

  /**
   * Apply filters to unique parts grid
   */
  const applyFilters = () => {
    const values = form.getFieldsValue(true);
    setNpiRecipeData((prev) => {
      return {
        ...prev,
        recipe_data: {
          ...(prev.recipe_data ?? {}),
          part_unique: values.part_unique,
          evaluate_within: values.evaluate_within,
          instance: values.instance,
        },
      };
    });

    const filters = {};
    filters[pageKey] = { ...gridFilters[pageKey], ...values };
    setUniquePartsGridFilters(filters);
    setGridKey(pageKey + JSON.stringify(values));
  };

  return (
    <>
      <Form form={form} className="flex gap-4">
        <Form.Item name="part_unique" label="Select Part Unique Data">
          <Select
            options={uniquePartsOptions?.part_unique ?? []}
            showSearch
            popupMatchSelectWidth={false}
          ></Select>
        </Form.Item>
        <Form.Item name="evaluate_within" label="Evaluate Within">
          <Select
            options={uniquePartsOptions?.evaluate_within ?? []}
            showSearch
            popupMatchSelectWidth={false}
          ></Select>
        </Form.Item>
        <Form.Item
          name="instance"
          label="Include and Highlight duplicates by"
          tooltip="This only applies if there are duplicate parts found in your ..."
        >
          <Select
            options={uniquePartsOptions?.instance ?? []}
            showSearch
            popupMatchSelectWidth={false}
          ></Select>
        </Form.Item>
        <Button type="primary" onClick={applyFilters}>
          Apply
        </Button>
      </Form>
      <div>
        {npiUniquePartsGridComponent && (
          <YHGrid
            key={gridKey}
            wrapperClassName="flex grow flex-col h-full"
            gridRef={npiUniquePartsGridRef}
            gridId={`${pageKey}_${npiUniquePartsGridComponent.name}`}
            pageKey={pageKey}
            component={npiUniquePartsGridComponent}
            filters={uniquePartsGridFilters}
          />
        )}
      </div>
    </>
  );
};

export default FindUniquePartsStep;
