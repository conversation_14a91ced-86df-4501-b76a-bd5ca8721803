"use client";

import { useEffect, useState } from "react";
import { Button, Flex, Form, Popconfirm, Select, Tooltip, message } from "antd";
import { useQuery } from "@tanstack/react-query";
import { useGetAnalysisSetList } from "../../../../../src/hooks/useGetAnalysisSetList";
import Api from "../../../../../src/utils/api";
import { useBoundStore } from "../../../../../src/store/store";
import SaveAnalysisSetModal from "./save_analysis_set_modal";
import FooterNavigation from "./footer_navigation";

/**
 * Analysis set options component
 *
 * @param {string} pageKey
 * @param {function} loadAnalysisSetBtnHandler
 * @param {array} analysisSetValues
 * @param {object} pageFilters
 * @param {object} applyFilters
 * @param {boolean} isNextBtnDisabled
 * @returns {JSX.Element}
 */
const AnalysisSetOptions = ({
  pageKey,
  loadAnalysisSetBtnHandler,
  analysisSetValues = [],
  pageFilters,
  applyFilters,
  isNextBtnDisabled,
}) => {
  const [form] = Form.useForm();
  const testListGridRef = useBoundStore(
    (state) => state.npiCharSimulationTestListGridRef,
  );
  const [saveAnalysisSetModalOpen, setSaveAnalysisSetModalOpen] =
    useState(false);
  const [isLoadDeleteBtnDisabled, setIsLoadDeleteBtnDisabled] = useState(true);
  const [isPopConfirmDeleteOpen, setIsPopConfirmDeleteOpen] = useState(false);
  const [hasSimulatedItem, setHasSimulatedItem] = useState(false);
  const [nextBtnDisabled, setNextBtnDisabled] = useState(isNextBtnDisabled);
  const [messageApi, contextHolder] = message.useMessage();

  const {
    data: analysisSetList,
    refetch: refetchAnalysisSetList,
    isSuccess: analysisSetListQueryIsSuccess,
    dataUpdatedAt: analysisSetListUpdatedAt,
  } = useGetAnalysisSetList(pageFilters);

  const {
    refetch: deleteAnalysisSet,
    dataUpdatedAt: deleteAnalysisSetUpdatedAt,
    data: deleteAnalysisSetData,
  } = useQuery({
    queryKey: ["delete_analysis_set", pageFilters],
    queryFn: async () => {
      return await Api.fetchData(`/api/v1/npi/analysis_set/delete`, "POST", {
        ...pageFilters,
        analysis_set_name: form.getFieldValue("analysis_set"),
      });
    },
    enabled: false,
  });

  // Update the analysis set list after deleting one
  useEffect(() => {
    if (deleteAnalysisSetData) {
      if (deleteAnalysisSetData.success) {
        refetchAnalysisSetList();
        if (testListGridRef) {
          const analysisSetName = form.getFieldValue("analysis_set");
          testListGridRef.current.api.forEachNode((rowNode) => {
            if (rowNode.data?.analysis_set === analysisSetName) {
              rowNode.data.analysis_set = "";
            }
          });
          testListGridRef.current.api.refreshCells();
        }
        messageApi.success(deleteAnalysisSetData.message);
      } else {
        messageApi.error(deleteAnalysisSetData.message);
      }
    }
  }, [deleteAnalysisSetUpdatedAt]);

  useEffect(() => {
    if (analysisSetListQueryIsSuccess) {
      form.setFieldValue("analysis_set", null);
      setNextBtnDisabled(analysisSetList?.data.length === 0);
    }
  }, [analysisSetListUpdatedAt]);

  useEffect(() => {
    let simulatedItemCount = 0;
    analysisSetValues.forEach((setGroup) => {
      simulatedItemCount += setGroup.tables.length + setGroup.charts.length;
    });
    setHasSimulatedItem(simulatedItemCount > 0);
  }, [analysisSetValues]);

  /**
   * Called when saving the analysis set is a success
   *
   * @param {boolean} isApplied
   * @param {string} analysisSet
   */
  const saveSuccessCallback = (isApplied = false, analysisSet = "") => {
    refetchAnalysisSetList();
    if (isApplied && testListGridRef) {
      testListGridRef.current.api.forEachNode((rowNode) => {
        if (
          rowNode.data?.actual_test_number ===
            applyFilters.actual_test_number &&
          rowNode.data?.test_name === applyFilters.test_name &&
          rowNode.data?.test_type === applyFilters.test_type
        ) {
          let analysisSetArr = rowNode.data.analysis_set
            ? rowNode.data.analysis_set.split(",")
            : [];
          if (!analysisSetArr.includes(analysisSet)) {
            analysisSetArr.push(analysisSet);
          }
          rowNode.data.analysis_set = analysisSetArr.join(",");
        }
      });
      testListGridRef.current.api.refreshCells();
    }
  };

  /**
   * When the value of analysis_set select changes
   */
  const handleAnalysisSetChange = () => {
    setIsLoadDeleteBtnDisabled(!form.getFieldValue("analysis_set"));
  };

  return (
    <>
      {contextHolder}
      <SaveAnalysisSetModal
        open={saveAnalysisSetModalOpen}
        setModalOpen={setSaveAnalysisSetModalOpen}
        analysisSetValues={analysisSetValues}
        pageFilters={pageFilters}
        applyFilters={applyFilters}
        successCallback={saveSuccessCallback}
      />
      <Flex justify="space-between">
        <div></div>
        <Form form={form} className="flex flex-wrap gap-3 !mt-3 justify-center">
          <Form.Item name="analysis_set" label="" className="w-40">
            <Select
              placeholder="Select Analysis Set"
              options={analysisSetList?.data ?? []}
              onChange={handleAnalysisSetChange}
            ></Select>
          </Form.Item>
          <Tooltip title="Load the selected analysis set.">
            <Button
              onClick={() => {
                loadAnalysisSetBtnHandler(form);
              }}
              disabled={isLoadDeleteBtnDisabled}
            >
              Load
            </Button>
          </Tooltip>
          <Popconfirm
            key="submit"
            title="Delete analysis set"
            description="Are you sure to delete this analysis set?"
            okText="Delete"
            cancelText="Cancel"
            onConfirm={deleteAnalysisSet}
            onOpenChange={(visible) => setIsPopConfirmDeleteOpen(visible)}
          >
            <Tooltip
              title="Delete the selected analysis set."
              open={isPopConfirmDeleteOpen ? false : undefined}
            >
              <Button disabled={isLoadDeleteBtnDisabled}>Delete</Button>
            </Tooltip>
          </Popconfirm>
          <Tooltip title="Save analysis set.">
            <Button
              onClick={() => {
                setSaveAnalysisSetModalOpen(true);
              }}
              disabled={analysisSetValues.length === 0}
            >
              Save Analysis Set
            </Button>
          </Tooltip>
        </Form>
        {hasSimulatedItem ? (
          <FooterNavigation
            pageKey={pageKey}
            pageFilters={pageFilters}
            backData={{ pageKey: "npi_recipe" }}
            forwardData={{
              pageKey: "generate_npi_report",
            }}
            isNextBtnDisabled={nextBtnDisabled}
          />
        ) : (
          <div></div>
        )}
      </Flex>
    </>
  );
};

export default AnalysisSetOptions;
