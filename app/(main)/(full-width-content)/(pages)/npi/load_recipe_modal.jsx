"use_client";

import { App, But<PERSON>, Form, Modal } from "antd";
import { useEffect, useState } from "react";
import { useGetRecipeData } from "../../../../../src/hooks/useGetRecipeData";
import FilterSelect from "../../../../../src/utils/grid/components/filter_select";

/**
 * Load recipe modal component for NPI
 *
 * @param {boolean} isModalOpen
 * @param {boolean} setIsModalOpen
 * @param {object} selectedParams
 * @param {function} renderRecipePage
 * @returns {JSX.Element}
 */
const LoadRecipeModal = ({
  isModalOpen,
  setIsModalOpen,
  selectedParams = {},
  renderRecipePage,
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [pageFilters, setPageFilters] = useState([]);
  const [isLoadRecipeBtnDisabled, setIsLoadRecipeBtnDisabled] = useState(true);
  const [analysisType, setAnalysisType] = useState("");
  const [recipeCreator, setRecipeCreator] = useState("");
  const [recipeDataParams, setRecipeDataParams] = useState({});

  /**
   * Construct filters based from the selected parameters of the search table
   *
   * @returns {object} filters
   */
  const getPageFilters = () => {
    const filters = {};
    Object.keys(selectedParams)
      .filter((key) => selectedParams[key]?.length)
      .forEach((key) => {
        filters[key] = Array.isArray(selectedParams[key])
          ? selectedParams[key].join(",")
          : selectedParams[key];
      });

    return filters;
  };

  /**
   * Setup the filters
   */
  useEffect(() => {
    const filters = getPageFilters();
    setPageFilters(filters);
  }, [selectedParams]);

  const { data: recipeData, dataUpdatedAt: recipeDataUpdatedAt } =
    useGetRecipeData(recipeDataParams);

  useEffect(() => {
    if (recipeData?.data?.source_dsks?.length) {
      setIsModalOpen(false);
      const values = form.getFieldsValue();
      renderRecipePage({
        dsk: Array.isArray(recipeData.data.source_dsks)
          ? recipeData.data.source_dsks.join(",")
          : recipeData.data.source_dsks,
        ...selectedParams,
        ...{
          recipe_category: values.recipe_category.value,
          user_name: values.user_name.value,
          recipe_name: values.recipe_name.value,
        },
      });
    }
  }, [recipeDataUpdatedAt]);

  /**
   * Handles the loading of selected recipe
   */
  const handleLoadRecipe = () => {
    form
      .validateFields()
      .then((values) => {
        setRecipeDataParams({
          recipe_type: "npi",
          ...pageFilters,
          ...{
            recipe_category: values.recipe_category.value,
            user_name: values.user_name.value,
            recipe_name: values.recipe_name.value,
          },
          time_initiated: Date.now(),
        });
      })
      .catch((error) => {
        message.error(error.errorFields[0].errors[0], 5);
      });
  };

  /**
   * Event handler when users cancels/closes the modal
   */
  const closeModal = () => {
    setIsModalOpen(false);
  };

  /**
   * When any of the form field changes
   */
  const handleFieldsChange = () => {
    const values = form.getFieldsValue();
    setAnalysisType(values.recipe_category.value);
    setRecipeCreator(values.user_name?.value);
  };

  return (
    <Modal
      title="Load NPI Recipe"
      open={isModalOpen}
      onOk={handleLoadRecipe}
      onCancel={closeModal}
      destroyOnHidden
      width={"40vw"}
      footer={[
        <Button key="back" onClick={closeModal}>
          Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleLoadRecipe}
          disabled={isLoadRecipeBtnDisabled}
        >
          Load Recipe
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        className="mt-4"
        onFieldsChange={handleFieldsChange}
      >
        <Form.Item name="recipe_category" label="Analysis Type">
          <FilterSelect
            form={form}
            placeholder="Select Analysis Type"
            className="w-full"
            componentKey="select_recipe_category"
            allowClear={false}
            params={{
              api: {
                url: `api/v1/npi/recipe/categories`,
                method: "GET",
                cache_it: 0,
              },
            }}
            showSearch
            labelInValue
            selectFirstOption
            fieldName={"recipe_category"}
            successCbk={(option) => {
              setAnalysisType(option.value);
            }}
          />
        </Form.Item>

        {analysisType && (
          <Form.Item name="user_name" label="Recipe Creator">
            <FilterSelect
              form={form}
              placeholder="Select Recipe Creator"
              className="w-full"
              componentKey="select_recipe_creator"
              allowClear={false}
              params={{
                api: {
                  url: `api/v1/npi/recipe/creators/${analysisType}`,
                  method: "GET",
                  cache_it: 0,
                },
              }}
              showSearch
              labelInValue
              selectFirstOption
              deps={[analysisType]}
              fieldName={"user_name"}
              successCbk={(option) => {
                setRecipeCreator(option.value);
              }}
            />
          </Form.Item>
        )}
        {recipeCreator && analysisType && (
          <Form.Item
            name="recipe_name"
            label="Recipe Name"
            rules={[{ required: true, message: "Select recipe." }]}
          >
            <FilterSelect
              form={form}
              placeholder="Select Recipe"
              className="w-full"
              componentKey="select_char_report"
              allowClear={false}
              labelInValue
              params={{
                api: {
                  url: `api/v1/npi/recipe/list`,
                  cache_it: 0,
                  dsk: pageFilters.dsk,
                  lot_id: pageFilters.lot_id,
                  mfg_process: pageFilters.mfg_process,
                  src_type: pageFilters.src_type,
                  src_value: pageFilters.src_value,
                  recipe_category: analysisType,
                  user_name: recipeCreator,
                },
              }}
              showSearch
              selectFirstOption
              deps={[recipeCreator, analysisType]}
              fieldName={"recipe_name"}
              successCbk={(value) => {
                setIsLoadRecipeBtnDisabled(value === undefined);
              }}
            />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default LoadRecipeModal;
