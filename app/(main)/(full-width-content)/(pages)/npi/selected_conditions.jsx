"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { useBoundStore } from "../../../../../src/store/store";
import { useGetRecipeData } from "../../../../../src/hooks/useGetRecipeData";
import { useRemoveNpiConditionFromRecipe } from "../../../../../src/hooks/useRemoveNpiConditionFromRecipe";
import { useGetAvailableNpiConditions } from "../../../../../src/hooks/useGetAvailableNpiConditions";
import InfiniteScrollConditions from "./infinite_scroll_conditions";

/**
 * Selected conditions component
 *
 * @param {object} pageFilters
 * @param {function} setDisableSkipToSimulateBtn
 * @param {function} setDisableNextBtn
 * @returns {JSX.Element}
 */
const SelectedConditions = ({
  pageFilters,
  setDisableSkipToSimulateBtn,
  setDisableNextBtn,
}) => {
  const selectedConditions = useBoundStore((state) => state.selectedConditions);
  const setAvailableConditions = useBoundStore(
    (state) => state.setAvailableConditions,
  );
  const setSelectedConditions = useBoundStore(
    (state) => state.setSelectedConditions,
  );
  const setNpiRecipeData = useBoundStore((state) => state.setNpiRecipeData);
  const npiRecipeProcessingStatus = useBoundStore(
    (state) => state.npiRecipeProcessingStatus,
  );
  const setNpiRecipeProcessingStatus = useBoundStore(
    (state) => state.setNpiRecipeProcessingStatus,
  );
  const [recipeDataParams, setRecipeDataParams] = useState({
    recipe_type: "npi",
    ...pageFilters,
  });
  const [conditionIsLoading, setConditionIsLoading] = useState(true);
  const [
    removeConditionFromRecipeFilters,
    setRemoveConditionFromRecipeFilters,
  ] = useState({});
  const [errorMessage, setErrorMessage] = useState();

  useEffect(() => {
    if (pageFilters.recipe_name) {
      setRecipeDataParams({
        recipe_type: "npi",
        ...pageFilters,
      });
    }
  }, [pageFilters.recipe_name ?? ""]);

  const { data: recipeData, dataUpdatedAt: recipeDataUpdatedAt } =
    useGetRecipeData(recipeDataParams);

  useEffect(() => {
    if (recipeData?.success) {
      setNpiRecipeData(() => recipeData.data);
    }
  }, [recipeDataUpdatedAt]);

  const {
    dataUpdatedAt: selectedConditionsUpdatedAt,
    data: selectedConditionsData,
    refetch: refetchSelectedConditionsData,
  } = useGetAvailableNpiConditions(pageFilters, 1);

  useEffect(() => {
    if (npiRecipeProcessingStatus.loading === false) {
      refetchSelectedConditionsData();
      setNpiRecipeProcessingStatus({});
    }
  }, [npiRecipeProcessingStatus]);

  useEffect(() => {
    if (selectedConditionsData) {
      if (selectedConditionsData?.success) {
        setSelectedConditions(() => selectedConditionsData.data.conditions);
        setConditionIsLoading(selectedConditionsData.data.loading);
        setErrorMessage();
      } else {
        setConditionIsLoading(selectedConditionsData.data.loading);
        setErrorMessage(selectedConditionsData.message);
        setDisableSkipToSimulateBtn(true);
        setDisableNextBtn(true);
      }
    }
  }, [selectedConditionsUpdatedAt]);

  const {
    dataUpdatedAt: removeConditionFromRecipeUpdatedAt,
    data: removeConditionFromRecipeData,
  } = useRemoveNpiConditionFromRecipe(removeConditionFromRecipeFilters);

  useEffect(() => {
    if (removeConditionFromRecipeData?.success) {
      if (removeConditionFromRecipeFilters.from_clear_all) {
        setAvailableConditions((prevState) => [
          ...prevState,
          ...removeConditionFromRecipeFilters.recipe_data.conditions,
        ]);
        setSelectedConditions(() => []);
      } else {
        const conditionToDelete =
          removeConditionFromRecipeFilters.recipe_data.conditions[0];
        setAvailableConditions((prevState) => [
          ...prevState,
          conditionToDelete,
        ]);
        setSelectedConditions((prevState) =>
          prevState.filter(
            (condition) =>
              conditionToDelete.condition_label !== condition.condition_label,
          ),
        );
      }
    }
  }, [removeConditionFromRecipeUpdatedAt]);

  /**
   * Delete a condition form the selected conditions section
   *
   * @param {object} conditionToDelete
   */
  const deleteCondition = (conditionToDelete) => {
    setRemoveConditionFromRecipeFilters({
      ...pageFilters,
      recipe_data: {
        conditions: [conditionToDelete],
      },
      time_initiated: Date.now(),
      from_clear_all: false,
    });
  };

  /**
   * Clear all the selected conditions
   */
  const clearSelectedConditions = () => {
    setRemoveConditionFromRecipeFilters({
      ...pageFilters,
      recipe_data: {
        conditions: selectedConditions,
      },
      time_initiated: Date.now(),
      from_clear_all: true,
    });

    setAvailableConditions((prevState) => [
      ...prevState,
      ...selectedConditions,
    ]);
    setSelectedConditions(() => []);
  };

  return (
    <>
      <Flex vertical gap={"small"}>
        <Divider>Selected Conditions</Divider>
        {errorMessage ? (
          <Alert message={errorMessage} type="error" showIcon />
        ) : (
          <InfiniteScrollConditions
            data={selectedConditions}
            setData={setSelectedConditions}
            removeConditionHandler={deleteCondition}
            loading={conditionIsLoading}
            pageFilters={pageFilters}
          />
        )}
      </Flex>
      {selectedConditions.length > 0 && !errorMessage && (
        <Flex justify="flex-end" className="mt-3">
          <Button onClick={clearSelectedConditions}>Clear All</Button>
        </Flex>
      )}
    </>
  );
};

export default SelectedConditions;
