"use client";

import { But<PERSON> } from "antd";
import { useQueryClient } from "@tanstack/react-query";
import Helper from "../../../../../src/utils/helper";

/**
 * Analysis set tab options component
 *
 * @returns {JSX.Element}
 */
const AnalysisSetTabOptions = ({ pageKey, filters, prerenderData }) => {
  const queryClient = useQueryClient();

  /**
   * Opens gallery of gage charts in new browser tab
   */
  const openChartGallery = () => {
    let requestParams = {
      report_key: filters[pageKey].report_key,
      recipe_key: prerenderData.recipe_key,
      recipe_name: prerenderData.recipe_name,
      recipe_category: prerenderData.recipe_category,
      analysis_set_key: prerenderData.analysis_set_key,
      dsk: prerenderData.dsk,
      template_key: "gage_charts_gallery",
    };
    const queryString = Helper.createQueryString(requestParams);
    Helper.renderPage(
      `#analysis/gage-charts-gallery?${queryString}`,
      "gage_charts_gallery",
      `#analysis/gage-charts-gallery?${queryString}`,
      queryClient,
      false,
      true,
    );
  };

  return (
    <div className="w-full h-full">
      <Button type="primary" onClick={openChartGallery}>
        View Chart Gallery
      </Button>
    </div>
  );
};

export default AnalysisSetTabOptions;
