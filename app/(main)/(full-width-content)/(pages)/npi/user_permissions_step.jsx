"use client";

import { Input, Form } from "antd";
import { useBoundStore } from "../../../../../src/store/store";
const { TextArea } = Input;

/**
 * User permissions step component
 *
 * @param {object} form
 * @returns {JSX.Element}
 */
const UserPermissionsStep = ({ form }) => {
  const npiRecipeData = useBoundStore((state) => state.npiRecipeData);

  return (
    <Form
      layout="vertical"
      form={form}
      initialValues={{
        notes: npiRecipeData.notes ?? "",
      }}
    >
      <Form.Item label="Notes" name="notes" className="w-1/2">
        <TextArea rows={4} placeholder="Describe this recipe" />
      </Form.Item>
    </Form>
  );
};

export default UserPermissionsStep;
