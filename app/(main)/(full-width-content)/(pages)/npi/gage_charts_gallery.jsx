import { Col, Form, Row, Select, message } from "antd";
import React, { useEffect, useState } from "react";
import Helper from "../../../../../src/utils/helper";
import { useEffectApiFetch } from "../../../../../src/hooks";
import Api from "../../../../../src/utils/api";
import { useBoundStore } from "../../../../../src/store/store";
import ChartHelper from "../../../../../src/utils/charts/chart_helper";
import SimulatedItem from "./simulated_item";

/**
 * Charts gallery for Gage R+R
 *
 * @param {object} filters
 * @param {function} setFilters
 * @param {string} pageKey
 * @param {object} chartCustomData
 * @param {function} testStatsInfoHandler
 * @returns {JSX.Element}
 */
export default function GageChartsGallery({
  filters,
  setFilters,
  pageKey,
  chartCustomData,
  testStatsInfoHandler,
}) {
  const [componentBlueprints, setComponentBlueprints] = useState();
  const [galleryCharts, setGalleryCharts] = useState({});
  const urlParams = useBoundStore((state) => state.urlParams);
  const chartKeys = useBoundStore((state) => state.chartKeys);
  const [messageApi, contextHolder] = message.useMessage();
  const [form] = Form.useForm();

  useEffectApiFetch(
    () => {
      return Api.getAnalysisTemplateComponents(
        (res) => {
          if (res.success) {
            const blueprints = res.data.test;
            setComponentBlueprints(blueprints);
          } else {
            messageApi.warning(res.message, 5);
          }
        },
        (err) => {
          messageApi.error(err, 5);
        },
        "test",
      );
    },
    () => {
      setComponentBlueprints();
    },
    [],
  );

  useEffect(() => {
    if (componentBlueprints) {
      getTestsData();
    }
  }, [componentBlueprints]);

  /**
   * Get data of tests and chart components to be rendered
   */
  const getTestsData = () => {
    Api.getAnalysisSetTests(
      (res) => {
        if (res.success) {
          renderGalleryCharts(componentBlueprints, res.data);
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      { ...urlParams[pageKey], ...form.getFieldsValue(), get_data: 1 },
    );
  };

  /**
   * Add chart object to gage gallery charts
   *
   * @param {object} componentBlueprints
   * @param {object} testsData
   */
  const renderGalleryCharts = (componentBlueprints, testsData) => {
    let charts = {};
    testsData.forEach((testData) => {
      testData.forEach((setData) => {
        setData.filters?.charts?.forEach((componentName) => {
          const chartComponentBlueprint = componentBlueprints.find(
            (component) => component.name === componentName,
          );
          if (chartComponentBlueprint) {
            const chartComponentBlueprintCopy = Helper.cloneObject(
              chartComponentBlueprint,
            );
            const chartKey = `${chartComponentBlueprintCopy.name}_${setData.test_unique}`;
            chartComponentBlueprintCopy.id = chartKey;
            charts[chartKey] = {
              key: chartKey,
              chartKey: chartKey,
              chartCustomData: chartCustomData,
              filters: filters,
              pageKey: pageKey,
              component: chartComponentBlueprintCopy,
              group: setData.filters,
              prerenderData: setData,
            };

            ChartHelper.storeChartKey(
              chartKey,
              chartKeys,
              pageKey,
              chartComponentBlueprintCopy.name,
            );
          }
        });
      });
    });

    setGalleryCharts(charts);
  };

  /**
   * Reload charts based on applied sorting
   */
  const sortCharts = () => {
    setGalleryCharts({});
    getTestsData();
  };

  return (
    <>
      {contextHolder}
      <Form
        form={form}
        className="!mt-2 !mb-4"
        layout="inline"
        initialValues={{
          sort_by: "test_seq",
          sort_dir: "asc",
        }}
      >
        <Form.Item name="sort_by" label="Order charts by">
          <Select
            options={[
              {
                value: "actual_test_number",
                label: "Test Number",
              },
              {
                value: "test_seq",
                label: "Test Sequence",
              },
              {
                value: "test_name",
                label: "Test Name",
              },
            ]}
            popupMatchSelectWidth={false}
            onChange={sortCharts}
          ></Select>
        </Form.Item>
        <Form.Item name="sort_dir" label="">
          <Select
            options={[
              {
                value: "asc",
                label: "Ascending",
              },
              {
                value: "desc",
                label: "Descending",
              },
            ]}
            popupMatchSelectWidth={false}
            onChange={sortCharts}
          ></Select>
        </Form.Item>
      </Form>
      <Row gutter={[16, 16]}>
        {Object.keys(galleryCharts).map(function (chartKey) {
          const component = galleryCharts[chartKey].component;
          return (
            <Col
              key={`gallery_chart_${chartKey}_wrapper`}
              style={{
                width: `${100 / 2}%`,
              }}
            >
              <SimulatedItem
                key={`${component.id}_item`}
                colKey={`${component.id}_wrapper`}
                component={component}
                group={galleryCharts[chartKey].group}
                pageKey={pageKey}
                filters={filters}
                setFilters={setFilters}
                prerenderData={galleryCharts[chartKey].prerenderData}
                testStatsInfoHandler={testStatsInfoHandler}
              />
            </Col>
          );
        })}
      </Row>
    </>
  );
}
