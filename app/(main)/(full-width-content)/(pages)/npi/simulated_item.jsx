"use client";

import { memo, useState } from "react";
import { <PERSON>, Col } from "antd";
import Helper from "../../../../../src/utils/helper";

/**
 * Simulated item component
 *
 * @param {string} colKey
 * @param {object} component
 * @param {group} object
 * @param {string} pageKey
 * @param {object} filters
 * @param {function} setFilters
 * @param {object} prerenderData
 * @param {object} requiredData
 * @param {object} cellActions
 * @param {function} testStatsInfoHandler
 * @returns {JSX.Element}
 */
const SimulatedItem = ({
  colKey,
  component,
  group,
  pageKey,
  filters,
  setFilters,
  prerenderData,
  requiredData,
  cellActions,
  testStatsInfoHandler,
}) => {
  const [isChartBoosted, setIsChartBoosted] = useState(false);

  return (
    <Col key={colKey} className={group.layout}>
      <Card title={component.display_name}>
        {component.type === "chart" && (
          <div id={`boosted_warning_msg_wrapper_${component.id}`}></div>
        )}
        {Helper.createComponent(
          component,
          cellActions,
          pageKey,
          testStatsInfoHandler,
          filters,
          setFilters,
          isChartBoosted,
          setIsChartBoosted,
          prerenderData,
          requiredData,
          {},
          false,
        )}
      </Card>
    </Col>
  );
};

export default memo(SimulatedItem);
