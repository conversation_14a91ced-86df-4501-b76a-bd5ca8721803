"use_client";

import { Button, Input, Form } from "antd";
import { useState } from "react";
import { unionBy } from "lodash";
import { useAddNpiConditionToRecipe } from "../../../../../src/hooks/useAddNpiConditionToRecipe";

/**
 * Assign condition values form component
 *
 * @param {object} gridRef
 * @param {object} customData
 * @param {object} pageFilters
 * @returns {JSX.Element}
 */
const AssignConditionValuesForm = ({
  gridRef,
  customData: conditionData,
  pageFilters,
}) => {
  const [form] = Form.useForm();
  const [addConditionToRecipeFilters, setAddConditionToRecipeFilters] =
    useState({});

  useAddNpiConditionToRecipe(addConditionToRecipeFilters);

  /**
   * Assign condition values
   */
  const assignConditionValues = () => {
    const conditionValue = form.getFieldValue("condition_value");
    const selectedData = gridRef.current.api.getSelectedRows();
    selectedData.forEach((data) => {
      data.condition_value = conditionValue;
    });
    gridRef.current.api.refreshCells();
    conditionData.table_data = unionBy(
      selectedData,
      conditionData.table_data,
      "part_unique",
    );

    // Tell the api that values are assigned to the condition
    setAddConditionToRecipeFilters({
      ...pageFilters,
      recipe_data: {
        conditions: [conditionData],
      },
      time_initiated: Date.now(),
    });
  };

  return (
    <Form form={form} className="flex gap-1">
      <Form.Item name="condition_value" noStyle>
        <Input className="w-1/2 mb-1" placeholder="Condition Value"></Input>
      </Form.Item>
      <Button onClick={assignConditionValues}>Assign Values</Button>
    </Form>
  );
};

export default AssignConditionValuesForm;
