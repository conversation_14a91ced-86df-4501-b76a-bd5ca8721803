"use_client";

import { Button, Input, Form, Modal } from "antd";
import { useEffect, useState } from "react";
import { recipeNameRules } from "../../../../../src/utils/antd_validation_rules";
import { generateDateNowHash } from "../../../../../src/utils/components/recipe_common/helpers";
import FilterSelect from "../../../../../src/utils/grid/components/filter_select";

/**
 * Create NPI recipe modal component
 *
 * @param {boolean} isModalOpen
 * @param {boolean} setIsModalOpen
 * @param {object} selectedParams
 * @param {function} renderCreatePage
 * @returns {JSX.Element}
 */
const CreateRecipeModal = ({
  isModalOpen,
  setIsModalOpen,
  selectedParams = {},
  renderCreatePage,
}) => {
  const [form] = Form.useForm();
  const [pageFilters, setPageFilters] = useState([]);
  const [isCreateBtnDisabled, setIsCreateBtnDisabled] = useState(false);

  /**
   * Construct filters based from the selected parameters of the search table
   *
   * @returns {object} filters
   */
  const getPageFilters = () => {
    const filters = {};
    Object.keys(selectedParams).forEach((key) => {
      filters[key] = Array.isArray(selectedParams[key])
        ? selectedParams[key].join(",")
        : selectedParams[key];
    });

    return filters;
  };

  /**
   * Setup the filters
   */
  useEffect(() => {
    setPageFilters(getPageFilters());
  }, [selectedParams]);

  /**
   * Generate recipe name based on category
   *
   * @param {string} recipeCategoryName
   */
  const setRecipeName = (recipeCategoryName = "NPI") => {
    form.setFieldsValue({
      recipe_name: `${recipeCategoryName.replace(/ /g, "-").replace(/\+/g, "")}-Recipe_${generateDateNowHash()}`,
    });
  };

  /**
   * Event handler when user closes the modal
   */
  const closeModal = () => {
    setIsModalOpen(false);
  };

  /**
   * When create recipe button is clicked
   */
  const handleCreateRecipe = () => {
    const values = form.getFieldsValue();
    renderCreatePage({
      ...pageFilters,
      ...values,
      recipe_category: values.recipe_category.value,
    });
    closeModal();
  };

  /**
   * When any of the form value changes
   */
  const handleValuesChange = () => {
    form.validateFields(["recipe_name"]).catch((error) => {
      const values = error.values;
      setIsCreateBtnDisabled(
        error.errorFields.length > 0 &&
          !(values.recipe_name && values.recipe_category),
      );
    });
  };

  return (
    <Modal
      title="Create NPI Recipe"
      open={isModalOpen}
      onOk={handleCreateRecipe}
      onCancel={closeModal}
      width={"40vw"}
      footer={[
        <Button key="back" onClick={closeModal}>
          Close
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleCreateRecipe}
          disabled={isCreateBtnDisabled}
        >
          Create
        </Button>,
      ]}
    >
      <Form
        form={form}
        className="mt-4"
        onValuesChange={handleValuesChange}
        layout="vertical"
      >
        <Form.Item name="recipe_category" label="Analysis Type">
          <FilterSelect
            form={form}
            placeholder="Select Analysis Type"
            className="w-full"
            componentKey="select_recipe_category"
            allowClear={false}
            params={{
              api: {
                url: `api/v1/npi/recipe/categories`,
                method: "GET",
                cache_it: 0,
              },
            }}
            showSearch
            labelInValue
            selectFirstOption
            fieldName={"recipe_category"}
            successCbk={(value) => {
              setRecipeName(value.label);
            }}
            onChange={(value) => {
              setRecipeName(value.label);
            }}
          />
        </Form.Item>

        <Form.Item
          name="recipe_name"
          label="Recipe Name"
          rules={recipeNameRules}
        >
          <Input placeholder="Recipe Name"></Input>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateRecipeModal;
