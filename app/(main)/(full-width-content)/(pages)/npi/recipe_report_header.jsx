"use client";

import { Flex, Typography } from "antd";
import RecipeInfo from "./recipe_info";

const { Title } = Typography;

/**
 * Recipe report header component
 *
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
const RecipeReportHeader = ({ component, filters, pageKey }) => {
  return (
    <>
      {component?.has_recipe_info && <RecipeInfo params={filters[pageKey]} />}
      <Flex justify="center" className="mt-3">
        <Title level={3}>{component.display_name}</Title>
      </Flex>
      {component?.title && (
        <Flex justify="center">
          <Title level={5}>{component.title}</Title>
        </Flex>
      )}
    </>
  );
};

export default RecipeReportHeader;
