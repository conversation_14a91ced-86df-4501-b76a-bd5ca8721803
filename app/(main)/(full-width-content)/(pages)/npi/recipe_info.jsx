"use client";

import { Flex, Typography } from "antd";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import Api from "../../../../../src/utils/api";
import { useBoundStore } from "../../../../../src/store/store";

const { Text } = Typography;

/**
 * Recipe info component
 *
 * @param {object} params
 * @returns {JSX.Element}
 */
const RecipeInfo = ({ params }) => {
  const [filters, setFilters] = useState(params);
  const [recipeInfo, setRecipeInfo] = useState({});
  const npiRecipeInfo = useBoundStore((state) => state.npiRecipeInfo);

  useEffect(() => {
    setFilters(params);
  }, [params]);

  const { data } = useQuery({
    queryKey: ["recipe_info", filters],
    queryFn: async () => {
      return await Api.fetchData(`/api/v1/npi/recipe/info`, "POST", filters);
    },
    enabled: typeof filters.recipe_name !== "undefined",
  });

  useEffect(() => {
    if (data?.data?.recipe_name && data.data.recipe_name !== "") {
      setRecipeInfo(data.data);
    }
  }, [data]);

  useEffect(() => {
    if (npiRecipeInfo) {
      setRecipeInfo(npiRecipeInfo);
    }
  }, [npiRecipeInfo]);

  return (
    <Flex vertical>
      <Text type="secondary" key="recipe_name">
        Recipe Name: {recipeInfo.recipe_name ?? ""}
      </Text>
      <Text type="secondary" key="recipe_category">
        Recipe Category: {recipeInfo.recipe_category ?? ""}
      </Text>
      <Text type="secondary" key="date_created">
        Date Created: {recipeInfo.date_created ?? ""}
      </Text>
      <Text type="secondary" key="date_updated">
        Last Modified: {recipeInfo.date_updated ?? ""}
      </Text>
    </Flex>
  );
};

export default RecipeInfo;
