"use client";

import { useEffect, useState } from "react";
import {
  Button,
  Checkbox,
  Divider,
  Flex,
  Form,
  Input,
  InputNumber,
  Select,
  Space,
  Tooltip,
} from "antd";
import { maxTagPlaceholder } from "../../../../../src/utils/components/recipe_common/helpers";
import StatsTypeSelect from "../../../../../src/utils/components/stats_type_select";
import { OptionsList } from "../../../../../src/utils/forms/options_list";

/**
 * Simulation options component
 *
 * @param {function} generateBtnHandler
 * @param {array} conditionOptions
 * @param {object} pageFilters
 * @param {object} form
 * @param {object} simulationOptions
 * @returns {JSX.Element}
 */
const SimulationOptions = ({
  generateBtnHandler,
  conditionOptions = [],
  pageFilters,
  form,
  simulationOptions,
}) => {
  const [isGenerateBtnDisabled, setIsGenerateBtnDisabled] = useState(false);
  const [enabledDataOptions, setEnabledDataOptions] = useState({});

  useEffect(() => {
    resetForm(simulationOptions);
  }, []);

  /**
   * Reset form to initial values
   *
   * @param {object} options
   */
  const resetForm = (options) => {
    let defaultConditionSets = [];
    switch (pageFilters.recipe_category) {
      case "npi_for_char":
        defaultConditionSets = [...conditionOptions]
          .splice(0, 3)
          .map((option) => option.value);
        break;
      case "npi_for_gage":
        defaultConditionSets = [...conditionOptions].map(
          (option) => option.value,
        );
        break;
      case "npi_for_die_drift":
        defaultConditionSets = [...conditionOptions]
          .splice(0, 1)
          .map((option) => option.value);
        break;
    }

    form.setFieldsValue({
      stats_type: "all",
      iqr_n: 1.5,
      layout: "w-full",
      append_new_items: true,
      tables: options.default_tables ?? [],
      charts: options.default_charts ?? [],
      condition_sets: defaultConditionSets,
    });
  };

  /**
   * Trigger when value updated
   *
   * @param {object} _ _
   * @param {object} allValues
   */
  const onValuesChange = (_, allValues) => {
    setIsGenerateBtnDisabled(
      allValues.charts?.length === 0 && allValues.tables?.length === 0,
    );
  };

  /**
   * Toggle data options enabled state
   *
   * @param {string} key
   * @param {boolean} checked
   */
  const toggleDataOption = (key, checked) => {
    setEnabledDataOptions((prev) => ({
      ...prev,
      [key]: checked,
    }));
  };

  /**
   * Checkbox to enable/disable data option
   *
   * @param {object} props
   * @returns {JSX.Element}
   */
  const DataOptionCheckbox = (props) => {
    return (
      <Form.Item
        name={[props.optionsGroup, props.option.value]}
        valuePropName="checked"
      >
        <Checkbox
          checked={enabledDataOptions[props.option.value]}
          onChange={(e) =>
            toggleDataOption(props.option.value, e.target.checked)
          }
        ></Checkbox>
      </Form.Item>
    );
  };

  /**
   * Generate data options fields
   *
   * @param {object} props
   * @returns {JSX.Element}
   */
  const DataOptions = (props) => {
    const dataOptions = [];
    simulationOptions?.[props?.optionsKey]?.forEach((option) => {
      const key = `${option.value}_wrapper`;
      switch (option.input_type) {
        case "inputNumber":
          dataOptions.push(
            <Space key={key}>
              <DataOptionCheckbox
                option={option}
                optionsGroup={props.optionsGroup}
              />
              <Form.Item
                name={[props.optionsGroup, option.input_name]}
                label={option.label}
              >
                <InputNumber disabled={!enabledDataOptions[option.value]} />
              </Form.Item>
            </Space>,
          );
          break;
        case "inputText":
          dataOptions.push(
            <Space key={key}>
              <DataOptionCheckbox
                option={option}
                optionsGroup={props.optionsGroup}
              />
              <Form.Item
                name={[props.optionsGroup, option.input_name]}
                label={option.label}
              >
                <Input disabled={!enabledDataOptions[option.value]} />
              </Form.Item>
            </Space>,
          );
          break;
        default:
          dataOptions.push(
            <Form.Item
              key={key}
              name={[props.optionsGroup, option.value]}
              valuePropName="checked"
            >
              <Checkbox>{option.label}</Checkbox>
            </Form.Item>,
          );
      }
    });

    return (
      <Flex gap="large" wrap>
        {dataOptions}
      </Flex>
    );
  };

  return (
    <Form
      form={form}
      className="flex gap-3 mt-3 mb-3"
      initialValues={{ stats_type: "all", iqr_n: 1.5 }}
      onValuesChange={onValuesChange}
    >
      <Flex className="w-1/2" gap="middle" wrap>
        <Form.Item name="condition_sets" label="Conditions" className="w-1/3">
          <Select
            placeholder="Select Condition Sets"
            options={conditionOptions}
            mode="multiple"
            allowClear={true}
            maxTagCount="responsive"
            maxTagPlaceholder={maxTagPlaceholder}
            showSearch
          ></Select>
        </Form.Item>
        <StatsTypeSelect
          gap="middle"
          className="w-1/2"
          selectClassName="w-2/3"
          iqrNClassName="w-1/3"
          selectOptions={simulationOptions?.stats_types}
        />
        <Divider>Include Data</Divider>
        <DataOptions
          optionsKey="include_data_filters"
          optionsGroup="include_data"
        />
        <Divider>Exclude Data</Divider>
        <DataOptions
          optionsKey="exclude_data_filters"
          optionsGroup="exclude_data"
        />
      </Flex>
      <Flex className="w-1/2" vertical>
        <Flex className="w-full" gap="middle">
          <Form.Item name="charts" label="Charts" className="w-1/2">
            <Select
              mode="multiple"
              allowClear={true}
              placeholder="Select Charts"
              options={simulationOptions?.charts}
              maxTagCount="responsive"
              maxTagPlaceholder={maxTagPlaceholder}
              showSearch
            ></Select>
          </Form.Item>
          <Form.Item name="tables" label="Tables" className="w-1/2">
            <Select
              mode="multiple"
              allowClear={true}
              placeholder="Select Tables"
              options={simulationOptions?.tables}
              showSearch
            ></Select>
          </Form.Item>
        </Flex>
        <Flex className="w-full" gap="middle">
          <Form.Item name="layout" label="Layout" className="w-1/3">
            <Select
              placeholder="Select Layout"
              options={OptionsList.npi_layout_options}
              showSearch
            ></Select>
          </Form.Item>
          <Tooltip title="Unticking this will replace existing charts and tables with new ones.">
            <Form.Item name="append_new_items" valuePropName="checked">
              <Checkbox>Always add all future charts below</Checkbox>
            </Form.Item>
          </Tooltip>
        </Flex>
        <Flex className="w-full" justify="flex-end" gap="middle">
          <Tooltip title="Reset to default">
            <Button
              onClick={() => {
                resetForm();
              }}
            >
              Reset
            </Button>
          </Tooltip>
          <Button
            type="primary"
            disabled={isGenerateBtnDisabled}
            onClick={() => {
              generateBtnHandler(form);
            }}
          >
            Generate
          </Button>
        </Flex>
      </Flex>
    </Form>
  );
};

export default SimulationOptions;
