"use_client";

import { Button, Input, Select, Flex } from "antd";
import { MinusCircleOutlined, PlusSquareOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { useAddNpiConditionToRecipe } from "../../../../../src/hooks/useAddNpiConditionToRecipe";

const digitOptions = [...Array(10).keys()];
digitOptions.splice(0, 1);

/**
 * First digit condition component
 *
 * @param {object} conditionData
 * @param {object} pageFilters
 * @returns {JSX.Element}
 */
const FirstDigitCondition = ({ conditionData, pageFilters }) => {
  const [conditionRows, setConditionRows] = useState([[]]);
  const [conditionValues, setConditionValues] = useState([]);
  const [selectedDigits, setSelectedDigits] = useState([]);
  const filteredDigitOptions = digitOptions.filter(
    (o) => !selectedDigits.includes(o),
  );
  const [addConditionToRecipeFilters, setAddConditionToRecipeFilters] =
    useState({});

  useAddNpiConditionToRecipe(addConditionToRecipeFilters);

  useEffect(() => {
    if (conditionData?.table_data?.length) {
      const digits = [],
        values = [];
      conditionData.table_data.forEach((rowData) => {
        digits.push(rowData.condition_digit);
        values.push(rowData.condition_value);
      });
      setConditionRows(digits);
      setConditionValues(values);
    }
  }, []);

  /**
   * Set the data per row of the condition
   *
   * @param {int} rowNum
   * @param {*} value
   * @param {string} propName
   */
  const setConditionRowData = (rowNum, value, propName) => {
    if (!conditionData?.table_data) {
      conditionData.table_data = [];
    }
    if (!conditionData.table_data?.[rowNum]) {
      conditionData.table_data[rowNum] = {};
    }
    conditionData.table_data[rowNum][propName] = value;

    // Tell the api that values are assigned to the condition
    setAddConditionToRecipeFilters({
      ...pageFilters,
      recipe_data: {
        conditions: [conditionData],
      },
    });
  };

  /**
   * Assign condition value
   */
  const addConditionRow = () => {
    setConditionRows((prevState) => {
      const newState = [...prevState];
      newState.push([]);
      return newState;
    });
  };

  /**
   * Set condition digit
   *
   * @param {int} value
   * @param {int} rowNum
   */
  const setConditionDigit = (value, rowNum) => {
    setConditionRows((prevState) => {
      const newState = [...prevState];
      newState[rowNum] = value;
      return newState;
    });
    setConditionRowData(rowNum, value, "condition_digit");
  };

  /**
   * Set condition values
   *
   * @param {string} value
   */
  const setConditionValue = (value, rowNum) => {
    setConditionValues((prevState) => {
      const newState = [...prevState];
      newState[rowNum] = value;
      return newState;
    });
    setConditionRowData(rowNum, value, "condition_value");
  };

  /**
   * Remove condition row
   *
   * @param {string} rowNum
   */
  const removeConditionRow = (rowNum) => {
    setConditionRows((prevState) => {
      const newState = [...prevState];
      newState.splice(rowNum, 1);
      return newState;
    });
    setConditionValues((prevState) => {
      const newState = [...prevState];
      newState.splice(rowNum, 1);
      return newState;
    });
    // Return the digits of removed row to the available digit options
    if (conditionData.table_data?.[rowNum]?.condition_digit) {
      const unselectedDigits = [
        ...conditionData.table_data[rowNum].condition_digit,
      ];
      setSelectedDigits((prevState) =>
        [...prevState].filter((digit) => !unselectedDigits.includes(digit)),
      );
    }
    conditionData.table_data.splice(rowNum, 1);
  };

  return (
    <>
      <div>
        <Button icon={<PlusSquareOutlined />} onClick={addConditionRow}>
          Add
        </Button>
      </div>
      {conditionRows.map((digit, rowNum) => {
        return (
          <Flex key={digit?.join("_") + rowNum} gap={"small"} className="pt-1">
            {conditionRows.length > 1 && (
              <MinusCircleOutlined
                onClick={() => {
                  removeConditionRow(rowNum);
                }}
                className={`text-red-400 ${rowNum > 0 ? "visible" : "invisible"}`}
              />
            )}
            <Select
              className="w-1/5"
              options={filteredDigitOptions.map((item) => ({
                value: item,
                label: item,
              }))}
              value={digit}
              mode="multiple"
              onDeselect={(value) => {
                setSelectedDigits((prevState) => {
                  const newState = [...prevState];
                  newState.splice(prevState.indexOf(value), 1);
                  return newState;
                });
              }}
              onSelect={(value) => {
                setSelectedDigits((prevState) => [...prevState, value]);
              }}
              onChange={(value) => {
                setConditionDigit(value, rowNum);
              }}
            ></Select>
            <Input
              className="w-4/5"
              placeholder="Condition Value"
              value={conditionValues[rowNum]}
              onBlur={(event) => {
                setConditionValue(event.target.value, rowNum);
              }}
            ></Input>
          </Flex>
        );
      })}
    </>
  );
};

export default FirstDigitCondition;
