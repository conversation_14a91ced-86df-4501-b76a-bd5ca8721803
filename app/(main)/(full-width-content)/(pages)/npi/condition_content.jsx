import { useCallback, useEffect, useState } from "react";
import { App, Input } from "antd";
import YHGridSimple from "../../../../../src/utils/grid/components/yh_grid_simple";
import { useBoundStore } from "../../../../../src/store/store";
import { useRenameNpiCondition } from "../../../../../src/hooks/useRenameNpiCondition";
import ConditionData from "./condition_data";
import FirstDigitCondition from "./first_digit_condition";

/**
 * Condition content component
 *
 * @param {object} item
 * @param {object} pageFilters
 * @returns {JSX.Element}
 */
const ConditionContent = ({ item, pageFilters }) => {
  const { message } = App.useApp();
  const [conditionNameValue, setConditionNameValue] = useState(
    item.condition_name_display,
  );
  const setAvailableConditions = useBoundStore(
    (state) => state.setAvailableConditions,
  );
  const setSelectedConditions = useBoundStore(
    (state) => state.setSelectedConditions,
  );
  const [renameConditionFilters, setRenameConditionFilters] = useState({});

  useEffect(() => {
    setConditionNameValue(item.condition_name_display);
  }, [item.condition_name_display]);

  /**
   * When user finished changing the name change
   *
   * @param {string} conditionNameValue
   */
  const handleConditionNameChange = useCallback((conditionNameValue) => {
    let alreadyFound = false;
    setAvailableConditions((prevState) =>
      prevState.map((condition) => {
        if (item.condition_label === condition.condition_label) {
          condition.condition_name_display = conditionNameValue;
          alreadyFound = true;
        }
        return condition;
      }),
    );
    // Don't search anymore in the selected conditions if the condition
    // was already found in the available conditions
    if (!alreadyFound) {
      setSelectedConditions((prevState) =>
        prevState.map((condition) => {
          if (item.condition_label === condition.condition_label) {
            condition.condition_name_display = conditionNameValue;
          }
          return condition;
        }),
      );
    }
  }, []);

  const { dataUpdatedAt: renameConditionUpdatedAt, data: renameConditionData } =
    useRenameNpiCondition(renameConditionFilters);

  useEffect(() => {
    if (renameConditionData?.success) {
      if (renameConditionData.data) {
        setConditionNameValue(item.condition_name_display);
        message.error("Condition name already exists. Please try again.", 5);
      } else {
        handleConditionNameChange(conditionNameValue);
      }
    }
  }, [renameConditionUpdatedAt]);

  /**
   * Details element to render depending on the type of condition
   *
   * @param {object} item
   * @returns {JSX.Element}
   */
  const detailsRender = useCallback((item) => {
    let el = "";
    if (item?.grid_name) {
      el = <ConditionData gridId={item.grid_name} conditionData={item} />;
    } else if (item.condition_type === "tnum_fdigit_as_cond") {
      el = (
        <FirstDigitCondition conditionData={item} pageFilters={pageFilters} />
      );
    } else {
      el = (
        <YHGridSimple
          columnDefs={item.col_def}
          rowData={item.table_data}
          wrapperClassName="h-48"
        />
      );
    }
    return <>{el}</>;
  }, []);

  return (
    <div>
      {item?.show_details && item.table_data && (
        <>
          <Input
            className="w-1/2 mb-1"
            value={conditionNameValue}
            placeholder="Condition Name"
            onChange={(event) => {
              setConditionNameValue(event.target.value);
            }}
            onBlur={() => {
              setRenameConditionFilters({
                ...pageFilters,
                recipe_data: {
                  conditions: [
                    {
                      ...item,
                      condition_name_display: conditionNameValue,
                    },
                  ],
                },
                time_initiated: Date.now(),
              });
            }}
          ></Input>
          {detailsRender(item)}
        </>
      )}
    </div>
  );
};

export default ConditionContent;
