"use_client";

import { useEffect, useState } from "react";
import { Form, Select } from "antd";
import { useQuery } from "@tanstack/react-query";
import Api from "../../../../../src/utils/api";

/**
 * Analysis set select component
 *
 * @param {object} params
 * @param {object} form
 * @returns {JSX.Element}
 */
const AnalysisSetSelect = ({ params, form }) => {
  const [filters, setFilters] = useState(params);

  useEffect(() => {
    setFilters(params);
  }, [params]);

  const { data, dataUpdatedAt } = useQuery({
    queryKey: ["analysis_sets", filters],
    queryFn: async () => {
      return await Api.fetchData("/api/v1/npi/analysis_set/list", "POST", {
        ...filters,
      });
    },
    enabled: filters?.recipe_name !== "",
  });

  useEffect(() => {
    if (data?.data.length) {
      form.setFieldValue("analysis_set", data.data[0].value);
    }
  }, [dataUpdatedAt]);

  return (
    <Form.Item name="analysis_set" label="" noStyle>
      <Select
        placeholder="Select Analysis Set"
        options={data?.data ?? []}
        showSearch
        popupMatchSelectWidth={false}
      ></Select>
    </Form.Item>
  );
};

export default AnalysisSetSelect;
