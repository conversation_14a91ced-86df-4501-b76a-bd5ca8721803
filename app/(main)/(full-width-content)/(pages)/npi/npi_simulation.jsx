"use client";

import { useEffect, useRef, useState } from "react";
import { Tabs, message } from "antd";
import { merge, union } from "lodash";
import { useGetAnalysisSetList } from "../../../../../src/hooks/useGetAnalysisSetList";
import { useBoundStore } from "../../../../../src/store/store";
import Helper from "../../../../../src/utils/helper";
import SimulationTabContent from "./simulation_tab_content";
import FooterNavigation from "./footer_navigation";

/**
 * NPI Simulation component
 *
 * @param {string} pageKey
 * @param {object} filters
 * @returns {JSX.Element}
 */
const NpiSimulation = ({ pageKey, filters }) => {
  const [pageFilters, setPageFilters] = useState({});
  const [selectedTests, setSelectedTests] = useState([]);
  const [activeTabKey, setActiveTabKey] = useState("");
  const [tabItems, setTabItems] = useState([]);
  const [conditionOptions, setConditionOptions] = useState({});
  const [requiredParams, setRequiredParams] = useState({});
  const [isNextBtnDisabled, setIsNextBtnDisabled] = useState(true);
  const selectedRowsForNpiSimulation = useBoundStore(
    (state) => state.selectedRowsForNpiSimulation,
  );
  const setSelectedRowsForNpiSimulation = useBoundStore(
    (state) => state.setSelectedRowsForNpiSimulation,
  );
  const setNpiSimulationTestTabsRef = useBoundStore(
    (state) => state.setNpiSimulationTestTabsRef,
  );
  const [messageApi, contextHolder] = message.useMessage();
  const tabsRef = useRef(null);

  /**
   * Set page filters
   */
  const setFilters = async () => {
    if (!filters[pageKey]?.recipe_category && filters[pageKey]?.report_key) {
      const recipeData = await Helper.getRecipeDataByReportKey(
        filters[pageKey].report_key,
        messageApi,
      );
      merge(filters[pageKey], recipeData);
    }
    const pageFilters = { ...filters[pageKey] };
    pageFilters.recipe_type = "npi";
    setPageFilters(pageFilters);
  };

  useEffect(() => {
    setFilters();
    setNpiSimulationTestTabsRef(tabsRef);

    return () => {
      setSelectedRowsForNpiSimulation(() => []);
    };
  }, []);

  useEffect(() => {
    if (selectedRowsForNpiSimulation.length) {
      const requiredParams = {};
      const tabIds = [],
        conditionOptions = {};
      selectedRowsForNpiSimulation.forEach((rowData) => {
        const uniqueParams = {
          actual_test_number: rowData.actual_test_number,
          test_type: rowData.test_type,
          test_name: rowData.test_name,
        };
        const unique = Object.values(uniqueParams).join("_");
        requiredParams[unique] = uniqueParams;
        tabIds.push(unique);
        conditionOptions[unique] = rowData.conditions
          ?.split(",")
          .map((cond) => {
            return { value: cond, label: cond };
          });
      });
      setRequiredParams((prevState) => {
        return { ...prevState, ...requiredParams };
      });
      setActiveTabKey(tabIds[tabIds.length - 1]);
      setSelectedTests((prevState) => {
        return union(prevState, tabIds);
      });
      setConditionOptions((prevState) => {
        return { ...prevState, ...conditionOptions };
      });
    }
  }, [selectedRowsForNpiSimulation]);

  const { data: analysisSetListData, dataUpdatedAt: analysisSetListUpdatedAt } =
    useGetAnalysisSetList(pageFilters);

  useEffect(() => {
    if (analysisSetListData?.success) {
      setIsNextBtnDisabled(analysisSetListData?.data.length === 0);
    }
  }, [analysisSetListUpdatedAt]);

  useEffect(() => {
    setTabItems(
      selectedTests.map((uniqueTnum) => {
        return Helper.createTabItem(
          uniqueTnum,
          `${requiredParams[uniqueTnum].actual_test_number} : ${requiredParams[uniqueTnum].test_name}`,
          <SimulationTabContent
            pageKey={pageKey}
            pageFilters={pageFilters}
            uniqueTnum={uniqueTnum}
            conditionOptions={conditionOptions[uniqueTnum]}
            requiredParams={requiredParams[uniqueTnum]}
            isNextBtnDisabled={isNextBtnDisabled}
          />,
        );
      }),
    );
  }, [selectedTests]);

  /**
   * Removes a tab when user clicks the close icon
   *
   * @param {string} targetKey
   */
  const removeTab = (targetKey) => {
    const targetIndex = tabItems.findIndex((pane) => pane.key === targetKey);
    const newPanes = tabItems.filter((pane) => pane.key !== targetKey);
    if (newPanes.length && targetKey === activeTabKey) {
      const { key } =
        newPanes[
          targetIndex === newPanes.length ? targetIndex - 1 : targetIndex
        ];
      setActiveTabKey(key);
    }
    setTabItems(newPanes);
    setSelectedTests((prevState) => {
      return prevState.filter((tnum) => targetKey !== tnum);
    });
  };

  return (
    <>
      {contextHolder}
      <div ref={tabsRef}>
        <Tabs
          type="editable-card"
          hideAdd
          activeKey={activeTabKey}
          tabPosition="top"
          className="mt-3"
          onEdit={(targetKey) => {
            removeTab(targetKey);
          }}
          onTabClick={(key) => {
            setActiveTabKey(key);
          }}
          items={tabItems}
        />
      </div>
      <FooterNavigation
        pageKey={pageKey}
        pageFilters={pageFilters}
        backData={{ pageKey: "npi_recipe" }}
        forwardData={{
          pageKey: "generate_npi_report",
        }}
        isNextBtnDisabled={isNextBtnDisabled}
      />
    </>
  );
};

export default NpiSimulation;
