"use client";

import { <PERSON><PERSON>, Form, Modal } from "antd";
import { useEffect, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import Helper from "../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../src/store/store";
import FilterSelect from "../../../../../src/utils/grid/components/filter_select";
import { reportNameSelectRules } from "../../../../../src/utils/antd_validation_rules";
import Api from "../../../../../src/utils/api";
import { NpiMapper } from "./npi_mapper";

/**
 * Load saved report modal component
 *
 * @param {boolean} open
 * @param {function} setModalOpen
 * @param {object} pageFilters
 * @param {function} loadReportCallback
 * @returns {JSX.Element}
 */
const LoadSavedReportModal = ({
  open,
  setModalOpen,
  pageFilters,
  loadReportCallback,
}) => {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const [isLoadBtnDisabled, setIsLoadBtnDisabled] = useState(true);
  const [reportDataParams, setReportDataParams] = useState(pageFilters);
  const [reportKeyParams, setReportKeyParams] = useState(pageFilters);
  const presetAnalysisTemplates = useBoundStore(
    (state) => state.presetAnalysisTemplates,
  );

  /**
   * Get NPI report data
   *
   * @param {object} filters
   * @param {function} callbackFn
   */
  const getNpiReportData = async (filters, callbackFn) => {
    if (typeof filters.report_name !== "undefined") {
      const data = await Api.fetchData(
        `/api/v1/npi/report/recipe/get/${filters.recipe_category}/${filters.recipe_name}/${filters.report_name}`,
        "GET",
      );
      callbackFn(data);
    }
  };

  useEffect(() => {
    getNpiReportData(reportDataParams, (reportData) => {
      if (reportData?.data) {
        loadReportCallback(reportData.data.report_options);
      }
    });
  }, [reportDataParams]);

  useEffect(() => {
    getNpiReportData(reportKeyParams, (reportKeyData) => {
      if (reportKeyData?.data) {
        goToReport(reportKeyData.data.npi_recipe_report_key);
      }
    });
  }, [reportKeyParams]);

  /**
   * Render the report immediately
   */
  const goToReport = (reportKey) => {
    Helper.generateAnalysis(
      NpiMapper.recipe_category_page_key[pageFilters.recipe_category],
      {
        report_key: reportKey,
      },
      presetAnalysisTemplates,
      queryClient,
      null,
      false,
      true,
    );

    setModalOpen(false);
  };

  /**
   * Load the selected report
   */
  const loadReport = () => {
    const selectedReport = form.getFieldValue("report_name").value;
    const params = { ...pageFilters, report_name: selectedReport };
    queryClient.invalidateQueries(["npi_report_data", params]);
    setReportDataParams(params);
    setModalOpen(false);
  };

  /**
   * When user cancels the modal
   */
  const handleCancel = () => {
    setModalOpen(false);
  };

  return (
    <Modal
      title="Load Report"
      open={open}
      onOk={loadReport}
      onCancel={handleCancel}
      destroyOnHidden
      footer={[
        <Button key="back" onClick={handleCancel}>
          Cancel
        </Button>,
        <Button key="submit" onClick={loadReport} disabled={isLoadBtnDisabled}>
          Load Settings
        </Button>,
        <Button
          key="goto"
          type="primary"
          onClick={() => {
            setReportKeyParams({
              ...pageFilters,
              report_name: form.getFieldValue("report_name").value,
            });
          }}
          disabled={isLoadBtnDisabled}
        >
          Go to Report
        </Button>,
      ]}
    >
      <Form form={form}>
        <Form.Item name="report_name">
          <FilterSelect
            form={form}
            className="w-full"
            componentKey="select_char_report"
            allowClear={false}
            labelInValue
            rules={reportNameSelectRules}
            placeholder="Select report"
            onChange={() => {
              setIsLoadBtnDisabled(false);
            }}
            params={{
              api: {
                url: `api/v1/npi/report/recipe/list/${pageFilters.recipe_category}/${pageFilters.recipe_name}`,
                method: "GET",
                cache_it: 0,
              },
            }}
            selectFirstOption
            fieldName="report_name"
            successCbk={(value) => {
              setIsLoadBtnDisabled(value === undefined);
            }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default LoadSavedReportModal;
