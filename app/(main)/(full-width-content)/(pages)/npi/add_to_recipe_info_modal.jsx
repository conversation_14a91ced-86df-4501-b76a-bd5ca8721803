"use client";

import { But<PERSON>, Checkbox, List, Modal, Typography } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { useGetRecipeData } from "../../../../../src/hooks/useGetRecipeData";
import GridHelper from "../../../../../src/utils/grid/grid_helper";

/**
 * Apply to recipe modal component
 *
 * @param {boolean} isModalOpen
 * @param {function} setIsModalOpen
 * @param {object} searchGridRef
 * @param {object} pageFilters
 * @param {function} renderRecipePage
 * @returns {JSX.Element}
 */
const AddToRecipeInfoModal = ({
  isModalOpen,
  setIsModalOpen,
  searchGridRef,
  pageFilters,
  renderRecipePage,
}) => {
  const [filenames, setFilenames] = useState([]);
  const [recipeDataParams, setRecipeDataParams] = useState({});

  useEffect(() => {
    if (isModalOpen && searchGridRef?.current) {
      const selectedRows = GridHelper.getServerSideSelectedRows(
        searchGridRef.current,
      );
      setFilenames(selectedRows.map((rowData) => rowData.file_name));
    }
  }, [isModalOpen]);

  const { data: recipeData, dataUpdatedAt: recipeDataUpdatedAt } =
    useGetRecipeData(recipeDataParams);

  useEffect(() => {
    if (recipeData?.data?.source_dsks?.length) {
      const filters = { ...pageFilters };
      const sourceDsks = Array.isArray(recipeData.data.source_dsks)
        ? recipeData.data.source_dsks.join(",")
        : recipeData.data.source_dsks;

      // Handle case where pageFilters.dsk might be undefined
      const existingDsks = pageFilters?.dsk ? pageFilters.dsk.split(",") : [];

      // Ensure sourceDsks is always an array before splitting
      const sourceArray =
        typeof sourceDsks === "string" ? sourceDsks.split(",") : [];

      // Merge and remove duplicates
      filters.dsk = [...new Set([...sourceArray, ...existingDsks])].join(",");

      renderRecipePage(filters);
      setIsModalOpen(false);
    }
  }, [recipeDataUpdatedAt]);

  /**
   * Closes this modal
   */
  const closeModal = () => {
    setIsModalOpen(false);
  };

  /**
   * Handles the generation of report
   */
  const handleGenerateReport = () => {};

  /**
   * Proceeds to recipe page
   */
  const handleGotoRecipe = () => {
    setRecipeDataParams({ recipe_type: "npi", ...pageFilters });
  };

  return (
    <Modal
      title={
        <>
          <InfoCircleOutlined className="text-amber-500 mr-2" />
          Datalogs Added to Recipe
        </>
      }
      open={isModalOpen}
      onOk={closeModal}
      onCancel={closeModal}
      width={"40vw"}
      destroyOnHidden
      footer={[
        <Button key="goto_recipe" onClick={handleGotoRecipe}>
          Go to Recipe
        </Button>,
        <Button key="generate" onClick={handleGenerateReport} disabled>
          Generate Report
        </Button>,
        <Button key="submit" type="primary" onClick={closeModal}>
          Close
        </Button>,
      ]}
    >
      <List
        pagination={{
          position: "bottom",
          align: "end",
          pageSize: 5,
        }}
        size="small"
        header={
          <div>
            Recipe{" "}
            <Typography.Text strong>{pageFilters.recipe_name}</Typography.Text>{" "}
            has been applied to these datalogs:
          </div>
        }
        footer={
          <div className="flex flex-col">
            <Typography.Text>
              You will be notified once the processing is done or if an issue
              was encountered.
            </Typography.Text>
            <Checkbox checked disabled>
              Notify me via email
            </Checkbox>
          </div>
        }
        dataSource={filenames}
        renderItem={(item) => (
          <List.Item>
            <Typography.Paragraph
              ellipsis={{
                rows: 2,
                expandable: true,
                symbol: "more",
              }}
            >
              {item}
            </Typography.Paragraph>
          </List.Item>
        )}
      ></List>
    </Modal>
  );
};

export default AddToRecipeInfoModal;
