"use_client";

import { But<PERSON>, Checkbox, Form, Popconfirm, message } from "antd";
import { useState } from "react";
import { useSaveAnalysisSet } from "../../../../../src/hooks/useSaveAnalysisSet";
import <PERSON>rid<PERSON><PERSON><PERSON> from "../../../../../src/utils/grid/grid_helper";
import AnalysisSetSelect from "./analysis_set_select";

/**
 * Assign/Remove analysis set form component
 *
 * @param {object} pageFilters
 * @param {object} gridRef
 * @returns {JSX.Element}
 */
const AssignAnalysisSetForm = ({ pageFilters, gridRef }) => {
  const [form] = Form.useForm();
  const [applyToAll, setApplyToAll] = useState(false);
  const [saveApplyFilters, setSaveApplyFilters] = useState({ recipe_type: "" });
  const [messageApi, contextHolder] = message.useMessage();

  /**
   * Add/Remove analysis set tag to selected rows
   *
   * @param {object} filters
   */
  const updateAnalysisSet = (filters) => {
    const rows = applyToAll
      ? GridHelper.getAllRows(gridRef.current)
      : GridHelper.getServerSideSelectedNodes(gridRef.current);
    rows.forEach((node) => {
      const analysisSet = filters.analysis_set_name;
      let analysisSetArr = node.data.analysis_set
        ? node.data.analysis_set.split(",")
        : [];
      if (filters.action === "assign") {
        if (!analysisSetArr.includes(analysisSet)) {
          analysisSetArr.push(analysisSet);
        }
      } else if (filters.action === "remove") {
        analysisSetArr = analysisSetArr.filter((item) => item !== analysisSet);
      }
      node.data.analysis_set = analysisSetArr.join(",");
    });
    gridRef.current.api.refreshCells();

    if (filters.action === "assign") {
      messageApi.success("Analysis set was saved successfully.");
    } else if (filters.action === "remove") {
      messageApi.success("Analysis set was removed successfully.");
    }
  };

  useSaveAnalysisSet(
    saveApplyFilters,
    saveApplyFilters.action,
    updateAnalysisSet,
    saveApplyFilters.all,
  );

  /**
   * Assign selected analysis set to selected rows
   *
   * @param {string} action
   */
  const assignAnalysisSet = (action) => {
    const values = form.getFieldsValue(true);
    const analysisSet = values.analysis_set;
    const applyToAll = values.apply_to_all;
    const testInfo = [];
    const rows = applyToAll
      ? GridHelper.getAllRows(gridRef.current)
      : GridHelper.getServerSideSelectedNodes(gridRef.current);
    rows.forEach((node) => {
      const rowData = node.data;
      testInfo.push({
        actual_test_number: rowData.actual_test_number,
        test_number: rowData.test_number,
        test_type: rowData.test_type,
        test_name: rowData.test_name,
      });
    });

    setSaveApplyFilters({
      recipe_type: "npi",
      analysis_set_name: analysisSet,
      ...pageFilters,
      test_info: testInfo,
      action: action,
      all: applyToAll ? 1 : 0,
    });
  };

  /**
   * Get assign or remove analysis set button element
   *
   * @param {string} label
   * @param {string} action
   * @param {object} btnProps
   * @returns {JSX.Element}
   */
  const getActionBtn = (label, action, btnProps = {}) => {
    return applyToAll ? (
      <Popconfirm
        title={`Are you sure you want to ${action} analysis set to all tests?`}
        onConfirm={() => assignAnalysisSet(action)}
        okText="Yes"
        cancelText="No"
      >
        <Button {...btnProps}>{label}</Button>
      </Popconfirm>
    ) : (
      <Button {...btnProps} onClick={() => assignAnalysisSet(action)}>
        {label}
      </Button>
    );
  };

  return (
    <>
      {contextHolder}
      <Form form={form} className="flex gap-1">
        <AnalysisSetSelect form={form} params={pageFilters} />
        {getActionBtn("Assign Analysis Set", "assign")}
        {getActionBtn("Remove Analysis Set", "remove", { danger: true })}
        <Form.Item
          name="apply_to_all"
          valuePropName="checked"
          className="!mb-0"
        >
          <Checkbox
            checked={applyToAll}
            onChange={(e) => {
              setApplyToAll(e.target.checked);
            }}
          >
            Apply to All
          </Checkbox>
        </Form.Item>
      </Form>
    </>
  );
};

export default AssignAnalysisSetForm;
