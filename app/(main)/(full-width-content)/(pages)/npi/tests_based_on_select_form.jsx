"use_client";

import { Form, Select } from "antd";
import { useEffect } from "react";
import { useBoundStore } from "../../../../../src/store/store";

/**
 * Tests based on select component
 *
 * @param {string} pageKey
 * @param {function} setGridKey
 * @returns {JSX.Element}
 */
const TestsBasedOnSelectForm = ({ pageKey, setGridKey }) => {
  const [form] = Form.useForm();
  const npiRecipeData = useBoundStore((state) => state.npiRecipeData);

  useEffect(() => {
    form.setFieldValue(
      "linked_by",
      npiRecipeData.recipe_data?.linked_by ?? "ttype-atnum-tname",
    );
  }, [npiRecipeData.recipe_data?.linked_by]);

  /**
   * Reload test list grid and set `linked by` new value
   *
   * @param {string} value
   */
  const reloadTestListGrid = (value) => {
    setGridKey(pageKey + value);
    npiRecipeData.recipe_data ??= {};
    npiRecipeData.recipe_data.linked_by = value;
  };

  return (
    <Form form={form} className="flex gap-1">
      <Form.Item name="linked_by" label="Tests Based On" className="w-1/4">
        <Select
          options={[
            {
              value: "ttype-atnum-tname",
              label: "Test Type + Test Number + Test Name",
            },
            {
              value: "ttype-atnum",
              label: "Test Type + Test Number",
            },
            {
              value: "ttype-tname",
              label: "Test Type + Test Name",
            },
          ]}
          onChange={reloadTestListGrid}
          showSearch
        ></Select>
      </Form.Item>
    </Form>
  );
};

export default TestsBasedOnSelectForm;
