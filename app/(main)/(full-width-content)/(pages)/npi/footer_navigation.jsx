"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, Popconfirm } from "antd";
import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import Helper from "../../../../../src/utils/helper";
import { useEffectApiFetch } from "../../../../../src/hooks";

/**
 * Footer navigation component
 *
 * @param {object} pageFilters
 * @param {object} backData
 * @param {object} forwardData
 * @param {boolean} isNextPopconfirmOpen
 * @param {function} nextBtnConfirmCallback
 * @param {function} nextBtnCancelCallback
 * @param {boolean} isNextBtnDisabled
 * @returns {JSX.Element}
 */
const FooterNavigation = ({
  pageFilters,
  backData,
  forwardData,
  isNextPopconfirmOpen,
  nextBtnConfirmCallback,
  nextBtnCancelCallback,
  isNextBtnDisabled = true,
}) => {
  const queryClient = useQueryClient();
  const [presetAnalysisTemplates, setPresetAnalysisTemplates] = useState([]);

  useEffectApiFetch(
    () => {
      return Helper.getPresetAnalysisTemplates(setPresetAnalysisTemplates);
    },
    () => {
      setPresetAnalysisTemplates([]);
    },
  );

  /**
   * Renders the previous page
   */
  const renderBackPage = () => {
    Helper.generateAnalysis(
      backData.pageKey,
      pageFilters,
      presetAnalysisTemplates,
      queryClient,
    );
  };

  /**
   * Renders the next page
   */
  const renderNextPage = () => {
    if (typeof forwardData?.callbackBeforeRender === "function") {
      forwardData.callbackBeforeRender();
    } else {
      Helper.generateAnalysis(
        forwardData.pageKey,
        pageFilters,
        presetAnalysisTemplates,
        queryClient,
        null,
        forwardData.saveHistory ?? true,
        forwardData?.newTab ?? false,
      );
    }
  };

  return (
    <Flex justify="flex-end" gap="small" className="!mt-4">
      <Button onClick={renderBackPage}>Back</Button>
      <Popconfirm
        title="Report Name Already Exists!"
        description="What do you want to do?"
        okText="Overwrite Existing"
        cancelText="Rename Report"
        open={isNextPopconfirmOpen}
        onConfirm={nextBtnConfirmCallback}
        onCancel={nextBtnCancelCallback}
      >
        <Button
          type={forwardData?.buttonType ?? "default"}
          onClick={renderNextPage}
          disabled={isNextBtnDisabled}
        >
          {forwardData?.buttonLabel ?? "Next"}
        </Button>
      </Popconfirm>
    </Flex>
  );
};

export default FooterNavigation;
