"use_client";

import { Button, Form, Modal, message } from "antd";
import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import Api from "../../../../../src/utils/api";
import FilterSelect from "../../../../../src/utils/grid/components/filter_select";
import AddToRecipeInfoModal from "./add_to_recipe_info_modal";

/**
 * Load recipe modal component for NPI
 *
 * @param {boolean} isModalOpen
 * @param {boolean} setIsModalOpen
 * @param {object} selectedParams
 * @param {object} searchGridRef
 * @param {function} renderRecipePage
 * @returns {JSX.Element}
 */
const AddToRecipeModal = ({
  isModalOpen,
  setIsModalOpen,
  selectedParams = {},
  searchGridRef,
  renderRecipePage,
}) => {
  const [pageFilters, setPageFilters] = useState([]);
  const [analysisType, setAnalysisType] = useState("");
  const [recipeCreator, setRecipeCreator] = useState("");
  const [isAddToRecipeBtnDisabled, setIsAddToRecipeBtnDisabled] =
    useState(true);
  const [formValues, setFormValues] = useState({});
  const [isAddToRecipeInfoModalOpen, setIsAddToRecipeInfoModalOpen] =
    useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [form] = Form.useForm();

  /**
   * Construct filters based from the selected parameters of the search table
   *
   * @returns {object} filters
   */
  const getPageFilters = () => {
    const filters = {};
    Object.keys(selectedParams)
      .filter((key) => selectedParams[key]?.length)
      .forEach((key) => {
        filters[key] = Array.isArray(selectedParams[key])
          ? selectedParams[key].join(",")
          : selectedParams[key];
      });

    return filters;
  };

  /**
   * Setup the filters
   */
  useEffect(() => {
    const filters = getPageFilters();
    setPageFilters(filters);
  }, [selectedParams]);

  const { dataUpdatedAt: addToRecipeUpdatedAt, data: addToRecipeData } =
    useQuery({
      queryKey: ["add_source_dsk", pageFilters, formValues],
      queryFn: async () => {
        return await Api.fetchData(
          `/api/v1/npi/recipe/add_source_dsk`,
          "POST",
          {
            ...pageFilters,
            ...formValues,
          },
        );
      },
      enabled: typeof formValues.recipe_name !== "undefined",
    });

  useEffect(() => {
    if (addToRecipeUpdatedAt) {
      if (addToRecipeData?.success) {
        if (addToRecipeData.data === 0) {
          messageApi.warning("Recipe has already been applied to the datalog.");
        } else {
          setIsAddToRecipeInfoModalOpen(true);
        }
      } else {
        messageApi.error(addToRecipeData?.message, 5);
      }
    }
  }, [addToRecipeUpdatedAt]);

  /**
   * Handles the adding of selected dsks to the selected recipe
   */
  const handleAddToRecipe = () => {
    form
      .validateFields()
      .then((values) => {
        setFormValues({
          recipe_category: values.recipe_category.value,
          user_name: values.user_name.value,
          recipe_name: values.recipe_name.value,
        });
        setIsModalOpen(false);
      })
      .catch((error) => {
        messageApi.error(error.errorFields[0].errors[0], 5);
      });
  };

  /**
   * Event handler when users cancels/closes the modal
   */
  const closeModal = () => {
    setIsModalOpen(false);
  };

  /**
   * When any of the form field changes
   */
  const handleFieldsChange = () => {
    const values = form.getFieldsValue();
    setAnalysisType(values.recipe_category.value);
    setRecipeCreator(values.user_name.value);
    setIsAddToRecipeBtnDisabled(!values.recipe_name);
  };

  return (
    <>
      {contextHolder}
      <AddToRecipeInfoModal
        isModalOpen={isAddToRecipeInfoModalOpen}
        setIsModalOpen={setIsAddToRecipeInfoModalOpen}
        searchGridRef={searchGridRef}
        pageFilters={{ ...pageFilters, ...formValues }}
        renderRecipePage={renderRecipePage}
      />
      <Modal
        title="Add to NPI Recipe"
        open={isModalOpen}
        onOk={handleAddToRecipe}
        onCancel={closeModal}
        width={"40vw"}
        destroyOnHidden
        footer={[
          <Button key="back" onClick={closeModal}>
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleAddToRecipe}
            disabled={isAddToRecipeBtnDisabled}
          >
            Add to Recipe
          </Button>,
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          className="mt-4"
          onFieldsChange={handleFieldsChange}
        >
          <Form.Item name="recipe_category" label="Analysis Type">
            <FilterSelect
              form={form}
              placeholder="Select Analysis Type"
              className="w-full"
              componentKey="select_recipe_category"
              allowClear={false}
              params={{
                api: {
                  url: `api/v1/npi/recipe/categories`,
                  method: "GET",
                  cache_it: 0,
                },
              }}
              showSearch
              labelInValue
              selectFirstOption
              fieldName={"recipe_category"}
              successCbk={(option) => {
                setAnalysisType(option.value);
              }}
            />
          </Form.Item>

          <Form.Item name="user_name" label="Recipe Creator">
            <FilterSelect
              form={form}
              placeholder="Select Recipe Creator"
              className="w-full"
              componentKey="select_recipe_creator"
              allowClear={false}
              params={{
                api: {
                  url: `api/v1/npi/recipe/creators`,
                  method: "GET",
                  cache_it: 0,
                },
              }}
              showSearch
              labelInValue
              selectFirstOption
              fieldName={"user_name"}
              successCbk={(option) => {
                setRecipeCreator(option.value);
              }}
            />
          </Form.Item>

          {recipeCreator && analysisType && (
            <Form.Item
              name="recipe_name"
              label="Recipe Name"
              rules={[{ required: true, message: "Select recipe." }]}
            >
              <FilterSelect
                form={form}
                placeholder="Select Recipe"
                className="w-full"
                componentKey="select_char_report"
                allowClear={false}
                labelInValue
                params={{
                  api: {
                    url: `api/v1/npi/recipe/list`,
                    cache_it: 0,
                    dsk: pageFilters.dsk,
                    lot_id: pageFilters.lot_id,
                    mfg_process: pageFilters.mfg_process,
                    src_type: pageFilters.src_type,
                    src_value: pageFilters.src_value,
                    recipe_category: analysisType,
                    user_name: recipeCreator,
                  },
                }}
                showSearch
                selectFirstOption
                deps={[recipeCreator, analysisType]}
                fieldName={"recipe_name"}
                successCbk={(value) => {
                  setIsAddToRecipeBtnDisabled(value === undefined);
                }}
              />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </>
  );
};

export default AddToRecipeModal;
