"use client";

import { <PERSON><PERSON>, <PERSON>lex, List, Skeleton, Typography } from "antd";
import InfiniteScroll from "react-infinite-scroll-component";
import { useCallback } from "react";
import ConditionContent from "./condition_content";

/**
 * Infinite scroll conditions component
 *
 * @param {object} data
 * @param {function} setData
 * @param {function} selectConditionHandler
 * @param {function} removeConditionHandler
 * @param {boolean} loading
 * @param {object} pageFilters
 * @returns {JSX.Element}
 */
const InfiniteScrollConditions = ({
  data,
  setData,
  selectConditionHandler,
  removeConditionHandler,
  loading = true,
  pageFilters,
}) => {
  /**
   * Open the details section
   */
  const openDetails = useCallback(
    (item) => {
      setData((prevState) => {
        const newState = [...prevState];
        newState.forEach((condition) => {
          if (condition.condition_label === item.condition_label) {
            condition.show_details = !condition?.show_details;
          }
        });
        return newState;
      });
    },
    [data, pageFilters],
  );

  return (
    <div id="scrollableDiv" className="h-[35vh] overflow-auto pr-4 pl-4">
      <InfiniteScroll
        dataLength={data.length}
        hasMore={data.length < 50}
        scrollableTarget="scrollableDiv"
        loader={<Skeleton loading={loading} active />}
      >
        <List
          loading={loading}
          dataSource={data}
          itemLayout="vertical"
          renderItem={(item) => (
            <List.Item key={item.condition_label}>
              <List.Item.Meta
                title={
                  <Flex justify="space-between">
                    <Flex>
                      <Typography.Text>
                        {item.condition_type_display ?? item.condition_type}
                        :{" "}
                      </Typography.Text>
                      <Typography.Text type="secondary">
                        {item.condition_name_display ?? item.condition_name}
                      </Typography.Text>
                    </Flex>
                    <Flex>
                      <Button
                        color="primary"
                        variant="link"
                        onClick={() => {
                          openDetails(item);
                        }}
                      >
                        {item?.show_details ? "Hide Details" : "Show Details"}
                      </Button>
                      {selectConditionHandler && (
                        <Button
                          color="primary"
                          variant="link"
                          disabled={item.condition_name_display === ""}
                          onClick={() => {
                            selectConditionHandler(item);
                          }}
                        >
                          Add
                        </Button>
                      )}
                      {removeConditionHandler && (
                        <Button
                          color="primary"
                          variant="link"
                          onClick={() => {
                            removeConditionHandler(item);
                          }}
                        >
                          Remove
                        </Button>
                      )}
                    </Flex>
                  </Flex>
                }
              />
              <ConditionContent item={item} pageFilters={pageFilters} />
            </List.Item>
          )}
        />
      </InfiniteScroll>
    </div>
  );
};

export default InfiniteScrollConditions;
