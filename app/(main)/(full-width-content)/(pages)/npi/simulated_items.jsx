"use client";

import { memo, useCallback, useState } from "react";
import { DeleteOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Row, Tooltip, message } from "antd";
import {
  getAnalysisSetFilters,
  getGroupKey,
} from "../../../../../src/utils/components/recipe_common/helpers";
import { OptionsList } from "../../../../../src/utils/forms/options_list";
import Api from "../../../../../src/utils/api";
import { useEffectApiFetch } from "../../../../../src/hooks";
import SimulatedItem from "./simulated_item";

/**
 * Simulate items component
 *
 * @param {string} pageKey
 * @param {object} pageFilters
 * @param {string} uniqueTnum
 * @param {array} allFormValues
 * @param {function} setAllFormValues
 * @param {object} requiredParams
 * @returns {JSX.Element}
 */
const SimulatedItems = ({
  pageKey,
  pageFilters,
  uniqueTnum,
  allFormValues,
  setAllFormValues,
  requiredParams = {},
}) => {
  const [componentBlueprints, setComponentBlueprints] = useState();
  const [messageApi, contextHolder] = message.useMessage();

  useEffectApiFetch(
    () => {
      return getComponentBlueprints();
    },
    () => {
      setComponentBlueprints();
    },
    [],
  );

  /**
   * Get component blueprints to be used when generating simulated items
   */
  const getComponentBlueprints = () => {
    return Api.getAnalysisTemplateComponents(
      (res) => {
        if (res.success) {
          const components = res.data.test;
          const blueprints = components.reduce((result, component) => {
            result[component.name] = component;
            return result;
          }, {});
          setComponentBlueprints(blueprints);
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      "test",
    );
  };

  /**
   * Get the filters per item
   */
  const getItemFilters = useCallback(
    (itemIdx) => {
      const filters = {};
      filters[pageKey] = {
        ...pageFilters,
        tNum: uniqueTnum.split("_")[0],
        ...requiredParams,
        ...getAnalysisSetFilters(allFormValues[itemIdx]),
      };
      filters[pageKey].stats_type_display = OptionsList.npi_stats_types.find(
        (option) => option.value === filters[pageKey].filters.stats_type,
      ).label;
      return filters;
    },
    [allFormValues],
  );

  /**
   * The index of the item group
   *
   * @param {int} itemIdx
   */
  const removeItemGroup = (itemIdx) => {
    setAllFormValues((prevState) => {
      const newState = [...prevState];
      newState.splice(itemIdx, 1);
      return newState;
    });
  };

  return (
    <>
      {contextHolder}
      {componentBlueprints &&
        allFormValues.map((itemGroup, groupIdx) => {
          const groupKey = getGroupKey(itemGroup);
          return (
            <Card
              className="mb-3"
              key={groupKey}
              extra={
                <Tooltip title="Remove this group of items.">
                  <Button
                    icon={<DeleteOutlined />}
                    onClick={() => {
                      removeItemGroup(groupIdx);
                    }}
                  >
                    Remove
                  </Button>
                </Tooltip>
              }
            >
              <Row gutter={[16, 16]}>
                {[...itemGroup.tables, ...itemGroup.charts].map((item) => {
                  const component = componentBlueprints[item];
                  component.id = `component_${groupIdx}_${component.name}_${item}_${uniqueTnum}`;

                  return (
                    <SimulatedItem
                      key={`${groupKey}_${item}_item`}
                      colKey={`${groupKey}_${item}`}
                      component={component}
                      group={itemGroup}
                      pageKey={pageKey}
                      filters={getItemFilters(groupIdx)}
                    />
                  );
                })}
              </Row>
            </Card>
          );
        })}
    </>
  );
};

export default memo(SimulatedItems);
