"use client";

import { useEffect, useState } from "react";
import { Collapse, Form, message, theme } from "antd";
import { useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Helper from "../../../../../src/utils/helper";
import { useSaveReport } from "../../../../../src/hooks/useSaveReport";
import { useCheckNpiReport } from "../../../../../src/hooks/useCheckNpiReport";
import { useEffectApiFetch } from "../../../../../src/hooks";
import { useBoundStore } from "../../../../../src/store/store";
import ReportOptions from "./report_options";
import FooterNavigation from "./footer_navigation";
import { NpiMapper } from "./npi_mapper";

/**
 * Generate report options component
 *
 * @param {string} pageKey
 * @param {object} filters
 * @returns {JSX.Element}
 */
const GenerateReportOptions = ({ pageKey, filters }) => {
  const { token } = theme.useToken();
  const [reportOptionsForm] = Form.useForm();
  const panelStyle = {
    marginBottom: 16,
    background: token.yhHeaderColorBg,
    borderRadius: token.borderRadius,
  };
  const queryClient = useQueryClient();
  const [pageFilters, setPageFilters] = useState();
  const [saveReportFilters, setSaveReportFilters] = useState({
    recipe_type: "",
  });
  const [activeCollapseKey, setActiveCollapseKey] = useState([
    "report_options",
  ]);
  const [checkReportParams, setCheckReportParams] = useState({});
  const [isNextPopconfirmOpen, setIsNextPopconfirmOpen] = useState(false);
  const [isNextBtnDisabled, setIsNextBtnDisabled] = useState(true);
  const [presetAnalysisTemplates, setPresetAnalysisTemplates] = useState([]);
  const npiReportOptionsTestFilters = useBoundStore(
    (state) => state.npiReportOptionsTestFilters,
  );
  const [messageApi, contextHolder] = message.useMessage();

  const forwardData = {
    pageKey:
      NpiMapper.recipe_category_page_key[filters[pageKey]?.recipe_category],
    buttonLabel: "Generate Report",
    saveHistory: false,
    newTab: true,
    buttonType: "primary",
  };

  useEffectApiFetch(
    () => {
      return Helper.getPresetAnalysisTemplates(setPresetAnalysisTemplates);
    },
    () => {
      setPresetAnalysisTemplates([]);
    },
  );

  useEffect(() => {
    setFilters();
  }, []);

  /**
   * Set page filters
   */
  const setFilters = async () => {
    if (!filters[pageKey]?.recipe_category && filters[pageKey]?.report_key) {
      const recipeData = await Helper.getRecipeDataByReportKey(
        filters[pageKey].report_key,
        messageApi,
      );
      merge(filters[pageKey], recipeData);
    }
    const pageFilters = { ...filters[pageKey] };
    pageFilters.recipe_type = "npi";
    setPageFilters(pageFilters);
  };

  /**
   * Render NPI report page
   *
   * @param {object} params
   */
  const renderNpiReport = (params) => {
    Helper.generateAnalysis(
      forwardData.pageKey,
      params,
      presetAnalysisTemplates,
      queryClient,
      null,
      forwardData.saveHistory,
      forwardData.newTab,
    );
  };
  const { data: saveReportData, dataUpdatedAt: saveReportDataUpdatedAt } =
    useSaveReport(saveReportFilters);

  useEffect(() => {
    if (saveReportDataUpdatedAt) {
      if (saveReportData.success) {
        renderNpiReport({
          report_key: saveReportData.data.report_key,
          timestamp: saveReportData.data.timestamp,
        });
      } else {
        messageApi.warning(saveReportData.message, 10);
      }
    }
  }, [saveReportDataUpdatedAt]);

  /**
   * Save report button handler
   */
  const saveReport = () => {
    const values = reportOptionsForm.getFieldsValue();
    setSaveReportFilters({
      ...pageFilters,
      recipe_type: "npi",
      report_name: values.report_name,
      report_description: values.report_description,
      report_options: { ...values, ...npiReportOptionsTestFilters },
      time_initiated: Date.now(),
    });
    setIsNextPopconfirmOpen(false);
  };

  const { data: checkReportData, dataUpdatedAt: checkReportDataUpdatedAt } =
    useCheckNpiReport(checkReportParams);

  useEffect(() => {
    if (typeof checkReportData?.success !== "undefined") {
      if (checkReportData.success) {
        saveReport();
      } else {
        setIsNextPopconfirmOpen(true);
      }
    }
  }, [checkReportDataUpdatedAt]);

  /**
   * Check report if already existed
   */
  const checkReport = () => {
    setCheckReportParams({
      ...pageFilters,
      report_name: reportOptionsForm.getFieldValue("report_name"),
      time_initiated: Date.now(),
    });
  };

  return (
    <>
      {contextHolder}
      {pageFilters && (
        <Collapse
          activeKey={activeCollapseKey}
          className="mt-3"
          onChange={(key) => {
            setActiveCollapseKey(key);
          }}
          style={{
            background: token.colorBgContainer,
          }}
          items={[
            {
              key: "report_options",
              label: "Report Details",
              style: panelStyle,
              children: (
                <ReportOptions
                  form={reportOptionsForm}
                  pageFilters={pageFilters}
                  setIsNextBtnDisabled={setIsNextBtnDisabled}
                />
              ),
            },
          ]}
        />
      )}
      <FooterNavigation
        pageKey={pageKey}
        pageFilters={pageFilters}
        backData={{
          pageKey: "simulate_npi_report",
        }}
        forwardData={{ ...forwardData, callbackBeforeRender: checkReport }}
        isNextPopconfirmOpen={isNextPopconfirmOpen}
        nextBtnConfirmCallback={saveReport}
        nextBtnCancelCallback={() => {
          setIsNextPopconfirmOpen(false);
        }}
        isNextBtnDisabled={isNextBtnDisabled}
      />
    </>
  );
};

export default GenerateReportOptions;
