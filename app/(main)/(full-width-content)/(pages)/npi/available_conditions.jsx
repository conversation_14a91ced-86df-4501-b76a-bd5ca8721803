"use client";

import { CheckOutlined, StopOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, A<PERSON>, But<PERSON>, Checkbox, Divider, Flex, Input } from "antd";
import { memo, useCallback, useEffect, useState } from "react";
import { debounce } from "lodash";
import { useGetNpiConditionTypes } from "../../../../../src/hooks/useGetNpiConditionTypes";
import { useBoundStore } from "../../../../../src/store/store";
import { useGetAvailableNpiConditions } from "../../../../../src/hooks/useGetAvailableNpiConditions";
import { useAddNpiConditionToRecipe } from "../../../../../src/hooks/useAddNpiConditionToRecipe";
import InfiniteScrollConditions from "./infinite_scroll_conditions";

const { Search } = Input;

/**
 * Available conditions component
 *
 * @param {object} pageFilters
 * @param {function} setDisableSkipToSimulateBtn
 * @param {function} setDisableNextBtn
 * @returns {JSX.Element}
 */
const AvailableConditions = ({
  pageFilters,
  setDisableSkipToSimulateBtn,
  setDisableNextBtn,
}) => {
  const { message } = App.useApp();
  const availableConditions = useBoundStore(
    (state) => state.availableConditions,
  );
  const setAvailableConditions = useBoundStore(
    (state) => state.setAvailableConditions,
  );
  const setSelectedConditions = useBoundStore(
    (state) => state.setSelectedConditions,
  );
  const filteredConditions = useBoundStore((state) => state.filteredConditions);
  const setFilteredConditions = useBoundStore(
    (state) => state.setFilteredConditions,
  );
  const npiRecipeProcessingStatus = useBoundStore(
    (state) => state.npiRecipeProcessingStatus,
  );
  const setNpiRecipeProcessingStatus = useBoundStore(
    (state) => state.setNpiRecipeProcessingStatus,
  );
  const [conditionTypes, setConditionTypes] = useState([]);
  const [selectedConditionTypes, setSelectedConditionTypes] = useState([]);
  const [searchedCondition, setSearchedCondition] = useState("");
  const [conditionIsLoading, setConditionIsLoading] = useState(true);
  const [addConditionToRecipeFilters, setAddConditionToRecipeFilters] =
    useState({});
  const [errorMessage, setErrorMessage] = useState();

  const { dataUpdatedAt: conditionTypesUpdatedAt, data: conditionTypesData } =
    useGetNpiConditionTypes(pageFilters.recipe_category);

  useEffect(() => {
    if (conditionTypesData?.success) {
      const conditionTypeKeys = Object.keys(conditionTypesData.data);
      setConditionTypes(
        conditionTypeKeys.map((key) => ({
          value: key,
          label: conditionTypesData.data[key],
        })),
      );
      setSelectedConditionTypes(Object.keys(conditionTypesData.data));
    }
  }, [conditionTypesUpdatedAt]);

  const {
    dataUpdatedAt: availableConditionsUpdatedAt,
    data: availableConditionsData,
    refetch: refetchAvailableConditionsData,
  } = useGetAvailableNpiConditions(pageFilters);

  useEffect(() => {
    if (npiRecipeProcessingStatus.loading === false) {
      refetchAvailableConditionsData();
      setNpiRecipeProcessingStatus({});
    }
  }, [npiRecipeProcessingStatus]);

  useEffect(() => {
    if (availableConditionsData) {
      if (availableConditionsData.success) {
        setAvailableConditions(() => availableConditionsData.data.conditions);
        setConditionIsLoading(availableConditionsData.data.loading);
        setErrorMessage();
      } else {
        setConditionIsLoading(availableConditionsData.data.loading);
        setErrorMessage(availableConditionsData.message);
        setDisableSkipToSimulateBtn(true);
        setDisableNextBtn(true);
      }
    }
  }, [availableConditionsUpdatedAt]);

  /**
   * Sync the filtered conditions with the available conditions
   */
  useEffect(() => {
    setFilteredConditions(() => availableConditions);
  }, [availableConditions]);

  useEffect(() => {
    handleConditionTypeFilterChange(selectedConditionTypes);
  }, [selectedConditionTypes]);

  const {
    dataUpdatedAt: addConditionToRecipeUpdatedAt,
    data: addConditionToRecipeData,
  } = useAddNpiConditionToRecipe(addConditionToRecipeFilters);

  useEffect(() => {
    if (addConditionToRecipeData?.success) {
      if (addConditionToRecipeData?.data) {
        message.error(
          "Unable to add the condition. Condition name already exists.",
          5,
        );
      } else {
        const selectedCondition =
          addConditionToRecipeFilters.recipe_data.conditions[0];
        setSelectedConditions((prevState) => [...prevState, selectedCondition]);
        setAvailableConditions((prevState) =>
          prevState.filter(
            (condition) =>
              selectedCondition.condition_label !== condition.condition_label,
          ),
        );
      }
    }
  }, [addConditionToRecipeUpdatedAt]);

  /**
   * Select a condition
   *
   * @param {object} selectedCondition
   */
  const selectCondition = useCallback(
    (selectedCondition) => {
      setAddConditionToRecipeFilters({
        ...pageFilters,
        recipe_data: {
          conditions: [selectedCondition],
        },
        time_initiated: Date.now(),
      });
    },
    [pageFilters],
  );

  /**
   * When user checks any of the condition type checkboxes
   *
   * @param {array} value
   */
  const handleConditionTypeFilterChange = (value) => {
    setSelectedConditionTypes(value);
    setFilteredConditions(() =>
      availableConditions.filter(
        (condition) =>
          value.includes(condition.condition_type) &&
          condition.condition_label.includes(searchedCondition),
      ),
    );
  };

  /**
   * Search the available conditions with keyword and with consideration to the
   *  condition type filter
   */
  const searchConditions = useCallback(
    debounce((value, conditions, selectedConditionTypes) => {
      const searchedConditions = conditions.filter(
        (condition) =>
          condition.condition_label
            .toLowerCase()
            .includes(value.toLowerCase()) &&
          selectedConditionTypes.includes(condition.condition_type),
      );
      setFilteredConditions(() => searchedConditions);
      setSearchedCondition(value);
    }, 250),
    [],
  );

  return (
    <Flex vertical gap={"small"}>
      <Divider>Available Conditions</Divider>
      {errorMessage ? (
        <Alert message={errorMessage} type="error" showIcon />
      ) : (
        <>
          <Flex
            gap="middle"
            justify="space-between"
            align="center"
            className="border border-solid border-gray-200 p-2"
          >
            <Search
              placeholder="Search condition"
              allowClear
              className="w-1/4"
              onChange={(event) => {
                searchConditions(
                  event.target.value,
                  availableConditions,
                  selectedConditionTypes,
                );
              }}
            />
            <div className="w-3/4">
              <Flex className="!m-1" justify="flex-end" gap="small">
                <Button
                  icon={<CheckOutlined />}
                  disabled={conditionIsLoading}
                  onClick={() =>
                    setSelectedConditionTypes(
                      conditionTypes.map(
                        (conditionType) => conditionType.value,
                      ),
                    )
                  }
                >
                  Select All
                </Button>
                <Button
                  icon={<StopOutlined />}
                  disabled={conditionIsLoading}
                  onClick={() => setSelectedConditionTypes([])}
                >
                  Clear All
                </Button>
              </Flex>
              <Checkbox.Group
                disabled={conditionIsLoading}
                options={conditionTypes}
                value={selectedConditionTypes}
                onChange={handleConditionTypeFilterChange}
              />
            </div>
          </Flex>
          <InfiniteScrollConditions
            data={filteredConditions}
            setData={setFilteredConditions}
            selectConditionHandler={selectCondition}
            loading={conditionIsLoading}
            pageFilters={pageFilters}
          />
        </>
      )}
    </Flex>
  );
};

export default memo(AvailableConditions);
