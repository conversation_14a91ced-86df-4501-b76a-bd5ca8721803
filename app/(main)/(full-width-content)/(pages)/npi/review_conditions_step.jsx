"use client";

import { useEffect, useState, useRef } from "react";
import ReorderableTagCellRenderer from "../../../../../src/utils/grid/grid_cell_renderers/reorderableTagCellRenderer";
import { useGetGridBlueprint } from "../../../../../src/hooks/useGetGridBlueprint";
import YHGrid from "../../../../../src/utils/grid/yh_grid";

/**
 * Review selected conditions step component
 *
 * @param {string} pageKey
 * @param {object} gridFilters
 * @returns {JSX.Element}
 */
const ReviewConditionsStep = ({ pageKey, gridFilters }) => {
  // TODO: make this editable
  const cellRenderers = {
    editableTagsColumn: ReorderableTagCellRenderer,
  };
  const reviewSelectedConditionsGridRef = useRef();
  const [
    reviewSelectedConditionsGridComponent,
    setReviewSelectedConditionsGridComponent,
  ] = useState();
  const { data: gridBlueprintData, dataUpdatedAt: gridBlueprintDataUpdatedAt } =
    useGetGridBlueprint(pageKey, "npi_review_selected_conditions");

  useEffect(() => {
    if (gridBlueprintData?.success) {
      setReviewSelectedConditionsGridComponent(gridBlueprintData.data);
    }
  }, [gridBlueprintDataUpdatedAt]);

  return (
    <>
      <div className={"h-full"}>
        {reviewSelectedConditionsGridComponent && (
          <YHGrid
            wrapperClassName="flex grow flex-col h-full"
            gridRef={reviewSelectedConditionsGridRef}
            gridId={`${pageKey}_${reviewSelectedConditionsGridComponent.name}`}
            pageKey={pageKey}
            component={reviewSelectedConditionsGridComponent}
            filters={gridFilters}
            cellRenderers={cellRenderers}
          />
        )}
      </div>
    </>
  );
};

export default ReviewConditionsStep;
