"use client";

import {
  <PERSON>pp,
  <PERSON><PERSON>,
  Checkbox,
  Col,
  Flex,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Tooltip,
  Typography,
} from "antd";
import { useEffect, useState } from "react";
import {
  CheckCircleOutlined,
  CodeSandboxOutlined,
  InfoCircleOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import GroupSelect from "../../../../../src/utils/components/recipe_common/group_select";
import Api from "../../../../../src/utils/api";
import RecipeInfo from "../../../../../src/utils/components/recipe_common/recipe_info";
import AppendSection from "./append_section";

const { TextArea } = Input;
const { Text } = Typography;
const { confirm } = Modal;
/**
 * Traceability append algorithms section component
 *
 * @param {array} setActiveCollapseKey
 * @param {function} updateVariableOptions
 * @param {array} appendItems
 * @param {array} outputOptions
 * @param {object} defaultRecipeFormValues
 * @param {function} loadedRecipeFormValues
 * @param {array} linkToOptions
 * @param {function} setAppendItems
 * @param {array} variableOptions
 * @param {function} setVariableOptions
 * @param {function} getAppendOptions
 * @param {array} addedEquationVariableOptions
 * @param {array} variablesFromTestsTable
 * @param {object} equationValues
 * @param {boolean} traceabilityConfigLoaded
 * @param {object} pageFilters
 * @param {function} setSimulationTableFilters
 * @param {function} getVariablesAndAlgos
 * @param {string} setAppendVariablesString
 * @param {int} recipeLoaded
 * @param {function} createRecipeForm
 * @param {array} equationOptions
 * @param {function} setAddedEquationVariables
 * @param {string} activeRecipeName
 * @param {function} setActiveRecipeName
 * @param {boolean} setSimulationIsMade
 * @param {array} defaultVariableOptions
 * @param {function} setIsApplyRecipeModalOpen
 * @param {function} setEquationOptions
 * @param {array} addedEquationVariables
 * @returns {JSX.Element}
 */
const TraceabilityAlgorithms = ({
  setActiveCollapseKey,
  updateVariableOptions,
  appendItems,
  outputOptions,
  defaultRecipeFormValues,
  loadedRecipeFormValues,
  linkToOptions,
  setAppendItems,
  variableOptions,
  setVariableOptions,
  getAppendOptions,
  addedEquationVariableOptions,
  variablesFromTestsTable,
  equationValues,
  traceabilityConfigLoaded,
  pageFilters,
  setSimulationTableFilters,
  getVariablesAndAlgos,
  setAppendVariablesString,
  recipeLoaded,
  createRecipeForm,
  equationOptions,
  setAddedEquationVariables,
  activeRecipeName,
  setActiveRecipeName,
  setSimulationIsMade,
  defaultVariableOptions,
  setIsApplyRecipeModalOpen,
  setEquationOptions,
  addedEquationVariables,
  loadEquationOptions,
}) => {
  const { message } = App.useApp();
  const [includedAlgorithms, setIncludedAlgorithms] = useState([]);
  const [isAutoTriggerDisabled, setIsAutoTriggerDisabled] = useState(true);
  const [isSimulateBtnDisabled, setIsSimulateBtnDisabled] = useState(true);

  useEffect(() => {
    createRecipeForm.resetFields();
  }, [defaultRecipeFormValues]);

  useEffect(() => {
    // Early return if no form values
    if (
      !loadedRecipeFormValues ||
      Object.keys(loadedRecipeFormValues).length === 0
    ) {
      return;
    }

    try {
      // Handle trigger data
      if (loadedRecipeFormValues.trigger_data?.length > 0) {
        const [firstTrigger] = loadedRecipeFormValues.trigger_data;
        const triggerValue = Boolean(firstTrigger?.trigger_value);

        // Update auto-trigger state and form values atomically
        setIsAutoTriggerDisabled(!triggerValue);
        createRecipeForm.setFieldsValue({
          trigger_value: {
            production: triggerValue,
          },
        });
      }

      // Create a copy of form values without trigger_data
      const formValues = { ...loadedRecipeFormValues };
      delete formValues.trigger_data;

      // Set remaining form values
      createRecipeForm.setFieldsValue(formValues);

      // Update simulate button state
      setSimulateBtnState();
    } catch (error) {
      console.error("Error setting recipe form values:", error);
      message.error("Failed to load recipe form values");
    }
  }, [loadedRecipeFormValues, createRecipeForm, setIsAutoTriggerDisabled]);

  /**
   * Save recipe confirmation
   */
  const saveConfirm = (content) => {
    confirm({
      title: "Recipe Saved Successfully!",
      icon: <CheckCircleOutlined style={{ color: "rgb(34 197 94)" }} />,
      content: <RecipeInfo recipeInfo={content}></RecipeInfo>,
      cancelButtonProps: { style: { display: "none" } },
    });
  };

  /**
   * Calls api to save the recipe
   *
   * @param {object} payload
   */
  const saveTraceabilityRecipe = (payload) => {
    Api.saveTraceabilityRecipe(
      (res) => {
        if (res.success) {
          saveConfirm(res.data);
          setActiveRecipeName(payload.recipe_name);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      payload,
    );
  };

  /**
   * Handles the saving of the recipe
   */
  const handleSaveRecipe = () => {
    createRecipeForm
      .validateFields()
      .then((values) => {
        saveTraceabilityRecipe({
          ...getVariablesAndAlgos(),
          ...pageFilters,
          ...values,
          equations: loadEquationOptions,
        });
      })
      .catch((error) => {
        message.error(error.errorFields[0].errors[0], 5);
      });
  };

  /**
   * Handles the simulation of the recipe
   */
  const handleSimulateRecipe = () => {
    setSimulationTableFilters({
      ...getVariablesAndAlgos(),
      ...pageFilters,
      equations: loadEquationOptions,
    });
    setAppendVariablesString(
      appendItems
        .filter((item) => item.vars.length)
        .map((item) => item.vars.map((variable) => variable.value).join(""))
        .join(""),
    );

    setActiveCollapseKey((prevState) => {
      return prevState.includes("simulation_table")
        ? prevState
        : [...prevState, "simulation_table"];
    });
    setSimulationIsMade(true);
  };

  /**
   * Handles the application of recipe
   */
  const handleApplyRecipe = () => {
    setIsApplyRecipeModalOpen(true);
  };

  /**
   * Check whether the recipe name already exists
   */
  const checkRecipeName = () => {
    Api.checkRecipeName(
      (res) => {
        if (res.success) {
          handleSaveRecipe();
        } else {
          // If there's already an existing name that is also a draft
          if (
            res.data.draft &&
            createRecipeForm.getFieldValue("save_type").draft === true
          ) {
            confirm({
              title: "Draft Recipe Name Already Exists!",
              icon: <InfoCircleOutlined style={{ color: "rgb(252 211 77)" }} />,
              content: "What do you want to do?",
              okText: "Overwrite Existing",
              cancelText: "Rename and Save as New",
              onOk() {
                handleSaveRecipe();
              },
              onCancel() {
                // Do nothing, this modal will just close and user will rename the recipe
              },
            });
          } else if (
            (res.data.draft === false &&
              activeRecipeName ===
                createRecipeForm.getFieldValue("recipe_name")) ||
            (res.data.draft &&
              createRecipeForm.getFieldValue("save_type").production === true)
          ) {
            handleSaveRecipe();
          } else {
            confirm({
              title: "Recipe Name Already Exists!",
              icon: <InfoCircleOutlined style={{ color: "rgb(252 211 77)" }} />,
              content: "Please rename and click the Save button again.",
              cancelButtonProps: { style: { display: "none" } },
            });
          }
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        recipe_name: createRecipeForm.getFieldValue("recipe_name"),
        recipe_type: "traceability",
      },
    );
  };

  /**
   * Enable/disable the simulate button
   */
  const setSimulateBtnState = () => {
    const formData = createRecipeForm.getFieldsValue([
      "recipe_process",
      "recipe_output",
    ]);
    setIsSimulateBtnDisabled(
      Object.values(formData).some(
        (data) => typeof data === "undefined" || data.length === 0,
      ),
    );
  };

  /**
   * Handles the event where a field changes in a form
   *
   * @param {array} changedFields
   */
  const handleRecipeFieldsChange = (changedFields) => {
    setSimulateBtnState();
    const saveType = createRecipeForm.getFieldValue("save_type");
    const allChecked = saveType.draft && saveType.production;
    const allUnchecked = !saveType.draft && !saveType.production;

    if (
      allChecked &&
      changedFields[0].name.includes("draft") &&
      changedFields[0].value === true
    ) {
      createRecipeForm.setFieldsValue({
        save_type: { draft: true, production: false },
      });
      setIsAutoTriggerDisabled(true);
    } else if (
      allChecked &&
      changedFields[0].name.includes("production") &&
      changedFields[0].value === true
    ) {
      createRecipeForm.setFieldsValue({
        save_type: { draft: false, production: true },
      });
      setIsAutoTriggerDisabled(false);
    } else if (
      allUnchecked &&
      changedFields[0].name.includes("draft") &&
      changedFields[0].value === false
    ) {
      createRecipeForm.setFieldsValue({
        save_type: { draft: true, production: false },
      });
      setIsAutoTriggerDisabled(true);
    } else if (
      allUnchecked &&
      changedFields[0].name.includes("production") &&
      changedFields[0].value === false
    ) {
      createRecipeForm.setFieldsValue({
        save_type: { draft: false, production: true },
      });
      setIsAutoTriggerDisabled(false);
    }
  };

  const rowGutter = [16, 24];

  return (
    <>
      <AppendSection
        appendItems={appendItems}
        setAppendItems={setAppendItems}
        variableOptions={variableOptions}
        setVariableOptions={setVariableOptions}
        setIncludedAlgorithms={setIncludedAlgorithms}
        getAppendOptions={getAppendOptions}
        updateVariableOptions={updateVariableOptions}
        addedEquationVariableOptions={addedEquationVariableOptions}
        variablesFromTestsTable={variablesFromTestsTable}
        equationValues={equationValues}
        traceabilityConfigLoaded={traceabilityConfigLoaded}
        recipeLoaded={recipeLoaded}
        equationOptions={equationOptions}
        setAddedEquationVariables={setAddedEquationVariables}
        createRecipeForm={createRecipeForm}
        includedAlgorithms={includedAlgorithms}
        defaultVariableOptions={defaultVariableOptions}
        setSimulateBtnState={setSimulateBtnState}
        setEquationOptions={setEquationOptions}
        addedEquationVariables={addedEquationVariables}
      />
      <Form
        form={createRecipeForm}
        onFieldsChange={handleRecipeFieldsChange}
        layout="vertical"
        initialValues={defaultRecipeFormValues}
      >
        <Row gutter={rowGutter} className="mt-2.5">
          <Col className="gutter-row" span={12}>
            <Form.Item
              name="recipe_process"
              label="Included Algorithms"
              rules={[
                {
                  required:
                    createRecipeForm.getFieldValue("save_type")?.draft !== true,
                  message: "Please select algorithms",
                  type: "array",
                },
              ]}
            >
              <Select
                mode="multiple"
                placeholder="Please select algorithms"
                options={includedAlgorithms}
              ></Select>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={12}>
            <Form.Item
              name="recipe_output"
              label="Outputs"
              rules={[
                {
                  required: true,
                  message: "Please select output",
                  type: "array",
                },
              ]}
            >
              <Select
                mode="multiple"
                placeholder="Please select output"
                options={outputOptions}
              ></Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={rowGutter}>
          <Col className="gutter-row" span={12}>
            <GroupSelect
              options={linkToOptions}
              pageFilters={pageFilters}
              groupFieldName={"recipe_input"}
              groupFieldLabel={"Link To"}
              initialValues={loadedRecipeFormValues.recipe_input}
              maxTagCount="responsive"
            />
          </Col>
        </Row>
        <Row gutter={rowGutter} className="mt-2.5">
          <Col className="gutter-row" span={12}>
            <Form.Item label="Notes" name="notes">
              <TextArea rows={3} placeholder="Describe this recipe" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={rowGutter} className="mt-2.5">
          <Col span={6}>
            <Text className="required-label">Save as</Text>
            <Flex>
              <Form.Item
                name={["save_type", "draft"]}
                valuePropName="checked"
                className="w-1/4"
              >
                <Checkbox>Draft</Checkbox>
              </Form.Item>
              <Form.Item
                name={["save_type", "production"]}
                valuePropName="checked"
                className="w-1/3"
              >
                <Checkbox>Production</Checkbox>
              </Form.Item>
              <Form.Item
                name={["trigger_value", "production"]}
                valuePropName="checked"
                className="w-1/3"
              >
                <Checkbox disabled={isAutoTriggerDisabled}>
                  Auto trigger
                </Checkbox>
              </Form.Item>
            </Flex>
          </Col>
        </Row>
        <Row gutter={rowGutter} className="mt-2.5">
          <Col className="gutter-row" span={6}>
            <Form.Item
              name="recipe_name"
              label="Recipe Name"
              rules={[
                {
                  required: true,
                  message: "Please input the recipe name",
                },
                {
                  pattern: /^[a-zA-Z_-]+[a-zA-Z0-9_-]*$/,
                  message:
                    "Can only include numbers 0-9, letters a-z, - and _ and should not start with a number.",
                },
                {
                  validator: (_, value) => {
                    if (typeof value === "undefined" || value.length > 150) {
                      return Promise.reject();
                    } else {
                      return Promise.resolve();
                    }
                  },
                  message: "Recipe name should not exceed 150 characters.",
                },
              ]}
              required
              tooltip="This is a required field"
            >
              <Input placeholder="Custom name" />
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={12}>
            <Form.Item label=" ">
              <Flex wrap="wrap" gap="middle">
                <Button
                  icon={<SaveOutlined />}
                  type="primary"
                  onClick={checkRecipeName}
                >
                  Save
                </Button>
                <Button
                  icon={<CodeSandboxOutlined />}
                  onClick={handleSimulateRecipe}
                  disabled={isSimulateBtnDisabled}
                >
                  Simulate Recipe
                </Button>
                <Tooltip title="To select more datalogs, go back to home page.">
                  <Button
                    icon={<CheckCircleOutlined />}
                    onClick={handleApplyRecipe}
                  >
                    Apply Recipe to Selected Datalog
                  </Button>
                </Tooltip>
              </Flex>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </>
  );
};

export default TraceabilityAlgorithms;
