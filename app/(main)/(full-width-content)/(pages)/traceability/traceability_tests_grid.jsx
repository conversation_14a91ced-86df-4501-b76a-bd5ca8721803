"use client";

import { App, Modal } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { unionBy } from "lodash";
import YHGrid from "../../../../../src/utils/grid/yh_grid";
import Api from "../../../../../src/utils/api";
import { QueryKeys } from "../../../../../src/utils/query_keys";
import AddTestsModal from "../../../../../src/utils/components/recipe_common/add_tests_modal";

/**
 * Traceability setup test grid component
 *
 * @param {string} pageKey
 * @param {object} pageFilters
 * @param {array} conversionOptions
 * @param {object} variablesFromTestsTable
 * @param {function} setVariablesFromTestsTable
 * @param {object} testsGridRef
 * @param {function} setInitialSetupTableRowData
 * @param {function} refetchUrlLoadedRecipe
 * @returns {JSX.Element}
 */
const TraceabilityTestsGrid = ({
  pageKey,
  pageFilters,
  conversionOptions,
  variablesFromTestsTable,
  setVariablesFromTestsTable,
  testsGridRef,
  setInitialSetupTableRowData,
  refetchUrlLoadedRecipe,
}) => {
  const [testGridComponent, setTestGridComponent] = useState();
  const [tableFilters, setTableFilters] = useState({});
  const [isAddTestModalOpen, setIsAddTestModalOpen] = useState(false);
  const [testsToAdd, setTestsToAdd] = useState([]);
  const { message } = App.useApp();
  const [{ confirm }, contextHolder] = Modal.useModal();
  const gridId = "traceability_setup_table";

  useEffect(() => {
    const filters = {};
    filters[pageKey] = pageFilters;
    setTableFilters(filters);
  }, [pageFilters]);

  useEffect(() => {
    if (testsToAdd.length && testsGridRef.current) {
      const rowData = [];
      testsGridRef.current.api.forEachNode((rowNode) => {
        rowData.push(rowNode.data);
      });
      const newRowData = unionBy(testsToAdd, rowData, "test_number");
      testsGridRef.current.api.setGridOption("rowData", newRowData);
    }
  }, [testsToAdd]);

  /**
   * Opens the modal that will give the user the test list to add
   */
  const addTestsToTraceability = () => {
    setIsAddTestModalOpen(true);
  };

  /**
   * Confirmation modal to delete selected tests
   */
  const handleDeleteTests = () => {
    const testsInRecipe = variablesFromTestsTable.map((varObj) => varObj.tnum);
    let hasVariableInUse = false;
    const rowData = [];
    testsGridRef.current.api.forEachNode((rowNode) => {
      if (!rowNode.isSelected()) {
        rowData.push(rowNode.data);
      } else if (
        rowNode.isSelected() &&
        testsInRecipe.includes(rowNode.data.test_number)
      ) {
        rowData.push(rowNode.data);
        hasVariableInUse = true;
      }
    });

    if (hasVariableInUse) {
      confirm({
        title: "Alert",
        icon: <InfoCircleOutlined style={{ color: "rgb(252 211 77)" }} />,
        content:
          "Unable to delete some tests because they are already added as variables in the recipe.",
        cancelButtonProps: { style: { display: "none" } },
      });
    }
    testsGridRef.current.api.setGridOption("rowData", rowData);
  };

  /**
   * Delete selected tests from setup table
   */
  const deleteTestsFromTraceability = () => {
    if (testsGridRef.current.api.getSelectedRows().length) {
      confirm({
        title: "Remove Selection",
        icon: <InfoCircleOutlined style={{ color: "rgb(252 211 77)" }} />,
        content: "Are you sure you want to remove the selected test(s)?",
        okText: "Delete",
        cancelText: "Cancel",
        onOk() {
          handleDeleteTests();
        },
        onCancel() {},
      });
    } else {
      message.error("No row(s) selected!");
    }
  };

  /**
   * Check if variable is already used
   *
   * @param {object} params
   * @param {string} newVariable
   * @returns {boolean}
   */
  const isVariableUsed = (params, newVariable) => {
    let isUsed = false;
    params.api.forEachNode((node) => {
      if (node.data.variable && node.data.variable === newVariable) {
        isUsed = true;
      }
    });

    return isUsed;
  };

  const eventHandlers = {
    onRowSelected: (event) => {
      if (
        event.node.isSelected() &&
        (event.data.variable === undefined || event.data.variable === "")
      ) {
        const variable = isVariableUsed(event, `T${event.node.rowIndex + 1}`)
          ? `T${event.node.rowIndex + 1}_${event.data.actual_test_number}`
          : `T${event.node.rowIndex + 1}`;
        event.data.variable = variable;
      }
    },
    setVariablesFromTestsTable: setVariablesFromTestsTable,
    addTestsToTraceability: addTestsToTraceability,
    deleteTestsFromTraceability: deleteTestsFromTraceability,
    handlePostReload: (responseData) => {
      if (responseData.table_data.length) {
        setInitialSetupTableRowData(responseData.table_data);
      }
      refetchUrlLoadedRecipe();
    },
    clearTraceabilityVariables: (grid) => {
      grid.api.forEachNode((node) => {
        if (node.data.variable) {
          node.data.variable = "";
        }
      });
    },
  };

  const valueSetters = {
    variableNameColumn: (event) => {
      let returnVal = false;
      if (event.newValue === null) {
        event.data.variable = event.newValue;
        returnVal = true;
      } else if (/^[a-zA-Z_-]+[a-zA-Z0-9_-]*$/.test(event.newValue) === false) {
        message.error(
          "Variable should only include numbers 0-9, letters a-z, - and _ and should not start with a number.",
        );
      } else {
        if (isVariableUsed(event, event.newValue)) {
          message.error("Variable is already taken.");
        } else {
          event.data.variable = event.newValue;
          returnVal = true;
        }
      }

      return returnVal;
    },
    maxCharColumn: (event) => {
      let returnVal = false;
      if (!Number.isInteger(event.newValue)) {
        message.error("Please input an integer.");
      } else if (event.newValue < event.data.max_chars) {
        message.error(
          "New max char length should not be less than the computed one.",
        );
      } else {
        event.data.max_chars = event.newValue;
        returnVal = true;
      }

      return returnVal;
    },
  };

  /**
   * Get the test grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getTestGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setTestGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err.message, 5);
      },
      {
        name: "traceability_setup_table",
      },
    );

    return abortCtl;
  };

  useQuery({
    queryKey: QueryKeys.grid(pageKey, gridId),
    queryFn: getTestGridComponent,
  });

  return (
    <div className="h-96">
      {contextHolder}
      <AddTestsModal
        selectedParams={pageFilters}
        isModalOpen={isAddTestModalOpen}
        setIsModalOpen={setIsAddTestModalOpen}
        setTestsToAdd={setTestsToAdd}
        testsGridRef={testsGridRef}
        defaultTestCategory="select"
      ></AddTestsModal>
      {testGridComponent && (
        <YHGrid
          gridRef={testsGridRef}
          gridId={gridId}
          component={testGridComponent}
          pageKey={pageKey}
          filters={tableFilters}
          wrapperClassName="flex grow flex-col h-full"
          eventHandlers={eventHandlers}
          valueSetters={valueSetters}
          conversionOptions={conversionOptions}
        />
      )}
    </div>
  );
};

export default TraceabilityTestsGrid;
