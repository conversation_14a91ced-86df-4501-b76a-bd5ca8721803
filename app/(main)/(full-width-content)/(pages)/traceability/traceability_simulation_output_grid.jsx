"use client";

import { App, Col, Row } from "antd";
import { useRef, useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import YHGrid from "../../../../../src/utils/grid/yh_grid";
import Api from "../../../../../src/utils/api";
import { QueryKeys } from "../../../../../src/utils/query_keys";

const gridName = "traceability_simulation_table";

/**
 * Traceability simulation output grid component
 *
 * @param {string} pageKey
 * @param {object} simulationTableFilters
 * @param {function} setSimulationAlerts
 * @param {string} appendVariablesString
 * @returns {JSX.Element}
 */
const TraceabilitySimulationOutputGrid = ({
  pageKey,
  simulationTableFilters,
  setSimulationAlerts,
  appendVariablesString,
}) => {
  const outputGridRef = useRef();
  const [simulationGridComponent, setSimulationGridComponent] = useState();
  const [tableFilters, setTableFilters] = useState({});
  const { message } = App.useApp();

  useEffect(() => {
    const filters = {};
    filters[pageKey] = simulationTableFilters;
    setTableFilters(filters);
  }, [appendVariablesString]);

  /**
   * Get the simulation output grid component
   */
  const getSimulationGridComponent = () => {
    Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setSimulationGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: gridName,
      },
    );
  };

  const eventHandlers = {
    handlePostReload: (responseData) => {
      setSimulationAlerts(responseData.warning_notice);
    },
  };

  useQuery({
    queryKey: [...QueryKeys.grid(pageKey, gridName), appendVariablesString],
    queryFn: getSimulationGridComponent,
  });

  return (
    <Row>
      <Col span={24} className="h-96">
        {simulationGridComponent && (
          <YHGrid
            key={appendVariablesString}
            gridRef={outputGridRef}
            gridId={`${pageKey}_${simulationGridComponent.name}`}
            component={simulationGridComponent}
            pageKey={pageKey}
            filters={tableFilters}
            wrapperClassName="flex grow flex-col h-full"
            eventHandlers={eventHandlers}
          />
        )}
      </Col>
    </Row>
  );
};

export default TraceabilitySimulationOutputGrid;
