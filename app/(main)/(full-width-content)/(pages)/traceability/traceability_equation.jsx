"use client";

import { useEffect, useState } from "react";
import {
  App,
  Button,
  Col,
  Flex,
  Form,
  Input,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  Spin,
  Tooltip,
  Typography,
} from "antd";
import {
  ClearOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import Api from "../../../../../src/utils/api";
const { Text } = Typography;
const { TextArea } = Input;
const { confirm } = Modal;

/**
 * Traceability equation component
 *
 * @param {function} setAddedEquationVariables
 * @param {array} addedEquationVariables
 * @param {object} equationValues
 * @param {function} setEquationValues
 * @param {function} getVariablesAndAlgos
 * @param {object} pageFilters
 * @param {object} appendItems
 * @param {object} recipeData
 * @param {object} loadEquationOptions
 * @param {function} setLoadEquationOptions
 * @returns {JSX.Element}
 */
const TraceabilityEquation = ({
  setAddedEquationVariables,
  addedEquationVariables,
  equationValues,
  setEquationValues,
  getVariablesAndAlgos,
  pageFilters,
  appendItems,
  recipeData,
  loadEquationOptions,
  setLoadEquationOptions,
}) => {
  const [isSaveEquationModalOpen, setIsSaveEquationModalOpen] = useState(false);
  const [isFirstPassingModalOpen, setIsFirstPassingModalOpen] = useState(false);
  const [isEquationLoaded, setIsEquationLoaded] = useState(false);
  const [isEquationEntered, setIsEquationEntered] = useState(false);
  const [isSaveEquationLoading, setIsSaveEquationLoading] = useState(false);
  const [isEquationResultLoading, setIsEquationResultLoading] = useState(true);
  const [firstPassingUnitValue, setFirstPassingUnitValue] = useState([]);
  const [loadEquationForm] = Form.useForm();
  const [createEquationForm] = Form.useForm();
  const [saveEquationForm] = Form.useForm();
  const { message } = App.useApp();

  useEffect(() => {
    setLoadEquationOptions(
      Object.keys(equationValues).map((equationName) => {
        return {
          value: equationName,
          label: equationName,
          actualValue: equationValues[equationName].value,
          result: equationValues[equationName].result,
        };
      }),
    );
  }, [equationValues, addedEquationVariables]);

  useEffect(() => {
    if (recipeData.equations.length) {
      setLoadEquationOptions(recipeData.equations);
      const equationValues = {};
      recipeData.equations.forEach((equation) => {
        equationValues[equation.value] = {
          value: equation.actualValue,
          result: equation.result ?? "",
        };
      });
      setEquationValues(equationValues);
    }
  }, [recipeData.equations]);

  /**
   * Handles the saving of equation
   */
  const handleSaveEquation = () => {
    saveEquationForm
      .validateFields()
      .then((values) => {
        setIsSaveEquationLoading(true);
        getEquationResult(
          {
            ...getVariablesAndAlgos(),
            ...pageFilters,
            ...{ equation: createEquationForm.getFieldValue("equation") },
          },
          (data) => {
            const equationName = values.equation_name;
            const newEquation = {};
            newEquation[equationName] = {
              value: createEquationForm.getFieldValue("equation"),
              result: data[data.length - 1].value,
            };
            setEquationValues({ ...equationValues, ...newEquation });
            loadEquationForm.setFieldsValue({ load_equation: equationName });
            setIsSaveEquationModalOpen(false);
            setIsEquationLoaded(true);
            setIsSaveEquationLoading(false);
            setIsFirstPassingModalOpen(false);
            message.success("Equation Saved!", 5);
          },
        );
      })
      .catch((error) => {
        message.error(error.errorFields[0].errors[0], 5);
        setIsSaveEquationLoading(false);
      });
  };

  /**
   * Handles the cancellation of saving an equation
   */
  const handleCancelSaveEquation = () => {
    setIsSaveEquationModalOpen(false);
  };

  /**
   * Set the showing of the save equation modal
   */
  const showSaveEquationModal = () => {
    setIsSaveEquationModalOpen(true);
  };

  /**
   * Handles the event when an equation is selected
   *
   * @param {string} value
   */
  const handleEquationSelection = (value) => {
    createEquationForm.setFieldsValue({
      equation: equationValues[value].value,
    });
  };

  /**
   * Get the result of the equaton
   *
   * @param {object} payload
   * @param {function} successCb
   */
  const getEquationResult = (payload, successCb) => {
    Api.getTraceabilityEquationResult(
      (res) => {
        if (res.success) {
          if (res.data[0].label === "Error") {
            confirm({
              title: "Equation Error",
              icon: <InfoCircleOutlined style={{ color: "rgb(239 68 68)" }} />,
              content: res.data[0].value,
              cancelButtonProps: { style: { display: "none" } },
              onOk() {
                setIsSaveEquationLoading(false);
                setIsSaveEquationModalOpen(false);
              },
            });
          } else {
            setIsFirstPassingModalOpen(true);
            successCb(res.data);
          }
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        setIsSaveEquationLoading(false);
        message.error(err, 5);
      },
      payload,
    );
  };

  /**
   * Opens the modal the shows the equation result
   */
  const viewFirstPassingUnitValue = () => {
    setIsEquationResultLoading(true);
    getEquationResult(
      {
        ...getVariablesAndAlgos(),
        ...pageFilters,
        equation: createEquationForm.getFieldValue("equation"),
        equations: loadEquationOptions,
      },
      (data) => {
        setIsEquationResultLoading(false);
        setFirstPassingUnitValue(data);
      },
    );
  };

  /**
   * Handles the closing of the equation result modal
   */
  const handleFirstPassingModal = () => {
    setIsFirstPassingModalOpen(false);
  };

  /**
   * Adds created equation to the recipe section
   */
  const addEquationToRecipe = () => {
    const equationName = loadEquationForm.getFieldValue("load_equation");
    setAddedEquationVariables((prevState) => {
      if (!prevState.includes(equationName)) {
        return [...prevState, equationName];
      }
      return prevState;
    });
  };

  /**
   * Shows the save as new equation modal
   */
  const saveAsNewEquation = () => {
    showSaveEquationModal();
  };

  /**
   * Handles the saving of equation
   */
  const saveEquation = () => {
    const equationName = loadEquationForm.getFieldValue("load_equation");
    if (typeof equationName !== "string") {
      saveAsNewEquation();
    } else {
      getEquationResult(
        {
          ...getVariablesAndAlgos(),
          ...pageFilters,
          equation: createEquationForm.getFieldValue("equation"),
          equations: loadEquationOptions,
        },
        (data) => {
          const newEquation = {};
          newEquation[equationName] = {
            value: createEquationForm.getFieldValue("equation"),
            result: data[data.length - 1].value,
          };
          setEquationValues({ ...equationValues, ...newEquation });
          setIsFirstPassingModalOpen(false);
          message.success("Equation Updated!", 5);
        },
      );
    }
  };

  /**
   * Save recipe confirmation
   *
   * @param {string} equation
   */
  const equationisIsUsedConfirm = (equation) => {
    confirm({
      title: `Cannot Delete Equation`,
      icon: <InfoCircleOutlined style={{ color: "rgb(239 68 68)" }} />,
      content: `The '${equation}' equation was already added as variable. Remove it from the algorithms and try again.`,
      cancelButtonProps: { style: { display: "none" } },
    });
  };

  /**
   * Checks whether the equation is already used as algorithm
   *
   * @param {string} equation
   * @returns {boolean}
   */
  const equationInUsed = (equation) => {
    return appendItems.some((item) => {
      return item.vars.some((variable) => variable.value === equation);
    });
  };

  /**
   *  Shows the modal that confirms the deletion of an equation
   */
  const confirmDeleteEquation = () => {
    const equation = loadEquationForm.getFieldValue("load_equation");
    if (equationInUsed(equation)) {
      equationisIsUsedConfirm(equation);
    } else {
      const newEquationValues = { ...equationValues };
      delete newEquationValues[equation];
      setEquationValues(newEquationValues);
      setAddedEquationVariables((prevState) => {
        return prevState.filter((variable) => equation !== variable);
      });
      createEquationForm.setFieldsValue({ equation: "" });
      loadEquationForm.setFieldsValue({ load_equation: null });
      setIsEquationLoaded(false);
      message.success("Equation Deleted!");
    }
  };

  /**
   * Handles the on change event of equation select
   */
  const handleOnChangeEquationSelection = () => {
    setIsEquationLoaded(true);
    setIsEquationEntered(true);
  };

  /**
   * Handles the event when an equation was entered
   *
   * @param {array} changedFields
   */
  const handleCreateEquationFieldsChange = (changedFields) => {
    setIsEquationEntered(
      changedFields[0].name.includes("equation") &&
        changedFields[0].value !== "",
    );
  };

  /**
   * Clears the equation input
   */
  const clearEquation = () => {
    createEquationForm.setFieldsValue({ equation: "" });
  };

  return (
    <>
      <Modal
        title="Save Equation"
        open={isSaveEquationModalOpen}
        onOk={handleSaveEquation}
        onCancel={handleCancelSaveEquation}
        centered
        footer={[
          <Button key="cancel" onClick={handleCancelSaveEquation}>
            Cancel
          </Button>,
          <Button
            type="primary"
            key="back"
            loading={isSaveEquationLoading}
            onClick={handleSaveEquation}
          >
            Save
          </Button>,
        ]}
      >
        <Form form={saveEquationForm}>
          <Form.Item
            name="equation_name"
            rules={[
              {
                required: true,
                message: "Please input equation name",
              },
              {
                max: 30,
                message: "Equation name cannot exceed 30 characters.",
              },
              {
                pattern: /^[a-zA-Z_-]+[a-zA-Z0-9_-]*$/,
                message:
                  "Can only include numbers 0-9, letters a-z, - and _ and should not start with a number.",
              },
              {
                validator: (_, value) => {
                  if (
                    loadEquationOptions
                      .map((equationObj) => equationObj.value)
                      .includes(value)
                  ) {
                    return Promise.reject();
                  } else {
                    return Promise.resolve();
                  }
                },
                message: "Equation name already exists.",
              },
            ]}
          >
            <Input placeholder="Custom Equation Label" maxLength={30} />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title="First Passing Unit Value"
        open={isFirstPassingModalOpen}
        onCancel={handleFirstPassingModal}
        onOk={handleFirstPassingModal}
        destroyOnClose
        centered
        footer={[
          <Button type="primary" key="back" onClick={handleFirstPassingModal}>
            Close
          </Button>,
        ]}
      >
        <Spin
          tip="Calculating equation..."
          spinning={isEquationResultLoading}
        ></Spin>
        {firstPassingUnitValue.map((result) => {
          return (
            <div key={result.value.replaceAll(" ", "_")}>
              <Text>
                {result.label} : {result.value}
              </Text>
            </div>
          );
        })}
      </Modal>
      <Form form={loadEquationForm} colon={false}>
        <Form.Item
          name="load_equation"
          label="Load Equation"
          wrapperCol={{
            span: 6,
          }}
        >
          <Select
            placeholder="Please select"
            options={loadEquationOptions}
            onSelect={handleEquationSelection}
            onChange={handleOnChangeEquationSelection}
            notFoundContent="No equations available"
          ></Select>
        </Form.Item>
      </Form>
      <Form
        form={createEquationForm}
        onFieldsChange={handleCreateEquationFieldsChange}
        layout="vertical"
      >
        <Row gutter={[16, 24]}>
          <Col className="gutter-row" flex={9}>
            <Form.Item
              label={
                <Flex>
                  <Text>Write Equation</Text>
                  <Tooltip title="Clear the entered equation.">
                    <ClearOutlined
                      onClick={clearEquation}
                      className="text-sky-500 ml-2"
                    ></ClearOutlined>
                  </Tooltip>
                </Flex>
              }
              name="equation"
            >
              <TextArea rows={4} />
            </Form.Item>
          </Col>
          <Col className="gutter-row" flex={1}>
            <Form.Item label=" ">
              <Flex vertical gap="small">
                <Space>
                  <Button
                    onClick={viewFirstPassingUnitValue}
                    disabled={!isEquationEntered}
                  >
                    View First Passing Unit Value
                  </Button>
                  <Button
                    disabled={!isEquationLoaded}
                    type="primary"
                    onClick={addEquationToRecipe}
                  >
                    Append Equation to Recipe
                  </Button>
                </Space>
                <Space>
                  <Button
                    icon={<SaveOutlined />}
                    onClick={saveEquation}
                    disabled={!isEquationEntered}
                  >
                    Save Equation
                  </Button>
                  <Button
                    icon={<SaveOutlined />}
                    onClick={saveAsNewEquation}
                    disabled={!isEquationEntered}
                  >
                    Save as New
                  </Button>
                </Space>
                <Space>
                  <Popconfirm
                    title="Delete the equation"
                    description="Are you sure to delete this equation?"
                    onConfirm={confirmDeleteEquation}
                    // onCancel={cancelDeleteEquation}
                    okText="Yes"
                    cancelText="No"
                  >
                    <Button
                      icon={<DeleteOutlined />}
                      disabled={!isEquationLoaded}
                    >
                      Delete Equation
                    </Button>
                  </Popconfirm>
                </Space>
              </Flex>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </>
  );
};

export default TraceabilityEquation;
