"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Flex,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Space,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import {
  ClearOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import { useEffect, useState } from "react";
const { Text } = Typography;
const { confirm } = Modal;

/**
 * Append algorithm component
 *
 * @param {array} appendItems
 * @param {function} setAppendItems
 * @param {array} variableOptions
 * @param {function} setVariableOptions
 * @param {function} setIncludedAlgorithms
 * @param {function} getAppendOptions
 * @param {function} updateVariableOptions
 * @param {array} addedEquationVariableOptions
 * @param {array} equationOptions
 * @param {array} variablesFromTestsTable
 * @param {object} equationValues
 * @param {boolean} traceabilityConfigLoaded
 * @param {int} recipeLoaded
 * @param {function} setAddedEquationVariables
 * @param {object} createRecipeForm
 * @param {object} includedAlgorithms
 * @param {array} defaultVariableOptions
 * @param {function} setSimulateBtnState
 * @param {function} setEquationOptions
 * @param {array} addedEquationVariables
 * @returns {JSX.Element}
 */
const AppendSection = ({
  appendItems,
  setAppendItems,
  variableOptions,
  setVariableOptions,
  setIncludedAlgorithms,
  getAppendOptions,
  updateVariableOptions,
  addedEquationVariableOptions,
  equationOptions,
  variablesFromTestsTable,
  equationValues,
  traceabilityConfigLoaded,
  recipeLoaded,
  setAddedEquationVariables,
  createRecipeForm,
  includedAlgorithms,
  defaultVariableOptions,
  setSimulateBtnState,
  setEquationOptions,
  addedEquationVariables,
}) => {
  const [addAlgorithmForm] = Form.useForm();
  const [addEquationForm] = Form.useForm();
  const { message } = App.useApp();
  const [isAddAlgorithmModalOpen, setIsAddAlgorithmModalOpen] = useState(false);
  const [isAddEquationModalOpen, setIsAddEquationModalOpen] = useState(false);
  const [isAddEquationBtnDisabled, setIsAddEquationBtnDisabled] =
    useState(true);
  const [algorithmCharCountExceeds, setAlgorithmCharCountExceeds] = useState(
    Object.keys(appendItems).map(() => false),
  );
  const [tempAppendItems, setTempAppendItems] = useState([...appendItems]);
  const [currentAlgorithmCharCount, setCurrentAlgorithmCharCount] = useState(
    [],
  );

  /**
   * Gets the character count color, red if the count exceeded, otherwise gray
   *
   * @param {int} appendKey
   * @returns {string}
   */
  const getCharCountColor = (appendKey) => {
    return algorithmCharCountExceeds[appendKey] ? "red" : "gray";
  };

  /**
   * Handles the switching of colors of the character count of the algorithm when
   *  user adds or removes variable
   */
  const updateAlgorithmCharCountColor = () => {
    const algorithmCharCountExceeds = Object.values(
      currentAlgorithmCharCount,
    ).map((charCount, appendKey) => {
      return charCount > tempAppendItems[appendKey].limit;
    });

    setAlgorithmCharCountExceeds(algorithmCharCountExceeds);
  };

  /**
   * Adds a variable to the currently active append algo
   *
   * @param {string} value The selected option
   * @param {string} algo The current active append algo
   */
  const handleAddVariable = (value, algo) => {
    const newVar = {
      value: value,
      label: variableOptions.filter((option) => option.value === value)[0]
        .label,
    };
    if (typeof equationValues[value] !== "undefined") {
      newVar.actualValue = equationValues[value].value;
      newVar.result = equationValues[value].result;
    }
    const newAppendItems = tempAppendItems.map((item) => {
      if (item.value === algo) {
        const vars = [...item.vars, newVar];
        return { ...item, vars };
      }
      return item;
    });
    setTempAppendItems(newAppendItems);
  };

  /**
   * Handles the removable of variable from append algorithm
   *
   * @param {int} varIndex
   * @param {int} appendKey
   */
  const handleRemoveVariable = (varIndex, appendKey) => {
    const newAppendItems = [...tempAppendItems];
    newAppendItems[appendKey].vars.splice(varIndex, 1);
    setTempAppendItems(newAppendItems);
  };

  /**
   * Checks whether the current algo is included in the option algo
   *
   * @param {string} optionAlgo Algo in the dropdown
   * @param {string} currentAlgo The current active algo
   * @returns {boolean} disabled
   */
  const thisOptionIncludesCurrentAlgo = (optionAlgo, currentAlgo) => {
    let disabled = false;
    if (optionAlgo === currentAlgo) {
      disabled = true;
    } else {
      // Get the algo object of the option algo
      const targetItem = appendItems.find(
        (itemObj) => itemObj.value === optionAlgo,
      );
      if (targetItem) {
        // If current algo is directly included in the option algo
        const algoIsIncluded = targetItem.vars.some(
          (variableOj) => variableOj.value === currentAlgo,
        );
        if (algoIsIncluded) {
          disabled = true;
        } else {
          const algosInsideThisAlgo = targetItem.vars.filter((variableObj) => {
            return appendItems.find(
              (algoObj) => algoObj.value === variableObj.value,
            );
          });
          // If current algo is indirectly included, but this check is only one level deep
          disabled = algosInsideThisAlgo.some((algoObj) => {
            return appendItems
              .find((itemObj) => itemObj.value === algoObj.value)
              ?.vars.some((variableOj) => variableOj.value === currentAlgo);
          });
        }
      }
    }

    return disabled;
  };

  /**
   * Handles the exclusion of algo from the dropdown of reusable variables
   *
   * @param {string} algo
   */
  const handleVariableExclusion = (algo) => {
    setVariableOptions((prevState) => {
      const newOptions = prevState.map((option) => {
        return {
          value: option.value,
          label: option.label,
          disabled: thisOptionIncludesCurrentAlgo(option.value, algo),
        };
      });
      return newOptions;
    });
  };

  /**
   * Get the test variable
   *
   * @param {string} variableValue
   * @returns {object} The test variable object
   */
  const getTestVariable = (variableValue) => {
    return variablesFromTestsTable.find(
      (testVariable) => testVariable.value === variableValue,
    );
  };

  /**
   * Update the algorithm characters count
   */
  const updateAlgorithmCharCount = () => {
    const currentAlgorithms = appendItems.map((item) => item.value);
    setCurrentAlgorithmCharCount(
      tempAppendItems.map((item) => {
        return item.vars
          .map((variable) => {
            let value = variable.value;
            // If variable is equation
            if (typeof equationValues[variable.value] !== "undefined") {
              value = equationValues[variable.value].result;
              // If variable is a test
            } else if (typeof getTestVariable(variable.value) !== "undefined") {
              value = getTestVariable(variable.value).actualValue;
              // If variable is an algorithm
            } else if (currentAlgorithms.includes(variable.value)) {
              value = appendItems[
                currentAlgorithms.indexOf(variable.value)
              ].vars
                .map((variable) => {
                  let value = variable.value;
                  if (typeof equationValues[variable.value] !== "undefined") {
                    value = equationValues[variable.value].result;
                  } else if (
                    typeof getTestVariable(variable.value) !== "undefined"
                  ) {
                    value = getTestVariable(variable.value).actualValue;
                  }
                  return value;
                })
                .join("");
            }
            return value;
          })
          .join("").length;
      }),
    );
  };

  useEffect(() => {
    setTempAppendItems([...appendItems]);
  }, [traceabilityConfigLoaded, recipeLoaded]);

  useEffect(() => {
    setIncludedAlgorithms(getAppendOptions());
    updateVariableOptions();
  }, [appendItems, addedEquationVariableOptions, variablesFromTestsTable]);

  useEffect(() => {
    createRecipeForm.setFieldsValue({
      recipe_process: includedAlgorithms.map((algo) => algo.value),
    });
    setSimulateBtnState();
  }, [includedAlgorithms]);

  useEffect(() => {
    updateAlgorithmCharCount();
  }, [tempAppendItems]);

  useEffect(() => {
    updateAlgorithmCharCountColor();
  }, [currentAlgorithmCharCount]);

  useEffect(() => {
    setEquationOptions(
      Object.keys(equationValues).map((equationName) => {
        return {
          value: equationName,
          label: equationName,
          actualValue: equationValues[equationName].value,
          disabled: addedEquationVariables.includes(equationName),
        };
      }),
    );
    setIsAddEquationBtnDisabled(Object.keys(equationValues).length === 0);
    // Remove the deleted equations
    setTempAppendItems((prevState) => {
      const newState = [...prevState];
      newState.forEach((appendItem) => {
        const newVars = appendItem.vars.filter((variable) => {
          // Not an equation
          if (typeof variable.result === "undefined") {
            return true;
          } else {
            return addedEquationVariables.includes(variable.value);
          }
        });
        appendItem.vars = newVars;
      });
      return newState;
    });
  }, [equationValues, addedEquationVariables]);

  /**
   * Handler when add equation form fields change
   */
  const handleAddEquationFieldsChange = () => {
    setIsAddEquationBtnDisabled(
      addEquationForm.getFieldValue("equation").length === 0,
    );
  };

  /**
   * Opens the add algorithm modal
   */
  const showAddAlgorithmModal = () => {
    setIsAddAlgorithmModalOpen(true);
  };

  /**
   * Opens the add equation modal
   */
  // const showAddEquationModal = () => {
  //   setIsAddEquationModalOpen(true);
  // };

  /**
   * Handles the adding of algorithm
   */
  const handleAddAlgorithm = () => {
    addAlgorithmForm
      .validateFields()
      .then((values) => {
        const customAlgo = values.algorithm;
        setTempAppendItems([
          ...tempAppendItems,
          {
            value: customAlgo,
            label: customAlgo,
            isCustom: true,
            limit: 100,
            vars: [],
          },
        ]);
        setIsAddAlgorithmModalOpen(false);
      })
      .catch((error) => {
        message.error(error.errorFields[0].errors[0], 5);
      });
  };

  /**
   * Handles the cancellation of adding the algorithm
   */
  const handleCancelAddAlgorithm = () => {
    setIsAddAlgorithmModalOpen(false);
  };

  /**
   * Handles the clearing of variables in an algorithm
   *
   * @param {int} appendKey
   */
  const clearAlgorithm = (appendKey) => {
    setTempAppendItems((prevState) => {
      const newState = [...prevState];
      newState[appendKey].vars = [];
      return newState;
    });
  };

  /**
   * Handles the deletion of a custom append algorithm
   *
   * @param {string} value
   */
  const deleteAlgorithm = (value) => {
    setTempAppendItems((prevState) => {
      return prevState.filter((appendItem) => appendItem.value !== value);
    });
    setAppendItems((prevState) => {
      return prevState.filter((appendItem) => appendItem.value !== value);
    });
  };

  /**
   * Handles the saving of append algorithm
   *
   * @param {int} appendKey
   */
  const saveAlgorithm = (appendKey) => {
    const currentAlgorithms = appendItems.map((item) => item.value);
    const addedVarNames = variablesFromTestsTable.map((varObj) => varObj.value);
    const defaultVars = defaultVariableOptions.map((varObj) => varObj.value);
    const everyTestVarIsLatest = tempAppendItems[appendKey].vars
      .filter((varObj) => {
        return (
          typeof varObj.result === "undefined" &&
          !currentAlgorithms.includes(varObj.value) &&
          !defaultVars.includes(varObj.value)
        );
      })
      .every((varObj) => addedVarNames.includes(varObj.value));
    if (!everyTestVarIsLatest) {
      confirm({
        title: "Outdated Variable Names Detected",
        icon: <InfoCircleOutlined style={{ color: "rgb(239 68 68)" }} />,
        content:
          "Edit the algorithm to use the new variable names then save again.",
        cancelButtonProps: { style: { display: "none" } },
      });
      return;
    } else if (!algorithmCharCountExceeds[appendKey]) {
      setAppendItems((prevState) => {
        const newAppendItems = [...prevState];
        newAppendItems[appendKey] = { ...tempAppendItems[appendKey] };
        return newAppendItems;
      });
      // So that the character count will rerender
      setTempAppendItems([...tempAppendItems]);
    }
  };

  /**
   * Closes the add equation modal
   */
  const handleCancelAddEquation = () => {
    setIsAddEquationModalOpen(false);
    addEquationForm.setFieldsValue({ equation: [] });
  };

  /**
   * Adds an equation
   */
  const handleAddEquation = () => {
    addEquationForm
      .validateFields()
      .then((values) => {
        setAddedEquationVariables((prevState) => {
          const newState = [...prevState, ...values.equation];
          return [...new Set(newState)];
        });
        setIsAddEquationModalOpen(false);
        addEquationForm.setFieldsValue({ equation: [] });
      })
      .catch((error) => {
        message.error(error.errorFields[0].errors[0], 5);
      });
  };

  const newAlgoValidator = {
    validator: (_, value) => {
      // Do not allow redundant algo name and added variable names and equations
      const invalidAlgoName =
        tempAppendItems.map((item) => item.value).includes(value) ||
        variablesFromTestsTable.map((varObj) => varObj.value).includes(value) ||
        addedEquationVariableOptions
          .map((equationObj) => equationObj.value)
          .includes(value);
      if (invalidAlgoName) {
        return Promise.reject();
      } else {
        return Promise.resolve();
      }
    },
    message:
      "Algo name entered already exists, is a reserve or is a variable name.",
  };

  return (
    <>
      <Row gutter={[16, 24]}>
        {tempAppendItems.map((item, key) => {
          return (
            <Col span={12} key={`algo_${key}`}>
              <Card
                title={
                  <>
                    <Text className="mr-3 overflow-visible whitespace-normal break-words">
                      {item.label}
                    </Text>
                    <Space className="font-normal text-sky-500">
                      <Tooltip title="Clear all variables added.">
                        <ClearOutlined
                          onClick={() => {
                            clearAlgorithm(key);
                          }}
                        ></ClearOutlined>
                      </Tooltip>
                      <Tooltip title="Save this algorithm.">
                        <SaveOutlined
                          onClick={() => {
                            !algorithmCharCountExceeds[key] &&
                              saveAlgorithm(key);
                          }}
                          className={`${algorithmCharCountExceeds[key] ? "text-gray-400 cursor-not-allowed!" : "cursor-pointer"}`}
                        />
                      </Tooltip>
                      {typeof item.isCustom !== "undefined" &&
                        item.isCustom === true && (
                          <Tooltip title="Delete this algorithm.">
                            <DeleteOutlined
                              onClick={() => {
                                deleteAlgorithm(item.value);
                              }}
                            ></DeleteOutlined>
                          </Tooltip>
                        )}
                    </Space>
                  </>
                }
                size="small"
                bordered={false}
              >
                <Flex gap="4px 0" wrap="wrap">
                  {item.vars.map((variable, index) => {
                    return (
                      <Tag
                        key={`${key}_${index}_${variable.value}`}
                        closable
                        style={{
                          userSelect: "none",
                        }}
                        onClose={() => handleRemoveVariable(index, key)}
                      >
                        {variable.label}
                      </Tag>
                    );
                  })}
                  <Select
                    showSearch
                    mode="multiple"
                    popupMatchSelectWidth={false}
                    className="w-28 border border-dashed border-gray-300"
                    variant="filled"
                    size="small"
                    placeholder="+ Add Variable"
                    value={[]}
                    optionFilterProp="children"
                    options={variableOptions}
                    filterOption={(input, option) =>
                      (option?.label ?? "").includes(input)
                    }
                    filterSort={(optionA, optionB) =>
                      (optionA?.label ?? "")
                        .toLowerCase()
                        .localeCompare((optionB?.label ?? "").toLowerCase())
                    }
                    onSelect={(value) => handleAddVariable(value, item.value)}
                    onDropdownVisibleChange={() =>
                      handleVariableExclusion(item.value)
                    }
                  />
                </Flex>
              </Card>
              <Flex justify="flex-end">
                <Text
                  style={{
                    color: getCharCountColor(key),
                  }}
                >
                  {`${currentAlgorithmCharCount[key] ?? 0} / ${
                    item.limit ?? 100
                  }`}
                </Text>
              </Flex>
              {algorithmCharCountExceeds[key] && (
                <Alert
                  message="Exceeds the allowed maximum number of characters."
                  type="error"
                  showIcon
                />
              )}
            </Col>
          );
        })}
      </Row>
      <Space className="mt-2.5 mb-2.5 h-20" size={"middle"}>
        <div className="border border-gray-300 border-dashed rounded-sm p-2.5 bg-gray-100">
          <button
            className="border-0 bg-inherit cursor-pointer"
            type="button"
            onClick={showAddAlgorithmModal}
          >
            <PlusOutlined />
            <div className="mt-2">Add Algorithm</div>
          </button>
        </div>
        {/* <div className="border border-gray-300 border-dashed rounded-sm p-2.5 bg-gray-100">
        <button
          className="border-0 bg-inherit cursor-pointer"
          type="button"
          onClick={showAddEquationModal}
        >
          <PlusOutlined />
          <div className="mt-2">Add Equation</div>
        </button>
      </div> */}
      </Space>
      <Modal
        title="Add Algorithm"
        open={isAddAlgorithmModalOpen}
        onOk={handleAddAlgorithm}
        onCancel={handleCancelAddAlgorithm}
        centered
      >
        <Form form={addAlgorithmForm} name="add_algorithm">
          <Form.Item
            name="algorithm"
            rules={[
              {
                required: true,
                message: "Please input algorithm name",
              },
              {
                pattern: /^[a-zA-Z_-]+[a-zA-Z0-9_-]*$/,
                message:
                  "Can only include numbers 0-9, letters a-z, - and _ and should not start with a number.",
              },
              newAlgoValidator,
            ]}
          >
            <Input placeholder="Custom name" />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title="Add Equation"
        open={isAddEquationModalOpen}
        onOk={handleAddEquation}
        onCancel={handleCancelAddEquation}
        centered
        destroyOnClose
        footer={[
          <Button key="back" onClick={handleCancelAddEquation}>
            Close
          </Button>,
          <Button
            type="primary"
            key="enter"
            onClick={handleAddEquation}
            disabled={isAddEquationBtnDisabled}
          >
            Add
          </Button>,
        ]}
      >
        <Form
          form={addEquationForm}
          name="add_equation"
          onFieldsChange={handleAddEquationFieldsChange}
        >
          <Form.Item
            name="equation"
            rules={[
              {
                required: true,
                message: "Please select an equation to add.",
              },
            ]}
          >
            <Select
              placeholder="Please select equation"
              mode="multiple"
              options={equationOptions}
            ></Select>
            {Object.keys(equationValues).length === 0 && (
              <Alert
                message="No Equations Available"
                description="To add equations to your recipe, you must create first in the Operation section."
                type="error"
                showIcon
                className="mt-1.5"
              />
            )}
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default AppendSection;
