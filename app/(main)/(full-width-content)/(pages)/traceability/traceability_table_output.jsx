"use_client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { useRef, useEffect, useState } from "react";
import YHGrid from "../../../../../src/utils/grid/yh_grid";
import { useEffectApiFetch } from "../../../../../src/hooks";
import Api from "../../../../../src/utils/api";

/**
 * Traceability table output component
 *
 * @param {string} pageKey
 * @param {boolean} isModalOpen
 * @param {function} setIsModalOpen
 * @param {object} selectedTraceabilityParams
 * @returns {JSX.Element}
 */
const TraceabilityTableOutput = ({
  pageKey,
  isModalOpen,
  setIsModalOpen,
  selectedTraceabilityParams,
}) => {
  const gridRef = useRef();
  const [gridComponent, setGridComponent] = useState();
  const [tableFilters, setTableFilters] = useState({});
  const [tableName, setTableName] = useState("traceability_report_table_test");
  const { message } = App.useApp();

  useEffect(() => {
    const filters = {};
    filters[pageKey] = {};
    Object.keys(selectedTraceabilityParams).forEach((key) => {
      filters[pageKey][key] = Array.isArray(selectedTraceabilityParams[key])
        ? selectedTraceabilityParams[key].join(",")
        : selectedTraceabilityParams[key];
    });
    setTableFilters(filters);
    if (typeof filters.mfg_process !== "undefined") {
      setTableName(
        `traceability_report_table_${filters.mfg_process.toLowerCase()}`,
      );
    }
  }, [selectedTraceabilityParams]);

  useEffectApiFetch(
    () => {
      return getGridComponent();
    },
    () => {
      setGridComponent();
    },
    [selectedTraceabilityParams],
  );

  /**
   * Get output grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: tableName,
      },
    );

    return abortCtl;
  };

  /**
   * Handles the cancellation of the modal
   */
  const handleCancelBtn = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <Modal
        title=""
        centered
        width={"85vw"}
        open={isModalOpen}
        onOk={handleCancelBtn}
        onCancel={handleCancelBtn}
        footer={[
          <Button key="back" type="primary" onClick={handleCancelBtn}>
            Close
          </Button>,
        ]}
      >
        <div className="h-[32rem] mt-4">
          {gridComponent && (
            <YHGrid
              key={selectedTraceabilityParams.toString()}
              gridRef={gridRef}
              gridId={tableName}
              component={gridComponent}
              pageKey={pageKey}
              filters={tableFilters}
              wrapperClassName="flex grow flex-col h-full"
            />
          )}
        </div>
      </Modal>
    </>
  );
};

export default TraceabilityTableOutput;
