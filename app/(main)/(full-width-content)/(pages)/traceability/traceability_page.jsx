"use client";

import { <PERSON>pp, <PERSON><PERSON>, <PERSON>lap<PERSON>, Flex, Form, theme, Modal } from "antd";
import {
  CheckCircleOutlined,
  FileAddOutlined,
  FolderOpenOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { useEffect, useRef, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import PagesLayout from "../layout";
import DatalogInfoGrid from "../../../../../src/utils/components/recipe_common/datalog_info_grid";
import Helper from "../../../../../src/utils/helper";
import Api from "../../../../../src/utils/api";
import LoadRecipeModal from "../../../../../src/utils/components/recipe_common/load_recipe_modal";
import ApplyRecipeModal from "../../../../../src/utils/components/recipe_common/apply_recipe_modal";
import RecipeInfoGrid from "../../../../../src/utils/components/recipe_common/recipe_info_grid";
import TraceabilitySimulationOutput from "./traceability_simulation_output";
import TraceabilityEquation from "./traceability_equation";
import TraceabilityTestsGrid from "./traceability_tests_grid";
import TraceabilityAlgorithms from "./traceability_algorithms";

/**
 * Traceability page component
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
const TraceabilityPage = ({ pageKey }) => {
  const { token } = theme.useToken();
  const panelStyle = {
    background: token.colorBgTextHover,
    marginBottom: 16,
    borderRadius: token.yhCollapseBorderRadius,
    border: token.yhCollapseBorder,
  };
  const [recipeInfoPanelStyle, setRecipeInfoPanelStyle] = useState({
    ...panelStyle,
    display: "none",
  });
  const [simulationPanelStyle, setSimulationPanelStyle] = useState({
    ...panelStyle,
    display: "none",
  });
  const [variablesFromTestsTable, setVariablesFromTestsTable] = useState([]);
  const [addedEquationVariables, setAddedEquationVariables] = useState([]);
  const [equationValues, setEquationValues] = useState({});
  const [pageFilters, setPageFilters] = useState({});
  const [selectedParams, setSelectedParams] = useState({});
  const [simulationTableFilters, setSimulationTableFilters] = useState({});
  const [activeCollapseKey, setActiveCollapseKey] = useState([
    "tests_table",
    "create_recipe",
  ]);
  const [defaultVariableOptions, setDefaultVariableOptions] = useState([]);
  const [variableOptions, setVariableOptions] = useState([]);
  const [appendItems, setAppendItems] = useState([]);
  const [initialAppendItems, setInitialAppendItems] = useState([]);
  const [linkToOptions, setLinkToOptions] = useState([]);
  const [outputOptions, setOutputOptions] = useState([]);
  const [conversionOptions, setConversionOptions] = useState([]);
  const [defaultRecipeFormValues, setDefaultRecipeFormValues] = useState({});
  const [loadedRecipeFormValues, setLoadedRecipeFormValues] = useState({});
  const [traceabilityConfigLoaded, setTraceabilityConfigLoaded] =
    useState(false);
  const [recipeLoaded, setRecipeLoaded] = useState(1);
  const [clearRecipePage, setClearRecipePage] = useState(1);
  const [initialSetupTableRowData, setInitialSetupTableRowData] = useState([]);
  const [addedEquationVariableOptions, setAddedEquationVariableOptions] =
    useState([]);
  const [appendVariablesString, setAppendVariablesString] = useState("");
  const [
    isLoadTraceabilityRecipeModalOpen,
    setIsLoadTraceabilityRecipeModalOpen,
  ] = useState(false);
  const [isApplyRecipeModalOpen, setIsApplyRecipeModalOpen] = useState(false);
  const [equationOptions, setEquationOptions] = useState([]);
  const [recipeData, setRecipeData] = useState({});
  const [activeRecipeName, setActiveRecipeName] = useState("");
  const [recipeVersion, setRecipeVersion] = useState("");
  const [simulationIsMade, setSimulationIsMade] = useState(false);
  const [loadEquationOptions, setLoadEquationOptions] = useState([]);
  const [createRecipeForm] = Form.useForm();
  const testsGridRef = useRef();
  const { message } = App.useApp();
  const [{ confirm }, contextHolder] = Modal.useModal();

  useEffect(() => {
    setAddedEquationVariableOptions(
      addedEquationVariables.map((equationVariable) => {
        return {
          value: equationVariable,
          label: equationVariable,
          actualValue: equationValues[equationVariable].actualValue,
          result: equationValues[equationVariable].result,
        };
      }),
    );
  }, [addedEquationVariables]);

  useEffect(() => {
    const pageFilters = Helper.getUrlParameters();
    setPageFilters(pageFilters);
    // For endpoints that requires param values to be array
    const params = {};
    Object.keys(pageFilters).forEach((key) => {
      params[key] = pageFilters[key].split(",");
    });
    setSelectedParams(params);
    setRecipeVersion(pageFilters.recipe_version ?? "");
  }, []);

  useEffect(() => {
    updateVariableOptions();
  }, [defaultVariableOptions]);

  useEffect(() => {
    if (activeRecipeName === "") {
      setRecipeInfoPanelStyle({
        ...panelStyle,
        display: "none",
      });
    } else {
      setRecipeInfoPanelStyle(panelStyle);
    }
  }, [activeRecipeName]);

  useEffect(() => {
    if (simulationIsMade) {
      setSimulationPanelStyle(panelStyle);
    }
  }, [simulationIsMade]);

  /**
   * Get the traceability configuration data
   *
   * @returns {AbortController} abortCtl
   */
  const getTraceabilityConfig = () => {
    const abortCtl = Api.getTraceabilityConfig(
      (res) => {
        if (res.success) {
          setDefaultVariableOptions(res.data.traceability_custom_append_chars);
          // Original copy that should not be mutated, will become the basis when loading a recipe
          setInitialAppendItems(res.data.traceability_fields);
          setAppendItems(res.data.traceability_fields);
          setOutputOptions(res.data.traceability_output_methods);
          setLinkToOptions(res.data.traceability_input_methods);
          setDefaultRecipeFormValues((prevState) => {
            return {
              ...prevState,
              save_type: { draft: true },
              recipe_output: res.data.default_output_methods,
            };
          });
          const conversionOptions = {};
          res.data.conversions.forEach((conversion) => {
            conversionOptions[conversion.value] = conversion.label;
          });
          setConversionOptions(conversionOptions);
          setTraceabilityConfigLoaded(true);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
    );

    return abortCtl;
  };

  useQuery({
    queryKey: ["traceability_config"],
    queryFn: getTraceabilityConfig,
  });

  /**
   * Get the append algorithms that have variables
   *
   * @returns {array} The non empty append algorithms
   */
  const getAppendOptions = () => {
    return appendItems.filter((item) => item.vars.length).map((item) => item);
  };

  /**
   * Get the variables and algorithms
   *
   * @returns {array} data
   */
  const getVariablesAndAlgos = () => {
    const data = {};
    // Append algos
    data.algos = appendItems.filter((item) => item.vars.length);
    const rowData = [];
    testsGridRef.current.api.forEachNode((node) => {
      rowData.push(node.data);
    });
    // Test variables
    const testVariables = variablesFromTestsTable.map(
      (variable) => variable.value,
    );
    data.test_info = rowData.filter((data) =>
      testVariables.includes(data.variable),
    );

    return data;
  };

  /**
   * Updates the variable list to append in algorithms
   */
  const updateVariableOptions = () => {
    const appendVariableOptions = getAppendOptions();
    setVariableOptions([
      ...defaultVariableOptions,
      ...appendVariableOptions,
      ...variablesFromTestsTable,
      ...addedEquationVariableOptions,
    ]);
  };

  /**
   * Clears the inputs of the page for new recipe
   *
   * @param {object} confirmModal
   */
  const addNewTraceabilityRecipe = (confirmModal) => {
    setClearRecipePage(Date.now());
    confirmModal.destroy();
  };

  /**
   * When user clicks the "Create New Recipe" button
   */
  const confirmNewTraceabilityRecipe = () => {
    const confirmModal = confirm({
      title: "Create New Recipe",
      width: 480,
      icon: <InfoCircleOutlined style={{ color: "rgb(252 211 77)" }} />,
      content:
        "Your new recipe will be using your currently selected datalog. If you want to use a different datalog, you must go back to the Homepage to select.",
      footer: (
        <Flex justify="right" gap="small" className="mt-3">
          <Button
            key="back"
            onClick={() => {
              confirmModal.destroy();
            }}
          >
            Cancel
          </Button>
          <Button
            key="home"
            onClick={() => {
              window.open("/page", "_blank");
              confirmModal.destroy();
            }}
          >
            Homepage
          </Button>
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              addNewTraceabilityRecipe(confirmModal);
            }}
          >
            Proceed
          </Button>
        </Flex>
      ),
    });
  };

  /**
   * When user clicks the "Load Recipe" button
   */
  const loadTraceabilityRecipe = () => {
    setIsLoadTraceabilityRecipeModalOpen(true);
  };

  /**
   * Sets the active collapse key when user clicks the header
   * This is to save the state of the key when user simulates and we
   * automatically opens the simulation section
   *
   * @param {array} key
   */
  const handleCollapseChange = (key) => {
    setActiveCollapseKey(key);
  };

  /**
   * Get the recipe data
   *
   * @returns {AbortController} abortCtl
   */
  const getTraceabilityRecipeData = () => {
    const abortCtl = Api.getTraceabilityRecipeData(
      (res) => {
        if (res.success) {
          setRecipeData(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        recipe_name: pageFilters.recipe_name,
        recipe_version: pageFilters.recipe_version,
      },
    );

    return abortCtl;
  };

  const { refetch: refetchUrlLoadedRecipe } = useQuery({
    queryKey: ["url_loaded_recipe"],
    queryFn: getTraceabilityRecipeData,
    refetchOnWindowFocus: false,
    enabled: false,
  });

  /**
   * Load append items from saved recipe
   */
  const loadAppendItems = () => {
    if (typeof recipeData.algos !== "undefined") {
      const initialAppendAlgos = initialAppendItems.map((item) => item.value);
      const standardAppendAlgos = [];
      const customAppendAlgos = [];
      recipeData.algos.forEach((algo) => {
        if (initialAppendAlgos.includes(algo.value)) {
          standardAppendAlgos.push(algo);
        } else {
          customAppendAlgos.push(algo);
        }
      });
      const newAppendItems = initialAppendItems.map((item) => {
        item.vars = [];
        return item;
      });
      newAppendItems.forEach((newItem) => {
        standardAppendAlgos.forEach((item) => {
          if (newItem.value === item.value) {
            newItem.vars = item.vars;
          }
        });
      });
      setAppendItems([...newAppendItems, ...customAppendAlgos]);
    }
  };

  /**
   * Load the recipe form values
   */
  const loadRecipeFormValues = () => {
    let inputValues = {};

    if (recipeData?.recipe_name) {
      const FORM_FIELDS = {
        recipe_name: { type: "string", required: true },
        recipe_process: { type: "array" },
        recipe_output: { type: "array" },
        recipe_input: { type: "array" },
        save_type: { type: "object" },
        trigger_data: { type: "array" },
        notes: { type: "string" },
      };

      inputValues = Object.entries(FORM_FIELDS).reduce(
        (acc, [fieldName, config]) => {
          const value = recipeData[fieldName];
          let shouldInclude = true;

          // Check conditions that would exclude this field
          const isValueMissing = value === undefined;
          const isRequiredFieldEmpty = config.required && !value;
          const isInvalidType = !(config.type === "array"
            ? Array.isArray(value)
            : typeof value === config.type);
          const isEmptyArray = Array.isArray(value) && value.length === 0;

          if (
            isValueMissing ||
            isRequiredFieldEmpty ||
            isInvalidType ||
            isEmptyArray
          ) {
            shouldInclude = false;

            // Log warnings for validation failures
            if (config.required && !value) {
              console.warn(`Required field ${fieldName} is empty`);
            } else if (
              !(config.type === "array"
                ? Array.isArray(value)
                : typeof value === config.type)
            ) {
              console.warn(
                `Invalid type for ${fieldName}. Expected ${config.type}, got ${typeof value}`,
              );
            }
          }

          if (shouldInclude) {
            acc[fieldName] = value;
          }

          return acc;
        },
        {},
      );
    }

    setLoadedRecipeFormValues(inputValues);
  };

  /**
   * Load the test number variables
   */
  const loadTestVariables = () => {
    if (
      typeof testsGridRef.current !== "undefined" &&
      typeof recipeData.test_info !== "undefined" &&
      Object.keys(recipeData.test_info).length
    ) {
      const testNumbers = [];
      const rowData = [];
      const testsVariables = Object.values(recipeData.test_info).map((obj) => {
        rowData.push(obj);
        testNumbers.push(obj.test_number);
        const actualValue = obj[obj.conversion];
        return {
          value: obj.variable,
          label: `${obj.variable} (${actualValue})`,
          actualValue: actualValue,
          tnum: obj.test_number,
        };
      });
      setVariablesFromTestsTable(testsVariables);

      testsGridRef.current.api.setGridOption("rowData", rowData);
      testsGridRef.current.api.forEachNode((node) => {
        node.setSelected(true);
      });
    }
  };

  /**
   * Load the equation variables and select options
   */
  const loadEquationVariablesAndOptions = () => {
    if (typeof recipeData.algos !== "undefined") {
      const equationOptions = [];
      const equationValues = {};
      recipeData.algos.forEach((algo) => {
        algo.vars.forEach((variable) => {
          if (typeof variable.actualValue !== "undefined") {
            equationOptions.push(variable);
            equationValues[variable.value] = {
              value: variable.actualValue,
              result: variable.result,
            };
          }
        });
      });
      setAddedEquationVariables(Object.keys(equationValues));
      setAddedEquationVariableOptions(equationOptions);
      setEquationValues(equationValues);
    }
  };

  // When loading a recipe within the page
  useEffect(() => {
    loadAppendItems();
    loadRecipeFormValues();
    loadTestVariables();
    loadEquationVariablesAndOptions();
    setActiveRecipeName(recipeData.recipe_name ?? "");
    setRecipeLoaded(Date.now());
  }, [recipeData]);

  /**
   * When creating new recipe
   */
  useEffect(() => {
    if (typeof testsGridRef.current !== "undefined") {
      setAppendItems(
        initialAppendItems.map((item) => {
          item.vars = [];
          return item;
        }),
      );
      createRecipeForm.resetFields();
      setVariablesFromTestsTable([]);
      if (initialSetupTableRowData.length) {
        testsGridRef.current.api.setGridOption(
          "rowData",
          initialSetupTableRowData,
        );
      }
      setAddedEquationVariables([]);
      setAddedEquationVariableOptions([]);
      setEquationValues({});
      setActiveRecipeName("");
      setRecipeLoaded(Date.now());
    }
  }, [clearRecipePage]);

  /**
   * Opens the apply recipe modal
   */
  const applyRecipe = () => {
    setIsApplyRecipeModalOpen(true);
  };

  return (
    <PagesLayout>
      {contextHolder}
      <LoadRecipeModal
        pageKey={pageKey}
        isModalOpen={isLoadTraceabilityRecipeModalOpen}
        setIsModalOpen={setIsLoadTraceabilityRecipeModalOpen}
        selectedParams={pageFilters}
        fromExternalPage={false}
        getRecipeListApi={Api.getTraceabilityRecipes}
        getRecipeVersionApi={Api.getTraceabilityRecipeVersion}
        getRecipeInfoApi={Api.getTraceabilityRecipeInfo}
        getRecipeDataApi={Api.getRecipeData}
        setRecipeData={setRecipeData}
        setRecipeVersion={setRecipeVersion}
        recipeType="traceability"
      />
      <ApplyRecipeModal
        pageKey={pageKey}
        isModalOpen={isApplyRecipeModalOpen}
        setIsModalOpen={setIsApplyRecipeModalOpen}
        selectedParams={selectedParams}
        getRecipeListApi={Api.getTraceabilityRecipes}
        getRecipeInfoApi={Api.getTraceabilityRecipeInfo}
        getRecipeVersionApi={Api.getTraceabilityRecipeVersion}
        applyRecipeApi={Api.applyTraceabilityRecipe}
        recipeType="traceability"
        activeRecipeName={activeRecipeName}
      />
      <Flex wrap="wrap" gap="middle" className="min-h-10">
        <Button
          icon={<FileAddOutlined />}
          onClick={confirmNewTraceabilityRecipe}
        >
          Create New Recipe
        </Button>
        <Button icon={<FolderOpenOutlined />} onClick={loadTraceabilityRecipe}>
          Load a Recipe
        </Button>
        <Button icon={<CheckCircleOutlined />} onClick={applyRecipe}>
          Apply Recipe to Selected Datalog
        </Button>
      </Flex>
      <Collapse
        items={[
          {
            key: "dlog_info",
            label: "Selected Datalogs Information",
            style: panelStyle,
            children: (
              <DatalogInfoGrid
                pageKey={pageKey}
                pageFilters={pageFilters}
                gridId="traceability_dlog_info"
              />
            ),
          },
          {
            key: "recipe_info",
            label: `Recipe Information - ${activeRecipeName}`,
            style: recipeInfoPanelStyle,
            children: (
              <RecipeInfoGrid
                pageKey={pageKey}
                recipeName={activeRecipeName}
                recipeType="traceability"
                recipeVersion={recipeVersion}
              />
            ),
          },
          {
            key: "tests_table",
            label: "Tests",
            style: panelStyle,
            children: (
              <TraceabilityTestsGrid
                pageKey={pageKey}
                pageFilters={pageFilters}
                conversionOptions={conversionOptions}
                variablesFromTestsTable={variablesFromTestsTable}
                setVariablesFromTestsTable={setVariablesFromTestsTable}
                testsGridRef={testsGridRef}
                setInitialSetupTableRowData={setInitialSetupTableRowData}
                refetchUrlLoadedRecipe={refetchUrlLoadedRecipe}
              />
            ),
          },
          {
            key: "equation",
            label: "Operation",
            style: panelStyle,
            children: (
              <TraceabilityEquation
                setAddedEquationVariables={setAddedEquationVariables}
                addedEquationVariables={addedEquationVariables}
                equationValues={equationValues}
                setEquationValues={setEquationValues}
                getVariablesAndAlgos={getVariablesAndAlgos}
                setEquationOptions={setEquationOptions}
                pageFilters={pageFilters}
                appendItems={appendItems}
                recipeData={recipeData}
                loadEquationOptions={loadEquationOptions}
                setLoadEquationOptions={setLoadEquationOptions}
              />
            ),
          },
          {
            key: "create_recipe",
            label: "Recipe",
            style: panelStyle,
            children: (
              <TraceabilityAlgorithms
                setActiveCollapseKey={setActiveCollapseKey}
                updateVariableOptions={updateVariableOptions}
                appendItems={appendItems}
                setAppendItems={setAppendItems}
                outputOptions={outputOptions}
                defaultRecipeFormValues={defaultRecipeFormValues}
                loadedRecipeFormValues={loadedRecipeFormValues}
                linkToOptions={linkToOptions}
                variableOptions={variableOptions}
                setVariableOptions={setVariableOptions}
                getAppendOptions={getAppendOptions}
                addedEquationVariableOptions={addedEquationVariableOptions}
                variablesFromTestsTable={variablesFromTestsTable}
                equationValues={equationValues}
                traceabilityConfigLoaded={traceabilityConfigLoaded}
                pageFilters={pageFilters}
                setSimulationTableFilters={setSimulationTableFilters}
                getVariablesAndAlgos={getVariablesAndAlgos}
                setAppendVariablesString={setAppendVariablesString}
                recipeLoaded={recipeLoaded}
                createRecipeForm={createRecipeForm}
                equationOptions={equationOptions}
                setAddedEquationVariables={setAddedEquationVariables}
                activeRecipeName={activeRecipeName}
                setActiveRecipeName={setActiveRecipeName}
                setSimulationIsMade={setSimulationIsMade}
                defaultVariableOptions={defaultVariableOptions}
                setIsApplyRecipeModalOpen={setIsApplyRecipeModalOpen}
                setEquationOptions={setEquationOptions}
                addedEquationVariables={addedEquationVariables}
                loadEquationOptions={loadEquationOptions}
              />
            ),
          },
          {
            key: "simulation_table",
            label: "Simulation Output Table",
            style: simulationPanelStyle,
            children: (
              <TraceabilitySimulationOutput
                pageKey={pageKey}
                simulationTableFilters={simulationTableFilters}
                appendVariablesString={appendVariablesString}
              />
            ),
          },
        ]}
        activeKey={activeCollapseKey}
        onChange={handleCollapseChange}
        style={{
          background: token.yhPageColorBg,
          border: token.yhTransparentBorder,
        }}
      />
    </PagesLayout>
  );
};

export default TraceabilityPage;
