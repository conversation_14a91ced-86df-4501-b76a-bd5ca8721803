"use client";

import { useState } from "react";
import {
  <PERSON>ton,
  Dropdown,
  Flex,
  Modal,
  Space,
  Tooltip,
  Typography,
} from "antd";
import { DownOutlined, InfoCircleOutlined } from "@ant-design/icons";
import Helper from "../../../../../src/utils/helper";
import TraceabilityTableOutput from "../../../(full-width-content)/(pages)/traceability/traceability_table_output";
import LoadRecipeModal from "../../../../../src/utils/components/recipe_common/load_recipe_modal";
import ApplyRecipeModal from "../../../../../src/utils/components/recipe_common/apply_recipe_modal";
import AddTestsModal from "../../../../../src/utils/components/recipe_common/add_tests_modal";
import Api from "../../../../../src/utils/api";

/**
 * Meta Data menu component
 *
 * @param {string} pageKey
 * @param {object} queryClient
 * @param {function} getSearchTableSelection
 * @returns {JSX.Element}
 */
const TraceabilityMenu = ({
  gridRef,
  pageKey,
  queryClient,
  getSearchTableSelection,
}) => {
  const [modalApi, modalContextHolder] = Modal.useModal();
  const [
    selectedDatalogsHaveMatchingAppliedRecipes,
    setSelectedDatalogsHaveMatchingAppliedRecipes,
  ] = useState(false);
  const [selectedParams, setSelectedParams] = useState({});
  const [isLoadRecipeModalOpen, setIsLoadRecipeModalOpen] = useState(false);
  const [isApplyRecipeModalOpen, setIsApplyRecipeModalOpen] = useState(false);
  const [isAddTestModalOpen, setIsAddTestModalOpen] = useState(false);
  const [selectedDlogCount, setSelectedDlogCount] = useState(0);
  const [
    isTraceabilityTableOutputModalOpen,
    setIsTraceabilityTableOutputModalOpen,
  ] = useState(false);

  /**
   * Render the traceability page
   *
   * @param {object} selectedParams
   */
  const renderRecipePage = (selectedParams) => {
    const queryString = Helper.createQueryString(selectedParams);
    Helper.renderPage(
      `#traceability?${queryString}`,
      "traceability",
      `#traceability?${queryString}`,
      queryClient,
    );
  };

  /**
   * Confirmation modal for open traceability table output
   *
   * @param {object} info
   */
  const confirmSelection = (info) => {
    const confirmModal = modalApi.confirm({
      title: info.title,
      width: 480,
      icon: <InfoCircleOutlined className="text-[rgb(252,211,77)]" />,
      content: info.content,
      footer: (
        <Flex justify="right" gap="small" className="mt-3">
          <Button
            key="back"
            onClick={() => {
              confirmModal.destroy();
            }}
          >
            Cancel
          </Button>
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              setIsTraceabilityTableOutputModalOpen(true);
              confirmModal.destroy();
            }}
          >
            {info.submitText}
          </Button>
        </Flex>
      ),
    });
  };

  const traceabilityMenuItemHandlers = {
    create_traceability: () => {
      setIsAddTestModalOpen(true);
    },
    load_traceability: () => {
      setIsLoadRecipeModalOpen(true);
    },
    apply_traceability: () => {
      setIsApplyRecipeModalOpen(true);
    },
    show_table_output: () => {
      if (
        selectedDlogCount === 1 ||
        selectedDatalogsHaveMatchingAppliedRecipes
      ) {
        setIsTraceabilityTableOutputModalOpen(true);
      } else {
        confirmSelection({
          title: "Inconsistent Applied Recipes",
          submitText: "Proceed Anyway",
          content: (
            <Typography.Text>
              The datalogs you selected do not share the same applied recipes.
            </Typography.Text>
          ),
        });
      }
    },
  };

  /**
   * Handle the traceability menu items
   *
   * @param {string} key
   */
  const generateTraceability = ({ key }) => {
    const selectedParams = getSearchTableSelection();
    if (selectedParams.search_selection) {
      delete selectedParams.search_selection;
    }
    setSelectedParams({ ...selectedParams });
    traceabilityMenuItemHandlers[key]();
  };

  const traceabilityItems = [
    {
      key: "create_traceability",
      label:
        selectedDlogCount === 1 ? (
          "Create Recipe"
        ) : (
          <Tooltip title="Select one datalog">
            <span>Create Recipe</span>
          </Tooltip>
        ),
      disabled: selectedDlogCount !== 1,
    },
    {
      key: "load_traceability",
      label:
        selectedDlogCount === 1 ? (
          "Load Recipe"
        ) : (
          <Tooltip title="Select one datalog">
            <span>Load Recipe</span>
          </Tooltip>
        ),
      disabled: selectedDlogCount !== 1,
    },
    {
      key: "apply_traceability",
      label:
        selectedDlogCount > 0 ? (
          "Apply Recipe"
        ) : (
          <Tooltip title="Select at least one datalog">
            <span>Apply Recipe</span>
          </Tooltip>
        ),
      disabled: selectedDlogCount === 0,
    },
    {
      key: "show_table_output",
      label:
        selectedDlogCount >= 1 ? (
          "Open Output Table"
        ) : (
          <Tooltip title="Select one or more datalog(s)">
            <span>Open Output Table</span>
          </Tooltip>
        ),
      disabled: selectedDlogCount < 1,
    },
  ];

  /**
   * Enable/disable menu items based on dlog selection
   */
  const validateMenuItems = () => {
    const selectedParams = getSearchTableSelection();

    if (selectedParams.dsk && selectedParams.dsk.length > 0) {
      const selectedRows = gridRef.current?.api.getSelectedRows();

      let firstRow = undefined;
      let firstRowTraceability = undefined;
      try {
        firstRow = JSON.parse(selectedRows[0]["applied_recipes"]);
        firstRowTraceability = JSON.parse(firstRow.traceability);
      } catch {
        firstRow = undefined;
        firstRowTraceability = undefined;
      }

      let hasMatchingAppliedRecipes =
        selectedRows.length === 0 || firstRow?.traceability === undefined
          ? false
          : selectedRows.every((item) => {
              try {
                const appliedRecipes = JSON.parse(item["applied_recipes"]);
                if (appliedRecipes.traceability === undefined) {
                  return false;
                }
                const traceability = JSON.parse(appliedRecipes.traceability);
                return traceability.name === firstRowTraceability.name;
              } catch {
                return false;
              }
            });
      setSelectedDatalogsHaveMatchingAppliedRecipes(hasMatchingAppliedRecipes);
    }

    if (selectedParams.dsk) {
      setSelectedDlogCount(selectedParams.dsk.length);
    } else {
      setSelectedDlogCount(0);
    }
  };

  return (
    <>
      {modalContextHolder}
      <AddTestsModal
        selectedParams={selectedParams}
        isModalOpen={isAddTestModalOpen}
        setIsModalOpen={setIsAddTestModalOpen}
        addTestsCallback={(additionalParams) => {
          renderRecipePage({ ...selectedParams, ...additionalParams });
        }}
      ></AddTestsModal>
      <LoadRecipeModal
        pageKey="traceability"
        isModalOpen={isLoadRecipeModalOpen}
        setIsModalOpen={setIsLoadRecipeModalOpen}
        selectedParams={selectedParams}
        renderRecipePage={renderRecipePage}
        fromExternalPage={true}
        getRecipeListApi={Api.getTraceabilityRecipes}
        getRecipeVersionApi={Api.getTraceabilityRecipeVersion}
        getRecipeInfoApi={Api.getTraceabilityRecipeInfo}
        recipeType="traceability"
      />
      <ApplyRecipeModal
        pageKey="traceability"
        isModalOpen={isApplyRecipeModalOpen}
        setIsModalOpen={setIsApplyRecipeModalOpen}
        selectedParams={selectedParams}
        getRecipeListApi={Api.getTraceabilityRecipes}
        getRecipeVersionApi={Api.getTraceabilityRecipeVersion}
        getRecipeInfoApi={Api.getTraceabilityRecipeInfo}
        applyRecipeApi={Api.applyTraceabilityRecipe}
        recipeType="traceability"
      />
      <TraceabilityTableOutput
        isModalOpen={isTraceabilityTableOutputModalOpen}
        setIsModalOpen={setIsTraceabilityTableOutputModalOpen}
        pageKey={pageKey}
        selectedTraceabilityParams={selectedParams}
      />
      <Dropdown
        menu={{ items: traceabilityItems, onClick: generateTraceability }}
        trigger="click"
        onClick={validateMenuItems}
      >
        <Typography.Text className="cursor-pointer">
          <Space>
            Traceability
            <DownOutlined />
          </Space>
        </Typography.Text>
      </Dropdown>
    </>
  );
};

export default TraceabilityMenu;
