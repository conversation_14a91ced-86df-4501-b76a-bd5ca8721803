"use client";

import { Alert } from "antd";
import { useState } from "react";
import TraceabilitySimulationOutputGrid from "./traceability_simulation_output_grid";

/**
 * Traceability simulation output component
 *
 * @param {string} pageKey
 * @param {object} simulationTableFilters
 * @param {string} appendVariablesString
 * @returns {JSX.Element}
 */
const TraceabilitySimulationOutput = ({
  pageKey,
  simulationTableFilters,
  appendVariablesString,
}) => {
  const [simulationAlerts, setSimulationAlerts] = useState([]);

  return (
    <>
      {simulationAlerts.length > 0 &&
        simulationAlerts.map((alert, key) => {
          return (
            <Alert
              message={alert.value}
              type={alert.label === "notice" ? "info" : "warning"}
              key={`${alert.label}_${key}`}
              showIcon
              closable
              style={{ marginBottom: 5 }}
            ></Alert>
          );
        })}
      <TraceabilitySimulationOutputGrid
        pageKey={pageKey}
        simulationTableFilters={simulationTableFilters}
        setSimulationAlerts={setSimulationAlerts}
        appendVariablesString={appendVariablesString}
      />
    </>
  );
};

export default TraceabilitySimulationOutput;
