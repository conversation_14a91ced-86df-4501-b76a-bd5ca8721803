"use client";

import { LoginOutlined } from "@ant-design/icons";
import { App, Typography, notification, Modal, Button, Row, Col } from "antd";
import { useEffect, useRef, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useEffectApiFetch } from "../../../../../src/hooks";
import { useBoundStore } from "../../../../../src/store/store";
import Api from "../../../../../src/utils/api";
import ChartHelper from "../../../../../src/utils/charts/chart_helper";
import Helper from "../../../../../src/utils/helper";
import PagesLayout from "../layout";
import RecipeVersionHistoryGrid from "../../../../../src/utils/grid/components/recipe_version_history_grid";
import RecipeSelectedDatalogsList from "../../../../../src/utils/grid/components/recipe_selected_datalogs_list";
import RecipeOwnerNotes from "../../../../../src/utils/grid/components/recipe_owner_notes";
import LoadConsolidationRecipe from "../datacleansing/consolidation/load_consolidation";
import RecipeInfo from "../../../../../src/utils/components/recipe_common/recipe_info";
import NpiReportsModal from "../npi/npi_reports_modal";
import RecipeTemplate from "../recipe/recipe_template";
import { TemplateRow } from "./template_row";

const { Title } = Typography;

/**
 * Analysis page component
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function Analysis({ pageKey }) {
  const [templateData, setTemplateData] = useState([]);
  const [templateRows, setTemplateRows] = useState([]);
  const [templateType, setTemplateType] = useState("");
  const [isHighchartsLoaded, setIsHighchartsLoaded] = useState(false);
  const [shouldFillHeight, setShouldFillHeight] = useState(false);
  const setPageMeta = useBoundStore((state) => state.setPageMeta);
  const setAnalysisName = useBoundStore((state) => state.setAnalysisName);
  const [topFailingTestData, setTopFailingTestData] = useState();
  const setUrlParams = useBoundStore((state) => state.setUrlParams);
  const urlParams = useBoundStore((state) => state.urlParams);
  const isNpiReportsModalOpen = useBoundStore(
    (state) => state.isNpiReportsModalOpen,
  );
  const setIsNpiReportsModalOpen = useBoundStore(
    (state) => state.setIsNpiReportsModalOpen,
  );
  const isRecipeVersionHistoryModalOpen = useBoundStore(
    (state) => state.isRecipeVersionHistoryModalOpen,
  );
  const setIsRecipeVersionHistoryModalOpen = useBoundStore(
    (state) => state.setIsRecipeVersionHistoryModalOpen,
  );
  const isRecipeSelectedDatalogsModalOpen = useBoundStore(
    (state) => state.isRecipeSelectedDatalogsModalOpen,
  );
  const setIsRecipeSelectedDatalogsModalOpen = useBoundStore(
    (state) => state.setIsRecipeSelectedDatalogsModalOpen,
  );
  const isRecipeOwnerNotesModalOpen = useBoundStore(
    (state) => state.isRecipeOwnerNotesModalOpen,
  );
  const setIsRecipeOwnerNotesModalOpen = useBoundStore(
    (state) => state.setIsRecipeOwnerNotesModalOpen,
  );
  const currentChart = useBoundStore((state) => state.currentChart);
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const setCurrentChartSettings = useBoundStore(
    (state) => state.setCurrentChartSettings,
  );
  const setCurrentChartComponent = useBoundStore(
    (state) => state.setCurrentChartComponent,
  );
  const setReloadQuantityData = useBoundStore(
    (state) => state.setReloadQuantityData,
  );
  const filters = useBoundStore((state) => state.filters);
  const setFilters = useBoundStore((state) => state.setFilters);
  const currentPageData = useBoundStore((state) => state.currentPageData);
  const cacheData = useBoundStore((state) => state.cacheData);
  const activeRecipeData = useBoundStore((state) => state.activeRecipeData);
  const OEEFilters = useBoundStore((state) => state.OEEFilters);
  const setPresetAnalysisTemplates = useBoundStore(
    (state) => state.setPresetAnalysisTemplates,
  );
  const setUserAnalysisTemplates = useBoundStore(
    (state) => state.setUserAnalysisTemplates,
  );
  const setPageTitle = useBoundStore((state) => state.setPageTitle);

  const { message } = App.useApp();
  const pageRef = useRef();
  const queryClient = useQueryClient();

  /**
   * Set url parameters global state used for fetching data
   *
   * @param {object} urlParameters
   * @param {object} surlParams
   */
  const setUrlParameters = (urlParameters, surlParams) => {
    if (surlParams !== undefined) {
      urlParameters = { ...urlParameters, ...surlParams };
    }
    let urlParamsCopy = Helper.cloneObject(urlParams);
    urlParamsCopy[pageKey] = urlParameters;
    setUrlParams(urlParamsCopy);
  };

  useEffectApiFetch(
    () => {
      return Helper.getPresetAnalysisTemplates(setPresetAnalysisTemplates);
    },
    () => {
      setPresetAnalysisTemplates([]);
    },
    [],
  );

  useEffectApiFetch(
    () => {
      return Helper.getUserAnalysisTemplates(setUserAnalysisTemplates);
    },
    () => {
      setUserAnalysisTemplates([]);
    },
    [],
  );

  useEffect(() => {
    if (isHighchartsLoaded === false) {
      ChartHelper.initHighcharts(setIsChartOptionsOpen, setCurrentChart);
      setIsHighchartsLoaded(true);
    }
  }, [templateRows]);

  useEffect(() => {
    setCurrentChartSettings(
      typeof currentChart?.userOptions?.plotOptions?.series?.custom
        ?.settings !== "undefined"
        ? currentChart.userOptions.plotOptions.series.custom.settings
        : {},
    );
    setCurrentChartComponent(
      typeof currentChart.userOptions?.plotOptions?.series?.custom
        ?.component !== "undefined"
        ? currentChart.userOptions.plotOptions.series.custom.component
        : {},
    );
  }, [currentChart]);

  useEffectApiFetch(
    () => {
      let urlParameters = Helper.getUrlParameters();
      if (urlParameters["surl"] !== undefined) {
        const surl = urlParameters["surl"];
        delete urlParameters["surl"];
        return Helper.getLongUrlParams(
          surl,
          urlParameters,
          setUrlParameters,
          {},
          message,
        );
      } else {
        setUrlParameters(urlParameters);
      }
    },
    () => {
      let urlParamsCopy = Helper.cloneObject(urlParams);
      urlParamsCopy[pageKey] = {};
      setUrlParams(urlParamsCopy);
    },
  );

  useEffect(() => {
    if (urlParams[pageKey] && urlParams[pageKey].template) {
      getTemplate(urlParams[pageKey].template);
    } else if (urlParams[pageKey] && urlParams[pageKey].template_key) {
      Helper.getPresetAnalysisTemplates(
        getTemplateByKey,
        urlParams[pageKey].template_key,
      );
    }
  }, [urlParams[pageKey], currentPageData.key]);

  useEffect(() => {
    if (
      pageKey === currentPageData.key &&
      urlParams[pageKey] &&
      templateData.template_data
    ) {
      const data = JSON.parse(templateData.template_data);
      currentPageData.name = templateData.key;
      setAnalysisName(templateData.name);
      setPageTitle(templateData.name);
      setShouldFillHeight(data.fill_height);
      setTemplateType(data?.type);
      if (data.prerender_process) {
        prerenderProcesses[data.prerender_process](setTopFailingTestData);
      } else {
        initAnalysis();
      }
    }
  }, [templateData, currentPageData.key]);

  useEffect(() => {
    if (topFailingTestData) {
      initAnalysis();
    }
  }, [topFailingTestData]);

  /**
   * Initialize analysis
   */
  const initAnalysis = () => {
    // set oee filters
    if (urlParams[pageKey] && urlParams[pageKey].template_key === "oee") {
      Object.assign(urlParams[pageKey], OEEFilters);
    }

    if (urlParams[pageKey] && urlParams[pageKey].src_type !== undefined) {
      getPageMeta(generateAnalysis);
    } else {
      generateAnalysis(templateData);
    }
  };

  /**
   * Get page meta
   *
   * @param {function} successCbk
   */
  const getPageMeta = (successCbk) => {
    Helper.getPageMeta(
      urlParams[pageKey].src_type,
      urlParams[pageKey].src_value,
      urlParams[pageKey].mfg_process,
      urlParams[pageKey].group_by,
      (pageMeta) => {
        setPageMeta(pageMeta);
        successCbk(templateData, pageMeta);
      },
      message.warning,
      message.error,
      cacheData,
    );
  };

  /**
   * Generate analysis page content
   *
   * @param {array} templateData
   */
  const generateAnalysis = (templateData) => {
    if (templateData.template_data) {
      const data = JSON.parse(templateData.template_data);
      if (data.template_rows) {
        Helper.removeEmptyTemplateRows(data.template_rows);
        setTemplateRows(data.template_rows);
      }
    }
  };

  /**
   * Get template data
   *
   * @param {int} templateId
   * @returns {AbortController}
   */
  const getTemplate = (templateId) => {
    const templateIdArr = templateId.split("_");
    const type = templateIdArr[0];
    const id = templateIdArr[1];
    const getAnalysisTemplate =
      type === "preset"
        ? Api.getPresetAnalysisTemplate
        : Api.getUserAnalysisTemplate;

    return getAnalysisTemplate(
      (res) => {
        if (res.success) {
          setTemplateData(res.data);
        } else {
          notification.warning({
            message: "Analysis Template",
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: "Analysis Template",
          description: err,
        });
      },
      {
        templateId: id,
      },
    );
  };

  /**
   * Get template data by key
   *
   * @param {object} templates
   * @param {string} templateKey
   */
  const getTemplateByKey = (templates, templateKey) => {
    const template = templates.filter((templateData) => {
      return templateData.key === templateKey;
    })[0];
    setTemplateData(template);
  };

  /**
   * Exclude dsk row data in calculation
   *
   * @param {*} value
   * @param {object} data
   */
  const updateLotSummaryQuantityData = (value, data) => {
    const excludeDskArr = filters[pageKey].exclude_dsk
      ? filters[pageKey].exclude_dsk.split(",")
      : [];
    if (value === false) {
      excludeDskArr.push(data.data_struc_key);
      filters[pageKey].exclude_dsk_data = excludeDskArr.join();
    } else {
      filters[pageKey].exclude_dsk_data = excludeDskArr
        .filter((dsk) => {
          return dsk.toString() !== data.data_struc_key.toString();
        })
        .join();
    }
    setFilters(filters);
    setReloadQuantityData(Date.now());
  };

  /**
   * Get lot top failing test data
   *
   * @param {function} successCbk
   */
  const getLotTopFailingTestData = (successCbk) => {
    Helper.getLotTopFailingTestData(
      urlParams[pageKey].src_type,
      urlParams[pageKey].src_value,
      urlParams[pageKey].mfg_process,
      successCbk,
      message.warning,
      message.error,
      cacheData,
    );
  };

  /**
   * Action mapper for cell input elements
   */
  const cellActions = {
    updateLotSummaryQuantityData,
  };

  /**
   * Action mapper for prerender process
   */
  const prerenderProcesses = {
    getLotTopFailingTestData,
  };

  /**
   * View recipe datalogs filtered in search page
   */
  const viewRecipeDatalogsInSearchPage = () => {
    Helper.getManufacturingProcessOptions(openRecipeDatalogsInSearchPage);
  };

  /**
   * Open recipe datalogs filtered in search page
   *
   * @param {array} mfgProcessOptions
   */
  const openRecipeDatalogsInSearchPage = (mfgProcessOptions) => {
    setIsRecipeSelectedDatalogsModalOpen(false);
    const key = "home";
    const filtersCopy = Helper.cloneObject(filters);
    filtersCopy[key] = {};
    filtersCopy[key].manufacturing_process = mfgProcessOptions
      .map((data) => {
        return data.value;
      })
      .join();
    filtersCopy[key].file_name = activeRecipeData[pageKey].selected_datalogs
      .map((data) => `"${data.file_name}"`)
      .join();
    filtersCopy[key].show = "production_data";
    filtersCopy[key].row_group = "datalog";
    setFilters(filtersCopy);
    Helper.navigatePage(key, queryClient);
  };

  return (
    <PagesLayout>
      <div
        ref={pageRef}
        className={shouldFillHeight ? "flex flex-col h-full" : ""}
      >
        {templateType === "create_recipe" ? (
          <RecipeTemplate
            templateData={templateData.template_data}
            urlParams={urlParams}
            pageKey={pageKey}
          />
        ) : (
          <div className={shouldFillHeight ? "flex flex-col h-full" : ""}>
            {templateData.title && (
              <Title level={3} className="mb-0!">
                {templateData.title}
              </Title>
            )}
            {templateRows.map((templateRow, index) => {
              const {
                columns,
                title,
                info_message,
                options,
                classname,
                prerender_process,
                postrender_process,
                required_data,
              } = templateRow;
              const rowKey = `${pageKey}_row_${index}`;
              return (
                <TemplateRow
                  key={rowKey}
                  templateRowKey={rowKey}
                  templateRow={templateRow}
                  title={title}
                  infoMessage={info_message}
                  rowOptions={options}
                  rowColumns={columns}
                  cellActions={cellActions}
                  pageKey={pageKey}
                  filters={filters}
                  setFilters={setFilters}
                  shouldFillHeight={shouldFillHeight}
                  classname={classname}
                  prerenderProcess={prerender_process}
                  postrenderProcess={postrender_process}
                  requiredData={required_data}
                />
              );
            })}
            {templateData.key === "recipes" &&
              pageKey === currentPageData.key && (
                <>
                  <NpiReportsModal
                    isModalOpen={isNpiReportsModalOpen}
                    setIsModalOpen={setIsNpiReportsModalOpen}
                    activeRecipeData={activeRecipeData}
                  />
                  <Modal
                    key="recipe_version_history_modal"
                    width="50%"
                    open={isRecipeVersionHistoryModalOpen}
                    title="View Version History"
                    onCancel={() => setIsRecipeVersionHistoryModalOpen(false)}
                    destroyOnClose
                    footer={[
                      <Button
                        key="close"
                        onClick={() =>
                          setIsRecipeVersionHistoryModalOpen(false)
                        }
                      >
                        Close
                      </Button>,
                    ]}
                  >
                    <RecipeInfo
                      recipeInfo={[
                        {
                          label: "Recipe Name",
                          value: activeRecipeData[pageKey]?.recipe_name,
                        },
                        {
                          label: "Recipe Type",
                          value: activeRecipeData[pageKey]?.recipe_category,
                        },
                        {
                          label: "Version Updated By",
                          value: activeRecipeData[pageKey]?.updated_by,
                        },
                        {
                          label: "Date Updated",
                          value: activeRecipeData[pageKey]?.date_created,
                        },
                        {
                          label: "Latest  Version Number",
                          value: activeRecipeData[pageKey]?.version,
                        },
                      ]}
                    />
                    <Row className="min-h-80">
                      <Col span={24}>
                        <RecipeVersionHistoryGrid pageKey={pageKey} />
                      </Col>
                    </Row>
                  </Modal>
                  <Modal
                    key="recipe_selected_datalogs_modal"
                    open={isRecipeSelectedDatalogsModalOpen}
                    title="View Selected Datalogs"
                    okText="View in Homepage"
                    cancelText="Close"
                    okButtonProps={{ icon: <LoginOutlined /> }}
                    onCancel={() => setIsRecipeSelectedDatalogsModalOpen(false)}
                    onOk={() => viewRecipeDatalogsInSearchPage()}
                    destroyOnClose
                  >
                    <RecipeSelectedDatalogsList pageKey={pageKey} />
                  </Modal>
                  <Modal
                    key="recipe_owner_notes_modal"
                    open={isRecipeOwnerNotesModalOpen}
                    title="Notes from the Creator"
                    onCancel={() => setIsRecipeOwnerNotesModalOpen(false)}
                    destroyOnClose
                    footer={[
                      <Button
                        key="close"
                        onClick={() => setIsRecipeOwnerNotesModalOpen(false)}
                      >
                        Close
                      </Button>,
                    ]}
                  >
                    <RecipeOwnerNotes pageKey={pageKey} />
                  </Modal>
                </>
              )}
            <LoadConsolidationRecipe pageKey={pageKey} />
          </div>
        )}
      </div>
    </PagesLayout>
  );
}
