import { Card, Col, Collapse, Empty, Tabs, message } from "antd";
import React, { useEffect, useRef, useState } from "react";
import Helper from "../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../src/store/store";
import { TemplateRow } from "./template_row";

/**
 * Column component of a template row
 *
 * @param {string} templateRowKey
 * @param {string} colKey
 * @param {int} colspan
 * @param {object} component
 * @param {object} container
 * @param {array} rows
 * @param {object} requiredData
 * @param {object} tabs
 * @param {array} tabItems
 * @param {function} setTabItems
 * @param {function} setSourceTab
 * @param {function} setActiveTabKey
 * @param {object} cellActions
 * @param {string} pageKey
 * @param {function} testStatsInfoHandler
 * @param {object} filters
 * @param {function} setFilters
 * @param {object} prerenderData
 * @param {function} setPrerenderData
 * @param {function} setShouldGenerateRow
 * @returns {JSX.Element}
 */
export const RowColumn = ({
  templateRowKey,
  colKey,
  colspan,
  component,
  container,
  rows,
  requiredData,
  tabs,
  tabItems,
  activeTabKey,
  setTabItems,
  setSourceTab,
  setActiveTabKey,
  cellActions,
  pageKey,
  testStatsInfoHandler,
  filters,
  setFilters,
  prerenderData = {},
  setPrerenderData,
  setShouldGenerateRow,
}) => {
  const [isChartBoosted, setIsChartBoosted] = useState(false);
  const [hasRow, setHasRow] = useState(true);
  const [shouldRenderComponent, setShouldRenderComponent] = useState({
    render: !(
      requiredData?.key &&
      (prerenderData?.[requiredData.key] === false ||
        (requiredData.should_not_empty === true &&
          (!Array.isArray(prerenderData?.[requiredData.key]) ||
            prerenderData?.[requiredData.key].length === 0)))
    ),
    version: 0,
    queued: false,
  });
  const pageMeta = useBoundStore((state) => state.pageMeta);
  const registerRenderComponentAction = useBoundStore(
    (state) => state.registerRenderComponentAction,
  );
  const setActiveTestNumber = useBoundStore(
    (state) => state.setActiveTestNumber,
  );
  const setTabsData = useBoundStore((state) => state.setTabsData);
  const tabsData = useBoundStore((state) => state.tabsData);
  const urlParams = useBoundStore((state) => state.urlParams);
  const updatePageTabs = useBoundStore((state) => state.updatePageTabs);
  const [messageApi, contextHolder] = message.useMessage();
  const tabsRef = useRef();

  useEffect(() => {
    if (tabsData[pageKey] === undefined) {
      tabsData[pageKey] = [];
    }
    if (tabItems.length > 0) {
      tabsData[pageKey].push({
        items: tabItems,
        setActiveTabKey: setActiveTabKey,
        activeTabKey: activeTabKey,
      });
    }
    setTabsData(tabsData);

    if (container?.key) {
      const containerKey = Helper.generateTabKey(container.key, prerenderData);
      updatePageTabs(containerKey, {
        items: tabItems,
        setItems: setTabItems,
        setActiveTabKey: setActiveTabKey,
        activeTabKey: activeTabKey,
        tabsRef: tabsRef,
      });
    }
  }, [tabItems]);

  useEffect(() => {
    if (tabs) {
      createTabItems();
    }
  }, []);

  useEffect(() => {
    setHasRow(!rows || (rows && Object.keys(rows).length > 0));
  }, [rows]);

  /**
   * Get container title based on analysis type
   *
   * @param {object} container
   * @returns {string} title
   */
  const getContainerTitle = (container) => {
    let title = container.title ?? null;
    if (
      pageMeta.analysis_type &&
      container.title_by_type &&
      container.title_by_type[pageMeta.analysis_type]
    ) {
      title = container.title_by_type[pageMeta.analysis_type];
    } else if (
      urlParams[pageKey] &&
      urlParams[pageKey].mfg_process &&
      container.title_by_mfg_process &&
      container.title_by_mfg_process[urlParams[pageKey].mfg_process]
    ) {
      title = container.title_by_mfg_process[urlParams[pageKey].mfg_process];
    }

    return title;
  };

  /**
   * Create tab items
   */
  const createTabItems = () => {
    const items = [];
    Object.keys(tabs).forEach((tabKey, index) => {
      let tabItemKey = tabs[tabKey].tab_key;
      let tabItemLabel = tabs[tabKey].label;
      // replace tab label with actual value if available
      if (typeof prerenderData === "object") {
        tabItemKey = Helper.generateTabKey(tabs[tabKey].tab_key, prerenderData);
        tabItemLabel = Helper.generateTabLabel(
          tabs[tabKey].label,
          tabs[tabKey].label_tooltip,
          prerenderData,
        );
      }
      if (index === 0) {
        setSourceTab({
          [tabKey]: Helper.cloneObject(tabs[tabKey]),
        });
        setActiveTabKey(tabItemKey);
      }
      if (tabs[tabKey].is_hidden !== true) {
        const tabContent = createTabContent(tabs, tabKey);
        items.push(
          Helper.createTabItem(
            tabItemKey,
            tabItemLabel,
            tabContent,
            tabs[tabKey].close_icon,
          ),
        );
      }
    });
    setTabItems(items);
  };

  /**
   * Create tab content
   *
   * @param {object} tabs
   * @param {string} tabKey
   * @returns {array} tabContent
   */
  const createTabContent = (tabs, tabKey) => {
    const tabComponent = tabs[tabKey].component
      ? Helper.createComponent(
          tabs[tabKey].component,
          cellActions,
          pageKey,
          testStatsInfoHandler,
          filters,
          setFilters,
          isChartBoosted,
          setIsChartBoosted,
          prerenderData,
          requiredData,
        )
      : null;

    const tabRows = tabs[tabKey].rows
      ? tabs[tabKey].rows.map((tabRow, index) => {
          const {
            columns,
            title,
            info_message,
            options,
            classname,
            prerender_process,
            postrender_process,
            required_data,
          } = tabRow;
          const rowKey = `${templateRowKey}_tab_${tabKey}_row_${index}`;
          return (
            <TemplateRow
              key={rowKey}
              templateRowKey={rowKey}
              templateRow={tabRow}
              title={title}
              infoMessage={info_message}
              rowOptions={options}
              rowColumns={columns}
              cellActions={cellActions}
              pageKey={pageKey}
              filters={filters}
              setFilters={setFilters}
              classname={classname}
              prerenderProcess={prerender_process}
              postrenderProcess={postrender_process}
              prerenderData={prerenderData}
              setPrerenderData={setPrerenderData}
              requiredData={required_data}
            />
          );
        })
      : null;

    const tabContent = [tabComponent, tabRows];

    return tabContent;
  };

  /**
   * Get component element based on render conditions
   *
   * @param {object} component
   * @returns {JSX.Element} element
   */
  const getComponentElement = (component) => {
    let element = null;
    if (shouldRenderComponent.queued === true) {
      element = Helper.showLoading(component);
    } else if (shouldRenderComponent.render === true) {
      element = Helper.createComponent(
        component,
        cellActions,
        pageKey,
        testStatsInfoHandler,
        filters,
        setFilters,
        isChartBoosted,
        setIsChartBoosted,
        prerenderData,
        requiredData,
        shouldRenderComponent,
      );
    } else {
      // check for queue process
      Helper.checkQueueProcess(
        Helper.getQueueGroup(component),
        requiredData?.render_key
          ? Helper.generateTabKey(requiredData.render_key, prerenderData)
          : component.name,
      );
      element = <Empty description={requiredData.error_msg}></Empty>;
    }

    return element;
  };

  /**
   * Generate column content
   *
   * @returns {JSX.Element}
   */
  const createColumnContent = () => {
    if (component?.props?.params?.body_params && prerenderData) {
      Object.keys(prerenderData).forEach((key) => {
        component.props.params.body_params[key] = prerenderData[key];
      });
    }

    if (component && requiredData?.render_key) {
      registerRenderComponentAction(
        Helper.generateTabKey(requiredData.render_key, prerenderData),
        setShouldRenderComponent,
      );
    }

    return (
      <>
        {component && (
          <div id={`boosted_warning_msg_wrapper_${component.id}`}></div>
        )}
        {(component || rows) && (
          <div
            className="h-full p-2 m-auto"
            style={{
              width: container
                ? container.content_width_by_colspan &&
                  container.content_width_by_colspan[colspan]
                  ? container.content_width_by_colspan[colspan]
                  : container.content_width
                    ? container.content_width
                    : "100%"
                : "100%",
            }}
          >
            {component && getComponentElement(component)}
            {rows &&
              rows.map((columnRow, index) => {
                let row = "";
                if (columnRow) {
                  const {
                    columns,
                    title,
                    info_message,
                    options,
                    classname,
                    prerender_process,
                    postrender_process,
                    required_data,
                  } = columnRow;
                  const rowKey = `${templateRowKey}_column_row_${index}`;
                  row = (
                    <TemplateRow
                      key={rowKey}
                      templateRowKey={rowKey}
                      templateRow={columnRow}
                      parentRows={rows}
                      parentRowIndex={index}
                      title={title}
                      infoMessage={info_message}
                      rowOptions={options}
                      rowColumns={columns}
                      cellActions={cellActions}
                      pageKey={pageKey}
                      filters={filters}
                      setFilters={setFilters}
                      classname={classname}
                      prerenderProcess={prerender_process}
                      postrenderProcess={postrender_process}
                      prerenderData={prerenderData}
                      setPrerenderData={setPrerenderData}
                      requiredData={required_data}
                    />
                  );
                }
                return row;
              })}
          </div>
        )}
      </>
    );
  };

  /**
   * Trigger when changing tab
   *
   * @param {string} activeKey
   */
  const onTabChange = (activeKey) => {
    setActiveTabKey(activeKey);
    setActiveTestNumber(activeKey.split("|")[1] ?? "topFailingTest");
  };

  /**
   * Trigger when adding or removing a tab
   *
   * @param {string} targetKey
   * @param {string} action
   */
  const onTabEdit = (targetKey, action) => {
    if (action === "remove") {
      if (tabItems.length > 1 || container.min === 0) {
        Helper.removeTabs(
          [targetKey],
          activeTabKey,
          tabItems,
          setTabItems,
          setActiveTabKey,
        );
      } else {
        messageApi.warning("Cannot remove all tabs.");
      }
    }
  };

  /**
   * Generate tabs
   *
   * @returns {JSX.Element}
   */
  const createTabs = () => {
    return (
      tabItems.length > 0 && (
        <Tabs
          ref={tabsRef}
          type="editable-card"
          className={`${tabItems.length === 0 ? "hidden" : ""}`}
          hideAdd
          centered={container.centered ?? false}
          tabBarStyle={container.hidden ? { display: "none" } : {}}
          items={tabItems}
          activeKey={activeTabKey}
          onChange={onTabChange}
          onEdit={onTabEdit}
        />
      )
    );
  };

  /**
   * Generate content with container if defined
   *
   * @returns {JSX.Element}
   */
  const createContent = () => {
    return container && container.type === "collapse" ? (
      <Collapse
        defaultActiveKey={container.collapsed !== true ? ["1"] : []}
        items={[
          {
            key: "1",
            label: getContainerTitle(container),
            children: createColumnContent(),
          },
        ]}
      />
    ) : container && container.type === "card" ? (
      <Card
        title={getContainerTitle(container)}
        className={`h-full ${container.classname ?? ""}`}
        styles={{
          body: {
            height: container.content_height ?? "auto",
          },
        }}
      >
        {createColumnContent()}
      </Card>
    ) : container && container.type === "tabs" ? (
      createTabs()
    ) : (
      createColumnContent()
    );
  };

  /**
   * Switch to chart zonal type tab
   */
  const toggleZonalType = ({ target: { value } }) => {
    const tab = Helper.filterArrayAndFind(tabs, "trigger_key", value);
    if (tab?.tab_key) {
      setActiveTabKey(tab.tab_key);
    }
  };

  return (
    hasRow && (
      <Col className="group/column" key={colKey} span={colspan}>
        {contextHolder}
        {container?.parent_container?.type === "card" ? (
          <Card
            title={getContainerTitle(container.parent_container)}
            className="h-full"
            styles={{
              body: {
                height: container.parent_container.content_height ?? "auto",
              },
            }}
          >
            {container.parent_container.options &&
              Helper.createRowOptions(
                container.parent_container.options,
                pageKey,
                filters,
                setFilters,
                prerenderData,
                setPrerenderData,
                { toggleZonalType },
                setShouldGenerateRow,
              )}

            {createContent()}
          </Card>
        ) : (
          createContent()
        )}
      </Col>
    )
  );
};
