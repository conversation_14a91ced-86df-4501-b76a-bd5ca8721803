import { LoadingOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Empty, Flex, Row, Spin, Typography, message } from "antd";
import React, { useEffect, useRef, useState, useCallback } from "react";
import { concat, merge } from "lodash";
import { useQueryClient } from "@tanstack/react-query";
import Helper from "../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../src/store/store";
import Api from "../../../../../src/utils/api";
import { getAnalysisSetFilters } from "../../../../../src/utils/components/recipe_common/helpers";
import { OptionsList } from "../../../../../src/utils/forms/options_list";
import SimulatedItem from "../npi/simulated_item";
import { usePartIdTabs } from "../../../../../src/hooks/usePartIdTabs";
import { RowColumn } from "./row_column";

const { Title } = Typography;

/**
 * Page template row component
 *
 * @param {string} templateRowKey
 * @param {object} templateRow
 * @param {array} parentRows
 * @param {int} parentRowIndex
 * @param {string} title
 * @param {string} infoMessage
 * @param {array} rowOptions
 * @param {array} rowColumns
 * @param {object} cellActions
 * @param {string} pageKey
 * @param {object} filters
 * @param {function} setFilters
 * @param {boolean} shouldFillHeight
 * @param {string} classname
 * @param {function} prerenderProcess
 * @param {function} postrenderProcess
 * @param {object} prerenderData
 * @param {string} requiredData
 * @returns {JSX.Element}
 */
export const TemplateRow = ({
  templateRowKey,
  templateRow,
  parentRows,
  parentRowIndex,
  title,
  infoMessage,
  rowOptions,
  rowColumns,
  cellActions,
  pageKey,
  filters,
  setFilters,
  shouldFillHeight,
  classname = "",
  prerenderProcess,
  postrenderProcess,
  prerenderData = {},
  requiredData,
}) => {
  const [tabItems, setTabItems] = useState([]);
  const [activeTabKey, setActiveTabKey] = useState();
  const [sourceTab, setSourceTab] = useState();
  const [shouldGenerateRow, setShouldGenerateRow] = useState({
    render: false,
    version: 0,
    queued: false,
  });
  const [loading, setLoading] = useState(false);
  const [hasColumn, setHasColumn] = useState(true);
  const [columnTabs, setColumnTabs] = useState();
  const [rowPrerenderData, setRowPrerenderData] = useState(prerenderData);
  const pageMeta = useBoundStore((state) => state.pageMeta);
  const testStatsInfo = useBoundStore((state) => state.testStatsInfo);
  const setTestStatsInfo = useBoundStore((state) => state.setTestStatsInfo);
  const urlParams = useBoundStore((state) => state.urlParams);
  const cacheData = useBoundStore((state) => state.cacheData);
  const presetAnalysisTemplates = useBoundStore(
    (state) => state.presetAnalysisTemplates,
  );
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const gridComponentRefs = useBoundStore((state) => state.gridComponentRefs);
  const chartKeys = useBoundStore((state) => state.chartKeys);
  const gridIds = useBoundStore((state) => state.gridIds);
  const registerRenderComponentAction = useBoundStore(
    (state) => state.registerRenderComponentAction,
  );
  const [messageApi, contextHolder] = message.useMessage();
  const queryClient = useQueryClient();
  // must use reference here to fix issue where old value (unfiltered by render conditions) is still rendered when revisiting the page
  const rowColumnsRef = useRef(rowColumns);

  useEffect(() => {
    if (
      prerenderProcess &&
      typeof prerenderProcesses[prerenderProcess] === "function"
    ) {
      setLoading(true);
      prerenderProcesses[prerenderProcess](initRow);
    } else {
      initRow(rowPrerenderData);
    }
  }, []);

  /**
   * Get lot top failing test data
   *
   * @param {function} successCbk
   */
  const getLotTopFailingTestData = (successCbk) => {
    Helper.getLotTopFailingTestData(
      urlParams[pageKey].src_type,
      urlParams[pageKey].src_value,
      urlParams[pageKey].mfg_process,
      successCbk,
      messageApi.warning,
      messageApi.error,
      cacheData,
    );
  };

  useEffect(() => {
    if (
      postrenderProcess &&
      postrenderProcess.dependency === "sourceTab" &&
      typeof postrenderProcesses[postrenderProcess.fn] === "function" &&
      sourceTab
    ) {
      postrenderProcesses[postrenderProcess.fn]();
    }
  }, [sourceTab]);

  /**
   * Set test data
   *
   * @param {function} successCbk
   * @param {object} prerenderData
   */
  const setTestData = (successCbk, prerenderData = {}) => {
    Helper.getTestData(
      {
        preserve_qmap_key: false,
        include_qmap_header: false,
        include_test_info: true,
        tnum: urlParams[pageKey].tnum,
        dsk: urlParams[pageKey].tnum_dsk,
        stats_type: "rp",
      },
      successCbk,
      messageApi.warning,
      messageApi.error,
      cacheData,
      prerenderData,
    );
  };

  /**
   * Set trend analysis data
   *
   * @param {function} successCbk
   */
  const setTrendAnalysisTestData = (successCbk) => {
    const prerenderData = Helper.cloneObject(rowPrerenderData);
    if (!urlParams[pageKey]?.group_by) {
      prerenderData.group_by = "file_name";
      prerenderData.group_by_label = "File Name";
    }

    Api.getDatalogInfo(
      (res) => {
        if (res.success) {
          const data = res.data;
          const uniqueSubcon = [...new Set(data.map((row) => row.subcon))].join(
            ",",
          );
          const uniqueProcId = [
            ...new Set(
              data.map((row) => row.proc_id).filter((value) => value !== ""),
            ),
          ].join(",");
          const uniquePartTyp = [
            ...new Set(data.map((row) => row.part_typ)),
          ].join(",");
          prerenderData.subcon = uniqueSubcon;
          prerenderData.part_typ = uniquePartTyp;
          prerenderData.proc_id = uniqueProcId;
          setTestData(successCbk, prerenderData);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        dsk: urlParams[pageKey].src_value,
      },
    );
  };

  /**
   * Set bin type
   *
   * @param {function} successCbk
   */
  const setBinType = (successCbk) => {
    successCbk({
      ...prerenderData,
      bin_type: "soft",
      bin_type_label: "Software",
    });
  };

  /**
   * Generate NPI report analysis set tabs
   */
  const generateAnalysisSetTabs = () => {
    Api.getNPIData(
      (res) => {
        if (res.success) {
          const filterNames = [
            "recipe_category",
            "dsk",
            "recipe_key",
            "recipe_name",
            "src_type",
            "src_value",
            "is_published",
          ];

          Object.values(
            res.data.report_options.npi_recipe_analysis_set_keys,
          ).forEach((set, index) => {
            const additionalFilters = {};
            filterNames.forEach((filter) => {
              if (res.data[filter] !== undefined) {
                additionalFilters[filter] = res.data[filter];
              }
            });
            const prerenderData = {
              ...{
                analysis_set_key: set.npi_recipe_analysis_set_key,
                analysis_set_name: set.analysis_set_name,
                test_summary_types: res.data.report_options.test_summary_types,
                analysis_set_data: set.analysis_set_data,
                simulated_charts: [
                  ...new Set(
                    set.analysis_set_data.flatMap((item) => item.charts || []),
                  ),
                ],
              },
              ...additionalFilters,
            };

            generateTabItem(undefined, prerenderData, index === 0);
          });
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      {
        report_key: urlParams[pageKey].report_key,
      },
    );
  };

  /**
   * Generate NPI report per test tabs
   */
  const generatePerTestTabs = () => {
    Api.getAnalysisSetTests(
      (res) => {
        if (res.success) {
          res.data.map((data, index) => {
            const tabPrerenderData = {
              ...prerenderData,
              ...{
                tNum: data.test_number,
                actual_test_number: data.actual_test_number,
                test_type: data.test_type,
                test_name: data.test_name,
                test_unique_id: data.test_unique,
                stats_type_display:
                  OptionsList.npi_stats_types.find(
                    (option) =>
                      option.value ===
                      prerenderData.analysis_set_data?.[0]?.stats_type,
                  )?.label ?? "",
              },
              ...getAnalysisSetFilters(
                prerenderData?.analysis_set_data?.[0] ?? {},
              ),
            };
            generateTabItem(undefined, tabPrerenderData, index === 0);
          });
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      { ...urlParams[pageKey], ...prerenderData },
    );
  };

  /**
   * Generate hardware tab
   */
  const generateHardwareTab = () => {
    generateTabItem(
      undefined,
      { ...prerenderData, bin_type: "hard", bin_type_label: "Hardware" },
      false,
    );
  };

  /**
   * Generate NPI per test tab content
   *
   * @param {arrray} tabContent
   * @param {object} prerenderData
   */
  const generateNPIPerTestTabContent = async (tabContent, prerenderData) => {
    if (rowPrerenderData.test) {
      const blueprints = rowPrerenderData.test.reduce((result, component) => {
        result[component.name] = component;
        return result;
      }, {});

      prerenderData.analysis_set_data.forEach((group, groupIndex) => {
        const componentNames = concat(group.tables, group.charts);
        const columns = [];
        componentNames.forEach((componentName, componentIndex) => {
          const component = Helper.cloneObject(blueprints[componentName]);
          if (component) {
            const componentId = `component_${groupIndex}_${componentIndex}_${component.name}_${prerenderData.test_unique_id}`;
            component.id = componentId;
            columns.push(
              <SimulatedItem
                key={`${componentId}_item`}
                colKey={`${componentId}_wrapper`}
                component={component}
                group={group}
                pageKey={pageKey}
                filters={filters}
                setFilters={setFilters}
                prerenderData={prerenderData}
                requiredData={requiredData}
                cellActions={cellActions}
                testStatsInfoHandler={testStatsInfoHandler}
              />,
            );
          }
        });

        tabContent.push(
          <Row
            key={`row_${prerenderData.test_unique_id}_${groupIndex}`}
            className="mb-4"
            gutter={[16, 16]}
          >
            {columns}
          </Row>,
        );
      });
    }
  };

  /**
   * Set mpr map data
   *
   * @param {function} successCbk
   */
  const setMPRMapData = (successCbk) => {
    Api.getPerPinMapData(
      (res) => {
        if (res.success) {
          successCbk(res.data);
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      {
        tnum: urlParams[pageKey].tnum,
        dsk: urlParams[pageKey].tnum_dsk,
      },
    );
  };

  /**
   * Get component blueprints of test data level
   *
   * @param {function} successCbk
   */
  const getTestComponentBlueprints = async (successCbk) => {
    const res = await Api.fetchData(
      `/api/v1/internal/blueprint/analysis_component_props/test`,
      "get",
      {},
    );
    if (res.success) {
      successCbk({ ...prerenderData, ...res.data });
    } else {
      messageApi.warning(res.message, 5);
    }
  };

  /**
   * Check for recipe data and get if not present
   *
   * @param {function} successCbk
   */
  const checkForRecipeData = async (successCbk) => {
    if (
      !urlParams[pageKey]?.recipe_category &&
      urlParams[pageKey]?.report_key
    ) {
      const recipeData = await Helper.getRecipeDataByReportKey(
        urlParams[pageKey].report_key,
        messageApi,
      );
      merge(urlParams[pageKey], recipeData);
      successCbk({ ...prerenderData, ...recipeData });
    } else {
      successCbk(rowPrerenderData);
    }
  };

  /**
   * Action mapper for prerender process
   */
  const prerenderProcesses = {
    getLotTopFailingTestData,
    setTestData,
    setMPRMapData,
    getTestComponentBlueprints,
    checkForRecipeData,
    setTrendAnalysisTestData,
    setBinType,
  };

  /**
   * Action mapper for postrender process
   */
  const postrenderProcesses = {
    generateAnalysisSetTabs,
    generatePerTestTabs,
    generateHardwareTab,
  };

  /**
   * Action mapper for processes to generate content dynamically
   */
  const contentProcesses = {
    generateNPIPerTestTabContent,
  };

  /**
   * Initialize row
   *
   * @param {object} data
   */
  const initRow = (data) => {
    if (data) {
      setRowPrerenderData(data);
    }

    const testSummaryTypes = data?.test_summary_types;
    const simulatedCharts = data?.simulated_charts;
    const testType = data?.test_type;

    filterTemplateColumnsByRenderConditions(
      templateRow.columns,
      pageMeta,
      testType,
      testSummaryTypes,
      simulatedCharts,
      parentRows,
      parentRowIndex,
    );

    // set tabs
    rowColumnsRef.current.forEach((column) => {
      if (column && column.tabs) {
        setColumnTabs(column.tabs);
      }
    });

    const meetDataExistence = requiredData && data[requiredData.key];
    const meetEmptinessRequirement =
      requiredData &&
      (requiredData.should_not_empty !== true ||
        (Array.isArray(data[requiredData.key]) &&
          data[requiredData.key].length > 0));
    setShouldGenerateRow((prev) => ({
      render: !requiredData || (meetDataExistence && meetEmptinessRequirement),
      version: prev.version + 1,
      queued: false,
    }));

    setLoading(false);

    if (requiredData?.render_key) {
      registerRenderComponentAction(
        requiredData.render_key,
        setShouldGenerateRow,
      );
    }
  };

  /**
   * Filter template row columns by render conditions based on analysis type and test type
   *
   * @param {array} columns
   * @param {object} pageMeta
   * @param {string} testType
   * @param {array} testSummaryTypes
   * @param {array} simulatedCharts
   * @param {array} parentRows
   * @param {int} parentRowIndex
   */
  const filterTemplateColumnsByRenderConditions = (
    columns,
    pageMeta,
    testType,
    testSummaryTypes,
    simulatedCharts,
    parentRows,
    parentRowIndex,
  ) => {
    const totalColspan = 24;
    const columnCount = Object.keys(columns).length;

    columns.forEach((column, columnKey) => {
      if (!column) {
        return;
      }
      if (column.render_conditions !== undefined) {
        Helper.filterByRenderConditions(
          column.render_conditions,
          column.invert_render_conditions,
          {
            analysis_types: pageMeta.analysis_type,
            test_types: testType,
            has_xy: pageMeta.has_xy,
            test_summary_types: testSummaryTypes,
            simulated_charts: simulatedCharts,
            mfg_process: urlParams[pageKey].mfg_process,
            recipe_categories: urlParams[pageKey].recipe_category,
            group_by:
              rowPrerenderData.group_by ?? urlParams[pageKey].group_by ?? "",
          },
          columns,
          columnKey,
          parentRows,
          parentRowIndex,
        );
      }
      if (
        column.component &&
        column.component.render_conditions !== undefined
      ) {
        Helper.filterByRenderConditions(
          column.component.render_conditions,
          column.component.invert_render_conditions,
          {
            analysis_types: pageMeta.analysis_type,
            test_types: testType,
            has_xy: pageMeta.has_xy,
            test_summary_types: testSummaryTypes,
            simulated_charts: simulatedCharts,
            mfg_process: urlParams[pageKey].mfg_process,
            group_by:
              rowPrerenderData.group_by ?? urlParams[pageKey].group_by ?? "",
          },
          columns,
          columnKey,
          parentRows,
          parentRowIndex,
        );
      }
    });

    // adjust column span if a column has been removed
    if (columnCount !== Object.keys(columns).length) {
      const colspan = totalColspan / Object.keys(columns).length;
      columns.forEach((column) => {
        if (column.colspan !== undefined) {
          column.colspan = colspan;
        }
      });
    }
    setHasColumn(Object.keys(columns).length > 0);
  };

  /**
   * Handles the test stats info that will be used for the charts relying on the results
   *
   * @param {object} data
   * @param {string} testNumber
   */
  const testStatsInfoHandler = (data, testNumber) => {
    let stats = {};
    stats[testNumber] = {};
    [
      "lo_lim",
      "hi_lim",
      "lo_lim_valid",
      "hi_lim_valid",
      "iqr_lo_lim",
      "iqr_hi_lim",
      "min_result",
      "max_result",
      "min_scale",
      "max_scale",
      "mean",
    ].forEach((name) => {
      if (typeof data[name] !== "undefined") {
        stats[testNumber][name] = data[name];
      }
    });
    setTestStatsInfo(merge(testStatsInfo, stats));
  };

  /**
   * Generate new test tab and generate test analysis
   *
   * @param {object} values
   * @param {boolean} shouldSetAsActive
   */
  const analyseTest = (values, shouldSetAsActive = true) => {
    const testKey = values.tnum.value;
    const testData = values.tnum.value.split("|");
    const bodyParams = { test_number: testData[1] };
    const prerenderData = Helper.cloneObject(rowPrerenderData);
    prerenderData.actual_test_number = testData[4];
    prerenderData.test_key = testKey;
    prerenderData.tnum = prerenderData.test_number = testKey.split("|")[1];
    prerenderData.tnum_dsk = testKey.split("|")[3];
    prerenderData.test_type = testKey.split("|")[2];
    prerenderData.test_name = prerenderData.tname = values.tnum.label;
    prerenderData.stats_type = values.stats_type;
    prerenderData.iqr_n = values.iqr_n;
    prerenderData.per_site = values.per_site;
    prerenderData.histogram_y_axis_as_percent =
      values.histogram_y_axis_as_percent;
    prerenderData.site = Array.isArray(values.site)
      ? values.site.join()
      : values.site === undefined
        ? "255"
        : values.site.toString();

    if (values.group_by && !values.group_by_disabled) {
      if (typeof values.group_by === "object") {
        prerenderData.group_by = values.group_by.value;
        prerenderData.group_by_label = values.group_by.label;
      } else {
        prerenderData.group_by = values.group_b;
      }
    } else {
      delete prerenderData.group_by;
    }

    if (
      Array.isArray(values.part_id_exclude) &&
      values.part_id_exclude.length > 0
    ) {
      prerenderData.part_id_exclude = values.part_id_exclude.join();
    } else {
      delete prerenderData.part_id_exclude;
    }

    if (Array.isArray(values.analyse_bins) && values.analyse_bins.length > 0) {
      prerenderData.analyse_bins = values.analyse_bins.join();
    } else {
      delete prerenderData.analyse_bins;
    }

    if (
      prerenderData.stats_type === "custom" ||
      prerenderData.stats_type === "custom_all"
    ) {
      if (values.custom_limit.min !== undefined) {
        prerenderData.custom_lo_lim = values.custom_limit.min;
      }
      if (values.custom_limit.max !== undefined) {
        prerenderData.custom_hi_lim = values.custom_limit.max;
      }
      if (values.custom_range.min !== undefined) {
        prerenderData.custom_range_min = values.custom_range.min;
      }
      if (values.custom_range.max !== undefined) {
        prerenderData.custom_range_max = values.custom_range.max;
      }
    } else {
      delete prerenderData.custom_lo_lim;
      delete prerenderData.custom_hi_lim;
      delete prerenderData.custom_range_min;
      delete prerenderData.custom_range_max;
    }

    if (values.show_original_limits === true) {
      prerenderData.show_original_limits = values.show_original_limits;
    } else {
      delete prerenderData.show_original_limits;
    }

    if (values.datalogged_parts) {
      prerenderData.datalogged_parts = `${values.datalogged_parts},`;
    }
    prerenderData.sort_by = values.sort_by;
    prerenderData.sort_dir = values.sort_dir;
    prerenderData.start_date = values.start_date
      ?.map((d) => d.format("YYYY-MM-DD"))
      .join(",");
    prerenderData.simulated_charts = values.simulated_charts;
    generateTabItem(bodyParams, prerenderData, shouldSetAsActive);
  };

  /**
   * Generate new tab items
   *
   * @param {object} bodyParams
   * @param {object} prerenderData
   * @param {boolean} shouldSetAsActive
   */
  const generateTabItem = useCallback(
    async (bodyParams, prerenderData, shouldSetAsActive = true) => {
      let sourceTabIndex = Object.keys(sourceTab || {})[0];
      let sourceTabKey = sourceTab?.[sourceTabIndex]?.tab_key;
      if (!sourceTabIndex) {
        // create new tab item and set it as source tab for adding new tabs
        let newSourceTab;
        templateRow.columns.forEach((column) => {
          if (column.tabs && Object.keys(column.tabs).length > 0) {
            newSourceTab = column.tabs;
          }
        });
        if (newSourceTab) {
          if (Object.keys(newSourceTab).length > 0) {
            sourceTabIndex = Object.keys(newSourceTab)[0];
            sourceTabKey = newSourceTab[0]?.tab_key;
          }
          // Keep original tab configuration as source; avoid mutating it later
          setSourceTab(Helper.cloneObject(newSourceTab));
        }

        if (
          newSourceTab &&
          newSourceTab[sourceTabIndex] &&
          newSourceTab[sourceTabIndex].rows
        ) {
          const baseTab = Helper.cloneObject(newSourceTab[sourceTabIndex]);
          const newTabKey = Helper.generateTabKey(
            baseTab.tab_key,
            prerenderData,
          );
          const newTabLabel = Helper.generateTabLabel(
            baseTab.label,
            baseTab.label_tooltip,
            prerenderData,
          );
          let newTabRows = [];
          baseTab.rows.map((tabRow) => {
            let newTabRow = Helper.cloneObject(tabRow);
            const { columns } = newTabRow;
            columns.forEach((column) => {
              if (!column) return;
              if (column.component) {
                column.component = copyComponent(
                  column.component,
                  sourceTabKey,
                  newTabKey,
                  bodyParams,
                );
              }
              if (column.rows) {
                column.rows.forEach((row) => {
                  if (!row) {
                    return;
                  }
                  const { columns } = row;
                  columns.forEach((rowColumn) => {
                    if (rowColumn && rowColumn.component) {
                      rowColumn.component = copyComponent(
                        rowColumn.component,
                        sourceTabKey,
                        newTabKey,
                        bodyParams,
                      );
                    }
                  });
                });
              }
            });

            newTabRows.push(newTabRow);
          });

          // Build new tab instance without mutating sourceTab
          const newColumnTabs = {
            [newTabKey]: {
              ...baseTab,
              label: newTabLabel,
              rows: newTabRows,
            },
          };
          setColumnTabs(newColumnTabs);

          setActiveTabKey(newTabKey);
          setShouldGenerateRow((prev) => ({
            render: true,
            version: prev.version + 1,
            queued: false,
          }));
        }
      } else if (sourceTab[sourceTabIndex]) {
        // Assign tab props if preset is present else assign the soure tab props
        const newTab = structuredClone(
          templateRow.columns
            .flatMap((column) => column.tabs || [])
            .find((tab) => tab.tab_key === bodyParams?.tab_key) ??
            sourceTab[sourceTabIndex],
        );
        sourceTabKey = newTab?.tab_key;

        const newTabKey = Helper.generateTabKey(newTab.tab_key, prerenderData);
        const newTabLabel = Helper.generateTabLabel(
          newTab.label,
          newTab.label_tooltip,
          prerenderData,
        );
        // create new tab from source tab, set as active tab if existing
        const exists = Object.keys(tabItems).some(function (key) {
          return tabItems[key].key === newTabKey;
        });
        if (!exists) {
          let tabContent = [];

          if (newTab.component) {
            const newTabComponent = copyComponent(
              newTab.component,
              sourceTabKey,
              newTabKey,
              bodyParams,
            );
            tabContent.push(
              Helper.createComponent(
                newTabComponent,
                cellActions,
                pageKey,
                testStatsInfoHandler,
                filters,
                setFilters,
                false,
                undefined,
                prerenderData,
                requiredData,
              ),
            );
          }

          if (newTab.rows) {
            const tabRows = newTab.rows.map((tabRow, tabRowIndex) => {
              let rowKey = `${templateRowKey}_tab_${newTabKey}_row_${tabRowIndex}`;
              let newTabRow = Helper.cloneObject(tabRow);
              const {
                columns,
                title,
                info_message,
                options,
                classname,
                prerender_process,
                postrender_process,
                required_data,
              } = newTabRow;
              columns.forEach((column) => {
                if (!column) return;
                if (column.component) {
                  column.component = copyComponent(
                    column.component,
                    sourceTabKey,
                    newTabKey,
                    bodyParams,
                  );
                }
                if (column.rows) {
                  column.rows.forEach((row, rowIndex) => {
                    if (!row) {
                      return;
                    }
                    rowKey = `${templateRowKey}_tab_${newTabKey}_row_${tabRowIndex}_row_${rowIndex}`;
                    const { columns } = row;
                    columns.forEach((rowColumn) => {
                      if (rowColumn && rowColumn.component) {
                        rowColumn.component = copyComponent(
                          rowColumn.component,
                          sourceTabKey,
                          newTabKey,
                          bodyParams,
                        );
                      }
                    });
                  });
                }
              });

              return (
                <TemplateRow
                  key={rowKey}
                  templateRowKey={rowKey}
                  templateRow={tabRow}
                  parentRows={newTab.rows}
                  parentRowIndex={tabRowIndex}
                  title={title}
                  infoMessage={info_message}
                  rowOptions={options}
                  rowColumns={columns}
                  cellActions={cellActions}
                  pageKey={pageKey}
                  filters={filters}
                  setFilters={setFilters}
                  classname={classname}
                  prerenderProcess={prerender_process}
                  postrenderProcess={postrender_process}
                  prerenderData={prerenderData}
                  requiredData={required_data}
                />
              );
            });

            tabContent.push(tabRows);
          }

          // this is used to generate content dynamically
          if (
            newTab.content_process &&
            typeof contentProcesses[newTab.content_process] === "function"
          ) {
            await contentProcesses[newTab.content_process](
              tabContent,
              prerenderData,
            );
          }

          setTabItems((prevItems) => [
            ...prevItems,
            Helper.createTabItem(
              newTabKey,
              newTabLabel,
              tabContent,
              newTab?.close_icon ?? sourceTab[sourceTabIndex].close_icon,
            ),
          ]);
        }
        if (shouldSetAsActive) {
          setActiveTabKey(newTabKey);
        }
      }
    },
    [
      sourceTab,
      templateRow,
      tabItems,
      cellActions,
      pageKey,
      filters,
      setFilters,
      requiredData,
      contentProcesses,
      templateRowKey,
    ],
  );

  usePartIdTabs(rowPrerenderData, generateTabItem);

  /**
   * Generate new tab and generate more wafer chart bin patterns
   *
   * @param {object} values
   */
  const plotBinPatterns = (values) => {
    // For tab label
    const prerenderData = Helper.cloneObject(rowPrerenderData);
    if (values?.bin_number) {
      prerenderData.bin_tab_key = values.bin_number;
    }
    if (values?.site_number) {
      prerenderData.site_tab_key = values.site_number;
    }

    // For chart endpoint filter
    prerenderData.bin_numbers = values.bin_number;
    prerenderData.site = values.site_number;
    prerenderData.site_colors = values.site_colors;
    prerenderData.tab_key = values.tab_key;

    prerenderData.component_name = values.component_name;
    prerenderData.group_name = filters[pageKey].group_by_label;

    generateTabItem(values, prerenderData);
  };

  /**
   * Generate new tab and generate per group wafer charts
   *
   * @param {object} values
   */
  const plotPerGroupWaferCharts = (values) => {
    const prerenderData = Helper.cloneObject(rowPrerenderData);
    prerenderData.component_name = values.component_name;
    prerenderData.group_name = values.group_name;
    generateTabItem(values, prerenderData);
  };

  /**
   * Generate new tab and generate MPR XY wafer charts
   *
   * @param {object} values
   */
  const plotMPRXYWaferCharts = (values) => {
    rowPrerenderData.component_name = values.component_name;
    rowPrerenderData.group_name = values.group_name;
    generateTabItem(values);
  };

  /**
   * Reload trend analysis charts and table
   *
   * @param {object} values
   * @param {object} rowData
   */
  const reloadTrendAnalysisChartsTable = (values, rowData) => {
    rowPrerenderData.test_step = values.test_step;
    rowPrerenderData.run_type = values.run_type;
    rowPrerenderData.show_related = values.show_related;
    const trendBoxPlotChartKeys = chartKeys[pageKey][rowData.trend_box_plot];
    const trendLineChartKeys = chartKeys[pageKey][rowData.trend_line_chart];
    const trendCpCpkBoxPlotChartKeys =
      chartKeys[pageKey][rowData.trend_cp_cpk_box_plot];
    const trendCpCpkLineChartKeys =
      chartKeys[pageKey][rowData.trend_cp_cpk_line_chart];
    const testTrendGridId = gridIds[pageKey][rowData.test_trend_table];

    if (Array.isArray(trendBoxPlotChartKeys)) {
      trendBoxPlotChartKeys.forEach((chartKey) => {
        chartComponentRefs[chartKey]?.current?.reloadChart();
      });
    }
    if (Array.isArray(trendLineChartKeys)) {
      trendLineChartKeys.forEach((chartKey) => {
        chartComponentRefs[chartKey]?.current?.reloadChart();
      });
    }
    if (Array.isArray(trendCpCpkBoxPlotChartKeys)) {
      trendCpCpkBoxPlotChartKeys.forEach((chartKey) => {
        chartComponentRefs[chartKey]?.current?.reloadChart();
      });
    }
    if (Array.isArray(trendCpCpkLineChartKeys)) {
      trendCpCpkLineChartKeys.forEach((chartKey) => {
        chartComponentRefs[chartKey]?.current?.reloadChart();
      });
    }

    gridComponentRefs[testTrendGridId]?.current?.reloadGridData();
  };

  /**
   * Change grouping of analysis
   *
   * @param {object} values
   */
  const changeAnalysisGrouping = (values) => {
    const input = Helper.cloneObject(urlParams[pageKey]);
    if (values.group_by) {
      input.group_by = values.group_by.value;
      input.group_by_label = values.group_by.label;
    } else {
      delete input.group_by;
      delete input.group_by_label;
    }
    Helper.generateAnalysis(
      urlParams[pageKey].template_key,
      input,
      presetAnalysisTemplates,
      queryClient,
      messageApi,
    );
  };

  /**
   * Reload binning group pareto charts
   *
   * @param {object} values
   */
  const reloadGroupParetoCharts = (values) => {
    rowPrerenderData.site = values.site.map((item) => item.value).join();
    rowPrerenderData.show_bin_count = values.show_bin_count;

    if (values.group_by === "use_pass_bins") {
      delete rowPrerenderData.bin_numbers;
      rowPrerenderData.use_pass_bins = true;
      rowPrerenderData.use_top_failing_bins = false;
      rowPrerenderData.use_all_bins = false;
    } else if (values.group_by === "select_bins") {
      values.bin_numbers = values?.bin_numbers
        ?.map((item) => item.value)
        ?.join();

      rowPrerenderData.use_pass_bins = false;
      rowPrerenderData.use_top_failing_bins = false;
      if (values.bin_numbers === "all") {
        rowPrerenderData.use_all_bins = true;
        delete rowPrerenderData.bin_numbers;
      } else {
        rowPrerenderData.bin_numbers = values.bin_numbers;
        rowPrerenderData.use_all_bins = false;
      }
    } else {
      delete rowPrerenderData.bin_numbers;
      rowPrerenderData.use_pass_bins = false;
      rowPrerenderData.use_top_failing_bins = true;
      rowPrerenderData.use_all_bins = false;
      rowPrerenderData.top_n_pareto = values.top_n_pareto;
    }

    const groupedBinParetoChartKeys = chartKeys[pageKey]["grouped_bin_pareto"];
    if (Array.isArray(groupedBinParetoChartKeys)) {
      groupedBinParetoChartKeys.forEach((chartKey) => {
        chartComponentRefs[chartKey].current.reloadChart();
      });
    }
    const stackedNormalizeBinChartKeys =
      chartKeys[pageKey]["stacked_normalize_bin_chart"];
    if (Array.isArray(stackedNormalizeBinChartKeys)) {
      stackedNormalizeBinChartKeys.forEach((chartKey) => {
        chartComponentRefs[chartKey].current.reloadChart();
      });
    }
  };

  /**
   * Update scatter chart based on set options
   *
   * @param {object} values
   * @param {object} rowData
   */
  const filterScatterChart = (values, rowData) => {
    const scatterChartKeys = chartKeys[pageKey][rowData.component_name];
    if (Array.isArray(scatterChartKeys)) {
      scatterChartKeys.forEach((chartKey) => {
        if (
          Array.isArray(values.pin_numbers) &&
          values.pin_numbers.length > 0
        ) {
          reloadChartFilters[chartKey].pin_index = values.pin_numbers.join(",");
        } else {
          delete reloadChartFilters[chartKey].pin_index;
        }
        if (values.x_axis_data) {
          reloadChartFilters[chartKey].x_axis_data = values.x_axis_data;
        } else {
          delete reloadChartFilters[chartKey].x_axis_data;
        }
        reloadChartFilters[chartKey].sort_by = values.sort_by;
        reloadChartFilters[chartKey].sort_dir = values.sort_dir;
        reloadChartFilters[chartKey].show_datalog_lines =
          values.show_datalog_lines;

        chartComponentRefs[chartKey].current?.reloadChart();
      });
    }
  };

  /**
   * Copy source tab component to be used in new tab
   *
   * @param {object} sourceComponent
   * @param {string} sourceTabKey
   * @param {string} newTabKey
   * @param {object} bodyParams
   * @returns {object} newTabComponent
   */
  const copyComponent = (
    sourceComponent,
    sourceTabKey,
    newTabKey,
    bodyParams,
  ) => {
    let newTabComponent = Helper.cloneObject(sourceComponent);
    const allFilters = { ...filters[pageKey], ...prerenderData };
    newTabComponent.id = Helper.generateLabelByData(
      newTabComponent.id.replace(sourceTabKey, newTabKey),
      allFilters,
    );
    newTabComponent.index = Helper.generateLabelByData(
      newTabComponent.index.replace(sourceTabKey, newTabKey),
      allFilters,
    );

    if (bodyParams && newTabComponent.props?.params?.body_params) {
      newTabComponent.props.params.body_params = {
        ...newTabComponent.props.params.body_params,
        ...bodyParams,
      };
    }

    return newTabComponent;
  };

  /**
   * Generate new trend tab and generate trend analysis
   *
   * @param {object} values
   */
  const plotTrendAnalysis = (values) => {
    values.tnum.forEach((test) => {
      analyseTest({ ...values, tnum: test });
    });
  };

  return (
    hasColumn && (
      <div
        className={`overflow-x-hidden group ${shouldFillHeight ? "flex flex-col h-full" : ""}`}
      >
        {contextHolder}
        {rowOptions && (
          <Flex>
            {Helper.createRowOptions(
              rowOptions,
              pageKey,
              filters,
              setFilters,
              rowPrerenderData,
              setRowPrerenderData,
              {
                analyseTest,
                plotBinPatterns,
                plotPerGroupWaferCharts,
                plotMPRXYWaferCharts,
                changeAnalysisGrouping,
                reloadGroupParetoCharts,
                filterScatterChart,
                plotTrendAnalysis,
                reloadTrendAnalysisChartsTable,
              },
              setShouldGenerateRow,
            )}
          </Flex>
        )}
        <Row
          className={`h-full ${classname} ${rowColumnsRef.current.length === 1 && !rowColumnsRef.current[0]?.rows && !rowColumnsRef.current[0]?.component ? "mt-1 mb-1" : "mt-2 mb-2"}`}
          gutter={8}
        >
          {title && (
            <Col span={24}>
              <Title level={5} className="w-full">
                {title}
              </Title>
            </Col>
          )}
          {infoMessage && (
            <Col span={24}>
              <Alert
                className="mb-2"
                message={
                  <div dangerouslySetInnerHTML={{ __html: infoMessage }} />
                }
                type="info"
                closable
              />
            </Col>
          )}
          {loading && (
            <Spin
              className="m-auto!"
              size="large"
              indicator={<LoadingOutlined spin />}
            />
          )}
          {shouldGenerateRow.render === true
            ? rowColumnsRef.current.map((column, key) => {
                return (
                  column && (
                    <RowColumn
                      key={`${templateRowKey}_column_${key}_${shouldGenerateRow.version ?? ""}`}
                      templateRowKey={templateRowKey}
                      colKey={key}
                      colspan={column.colspan}
                      component={column.component}
                      container={column.container}
                      rows={column.rows}
                      requiredData={column.required_data}
                      tabs={columnTabs}
                      tabItems={tabItems}
                      activeTabKey={activeTabKey}
                      sourceTab={sourceTab}
                      setSourceTab={setSourceTab}
                      setTabItems={setTabItems}
                      setActiveTabKey={setActiveTabKey}
                      cellActions={cellActions}
                      pageKey={pageKey}
                      testStatsInfoHandler={testStatsInfoHandler}
                      filters={filters}
                      setFilters={setFilters}
                      prerenderData={rowPrerenderData}
                      setPrerenderData={setRowPrerenderData}
                      setShouldGenerateRow={setShouldGenerateRow}
                    />
                  )
                );
              })
            : prerenderProcess &&
              !loading && (
                <Empty
                  className="m-auto!"
                  description={requiredData && requiredData.error_msg}
                ></Empty>
              )}
        </Row>
      </div>
    )
  );
};
