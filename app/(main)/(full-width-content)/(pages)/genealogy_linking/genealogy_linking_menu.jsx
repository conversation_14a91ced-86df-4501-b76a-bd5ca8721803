import { useState } from "react";
import { Dropdown, Space, Tooltip, Typography } from "antd";
import { DownOutlined } from "@ant-design/icons";
import GenealogyLinkModal from "./modals/genealogy_link_modal";

/*
 * Genealogy Linking Menu component
 *
 * @param {string} pageKey
 * @param {object} analysisInput
 * @returns {JSX.Element}
 * */
export default function GenealogyLinkingMenu({ pageKey, analysisInput }) {
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [isValid, setIsValid] = useState(false);

  return (
    <>
      <GenealogyLinkModal
        pageKey={pageKey}
        isLinkModalOpen={isLinkModalOpen}
        setIsLinkModalOpen={setIsLinkModalOpen}
      />
      <Dropdown
        trigger="click"
        menu={{
          items: [
            {
              key: "link_genealogy_lots",
              disabled: !isValid,
              label: isValid ? (
                "Link Genealogy Lots"
              ) : (
                <Tooltip title="Please select 2 or more datalogs">
                  Link Genealogy Lots
                </Tooltip>
              ),
              onClick: () => {
                setIsLinkModalOpen(true);
              },
            },
          ],
        }}
        onClick={() => {
          setIsValid(
            analysisInput?.lot_id !== undefined &&
              analysisInput.lot_id.length > 1,
          );
        }}
      >
        <Typography.Text className="cursor-pointer">
          <Space>
            Genealogy Linking
            <DownOutlined />
          </Space>
        </Typography.Text>
      </Dropdown>
    </>
  );
}
