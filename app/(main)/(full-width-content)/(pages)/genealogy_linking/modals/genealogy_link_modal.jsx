import { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, message, Modal, Typography } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { useEffectApiFetch } from "../../../../../../src/hooks";
import { useBoundStore } from "../../../../../../src/store/store";
import YHGrid from "../../../../../../src/utils/grid/yh_grid";
import Api from "../../../../../../src/utils/api";
import { ComponentNameMapper } from "../../../../../../src/utils/grid/component_name_mapper";
import PageLoading from "../../../../loading";

/*
 * Genealogy Linking Modal component
 *
 * @param {string} pageKey
 * @param {bool} isLinkModalOpen
 * @param {function} setIsLinkModalOpen
 * @returns {JSX.Element}
 * */
export default function GenealogyLinkModal({
  pageKey,
  isLinkModalOpen,
  setIsLinkModalOpen,
}) {
  const [messageApi, messageContextHolder] = message.useMessage();
  const [modalApi, modalContextHolder] = Modal.useModal();
  const genealogyLinkGridRef = useRef();
  const [gridComponent, setGridComponent] = useState();
  const [lotsForLinkingValidation, setLotsForLinkingValidation] = useState({});
  const [isLinkingDone, setIsLinkingDone] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLinkingButtonDisabled, setIsLinkingButtonDisabled] = useState(false);
  const filters = useBoundStore((state) => state.filters);
  const isGenealogyLinkEditButtonDisabled = useBoundStore(
    (state) => state.isGenealogyLinkEditButtonDisabled,
  );
  const isGenealogyLinkInEditMode = useBoundStore(
    (state) => state.isGenealogyLinkInEditMode,
  );
  const setIsGenealogyLinkInEditMode = useBoundStore(
    (state) => state.setIsGenealogyLinkInEditMode,
  );
  const setIsGenealogyLinkEditButtonDisabled = useBoundStore(
    (state) => state.setIsGenealogyLinkEditButtonDisabled,
  );

  const genealogyLinkEditedLots = useBoundStore(
    (state) => state.genealogyLinkEditedLots,
  );
  const setGenealogyLinkEditedLots = useBoundStore(
    (state) => state.setGenealogyLinkEditedLots,
  );

  useEffect(() => {
    setIsLinkingButtonDisabled(
      !validateGenealogyData(lotsForLinkingValidation),
    );
  }, [lotsForLinkingValidation]);

  useEffectApiFetch(
    () => {
      return getLinkGridComponent();
    },
    () => {
      setGridComponent();
    },
  );

  /*
   * Validate data if fit for linking
   *
   * @param {Array} data
   * @returns {boolean} isValid
   * */
  const validateGenealogyData = (data) => {
    const isValid =
      Object.keys(data).length > 0
        ? !Object.keys(data)
            .map((key) => data[key])
            .some((item) => {
              return (
                [
                  item["wafer_id"],
                  item["probe_lot_id"],
                  item["fab_lot_id"],
                ].filter((v) => v === null).length >= 2
              );
            })
        : false;
    return isValid;
  };

  /*
   * Get genealogy linking grid component
   *
   * @returns {AbortController} abortCtl
   * */
  const getLinkGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setGridComponent(res.data);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      { name: ComponentNameMapper.genealogy_link_table },
    );
    return abortCtl;
  };

  /*
   * Send all lots data to the api
   *
   * @returns {AbortController} abortCtl
   * */
  const handleSendEditedLots = () => {
    setIsLoading(true);
    return Api.getData(
      "/api/v1/genealogy/link/update",
      "post",
      () => {
        setIsLoading(false);
        setIsLinkingDone(true);
        setIsGenealogyLinkEditButtonDisabled(false);
      },
      (err) => {
        messageApi.error(err, 5);
      },
      {
        table_data: Object.keys(lotsForLinkingValidation).map(
          (key) => lotsForLinkingValidation[key],
        ),
      },
    );
  };

  return (
    <>
      {messageContextHolder}
      {modalContextHolder}
      <Modal
        className="!w-164"
        title="Genealogy Lot Linking"
        keyboard={false}
        closable={!isLoading}
        maskClosable={!isLoading}
        destroyOnClose={true}
        open={isLinkModalOpen}
        okButtonProps={{
          disabled: isLinkingButtonDisabled,
          loading: isLoading,
        }}
        okText="Perform Linking for Genealogy Correlation"
        onOk={handleSendEditedLots}
        cancelButtonProps={{ disabled: isLoading }}
        onCancel={() => {
          modalApi.confirm({
            title: "Confirm Exit",
            content: (
              <Typography.Text>
                Are you sure you want to leave without performing the linking
                process?. All changees will be lost.
              </Typography.Text>
            ),
            okText: "Leave without Linking",
            onOk: () => {
              setIsLinkingDone(false);
              setIsLinkModalOpen(false);
            },
            centered: true,
          });
        }}
        closeIcon={<CloseOutlined />}
        footer={(_, { OkBtn, CancelBtn }) => {
          return (
            <>
              <CancelBtn />
              {isLinkingDone ? (
                <Button
                  disabled={isGenealogyLinkEditButtonDisabled || isLoading}
                  onClick={() => {
                    genealogyLinkGridRef.current?.api.stopEditing();
                    setIsGenealogyLinkInEditMode(!isGenealogyLinkInEditMode);
                  }}
                >
                  {isGenealogyLinkInEditMode ? "Cancel Edit" : "Edit"}
                </Button>
              ) : (
                <></>
              )}
              <OkBtn />
            </>
          );
        }}
      >
        <Typography.Text>
          Manually fill in the linked lot IDs and wafer numbers by clicking the
          ADD DATA in the table.
        </Typography.Text>

        {gridComponent &&
          (isLoading ? (
            <PageLoading />
          ) : (
            <>
              <YHGrid
                gridRef={genealogyLinkGridRef}
                gridId="link_grid_modal"
                gridOptions={{
                  rowModelType: "serverSide",
                  suppressClickEdit: true,
                  stopEditingWhenCellsLoseFocus: true,
                }}
                component={gridComponent}
                filters={filters}
                rowGroups={[]}
                pageKey={pageKey}
                wrapperClassName="flex grow flex-col h-full"
                valueSetters={{
                  editableColumn: (params) => {
                    const newValue = params.newValue.trim();
                    params.data[params.colDef.field] =
                      newValue !== "" ? newValue : null;
                    return true;
                  },
                }}
                eventHandlers={{
                  handlePostReload: ({ table_data }) => {
                    let data = {};
                    table_data.forEach((item) => {
                      data[item["genealogy_key"]] = item;
                    });
                    setLotsForLinkingValidation(data);
                    setIsLinkingButtonDisabled(!validateGenealogyData(data));
                  },
                  onCellEditingStarted: () => {
                    setIsLinkingButtonDisabled(true);
                  },
                  onCellEditingStopped: ({ column, node, data, value }) => {
                    let colId = column.colId;
                    let rowIndex = node.rowIndex;
                    let lots = genealogyLinkEditedLots;
                    setIsGenealogyLinkEditButtonDisabled(false);
                    setLotsForLinkingValidation((prev) => ({
                      ...prev,
                      [data["genealogy_key"]]: {
                        ...(prev[data["genealogy_key"]] || {}),
                        [colId]: value !== "" ? value : null,
                      },
                    }));
                    lots[`${colId}_${rowIndex}`] =
                      value !== null ? true : false;
                    setGenealogyLinkEditedLots(lots);
                  },
                }}
              />
              {isLinkingDone && (
                <Alert
                  message={
                    <>
                      <b>Note:</b>{" "}
                      {
                        "All inputted lot IDs that do not match any existing lot IDs will be in "
                      }
                      <span className="text-red-800">red.</span>
                    </>
                  }
                  type="warning"
                  showIcon={true}
                  closable={true}
                />
              )}
            </>
          ))}
      </Modal>
    </>
  );
}
