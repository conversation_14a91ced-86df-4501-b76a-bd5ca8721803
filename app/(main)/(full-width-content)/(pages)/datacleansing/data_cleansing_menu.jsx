"use client";

import { useEffect, useState } from "react";
import { Modal, Space, Dropdown, Typography } from "antd";
import { DownOutlined } from "@ant-design/icons";
import { useBoundStore } from "../../../../../src/store/store";
import Helper from "../../../../../src/utils/helper";
import ConsolidationForm from "./consolidation/consolidation_form";
import ApplyConsolidationRecipe from "./consolidation/apply_consolidation";
import LoadConsolidationRecipe from "./consolidation/load_consolidation";

const { error } = Modal;

export const defaultToggleAlgoValues = {
  invalidate_after_cons: false,
  cons_by_serial_number: false,
  multiple_batch_rescreen: false,
  qa_consolidation: false,
  cons_ft_retain_part_id: false,
  cons_unique_part_id_ft: false,
  use_strip_consolidation: false,
  cons_probe_cons_part_id: false,
};

/**
 * Data cleansing menu section
 *
 * @param {object} searchGridComponent
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
const DataCleansingMenu = ({ searchGridComponent, pageKey }) => {
  const [selectionStoreKey, setSelectionStoreKey] = useState();
  const [selectedConsolidationParams, setSelectedConsolidationParams] =
    useState({});
  const [consolidationMenuBtnIsDisabled, setConsolidationMenuBtnIsDisabled] =
    useState(true);
  const [
    isCreateConsolidationRecipeModalOpen,
    setIsCreateConsolidationRecipeModalOpen,
  ] = useState(false);
  const [isSelectRecipeModalOpen, setIsSelectRecipeModalOpen] = useState(false);
  const [
    isApplyConsolidationRecipeModalOpen,
    setIsApplyConsolidationRecipeModalOpen,
  ] = useState(false);
  const consolidationConfig = useBoundStore(
    (state) => state.consolidationConfig,
  );
  const gridSelectionData = useBoundStore((state) => state.gridSelectionData);

  useEffect(() => {
    searchGridComponent &&
      setSelectionStoreKey(searchGridComponent.selection_store_key);
  }, [searchGridComponent]);

  const consolidationItems = [
    {
      key: "create_consolidation",
      label: "Create Consolidation Recipe",
    },
    {
      key: "apply_consolidation",
      label: "Apply Consolidation Recipe",
      disabled: consolidationMenuBtnIsDisabled,
    },
    {
      key: "load_consolidation",
      label: "Load Consolidation Recipe",
    },
  ];

  /**
   * Data cleansing cascader on change handler
   */
  const generateConsolidation = ({ key }) => {
    let input = {};

    if (gridSelectionData[selectionStoreKey].length) {
      input.dsk = [];
      gridSelectionData[selectionStoreKey].forEach((data) => {
        if (data.data_struc_key) {
          input.dsk = Helper.arrayUnique(
            input.dsk.concat(data.data_struc_key.toString().split(",")),
          );
        }
      });
      input.lot_id = Helper.arrayUnique(
        gridSelectionData[selectionStoreKey].map((data) => {
          return data.lot_id;
        }),
      );
      input.mfg_process = Helper.arrayUnique(
        gridSelectionData[selectionStoreKey].map((data) => {
          return data.manufacturing_process;
        }),
      );
    }
    setSelectedConsolidationParams({ ...input });

    switch (key) {
      case "create_consolidation":
        setIsCreateConsolidationRecipeModalOpen(true);
        break;
      case "apply_consolidation":
        datalogsValidForConsolidationRecipe(input) &&
          setIsApplyConsolidationRecipeModalOpen(true);
        break;
      case "load_consolidation":
        setIsSelectRecipeModalOpen(true);
        break;
    }
  };

  /**
   * Enable/disable consolidation menu buttons based on grid selection
   */
  const validateMenuItems = () => {
    const hasManufacturingProcess = gridSelectionData[selectionStoreKey].every(
      (data) =>
        Object.keys(consolidationConfig.grouping_fields).includes(
          data.manufacturing_process,
        ),
    );
    const hasDataStrucKey = Object.values(
      gridSelectionData[selectionStoreKey],
    ).some((selectedDatalog) => "data_struc_key" in selectedDatalog);

    setConsolidationMenuBtnIsDisabled(
      !hasManufacturingProcess || !hasDataStrucKey,
    );
  };

  /**
   * Check if selected datalogs are valid for consolidation recipe
   *
   * @param {object} input
   * @returns {boolean} isValid
   */
  const datalogsValidForConsolidationRecipe = (input) => {
    let isValid = true;

    if (input.mfg_process.length > 1) {
      error({
        title: "Manufacturing Process Mismatch",
        content:
          "Please ensure all selected datalogs share the same manufacturing process before proceeding.",
      });

      isValid = false;
    }

    return isValid;
  };

  return (
    <>
      <ConsolidationForm
        isModalOpen={isCreateConsolidationRecipeModalOpen}
        setIsModalOpen={setIsCreateConsolidationRecipeModalOpen}
        pageKey={pageKey}
      />
      <ApplyConsolidationRecipe
        pageKey={pageKey}
        isApplyConsolidationRecipeModalOpen={
          isApplyConsolidationRecipeModalOpen
        }
        setIsApplyConsolidationRecipeModalOpen={
          setIsApplyConsolidationRecipeModalOpen
        }
        selectedConsolidationParams={selectedConsolidationParams}
        homeSelectionStoreKey={selectionStoreKey}
      />
      <LoadConsolidationRecipe
        pageKey={pageKey}
        selectedConsolidationParams={selectedConsolidationParams}
        isSelectRecipeModalOpen={isSelectRecipeModalOpen}
        setIsSelectRecipeModalOpen={setIsSelectRecipeModalOpen}
      />
      <Dropdown
        menu={{ items: consolidationItems, onClick: generateConsolidation }}
        trigger="click"
        onClick={validateMenuItems}
      >
        <Typography.Text className="cursor-pointer">
          <Space>
            Consolidation
            <DownOutlined />
          </Space>
        </Typography.Text>
      </Dropdown>
    </>
  );
};

export default DataCleansingMenu;
