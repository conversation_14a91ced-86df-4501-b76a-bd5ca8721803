"use_client";

import {
  App,
  Col,
  Input,
  Form,
  Row,
  Select,
  Checkbox,
  Radio,
  Collapse,
  Typography,
  Alert,
  Modal,
  Button,
  Space,
} from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { useEffect, useState, memo } from "react";
import { isEqual, keys, intersection, pick } from "lodash";
import Api from "../../../../../../src/utils/api";
import GroupSelect from "../../../../../../src/utils/components/recipe_common/group_select";
import HierarchicalSelect from "../../../../../../src/utils/components/recipe_common/hierarchical_select";
import Helper from "../../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../../src/store/store";
import { defaultToggleAlgoValues } from "../../../../(full-width-content)/(pages)/datacleansing/data_cleansing_menu";

const { TextArea } = Input;
const { Text } = Typography;

/**
 * Form used for consolidation
 *
 * @param {boolean} isModalOpen
 * @param {function} setIsModalOpen
 * @param {string} pageKey
 * @param {object} recipeData
 * @returns {JSX.Element}
 */
const ConsolidationForm = ({
  isModalOpen,
  setIsModalOpen,
  pageKey,
  recipeData,
}) => {
  const [consolidationForm] = Form.useForm();
  const { message } = App.useApp();
  const [options, setOptions] = useState({});
  const [initialValues, setInitialValues] = useState({});
  const [isRecipeLoaded, setIsRecipeLoaded] = useState(false);
  const [shouldClearFormValues, setShouldClearFormValues] = useState(false);
  const [isFormChanged, setIsFormChanged] = useState(false);
  const setReloadGrids = useBoundStore((state) => state.setReloadGrids);
  const setReloadGrid = useBoundStore((state) => state.setReloadGrid);
  const consolidationConfig = useBoundStore(
    (state) => state.consolidationConfig,
  );
  const rowGutter = [16, 24];
  const [{ confirm, success, error, warning }, contextHolder] =
    Modal.useModal();
  const reloadGridKey =
    pageKey === "recipe_drafts" ? "recipe_draft_table" : "recipe_table";
  const defaultInitialValues = {
    recipe_name: isRecipeLoaded ? recipeData.recipe_name : undefined,
    mfg_process: isRecipeLoaded
      ? recipeData.mfg_process
      : consolidationConfig?.grouping_fields &&
        Object.keys(consolidationConfig?.grouping_fields)[0],
    grouping_fields: undefined,
    grouping_fields_level_0: undefined,
    grouping_fields_level_1: undefined,
    grouping_fields_level_2: undefined,
    bin_option: undefined,
    bin_option_value: undefined,
    recipe_input: undefined,
    recipe_input_exclude: undefined,
    recipe_input_exclude_value: undefined,
    toggle_algos: defaultToggleAlgoValues,
    save_type: "Draft",
    trigger_value: false,
    notes: undefined,
    user_name_updated: isRecipeLoaded
      ? recipeData.user_name_updated
      : undefined,
    date_updated: isRecipeLoaded ? recipeData.date_updated : undefined,
    user_name: isRecipeLoaded ? recipeData.user_name : undefined,
    date_created: isRecipeLoaded ? recipeData.date_created : undefined,
  };

  useEffect(() => {
    getConsolidationFieldOptions();
  }, []);

  useEffect(() => {
    if (isModalOpen) {
      setIsRecipeLoaded(recipeData && Object.keys(recipeData).length > 0);
      setIsFormChanged(false);
      if (recipeData) {
        setInitialValues(recipeData);
        consolidationForm.setFieldsValue(recipeData);
      } else {
        setInitialValues(defaultInitialValues);
        consolidationForm.setFieldsValue(defaultInitialValues);
      }
    }
  }, [isModalOpen]);

  /**
   * Event handler when user selects a manufacturing process
   */
  const handleMfgProcessSelection = () => {
    setShouldClearFormValues(true);
    consolidationForm.resetFields([
      "grouping_fields",
      "grouping_fields_level_0",
      "grouping_fields_level_1",
      "grouping_fields_level_2",
    ]);
  };

  /**
   * Get consolidation grouping field list
   *
   * @returns {AbortController} abortCtl
   */
  const getConsolidationFieldOptions = () => {
    const abortCtl = Api.getConsolidationFieldOptions(
      (res) => {
        if (res.success) {
          setOptions(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
    );

    return abortCtl;
  };

  /**
   * Trigger when form values change
   *
   * @param {object} allValues
   */
  const handleValuesChange = (_, allValues) => {
    // Adjust the format to match initialValues before performing the comparison
    allValues.toggle_algos = {
      ...defaultToggleAlgoValues,
      ...allValues.toggle_algos,
    };
    allValues.grouping_fields =
      [
        allValues.grouping_fields_level_0,
        allValues.grouping_fields_level_1,
        allValues.grouping_fields_level_2,
      ]
        .filter(Boolean)
        .join("-") || undefined;
    if (allValues.recipe_input) {
      const formattedRecipeInput = Object.fromEntries(
        Object.entries(allValues.recipe_input).filter(
          ([, item]) => item && Array.isArray(item) && item.length > 0,
        ),
      );
      allValues.recipe_input = Object.keys(formattedRecipeInput).length
        ? formattedRecipeInput
        : undefined;
    }

    // Get the keys that are present in both objects
    const commonKeys = intersection(keys(initialValues), keys(allValues));
    // Create new objects with only the common keys
    const initialValuesSubset = pick(initialValues, commonKeys);
    const allValuesSubset = pick(allValues, commonKeys);
    // Compare these new objects
    const isEqualValue = isEqual(initialValuesSubset, allValuesSubset);
    setIsFormChanged(!isEqualValue);

    // Trigger validations
    if (_.recipe_input || _.recipe_input_exclude) {
      consolidationForm.validateFields(["recipe_input_exclude_value"]);
    }
  };

  /**
   * Event handler when user cancels the modal
   */
  const handleCancel = () => {
    if (isFormChanged) {
      const modal = warning({
        title: "Leave Without Saving?",
        content:
          "Are you sure you want to leave this page without saving? All unsaved changes will be lost.",
        footer: (
          <div className="flex justify-end space-x-1 mt-2">
            <Button key="back" onClick={() => modal.destroy()}>
              No
            </Button>
            <Button
              key="exit"
              type="primary"
              onClick={() => {
                modal.destroy();
                setIsModalOpen(false);
                clearFieldsValue();
              }}
            >
              Yes
            </Button>
            <Button
              key="save_as_draft"
              onClick={() => {
                modal.destroy();
                consolidationForm.setFieldsValue({
                  save_type: "Draft",
                  trigger_value: false,
                });
                handleSaveRecipe();
              }}
              className="bg-white text-[#154495] border border-[#154495] "
            >
              Save as Draft and Leave
            </Button>
            ,
          </div>
        ),
      });
    } else {
      setIsModalOpen(false);
    }
  };

  /**
   * Handles the saving of the recipe
   */
  const handleSaveRecipe = () => {
    Api.checkConsolidationRecipeName(
      (res) => {
        if (res.success) {
          validateAndSaveRecipe(0);
        } else {
          if (
            initialValues.recipe_name ===
              consolidationForm.getFieldValue("recipe_name") ||
            (res.data.draft &&
              consolidationForm.getFieldValue("save_type") === "Production")
          ) {
            validateAndSaveRecipe(1);
          } else if (
            res.data.draft &&
            consolidationForm.getFieldValue("save_type") === "Draft"
          ) {
            confirm({
              title: "Draft Recipe Name Already Exists!",
              icon: <InfoCircleOutlined style={{ color: "rgb(252 211 77)" }} />,
              content: "What do you want to do?",
              okText: "Overwrite Existing",
              cancelText: "Rename and Save as New",
              onOk() {
                validateAndSaveRecipe(1);
              },
            });
          } else {
            warning({
              title: "Recipe Name Already Exists!",
              content: (
                <>
                  The recipe name you entered is already taken.
                  <br />
                  Please rename and click the Save button again.
                </>
              ),
            });
          }
        }
      },
      (err) => {
        message.error(err, 5);
      },
      consolidationForm.getFieldsValue("recipe_name"),
    );
  };

  /**
   * Validates the form fields and saves the consolidation recipe if valid
   *
   * @param {number} overwrite
   */
  const validateAndSaveRecipe = (overwrite) => {
    consolidationForm
      .validateFields()
      .then((values) => {
        saveConsolidationRecipe(
          formatValues({ ...values, overwrite: overwrite }),
        );
      })
      .catch(() => {
        error({
          title: "Kindly input the required fields.",
        });
      });
  };

  /**
   * Format values before passing to save consolidation api
   *
   * @param {object} values
   */
  const formatValues = (values) => {
    let formattedValues = { ...values };

    formattedValues.save_type = {
      draft: values.save_type === "Draft",
      production: values.save_type === "Production",
      pdb: false,
    };
    formattedValues.trigger_value = {
      production: values.trigger_value === true,
      pdb: false,
    };

    if (values.recipe_input_exclude) {
      formattedValues.recipe_input_exclude = {
        [values.recipe_input_exclude]: values.recipe_input_exclude_value,
      };
      delete formattedValues.recipe_input_exclude_value;
    }

    formattedValues.toggle_algos = {
      ...defaultToggleAlgoValues,
      ...values.toggle_algos,
    };

    return formattedValues;
  };

  /**
   * Calls api to save the recipe
   *
   * @param {object} payload
   */
  const saveConsolidationRecipe = (payload) => {
    Api.saveConsolidationRecipe(
      (res) => {
        if (res.success) {
          if (isRecipeLoaded) {
            setReloadGrids([reloadGridKey]);
            setReloadGrid(Date.now());
          }
          success({
            title: `${res.message}`,
            content: (
              <Space size="small" direction="vertical">
                {res.data.recipe_info.map((item, index) => (
                  <Text key={index}>
                    {item.label} : {item.value}
                  </Text>
                ))}
              </Space>
            ),
            onOk: () => {
              setIsModalOpen(false);
              clearFieldsValue();
            },
            onCancel() {
              setIsModalOpen(false);
              clearFieldsValue();
            },
          });
        } else {
          error({
            title: `${res.message}`,
          });
        }
      },
      (err) => {
        message.error(err, 5);
      },
      payload,
    );
  };

  /**
   * Clear form fields value
   */
  const clearFieldsValue = () => {
    consolidationForm.setFieldsValue(defaultInitialValues);
    setInitialValues(defaultInitialValues);
    setShouldClearFormValues(true);
  };

  return (
    <>
      {contextHolder}
      <Modal
        title={`${isRecipeLoaded ? "Load" : "Create"} Consolidation Recipe`}
        open={isModalOpen}
        destroyOnClose={true}
        centered
        onCancel={handleCancel}
        footer={null}
      >
        <Form
          form={consolidationForm}
          initialValues={initialValues}
          onValuesChange={handleValuesChange}
          name={`${isRecipeLoaded ? "load" : "create"}_consolidation_recipe`}
          layout="vertical"
        >
          <Row gutter={rowGutter}>
            <Col span={12}>
              <Form.Item
                name="recipe_name"
                label="Recipe Name"
                rules={[
                  {
                    required: true,
                    message: "Please input the recipe name",
                  },
                  {
                    pattern: /^[a-zA-Z _-]+[a-zA-Z0-9 _-]*$/,
                    message:
                      "Can only include numbers 0-9, letters a-z, - and _ and should not start with a number.",
                  },
                  {
                    validator: (_, value) => {
                      if (value.length > 150) {
                        return Promise.reject();
                      } else {
                        return Promise.resolve();
                      }
                    },
                    message: "Recipe name should not exceed 150 characters.",
                  },
                ]}
              >
                <Input placeholder="Custom name" />
              </Form.Item>
            </Col>
            {isRecipeLoaded && (
              <Col span={3}>
                <Form.Item name="version" label="Version">
                  <Select disabled={isRecipeLoaded} placeholder="-"></Select>
                </Form.Item>
              </Col>
            )}

            <Col span={isRecipeLoaded ? 9 : 12}>
              <Form.Item
                name="mfg_process"
                label="Manufacturing Process"
                rules={[
                  {
                    required: true,
                    message: "Please select a process.",
                  },
                ]}
                tooltip="Selection is permanent after saving; changes only possible during creation"
              >
                <Select
                  disabled={isRecipeLoaded}
                  options={options.mfg_arr}
                  onSelect={handleMfgProcessSelection}
                ></Select>
              </Form.Item>
            </Col>
          </Row>
          {isRecipeLoaded && (
            <Row gutter={rowGutter}>
              <Col span={24}>
                <Form.Item noStyle shouldUpdate>
                  {() => (
                    <Form.Item name="info">
                      <Collapse
                        items={[
                          {
                            key: "1",
                            label: "Recipe Information",
                            children: (
                              <>
                                <Row gutter={rowGutter}>
                                  <Col span={12}>
                                    <Text strong>Updated By: </Text>
                                    <Text>
                                      {initialValues.user_name_updated}
                                    </Text>
                                  </Col>
                                  <Col span={12}>
                                    <Text strong>Date Updated: </Text>
                                    <Text>{initialValues.date_updated}</Text>
                                  </Col>
                                </Row>

                                <Row gutter={rowGutter}>
                                  <Col span={12}>
                                    <Text strong>Created By: </Text>
                                    <Text>{initialValues.user_name}</Text>
                                  </Col>
                                  <Col span={12}>
                                    <Text strong>Date Created: </Text>
                                    <Text>{initialValues.date_created}</Text>
                                  </Col>
                                </Row>
                              </>
                            ),
                          },
                        ]}
                        defaultActiveKey={["0"]}
                      />
                    </Form.Item>
                  )}
                </Form.Item>
              </Col>
            </Row>
          )}
          <Row gutter={rowGutter}>
            <Col span={24}>
              <HierarchicalSelect
                form={consolidationForm}
                data={
                  consolidationForm.getFieldValue("mfg_process") === "Test"
                    ? options.grouping_fields?.Test
                    : options.grouping_fields?.Probe
                }
                initialValue={initialValues?.grouping_fields}
                shouldClearFormValues={shouldClearFormValues}
                setShouldClearFormValues={setShouldClearFormValues}
              />
            </Col>
          </Row>
          {consolidationForm.getFieldValue("mfg_process") === "Test" && (
            <Row gutter={rowGutter}>
              <Col span={12}>
                <Form.Item noStyle shouldUpdate>
                  {() => (
                    <Form.Item name="bin_option" label="Select Bin Settings">
                      <Select
                        placeholder="Select"
                        options={[
                          {
                            value: "exclude_bins",
                            label: "Exclude bins for consolidation",
                          },
                          {
                            value: "consolidate_given_bins",
                            label: "Consolidate given bins",
                          },
                          {
                            value: "consolidate_fn_bins_regrex",
                            label: "Consolidate bins in filename (regex)",
                          },
                        ]}
                        disabled={
                          consolidationForm.getFieldValue([
                            "toggle_algos",
                            "use_strip_consolidation",
                          ]) === true
                        }
                        allowClear
                        onClear={() => {
                          consolidationForm.setFieldsValue({
                            bin_option_value: undefined,
                          });
                        }}
                      ></Select>
                    </Form.Item>
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item noStyle shouldUpdate>
                  {() => (
                    <Form.Item
                      name="bin_option_value"
                      label=" "
                      rules={[
                        {
                          validator: (_, value) => {
                            if (
                              !value ||
                              consolidationForm.getFieldValue("bin_option") !==
                                "consolidate_fn_bins_regrex"
                            ) {
                              return Promise.resolve();
                            }

                            try {
                              new RegExp(value);

                              // Check if the regex includes the delimiter pattern
                              const delimiterPattern = /^\/.*\/$/;
                              if (delimiterPattern.test(value)) {
                                return Promise.resolve();
                              } else {
                                return Promise.reject(
                                  new Error(
                                    "Regex must be enclosed in delimiters ex. /abc/",
                                  ),
                                );
                              }
                            } catch (e) {
                              console.error(e);
                              return Promise.reject(
                                new Error(
                                  "Input is not a valid regular expression",
                                ),
                              );
                            }
                          },
                        },
                      ]}
                    >
                      <Input
                        placeholder={
                          consolidationForm.getFieldValue("bin_option") ===
                          "consolidate_fn_bins_regrex"
                            ? "Enter regex that matches /_R\\d+_(.+)_\\d/"
                            : "Select bin numbers"
                        }
                        disabled={
                          !consolidationForm.getFieldValue("bin_option") ||
                          consolidationForm.getFieldValue([
                            "toggle_algos",
                            "use_strip_consolidation",
                          ]) === true
                        }
                      />
                    </Form.Item>
                  )}
                </Form.Item>
              </Col>
            </Row>
          )}
          <Row gutter={rowGutter}>
            <Col span={24}>
              <GroupSelect
                options={options.consolidation_input_methods}
                pageFilters={consolidationForm.getFieldValue("mfg_process")}
                groupFieldName={"recipe_input"}
                groupFieldLabel={"Link To"}
                tooltipMessage={
                  "Consolidate the datalogs with given field value."
                }
                initialValues={initialValues.recipe_input}
                shouldClearFormValues={shouldClearFormValues}
                setShouldClearFormValues={setShouldClearFormValues}
                mode="multiple"
              />
            </Col>
          </Row>
          <Row gutter={rowGutter}>
            <Col span={12}>
              <Form.Item
                name="recipe_input_exclude"
                label="Exclude"
                tooltip="Choose from the selection you want to exclude from the output."
              >
                <Select
                  options={options.consolidation_input_exclude_methods}
                  allowClear
                  onClear={() => {
                    consolidationForm.setFieldsValue({
                      recipe_input_exclude_value: undefined,
                    });
                  }}
                ></Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item noStyle shouldUpdate>
                {() => (
                  <Form.Item
                    name="recipe_input_exclude_value"
                    label=" "
                    rules={[
                      {
                        validator: (_, value) => {
                          const recipe_input =
                            consolidationForm.getFieldValue("recipe_input");
                          if (!value || !recipe_input) {
                            return Promise.resolve();
                          }

                          const excludeValuesArray = value
                            .split(",")
                            ?.map((item) => item.trim().toLowerCase());
                          const recipe_input_exclude =
                            consolidationForm.getFieldValue(
                              "recipe_input_exclude",
                            );
                          const recipeInputArray = (
                            recipe_input[recipe_input_exclude] || []
                          ).map((item) => item.toLowerCase());

                          const hasOverlap = excludeValuesArray.some((val) =>
                            recipeInputArray.includes(val),
                          );

                          if (hasOverlap) {
                            return Promise.reject(
                              new Error(
                                `You can not link and exclude the same ${recipe_input_exclude} value.`,
                              ),
                            );
                          }

                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input
                      placeholder="Field value"
                      disabled={
                        !consolidationForm.getFieldValue("recipe_input_exclude")
                      }
                    />
                  </Form.Item>
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={rowGutter}>
            <Alert
              message={
                <Text strong>
                  To learn more about each checkbox, please click{" "}
                  <a
                    href="https://yieldhub.zendesk.com"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    here
                  </a>
                  .
                </Text>
              }
              type="info"
              showIcon
              className="w-full mx-2"
            />
          </Row>
          <Row gutter={[0, 0]}>
            <Col span={12}>
              <Form.Item
                name={["toggle_algos", "invalidate_after_cons"]}
                valuePropName="checked"
                initialValue={false}
                style={
                  consolidationForm.getFieldValue("mfg_process") === "Test"
                    ? {
                        marginBottom: 0,
                      }
                    : null
                }
              >
                <Checkbox>Invalidate after consolidation</Checkbox>
              </Form.Item>
            </Col>
            {consolidationForm.getFieldValue("mfg_process") === "Test" ? (
              <Col span={12}>
                <Form.Item noStyle shouldUpdate>
                  {() => (
                    <Form.Item
                      name={["toggle_algos", "cons_by_serial_number"]}
                      valuePropName="checked"
                      initialValue={false}
                      className="mb-0"
                    >
                      <Checkbox
                        disabled={
                          consolidationForm.getFieldValue([
                            "toggle_algos",
                            "use_strip_consolidation",
                          ]) === true
                        }
                      >
                        Consolidate by Serial Number
                      </Checkbox>
                    </Form.Item>
                  )}
                </Form.Item>
              </Col>
            ) : (
              <Col span={12}>
                <Form.Item noStyle shouldUpdate>
                  {() => (
                    <Form.Item
                      name={["toggle_algos", "cons_probe_cons_part_id"]}
                      valuePropName="checked"
                      initialValue={false}
                      className="mb-0"
                    >
                      <Checkbox
                        disabled={
                          consolidationForm.getFieldValue([
                            "toggle_algos",
                            "use_strip_consolidation",
                          ]) === true
                        }
                      >
                        Consolidate by Part Id
                      </Checkbox>
                    </Form.Item>
                  )}
                </Form.Item>
              </Col>
            )}

            {consolidationForm.getFieldValue("mfg_process") === "Test" && (
              <>
                <Col span={12}>
                  <Form.Item noStyle shouldUpdate>
                    {() => (
                      <Form.Item
                        name={["toggle_algos", "multiple_batch_rescreen"]}
                        valuePropName="checked"
                        initialValue={false}
                        className="mb-0"
                      >
                        <Checkbox
                          disabled={
                            consolidationForm.getFieldValue([
                              "toggle_algos",
                              "use_strip_consolidation",
                            ]) === true
                          }
                        >
                          Multiple batch rescreen
                        </Checkbox>
                      </Form.Item>
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={["toggle_algos", "qa_consolidation"]}
                    valuePropName="checked"
                    initialValue={false}
                    className="mb-0"
                  >
                    <Checkbox>QA Consolidation</Checkbox>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item noStyle shouldUpdate>
                    {() => (
                      <Form.Item
                        name={["toggle_algos", "cons_ft_retain_part_id"]}
                        valuePropName="checked"
                        initialValue={false}
                        className="mb-0"
                      >
                        <Checkbox>Retain Part ID</Checkbox>
                      </Form.Item>
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item noStyle shouldUpdate>
                    {() => (
                      <Form.Item
                        name={["toggle_algos", "cons_unique_part_id_ft"]}
                        valuePropName="checked"
                        initialValue={false}
                        className="mb-0"
                      >
                        <Checkbox
                          disabled={
                            consolidationForm.getFieldValue([
                              "toggle_algos",
                              "use_strip_consolidation",
                            ]) === true
                          }
                        >
                          Unique Part ID
                        </Checkbox>
                      </Form.Item>
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item noStyle shouldUpdate>
                    {() => (
                      <Form.Item
                        name={["toggle_algos", "use_strip_consolidation"]}
                        valuePropName="checked"
                        initialValue={false}
                      >
                        <Checkbox
                          onChange={() => {
                            consolidationForm.setFieldsValue({
                              toggle_algos: {
                                multiple_batch_rescreen: false,
                                cons_by_serial_number: false,
                                cons_unique_part_id_ft: false,
                              },
                            });
                          }}
                        >
                          Use Strip Consolidation
                        </Checkbox>
                      </Form.Item>
                    )}
                  </Form.Item>
                </Col>
              </>
            )}
          </Row>
          {/* <Row gutter={rowGutter}>
        <Col span={12}>
          <Form.Item>
            <Button icon={<LockOutlined />}>Permission Settings</Button>
          </Form.Item>
        </Col>
      </Row> */}
          <Row gutter={rowGutter}>
            <Col span={24}>
              <Form.Item label="Notes" name="notes">
                <TextArea rows={1} placeholder="Create notes" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={rowGutter}>
            <Col span={24}>
              <Form.Item
                name="save_type"
                label="Select Recipe Status"
                rules={[
                  {
                    required: true,
                    message: "Please select receipe status!",
                  },
                ]}
              >
                <Radio.Group
                  onChange={(e) => {
                    e.target.value === "Draft" &&
                      consolidationForm.setFieldsValue({
                        trigger_value: false,
                      });
                  }}
                >
                  <Radio value="Draft">Draft</Radio>
                  <Radio value="Production">Production</Radio>
                  <Form.Item noStyle shouldUpdate>
                    {() => (
                      <Form.Item
                        noStyle
                        name={"trigger_value"}
                        valuePropName="checked"
                        initialValue={false}
                      >
                        <Checkbox
                          disabled={
                            consolidationForm.getFieldValue("save_type") ===
                            "Draft"
                          }
                        >
                          Auto-trigger
                        </Checkbox>
                      </Form.Item>
                    )}
                  </Form.Item>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>
          <Row
            gutter={[8, 0]}
            style={{
              display: "flex",
              justifyContent: "flex-end",
            }}
          >
            <Col>
              <Button key="cancel" onClick={handleCancel}>
                Cancel
              </Button>
            </Col>
            <Col>
              <Button
                key="clear_all"
                onClick={() => {
                  clearFieldsValue();
                  setIsFormChanged(isRecipeLoaded);
                }}
              >
                Clear All
              </Button>
            </Col>
            <Col>
              <Form.Item key="submit" noStyle shouldUpdate>
                {() => {
                  return (
                    <Button
                      type="primary"
                      onClick={handleSaveRecipe}
                      disabled={
                        !isFormChanged ||
                        !consolidationForm.getFieldValue("grouping_fields") ||
                        !consolidationForm.getFieldValue("recipe_name") ||
                        !consolidationForm.getFieldValue("save_type") ||
                        Helper.isEmptyRecipeInput(
                          consolidationForm.getFieldValue("recipe_input"),
                        )
                      }
                    >
                      Save Recipe
                    </Button>
                  );
                }}
              </Form.Item>
              ,
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default memo(ConsolidationForm);
