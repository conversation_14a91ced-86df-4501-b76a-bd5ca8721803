"use_client";

import { <PERSON><PERSON>, <PERSON>ton, Col, Form, Modal, Space, Typography } from "antd";
import { useEffect, useState, useRef } from "react";
import { CheckCircleOutlined } from "@ant-design/icons";
import YHGrid from "../../../../../../src/utils/grid/yh_grid";
import Api from "../../../../../../src/utils/api";
import { useEffectApiFetch } from "../../../../../../src/hooks";
import { useBoundStore } from "../../../../../../src/store/store";
import Helper from "../../../../../../src/utils/helper";
import ApplyRecipeModal from "../../../../../../src/utils/components/recipe_common/apply_recipe_modal";

const { Text } = Typography;

/**
 * Apply consolidation section component
 *
 * @param {string} pageKey
 * @param {boolean} isApplyConsolidationRecipeModalOpen
 * @param {function} setIsApplyConsolidationRecipeModalOpen
 * @param {object} selectedConsolidationParams
 * @param {string} homeSelectionStoreKey
 * @returns {JSX.Element}
 */
const ApplyConsolidationRecipe = ({
  pageKey,
  isApplyConsolidationRecipeModalOpen,
  setIsApplyConsolidationRecipeModalOpen,
  selectedConsolidationParams = {},
  homeSelectionStoreKey,
}) => {
  const { message } = App.useApp();
  const [applyRecipeForm] = Form.useForm();
  const consRecommendTableGridRef = useRef();
  const [consRecommendTableGridComponent, setConsRecommendTableGridComponent] =
    useState();
  const gridSelectionData = useBoundStore((state) => state.gridSelectionData);
  const [applyToConsolidateBtnIsDisabled, setApplyToConsolidateBtnIsDisabled] =
    useState(true);
  const [isRecommendedDatalogModalOpen, setIsRecommendedDatalogModalOpen] =
    useState(false);
  const [pageFilters, setPageFilters] = useState([]);
  const [recommendFilters, setRecommendFilters] = useState({});
  const [recommendedSelectionStoreKey, setRecommendedSelectionStoreKey] =
    useState();
  const [{ warning, error }, contextHolder] = Modal.useModal();

  useEffect(() => {
    if (recommendFilters[pageKey] === undefined) {
      recommendFilters[pageKey] = {};
    }
  }, []);

  useEffect(() => {
    consRecommendTableGridComponent &&
      setRecommendedSelectionStoreKey(
        consRecommendTableGridComponent.selection_store_key,
      );
  }, [consRecommendTableGridComponent]);

  useEffect(() => {
    const filters = {};
    Object.keys(selectedConsolidationParams).forEach((key) => {
      filters[key] = Array.isArray(selectedConsolidationParams[key])
        ? selectedConsolidationParams[key].join(",")
        : selectedConsolidationParams[key];
    });
    setPageFilters(filters);
  }, [selectedConsolidationParams]);

  useEffect(() => {
    gridSelectionData[recommendedSelectionStoreKey] &&
      updateApplyToConsolidateButtonsState();
  }, [gridSelectionData[recommendedSelectionStoreKey]]);

  useEffectApiFetch(
    () => {
      return getConsRecommendTableGridComponent();
    },
    () => {
      setConsRecommendTableGridComponent();
    },
  );

  /**
   * Get and set the consolidation recommend table grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getConsRecommendTableGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setConsRecommendTableGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: "cons_recommend_table",
      },
    );

    return abortCtl;
  };

  /**
   * Update apply to consolidate button state based on recommended grid selection
   */
  const updateApplyToConsolidateButtonsState = () => {
    if (
      gridSelectionData[recommendedSelectionStoreKey].find((data) => {
        return data.run_type === "Raw Stock";
      })
    ) {
      setApplyToConsolidateBtnIsDisabled(
        gridSelectionData[recommendedSelectionStoreKey].length < 2,
      );
    } else {
      setApplyToConsolidateBtnIsDisabled(true);
    }
  };

  /**
   * Handler when use clicks the apply recipe for recommendation
   */
  const handleApplyRecipeToRecommend = () => {
    applyRecipeForm
      .validateFields()
      .then((values) => {
        validateConsolidationDatalogs({
          ...pageFilters,
          ...{
            recipe_name: values.recipe_name,
            list_category: "dlog_related",
          },
        });
      })
      .catch((error) => {
        message.error(error.errorFields[0].errors[0], 5);
      });
  };

  /**
   * Validate selected datalogs for consolidation
   *
   * @param {object} payload
   */
  const validateConsolidationDatalogs = (payload) => {
    Api.validateConsolidationDatalogs(
      (res) => {
        if (res.success) {
          const recommendFilterCopy = Helper.cloneObject(recommendFilters);
          recommendFilterCopy[pageKey].dsk = pageFilters.dsk;
          recommendFilterCopy[pageKey].recipe_name =
            applyRecipeForm.getFieldValue("recipe_name");
          recommendFilterCopy[pageKey].list_category = "dlog_related";
          setRecommendFilters(recommendFilterCopy);
          setIsApplyConsolidationRecipeModalOpen(false);
          setIsRecommendedDatalogModalOpen(true);
        } else {
          error({
            title: `${res.message}`,
            content: `${res.data.error_msg.length ? res.data.error_msg[0] : ""}`,
          });
        }
      },
      (err) => {
        message.error(err, 5);
      },
      payload,
    );
  };

  /**
   * Handler when use clicks the apply to consolidation button
   */
  const handleApplyToConsolidate = () => {
    const input = getConsolidationInput();
    setIsRecommendedDatalogModalOpen(false);
    applyConsolidationRecipe({
      ...input,
      ...{
        recipe_name: applyRecipeForm.getFieldValue("recipe_name"),
        list_category: "dlog_related",
      },
    });
    handleCloseApplyRecipe();
  };

  /**
   * Apply recipe to selected datalogs
   *
   * @param {object} payload
   */
  const applyConsolidationRecipe = (payload) => {
    Api.applyConsolidationRecipe(
      (res) => {
        if (res.success) {
          warning({
            title: "Recipe is currently processing",
            content: (
              <div>
                <span>Your selected recipe: </span>
                <Text strong>
                  {applyRecipeForm.getFieldValue("recipe_name")}
                </Text>
                <span> has been applied to these datalogs:</span>

                <Space className="mb-5 gap-y-0 w-full" direction="vertical">
                  {gridSelectionData[recommendedSelectionStoreKey].map(
                    (obj, index) => {
                      if (index < 5) {
                        return (
                          <Text key={index} strong>
                            {Helper.truncateString(obj.file_name, 50, "middle")}
                          </Text>
                        );
                      } else if (index === 5) {
                        return (
                          <Text key={index}>
                            and{" "}
                            {gridSelectionData[recommendedSelectionStoreKey]
                              .length - 5}{" "}
                            other datalogs.
                          </Text>
                        );
                      }
                    },
                  )}
                </Space>
                <span>
                  You will be notified once the processing is done or if an
                  error occured.
                </span>
              </div>
            ),
          });
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      payload,
    );
  };

  /**
   * Closes the apply consolidation recipe modal
   */
  const handleCloseApplyRecipe = () => {
    setApplyToConsolidateBtnIsDisabled(true);
    setIsApplyConsolidationRecipeModalOpen(false);
    setIsRecommendedDatalogModalOpen(false);
  };

  /**
   * Get selected consolidation input data based on recommended grid selection
   *
   * @returns {object} input
   */
  const getConsolidationInput = () => {
    let input = {};
    let dsklist = [];

    if (gridSelectionData[recommendedSelectionStoreKey].length) {
      dsklist = [];
      gridSelectionData[recommendedSelectionStoreKey].forEach((data) => {
        dsklist = Helper.arrayUnique(
          dsklist.concat(data.data_struc_key.toString().split(",")),
        );
      });
    }
    input.dsk = dsklist.join(",");

    return input;
  };

  /**
   * Set selected rows based from home search selection
   */
  const setSelectedRows = () => {
    // traverse recommended consolidation datalog table and set selected datalogs from home search table
    consRecommendTableGridRef.current &&
      consRecommendTableGridRef.current.api &&
      consRecommendTableGridRef.current.api.forEachNode((recommendedNode) => {
        if (
          gridSelectionData[homeSelectionStoreKey].some(
            (homeSelectionNode) =>
              homeSelectionNode.file_name === recommendedNode.data.file_name,
          )
        ) {
          recommendedNode.setSelected(true);
        } else {
          recommendedNode.setSelected(false);
        }
      });
  };

  return (
    <>
      {contextHolder}
      <ApplyRecipeModal
        form={applyRecipeForm}
        pageKey="consolidation"
        isModalOpen={isApplyConsolidationRecipeModalOpen}
        setIsModalOpen={setIsApplyConsolidationRecipeModalOpen}
        selectedParams={selectedConsolidationParams}
        getRecipeListApi={Api.getConsolidationRecipes}
        getRecipeVersionApi={Api.getConsolidationRecipeVersion}
        getRecipeInfoApi={Api.getConsolidationRecipeInfo}
        handleApplyRecipe={handleApplyRecipeToRecommend}
        recipeType="consolidation"
      />
      <Modal
        destroyOnHidden
        title={
          "Select Files to Apply: " +
          applyRecipeForm.getFieldValue("recipe_name")
        }
        width={"45%"}
        open={isRecommendedDatalogModalOpen}
        onCancel={handleCloseApplyRecipe}
        onOk={handleApplyToConsolidate}
        footer={[
          <Button key="back" onClick={handleCloseApplyRecipe}>
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleApplyToConsolidate}
            icon={<CheckCircleOutlined />}
            disabled={applyToConsolidateBtnIsDisabled}
          >
            Apply to Consolidate
          </Button>,
        ]}
      >
        <span>
          Your selected file is highlighted below. Please make sure to select at
          least two (2) file names and at least one (1) Raw stock file from the
          same table to proceed.
        </span>

        <Col span={24} className="flex grow h-80 mb-8">
          {consRecommendTableGridComponent && (
            <YHGrid
              gridRef={consRecommendTableGridRef}
              gridId={pageKey + "_" + consRecommendTableGridComponent.name}
              component={consRecommendTableGridComponent}
              filters={recommendFilters}
              pageKey={pageKey}
              wrapperClassName="flex grow flex-col h-full"
              onRowDataUpdated={setSelectedRows}
            />
          )}
        </Col>
      </Modal>
    </>
  );
};

export default ApplyConsolidationRecipe;
