"use_client";

import { App } from "antd";
import { useEffect, useState } from "react";
import Api from "../../../../../../src/utils/api";
import { useBoundStore } from "../../../../../../src/store/store";
import LoadRecipeModal from "../../../../../../src/utils/components/recipe_common/load_recipe_modal";
import ConsolidationForm from "./consolidation_form";

/**
 * Load consolidation modal component
 *
 * @param {string} pageKey
 * @param {object} selectedConsolidationParams
 * @param {boolean} isSelectRecipeModalOpen
 * @param {function} setIsSelectRecipeModalOpen
 * @returns {JSX.Element}
 */
const LoadConsolidationRecipe = ({
  pageKey,
  selectedConsolidationParams = {},
  isSelectRecipeModalOpen,
  setIsSelectRecipeModalOpen,
}) => {
  const { message } = App.useApp();
  const [recipeData, setRecipeData] = useState({});
  const [isConsolidationRecipeModalOpen, setIsConsolidationRecipeModalOpen] =
    useState(false);
  const currentPageData = useBoundStore((state) => state.currentPageData);
  const consolidationRecipeParams = useBoundStore(
    (state) => state.consolidationRecipeParams,
  );
  const setConsolidationRecipeParams = useBoundStore(
    (state) => state.setConsolidationRecipeParams,
  );

  useEffect(() => {
    if (
      currentPageData.key === pageKey &&
      Object.keys(consolidationRecipeParams).length
    ) {
      getConsolidationRecipeData(consolidationRecipeParams);
    }
  }, [consolidationRecipeParams]);

  /**
   * Get the consolidation recipe data
   *
   * @param {string} recipe_name
   * @param {string} recipe_version
   * @returns {AbortController} abortCtl
   */
  const getConsolidationRecipeData = ({ recipe_name, recipe_version }) => {
    const abortCtl = Api.getConsolidationRecipeData(
      (res) => {
        if (res.success) {
          // Format values for compatibility with form input fields
          let formattedData = {
            ...res.data,
          };

          formattedData.mfg_process =
            res.data.recipe_input.manufacturing_process;
          delete formattedData.recipe_input.manufacturing_process;
          if (formattedData.recipe_input_exclude) {
            formattedData.recipe_input_exclude = Object.keys(
              res.data.recipe_input_exclude,
            )[0];
            formattedData.recipe_input_exclude_value =
              res.data.recipe_input_exclude[
                Object.keys(res.data.recipe_input_exclude)[0]
              ];
          } else {
            formattedData.recipe_input_exclude = undefined;
          }
          formattedData.trigger_value = res.data.trigger_value.production
            ? true
            : false;
          formattedData.bin_option =
            formattedData.bin_option === ""
              ? undefined
              : formattedData.bin_option;

          setRecipeData(formattedData);
          setConsolidationRecipeParams({});
          // only display the consolidation form if recipe data is already available to prevent unnecessary form field rerender
          setIsConsolidationRecipeModalOpen(true);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      { recipe_name, recipe_version },
    );

    return abortCtl;
  };

  return (
    <>
      <LoadRecipeModal
        isModalOpen={isSelectRecipeModalOpen}
        setIsModalOpen={setIsSelectRecipeModalOpen}
        selectedParams={selectedConsolidationParams}
        renderRecipePage={(values) => {
          getConsolidationRecipeData(values);
        }}
        fromExternalPage={true}
        getRecipeListApi={Api.getConsolidationRecipes}
        getRecipeVersionApi={Api.getConsolidationRecipeVersion}
        getRecipeInfoApi={Api.getConsolidationRecipeInfo}
        getRecipeDataApi={Api.getRecipeData}
        setRecipeData={setRecipeData}
        recipeType="consolidation"
      />
      {isConsolidationRecipeModalOpen && (
        <ConsolidationForm
          isModalOpen={isConsolidationRecipeModalOpen}
          setIsModalOpen={setIsConsolidationRecipeModalOpen}
          pageKey={pageKey}
          recipeData={recipeData}
        />
      )}
    </>
  );
};

export default LoadConsolidationRecipe;
