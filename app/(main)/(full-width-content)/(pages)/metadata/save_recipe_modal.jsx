"use client";

import {
  App,
  But<PERSON>,
  Checkbox,
  Flex,
  Form,
  Input,
  Modal,
  Radio,
  Select,
} from "antd";
import { CheckCircleOutlined, InfoCircleOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { isEmpty } from "lodash";
import GroupSelect from "../../../../../src/utils/components/recipe_common/group_select";
import RecipeInfo from "../../../../../src/utils/components/recipe_common/recipe_info";
import Api from "../../../../../src/utils/api";
const { TextArea } = Input;
const { confirm } = Modal;

/**
 * Save recipe modal component
 *
 * @param {boolean} isModalOpen
 * @param {function} setIsModalOpen
 * @param {object} pageFilters
 * @param {string} recipeType
 * @param {Array} includedAlgorithmOptions
 * @param {Array} algos
 * @param {object} loadedRecipeData
 * @param {function} setLoadedRecipeData
 * @param {string} activeRecipeName
 * @param {function} setActiveRecipeName
 * @returns {JSX.Element}
 */
const SaveRecipeModal = ({
  isModalOpen,
  setIsModalOpen,
  pageFilters,
  recipeType,
  includedAlgorithmOptions,
  algos,
  setLoadedRecipeData,
  loadedRecipeData = {},
  activeRecipeName,
  setActiveRecipeName,
}) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const [outputOptions, setOutputOptions] = useState([]);
  const [linkToOptions, setLinkToOptions] = useState([]);
  const [isAutoTriggerDisabled, setIsAutoTriggerDisabled] = useState(true);
  const [shouldClearFormValues, setShouldClearFormValues] = useState(false);

  useEffect(() => {
    if (isModalOpen) {
      const fieldsValue = {
        recipe_process: includedAlgorithmOptions.map((option) => option.value),
      };
      if (!isEmpty(loadedRecipeData)) {
        setIsAutoTriggerDisabled(!loadedRecipeData.save_type.production);
        fieldsValue.recipe_name = loadedRecipeData.recipe_name;
        fieldsValue.save_type = Object.keys(loadedRecipeData.save_type).find(
          (key) => loadedRecipeData.save_type[key] === true,
        );
        fieldsValue.recipe_output = loadedRecipeData.recipe_output;
        fieldsValue.notes = loadedRecipeData.notes;
        fieldsValue.trigger_value =
          loadedRecipeData.trigger_data[0].trigger_value;
        // Don't set if empty to avoid bug where only one select can have selected option
        if (Object.keys(loadedRecipeData.recipe_input).length) {
          fieldsValue.recipe_input = loadedRecipeData.recipe_input;
        }
      }
      form.setFieldsValue(fieldsValue);
    }
  }, [isModalOpen]);

  /**
   * Get the config options of the recipe
   */
  const getRecipeConfig = () => {
    Api.getRecipeConfig(
      recipeType,
      (res) => {
        if (res.success) {
          setOutputOptions(res.data.metadata_header_output_methods);
          setLinkToOptions(res.data.metadata_header_input_methods);
          form.setFieldsValue({
            recipe_output: res.data.default_output_methods,
          });
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      pageFilters,
    );
  };

  useQuery({
    queryKey: ["metadata_header_recipe_config"],
    queryFn: getRecipeConfig,
  });

  /**
   * When user cancels the modal
   */
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  /**
   * Save recipe confirmation
   */
  const saveConfirm = (content) => {
    confirm({
      title: "Recipe Saved Successfully!",
      icon: <CheckCircleOutlined style={{ color: "rgb(34 197 94)" }} />,
      content: <RecipeInfo recipeInfo={content}></RecipeInfo>,
      cancelButtonProps: { style: { display: "none" } },
    });
  };

  /**
   * Calls api to save the recipe
   *
   * @param {object} payload
   */
  const saveRecipe = (payload) => {
    Api.saveMetadataRecipe(
      (res) => {
        if (res.success) {
          saveConfirm(res.data);
          setActiveRecipeName(payload.recipe_name);
          // Copy some updated recipe properties to the loaded recipe data
          if (!isEmpty(loadedRecipeData)) {
            setLoadedRecipeData((prevState) => {
              const newState = { ...prevState };
              newState.recipe_input = payload.recipe_input;
              newState.recipe_output = payload.recipe_output;
              newState.recipe_process = payload.recipe_process;
              newState.notes = payload.notes;
              newState.save_type = payload.save_type;
              return newState;
            });
          }
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      payload,
    );
  };

  /**
   * Temporary conversion of the save type value until pdb will be implemented
   *
   * @param {string} saveType
   * @returns {object}
   */
  const convertSaveType = (saveType) => {
    return {
      draft: saveType === "draft",
      production: saveType === "production",
    };
  };

  /**
   * Check whether the recipe name already exists
   */
  const checkRecipeName = () => {
    Api.checkRecipeName(
      (res) => {
        if (res.success) {
          handleSaveRecipe();
        } else {
          // If there's already an existing name that is also a draft
          if (
            res.data.draft &&
            form.getFieldValue("save_type").draft === true
          ) {
            confirm({
              title: "Draft Recipe Name Already Exists!",
              icon: <InfoCircleOutlined style={{ color: "rgb(252 211 77)" }} />,
              content: "What do you want to do?",
              okText: "Overwrite Existing",
              cancelText: "Rename and Save as New",
              onOk() {
                handleSaveRecipe();
              },
              onCancel() {
                // Do nothing, this modal will just close and user will rename the recipe
              },
            });
          } else if (
            (res.data.draft === false &&
              activeRecipeName === form.getFieldValue("recipe_name")) ||
            (res.data.draft &&
              form.getFieldValue("save_type").production === true)
          ) {
            handleSaveRecipe();
          } else {
            confirm({
              title: "Recipe Name Already Exists!",
              icon: <InfoCircleOutlined style={{ color: "rgb(252 211 77)" }} />,
              content: "Please rename and click the Save button again.",
              cancelButtonProps: { style: { display: "none" } },
            });
          }
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        recipe_name: form.getFieldValue("recipe_name"),
        recipe_type: "metadata_header",
      },
    );
  };

  /**
   * When user saves the recipe
   */
  const handleSaveRecipe = () => {
    form
      .validateFields()
      .then((values) => {
        saveRecipe({
          ...pageFilters,
          ...values,
          algos: algos,
          save_type: convertSaveType(values.save_type),
          trigger_value: {
            production: values.trigger_value,
          },
        });
        setIsModalOpen(false);
      })
      .catch((error) => {
        message.error(error.errorFields[0].errors[0], 5);
      });
  };

  /**
   * When user resets the form
   */
  const handleClearAll = () => {
    form.resetFields();
    setShouldClearFormValues(true);
  };

  /**
   * Triggered when item in the form is changed
   */
  const handleFieldsChange = () => {
    const saveType = form.getFieldValue("save_type");
    setIsAutoTriggerDisabled(saveType === "draft");
  };

  return (
    <Modal
      title="Save Recipe"
      open={isModalOpen}
      onOk={checkRecipeName}
      onCancel={handleCancel}
      width={"40vw"}
      footer={[
        <Button key="back" onClick={handleCancel}>
          Cancel
        </Button>,
        <Button key="cancel" onClick={handleClearAll}>
          Clear All
        </Button>,
        <Button key="submit" type="primary" onClick={checkRecipeName}>
          Save Recipe
        </Button>,
      ]}
    >
      <Form
        layout="vertical"
        form={form}
        onFieldsChange={handleFieldsChange}
        initialValues={{ save_type: "draft" }}
      >
        <Form.Item
          label="Recipe Name"
          name="recipe_name"
          rules={[
            {
              required: true,
              message: "Please enter recipe name",
            },
            {
              pattern: /^[a-zA-Z_-]+[a-zA-Z0-9_-]*$/,
              message:
                "Can only include numbers 0-9, letters a-z, - and _ and should not start with a number.",
            },
            {
              max: 150,
              message: "Recipe name should not exceed 150 characters",
            },
          ]}
        >
          <Input></Input>
        </Form.Item>
        <Form.Item
          name="recipe_process"
          label="Included Algorithms"
          rules={[
            {
              required: true,
              message: "Please select algorithms",
              type: "array",
            },
          ]}
        >
          <Select
            mode="multiple"
            placeholder="Please select algorithms"
            options={includedAlgorithmOptions}
          ></Select>
        </Form.Item>
        <GroupSelect
          options={linkToOptions}
          pageFilters={pageFilters}
          groupFieldName={"recipe_input"}
          groupFieldLabel={"Link To"}
          initialValues={loadedRecipeData.recipe_input}
          shouldClearFormValues={shouldClearFormValues}
          setShouldClearFormValues={setShouldClearFormValues}
          maxTagCount="responsive"
        />
        <Form.Item
          name="recipe_output"
          label="Outputs"
          className="mt-2"
          rules={[
            {
              required: true,
              message: "Please select output",
              type: "array",
            },
          ]}
        >
          <Select
            mode="multiple"
            placeholder="Please select output"
            options={outputOptions}
          ></Select>
        </Form.Item>
        <Form.Item label="Notes" name="notes">
          <TextArea rows={2} />
        </Form.Item>
        <Flex>
          <Form.Item name="save_type" label="Select Recipe Status">
            <Radio.Group>
              <Radio value="draft">Draft</Radio>
              <Radio value="production">Production</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item name="trigger_value" label=" " valuePropName="checked">
            <Checkbox disabled={isAutoTriggerDisabled}>Auto trigger</Checkbox>
          </Form.Item>
        </Flex>
      </Form>
    </Modal>
  );
};

export default SaveRecipeModal;
