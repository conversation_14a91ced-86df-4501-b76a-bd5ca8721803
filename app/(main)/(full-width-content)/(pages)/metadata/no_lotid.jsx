"use client";

import { useEffect, useState } from "react";
import {
  App,
  Button,
  Input,
  Flex,
  Form,
  Tag,
  Typography,
  Space,
  theme,
} from "antd";
import { CheckOutlined, PlusSquareOutlined } from "@ant-design/icons";
import { alphanumericRule } from "../../../../../src/utils/antd_validation_rules";
import Api from "../../../../../src/utils/api";
const { Text } = Typography;
const { CheckableTag } = Tag;
const collapseKey = "no_lotid";
const defaultInvalidatedItems = [
  "Null",
  "Empty",
  "Any Single Digit Number",
  "Default",
];

/**
 * No Lot Id component
 *
 * @param {string} currentStepOutput
 * @param {function} setCurrentStepOutput
 * @param {function} setParsingSteps
 * @param {object} stepOnEdit
 * @param {object} stepOnEditIndex
 * @param {function} setStepOnEditIndex
 * @param {object} pageFilters
 * @param {object} mainForm
 * @returns {JSX.Element}
 */
const NoLotId = ({
  currentStepOutput = "",
  setCurrentStepOutput,
  setParsingSteps,
  stepOnEdit,
  stepOnEditIndex,
  setStepOnEditIndex,
  pageFilters,
  mainForm,
}) => {
  const { token } = theme.useToken();
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const [invalidatedItems, setInvalidatedItems] = useState(
    defaultInvalidatedItems,
  );
  const [selectedInvalidatedItems, setSelectedInvalidatedItems] = useState(
    defaultInvalidatedItems,
  );
  const [addToInvalidateBtnIsDisabled, setAddToInvalidBtnIsDisabled] =
    useState(true);

  useEffect(() => {
    if (
      Object.keys(stepOnEdit).length &&
      stepOnEdit.collapseKey === "no_lotid"
    ) {
      setEditStep(stepOnEdit);
    }
  }, [stepOnEdit]);

  /**
   * Add the entered invalid lotid
   */
  const addToInvalidate = () => {
    const invalid_lotid = form.getFieldValue("invalid_lotid");
    setInvalidatedItems((prevState) => {
      if (!prevState.includes(invalid_lotid)) {
        return [...prevState, invalid_lotid];
      }
      return [...prevState];
    });
  };

  /**
   * When user clicks a tag item
   *
   * @param {string} item The selected splitted item
   */
  const handleInvalidItemsChange = (item, checked) => {
    const selectedItems = checked
      ? [...selectedInvalidatedItems, item]
      : selectedInvalidatedItems.filter((t) => t !== item);
    setSelectedInvalidatedItems(selectedItems);
  };

  /**
   * Generate the current parsing step
   *
   * @param {string} output
   * @returns {object}
   */
  const getStep = (output) => {
    return {
      value: selectedInvalidatedItems,
      label: "No LotID",
      inputValue: currentStepOutput,
      outputValue: output,
      collapseKey: collapseKey,
    };
  };

  /**
   * Execute step thru api
   *
   * @param {function} successCb
   */
  const execStep = (successCb) => {
    Api.executeMetadataParsingSteps(
      (res) => {
        if (res.success) {
          successCb(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        ...pageFilters,
        invalidatedItems: selectedInvalidatedItems,
        ...mainForm.getFieldsValue(true),
        collapseKey: collapseKey,
        inputValue: currentStepOutput,
      },
    );
  };

  /**
   * When user clicks the Show Preview button
   */
  const showStepPreview = () => {
    execStep((data) => {
      const step = getStep(data);
      form.setFieldsValue({ step_preview: step.label });
    });
  };

  /**
   * Reset the fields to default
   */
  const resetFields = () => {
    form.resetFields();
    setAddToInvalidBtnIsDisabled(true);
    setSelectedInvalidatedItems([...defaultInvalidatedItems]);
    setInvalidatedItems([...defaultInvalidatedItems]);
  };

  /**
   * Populate the form
   *
   * @param {object} step
   */
  const setEditStep = (step) => {
    resetFields();
    setInvalidatedItems(step.value);
    setSelectedInvalidatedItems(step.value);
  };

  /**
   * Handles the event where a field changes in a form
   *
   */
  const handleFieldsChange = () => {
    setAddToInvalidBtnIsDisabled(
      form.getFieldValue("invalid_lotid") === "" ||
        form.getFieldValue("invalid_lotid") === undefined,
    );
  };

  /**
   * Add current step to the parsing steps
   */
  const addStep = () => {
    execStep((data) => {
      const step = getStep(data);
      setParsingSteps((prevState) => {
        let newState = [...prevState];
        if (stepOnEditIndex !== null) {
          newState[stepOnEditIndex] = {
            ...step,
            inputValue: newState[stepOnEditIndex].inputValue,
          };
        } else {
          newState = [...prevState, step];
        }
        return newState;
      });
      resetFields();
      setCurrentStepOutput(step.outputValue);
      setStepOnEditIndex(null);
    });
  };

  /**
   * Checks/selects the clicked tag item
   *
   * @param {string} item The tag item
   * @returns {boolean}
   */
  const isChecked = (item) => {
    return (
      defaultInvalidatedItems.includes(item) ||
      selectedInvalidatedItems.includes(item)
    );
  };

  return (
    <Form layout="vertical" form={form} onFieldsChange={handleFieldsChange}>
      <Form.Item
        name="invalid_lotid"
        label="Add Invalid Lot ID"
        className="w-full"
        rules={[alphanumericRule]}
      >
        <Input placeholder="Input Lot ID" />
      </Form.Item>
      <Flex justify="flex-end" align="center">
        <Button
          onClick={addToInvalidate}
          disabled={addToInvalidateBtnIsDisabled}
        >
          Add to Invalidate
        </Button>
      </Flex>
      <Text>Invalidated Lot ID</Text>
      <div className="border border-solid border-gray-300 p-1 min-h-8">
        {invalidatedItems.map((item, idx) => (
          <CheckableTag
            key={`${item}_${idx}`}
            checked={isChecked(item)}
            onChange={(checked) => handleInvalidItemsChange(item, checked)}
            style={{
              backgroundColor: isChecked(item)
                ? token.colorCheckedTagBg
                : token.colorUncheckedTagBg,
            }}
          >
            {item}
            {isChecked(item) && <CheckOutlined className="ml-1!" />}
          </CheckableTag>
        ))}
      </div>
      <Flex justify="flex-end" align="center" className="mt-1.5!">
        <Button onClick={showStepPreview}>Show Preview</Button>
      </Flex>
      <Form.Item name="step_preview" label="Step Preview" className="w-full">
        <Input disabled />
      </Form.Item>
      <Flex justify="flex-end" align="center" className="mt-1.5">
        <Space>
          <Button onClick={resetFields}>Clear All</Button>
          <Button
            type="primary"
            icon={<PlusSquareOutlined />}
            onClick={addStep}
          >
            Add as Step
          </Button>
        </Space>
      </Flex>
    </Form>
  );
};

export default NoLotId;
