"use client";

import { useEffect, useState } from "react";
import { App, Button, Input, Flex, Form, Space, Tag, Typography } from "antd";
import { CheckOutlined, PlusSquareOutlined } from "@ant-design/icons";
import Api from "../../../../../src/utils/api";
import RegularExpressionSuggestionModal from "./regular_expression_suggestion_modal";

const { Text } = Typography;
const { CheckableTag } = Tag;
const { TextArea } = Input;
const collapseKey = "regular_expression";

/**
 * Regular Expression component
 *
 * @param {string} currentStepOutput
 * @param {function} setCurrentStepOutput
 * @param {function} setParsingSteps
 * @param {object} stepOnEdit
 * @param {object} pageFilters
 * @param {object} mainForm
 * @param {object} stepOnEditIndex
 * @param {function} setStepOnEditIndex
 * @returns {JSX.Element}
 */
const RegularExpression = ({
  currentStepOutput = "",
  setCurrentStepOutput,
  setParsingSteps,
  stepOnEdit,
  pageFilters,
  mainForm,
  stepOnEditIndex,
  setStepOnEditIndex,
}) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const [previewBtnDisabled, setPreviewBtnIsDisabled] = useState(true);
  const [
    isRegularExpressionSuggestionModalOpen,
    setIsRegularExpressionSuggestionModalOpen,
  ] = useState(false);
  const [selectedPatternOutput, setSelectedPatternOutput] = useState("");
  const [patternOutput, setPatternOutput] = useState([]);
  const [step, setStep] = useState({});

  useEffect(() => {
    if (
      Object.keys(stepOnEdit).length &&
      stepOnEdit.collapseKey === collapseKey
    ) {
      setEditStep(stepOnEdit);
    }
  }, [stepOnEdit]);

  useEffect(() => {
    form.setFieldsValue({ step_preview: step.label ?? "" });
  }, [step.label]);

  /**
   * Populate the form
   *
   * @param {object} step
   */
  const setEditStep = (step) => {
    form.setFieldsValue(step.value);
    setPreviewBtnIsDisabled(false);
  };

  /**
   * Execute step thru api
   *
   * @param {function} successCb
   */
  const execStep = (successCb) => {
    Api.executeMetadataParsingSteps(
      (res) => {
        if (res.success) {
          successCb(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        ...pageFilters,
        ...form.getFieldsValue(true),
        ...mainForm.getFieldsValue(true),
        collapseKey: collapseKey,
        inputValue: currentStepOutput,
      },
    );
  };

  /**
   * When user clicks the Show Preview button
   */
  const showPreview = () => {
    execStep((data) => {
      setPatternOutput(data);
      if (data.length) {
        // Set first splitted item as default selected tag
        setActiveStep(data[0], 0);
      } else {
        setSelectedPatternOutput("");
        form.setFieldsValue({ step_preview: "" });
      }
    });
  };

  /**
   * Show the pattern suggestions modal
   */
  const showPatternSuggestions = () => {
    setIsRegularExpressionSuggestionModalOpen(true);
  };

  /**
   * Reset the fields to default
   */
  const resetFields = () => {
    setPreviewBtnIsDisabled(true);
    form.resetFields();
    setPatternOutput([]);
  };

  /**
   * Handles the event where a field changes in a form
   *
   */
  const handleFieldsChange = () => {
    const pattern = form.getFieldValue("pattern");
    setPreviewBtnIsDisabled(pattern === undefined || pattern === "");
  };

  /**
   * Add current step to the parsing steps
   */
  const addStep = () => {
    setParsingSteps((prevState) => {
      let newState = [...prevState];
      if (stepOnEditIndex !== null) {
        newState[stepOnEditIndex] = {
          ...step,
          inputValue: newState[stepOnEditIndex].inputValue,
        };
      } else {
        newState = [...prevState, step];
      }
      return newState;
    });
    resetFields();
    setCurrentStepOutput(step.outputValue);
    setStepOnEditIndex(null);
  };

  /**
   * When a pattern output is selected
   *
   * @param {string} item The selected splitted item
   * @param {int} idx The index of the selected item
   */
  const setActiveStep = (item, idx) => {
    setSelectedPatternOutput(item);
    const pattern = form.getFieldValue("pattern");
    setStep({
      value: pattern,
      label: `Regular Expression: '${pattern}'`,
      inputValue: currentStepOutput,
      outputValue: item,
      outputIndex: idx,
      collapseKey: collapseKey,
    });
  };

  return (
    <Form layout="vertical" form={form} onFieldsChange={handleFieldsChange}>
      <Flex justify="space-between" align="center">
        <Form.Item
          name="pattern"
          label="Pattern"
          className="w-full"
          rules={[
            {
              validator: (_, value) => {
                let isValid = true;
                try {
                  new RegExp(value);
                } catch (e) {
                  console.error(e);
                  isValid = false;
                }

                if (!isValid) {
                  return Promise.reject();
                } else {
                  return Promise.resolve();
                }
              },
              message: "Regular expression is invalid.",
            },
          ]}
        >
          <TextArea rows={3} />
        </Form.Item>
      </Flex>
      <Flex justify="flex-end" align="center" className="mt-1.5">
        <Space>
          <RegularExpressionSuggestionModal
            isModalOpen={isRegularExpressionSuggestionModalOpen}
            setIsModalOpen={setIsRegularExpressionSuggestionModalOpen}
          ></RegularExpressionSuggestionModal>
          <Button onClick={showPatternSuggestions}>Pattern Suggestions</Button>
          <Button onClick={showPreview} disabled={previewBtnDisabled}>
            Show Preview
          </Button>
        </Space>
      </Flex>
      <Text>Select Output</Text>
      <div className="border border-solid border-gray-300 p-1 min-h-8">
        {patternOutput.map((output, idx) => (
          <CheckableTag
            key={`${output}_${idx}`}
            checked={selectedPatternOutput === output}
            onChange={() => setActiveStep(output, idx)}
          >
            {output}
            {selectedPatternOutput === output && (
              <CheckOutlined className="ml-1"></CheckOutlined>
            )}
          </CheckableTag>
        ))}
      </div>
      <Form.Item name="step_preview" label="Step Preview">
        <Input disabled />
      </Form.Item>
      <Flex justify="flex-end" align="center" className="mt-1.5">
        <Space>
          <Button onClick={resetFields}>Clear All</Button>
          <Button
            type="primary"
            icon={<PlusSquareOutlined />}
            onClick={addStep}
            disabled={selectedPatternOutput === ""}
          >
            Add as Step
          </Button>
        </Space>
      </Flex>
    </Form>
  );
};

export default RegularExpression;
