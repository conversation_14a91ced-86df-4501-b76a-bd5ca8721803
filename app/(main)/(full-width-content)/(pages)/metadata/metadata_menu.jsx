"use client";

import { useState } from "react";
import { Dropdown, Space, Tooltip, Typography } from "antd";
import { DownOutlined } from "@ant-design/icons";
import Helper from "../../../../../src/utils/helper";
import Api from "../../../../../src/utils/api";
import LoadRecipeModal from "../../../../../src/utils/components/recipe_common/load_recipe_modal";
import ApplyRecipeModal from "../../../../../src/utils/components/recipe_common/apply_recipe_modal";

/**
 * Meta Data menu component
 *
 * @param {function} getSearchTableSelection
 * @param {object} queryClient
 * @param {object} searchGridRef
 * @returns {JSX.Element}
 */
const MetadataMenu = ({
  getSearchTableSelection,
  queryClient,
  searchGridRef,
}) => {
  const [selectedParams, setSelectedParams] = useState({});
  const [isLoadRecipeModalOpen, setIsLoadRecipeModalOpen] = useState(false);
  const [isApplyRecipeModalOpen, setIsApplyRecipeModalOpen] = useState(false);
  const [selectedDlogCount, setSelectedDlogCount] = useState(0);

  /**
   * Renders the metadata page
   *
   * @param {object} selectedParams
   */
  const renderMetadataPage = (selectedParams) => {
    const queryString = Helper.createQueryString(selectedParams);
    Helper.renderPage(
      `#metadata?${queryString}`,
      "metadata_header",
      `#metadata?${queryString}`,
      queryClient,
    );
  };

  const metadataMenuItemHandlers = {
    create_metadata: (selectedParams) => {
      renderMetadataPage(selectedParams);
    },
    load_metadata: () => {
      setIsLoadRecipeModalOpen(true);
    },
    apply_metadata: () => {
      setIsApplyRecipeModalOpen(true);
    },
  };

  /**
   * Handle the metadata menu items
   *
   * @param {string} key
   */
  const generateMetadata = ({ key }) => {
    const selectedParams = getSearchTableSelection();
    if (selectedParams.search_selection) {
      delete selectedParams.search_selection;
    }
    setSelectedParams({ ...selectedParams });
    metadataMenuItemHandlers[key](selectedParams);
  };

  const metadataMenuItems = [
    {
      key: "create_metadata",
      label:
        selectedDlogCount === 1 ? (
          "Create Recipe"
        ) : (
          <Tooltip title="Select one datalog">
            <span>Create Recipe</span>
          </Tooltip>
        ),
      disabled: selectedDlogCount !== 1,
    },
    {
      key: "load_metadata",
      label:
        selectedDlogCount === 1 ? (
          "Load Recipe"
        ) : (
          <Tooltip title="Select one datalog">
            <span>Load Recipe</span>
          </Tooltip>
        ),
      disabled: selectedDlogCount !== 1,
    },
    {
      key: "apply_metadata",
      label:
        selectedDlogCount > 0 ? (
          "Apply Recipe"
        ) : (
          <Tooltip title="Select at least one datalog">
            <span>Apply Recipe</span>
          </Tooltip>
        ),
      disabled: selectedDlogCount === 0,
    },
  ];

  /**
   * Enable/disable menu items based on dlog selection
   */
  const validateMenuItems = () => {
    const selectedParams = getSearchTableSelection();
    if (selectedParams.dsk) {
      setSelectedDlogCount(selectedParams.dsk.length);
    } else {
      setSelectedDlogCount(0);
    }
  };

  return (
    <>
      <LoadRecipeModal
        pageKey="metadata"
        isModalOpen={isLoadRecipeModalOpen}
        setIsModalOpen={setIsLoadRecipeModalOpen}
        selectedParams={selectedParams}
        renderRecipePage={renderMetadataPage}
        fromExternalPage={true}
        getRecipeListApi={Api.getMetadataRecipeList}
        getRecipeVersionApi={Api.getMetadataRecipeVersion}
        getRecipeInfoApi={Api.getMetadataRecipeInfo}
        recipeType="metadata_header"
      />
      <ApplyRecipeModal
        pageKey="metadata"
        isModalOpen={isApplyRecipeModalOpen}
        setIsModalOpen={setIsApplyRecipeModalOpen}
        selectedParams={selectedParams}
        getRecipeListApi={Api.getMetadataRecipeList}
        getRecipeInfoApi={Api.getMetadataRecipeInfo}
        applyRecipeApi={Api.applyMetadataRecipe}
        searchGridRef={searchGridRef}
        requiresRowDataUpdate={true}
        recipeType="metadata_header"
        showNote={true}
      />
      <Dropdown
        menu={{ items: metadataMenuItems, onClick: generateMetadata }}
        trigger="click"
        onClick={validateMenuItems}
      >
        <Typography.Text className="cursor-pointer">
          <Space>
            Meta Data
            <DownOutlined />
          </Space>
        </Typography.Text>
      </Dropdown>
    </>
  );
};

export default MetadataMenu;
