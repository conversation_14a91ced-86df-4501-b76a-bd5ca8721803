"use client";

import { App, But<PERSON>, Empty, Flex, Space, Tag } from "antd";
import {
  CodeSandboxOutlined,
  DeleteOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { useEffect, useState } from "react";
import Api from "../../../../../src/utils/api";

const parsingStepsContainerClasses =
  "border-solid! border! border-gray-300! p-1! overflow-y-auto! overflow-x-hidden! h-96!";

/**
 * Parsing steps component
 *
 * @param {array} parsingSteps
 * @param {function} setParsingSteps
 * @param {function} setStepOnEdit
 * @param {function} setActiveProcedure
 * @param {object} pageFilters
 * @param {object} editForm
 * @param {function} setStepOnEditIndex
 * @param {function} setCurrentStepOutput
 * @returns {JSX.Element}
 */
const MetadataParsingSteps = ({
  parsingSteps,
  setParsingSteps,
  setStepOnEdit,
  setActiveProcedure,
  pageFilters,
  editForm,
  setStepOnEditIndex,
  setCurrentStepOutput,
}) => {
  const { message } = App.useApp();
  const [simulationTime, setSimulationTime] = useState();
  const [stepsBtnsDisabled, setStepsBtnsDisabled] = useState(true);

  useEffect(() => {
    if (simulationTime) {
      simulateSteps();
    }
  }, [simulationTime]);

  useEffect(() => {
    setStepsBtnsDisabled(parsingSteps.length === 1);
  }, [parsingSteps]);

  /**
   * Clear the steps except the first one
   */
  const clearSteps = () => {
    setParsingSteps((prevState) => {
      const newSteps = [...prevState];
      newSteps.length = 1;
      return newSteps;
    });
  };

  /**
   * Delete a parsing step
   *
   * @param {int} key
   */
  const deleteStep = (key) => {
    setParsingSteps((prevState) => {
      return prevState.filter((step, idx) => idx !== key);
    });
    setSimulationTime(Date.now());
  };

  /**
   * Edit a parsing step
   *
   * @param {object} step
   * @param {int} idx
   */
  const editStep = (step, idx) => {
    setActiveProcedure([step.collapseKey]);
    setStepOnEdit({ ...step });
    setStepOnEditIndex(idx);
  };

  /**
   * Simulate the steps
   */
  const simulateSteps = () => {
    Api.simulateMetadataParsingSteps(
      (res) => {
        if (res.success) {
          setCurrentStepOutput(res.data.output_value);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        ...pageFilters,
        parsing_steps: parsingSteps,
        ...editForm.getFieldsValue(),
      },
    );
  };

  return (
    <>
      {parsingSteps.length < 2 ? (
        <Flex
          align="center"
          justify="center"
          className={parsingStepsContainerClasses}
        >
          <Empty description="No parsing steps"></Empty>
        </Flex>
      ) : (
        <Flex vertical className={parsingStepsContainerClasses} gap={"small"}>
          {parsingSteps.map(
            (step, idx) =>
              idx > 0 && (
                <Tag
                  key={`step_${idx}`}
                  color="blue"
                  className="p-2! text-xs! flex! justify-between! w-full!"
                >
                  <p className="break-word! text-wrap! w-11/12!">
                    {step.label}
                  </p>
                  <Space>
                    <EditOutlined
                      onClick={() => {
                        editStep(step, idx);
                      }}
                    />
                    <DeleteOutlined
                      onClick={() => {
                        deleteStep(idx);
                      }}
                    />
                  </Space>
                </Tag>
              ),
          )}
        </Flex>
      )}
      <Flex justify="flex-end" align="center" className="mt-1!">
        <Space>
          <Button onClick={clearSteps} disabled={stepsBtnsDisabled}>
            Clear All Steps
          </Button>
          <Button
            type="primary"
            icon={<CodeSandboxOutlined />}
            onClick={simulateSteps}
            disabled={stepsBtnsDisabled}
          >
            Simulate
          </Button>
        </Space>
      </Flex>
    </>
  );
};

export default MetadataParsingSteps;
