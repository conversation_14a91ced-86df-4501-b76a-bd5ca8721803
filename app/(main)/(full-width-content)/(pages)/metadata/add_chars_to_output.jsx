"use client";

import { Col, Form, Input, Select } from "antd";

const delimiterOptions = [
  {
    value: "_",
    label: "_",
  },
  {
    value: "-",
    label: "-",
  },
  {
    value: ".",
    label: ".",
  },
];

/**
 * Add characters to output component
 *
 * @param {string} addType
 * @param {object} fieldOptions
 * @param {object} fieldData
 * @param {object} form
 * @returns {JSX.Element}
 */
const AddCharsToOutput = ({
  addType = "Prepend",
  fieldOptions,
  fieldData,
  form,
}) => {
  /**
   * When something changes in the append/prepend field select
   *
   * @param {string} value
   */
  const handleFieldChange = (value) => {
    const field = {};
    field[`${addType.toLowerCase()}_value`] = fieldData[value];
    field[`${addType.toLowerCase()}_field`] = value;
    form.setFieldsValue(field);
  };

  return (
    <>
      <Col span={5}>
        <Form.Item label={addType} name={`${addType.toLowerCase()}_field`}>
          <Select
            showSearch
            placeholder="-Select or type-"
            options={fieldOptions}
            onChange={handleFieldChange}
            mode="tags"
            maxCount={1}
          />
        </Form.Item>
      </Col>
      <Col span={5}>
        <Form.Item label="Value to Use" name={`${addType.toLowerCase()}_value`}>
          <Input disabled></Input>
        </Form.Item>
      </Col>
      <Col span={2}>
        <Form.Item
          label="Delimiter"
          name={`${addType.toLowerCase()}_delimiter`}
        >
          <Select placeholder="-Select-" options={delimiterOptions} />
        </Form.Item>
      </Col>
    </>
  );
};

export default AddCharsToOutput;
