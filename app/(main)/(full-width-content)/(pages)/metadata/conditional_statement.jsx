"use client";

import { useEffect, useState } from "react";
import {
  App,
  Button,
  Input,
  Flex,
  Form,
  Space,
  Typography,
  Select,
} from "antd";
import {
  MinusCircleOutlined,
  PlusCircleOutlined,
  PlusSquareOutlined,
} from "@ant-design/icons";
import Api from "../../../../../src/utils/api";
import { alphanumericRule } from "../../../../../src/utils/antd_validation_rules";
const { Text } = Typography;

const collapseKey = "conditional_statement";
const fieldsThatRequireSelectComponent = ["run_type", "manufacturing_process"];
const predefinedList = {
  run_type: "run_type",
  manufacturing_process: "mfg_process",
};

/**
 * Conditional Statement component
 *
 * @param {string} currentStepOutput
 * @param {function} setCurrentStepOutput
 * @param {function} setParsingSteps
 * @param {object} stepOnEdit
 * @param {object} pageFilters
 * @param {object} mainForm
 * @param {string} selectedYHField
 * @param {object} stepOnEditIndex
 * @param {function} setStepOnEditIndex
 * @returns {JSX.Element}
 */
const ConditionalStatement = ({
  currentStepOutput = "",
  setCurrentStepOutput,
  setParsingSteps,
  stepOnEdit,
  pageFilters,
  mainForm,
  selectedYHField,
  stepOnEditIndex,
  setStepOnEditIndex,
}) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const [previewBtnDisabled, setPreviewBtnIsDisabled] = useState(true);
  const [predefinedListOptions, setPredefinedListOptions] = useState([]);
  const [elseIfInput, setElseIfInput] = useState([]);

  useEffect(() => {
    if (
      Object.keys(stepOnEdit).length &&
      stepOnEdit.collapseKey === collapseKey
    ) {
      setEditStep(stepOnEdit);
    }
  }, [stepOnEdit]);

  useEffect(() => {
    if (fieldsThatRequireSelectComponent.includes(selectedYHField)) {
      getYHFieldOptions(selectedYHField);
    }
  }, [selectedYHField]);

  /**
   * Fetch yh field options
   *
   * @param {string} selectedYHField
   */
  const getYHFieldOptions = (selectedYHField) => {
    Api.getPredefinedList(
      predefinedList[selectedYHField],
      (res) => {
        if (res.success) {
          setPredefinedListOptions(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
    );
  };

  /**
   * Populate the form
   *
   * @param {object} step
   */
  const setEditStep = (step) => {
    const conditions = Array.from(Array(step.value.pattern_cond.length).keys());
    conditions.shift();
    setElseIfInput(conditions);
    form.setFieldsValue(step.value);
    setPreviewBtnIsDisabled(false);
  };

  /**
   * Execute step thru api
   *
   * @param {function} successCb
   */
  const execStep = (successCb) => {
    Api.executeMetadataParsingSteps(
      (res) => {
        if (res.success) {
          successCb(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        ...pageFilters,
        ...form.getFieldsValue(true),
        ...mainForm.getFieldsValue(true),
        collapseKey: collapseKey,
        inputValue: currentStepOutput,
      },
    );
  };

  /**
   * Get the step
   *
   * @param {string} output
   * @returns {object} Current step object
   */
  const getStep = (output) => {
    const conditions = form.getFieldsValue(true);
    const ifConditions = [...conditions["pattern_cond"]];
    ifConditions.shift();
    const elseIfs = ifConditions
      .map((cond, idx) => {
        return `else if '${cond}' then value is '${conditions["pattern_result"][idx + 1]}'`;
      })
      .join(" ");
    const ifElse = `If '${conditions["pattern_cond"][0]}' then value is '${conditions["pattern_result"][0]}' ${elseIfs} else '${conditions["else_value"]}'`;

    return {
      value: conditions,
      label: `Conditional Statement: ${ifElse} `,
      inputValue: currentStepOutput,
      outputValue: output,
      collapseKey: collapseKey,
    };
  };

  /**
   * When user clicks the Show Preview button
   */
  const showPreview = () => {
    execStep((data) => {
      const step = getStep(data);
      form.setFieldsValue({
        output_preview: step.outputValue,
        step_preview: step.label,
      });
    });
  };

  /**
   * Reset the fields to default
   */
  const resetFields = () => {
    setPreviewBtnIsDisabled(true);
    setElseIfInput([]);
    form.resetFields();
  };

  /**
   * Handles the event where a field changes in a form
   */
  const handleFieldsChange = () => {
    const fieldsValue = form.getFieldsValue();
    setPreviewBtnIsDisabled(
      !fieldsValue.pattern_cond[0] ||
        !fieldsValue.pattern_result[0] ||
        !fieldsValue.else_value,
    );
  };

  /**
   * Add else if condition
   */
  const addCondition = () => {
    setElseIfInput((prevState) => {
      if (prevState.length < 1) {
        return [1];
      }
      return [...prevState, prevState[prevState.length - 1] + 1];
    });
  };

  /**
   * Remove else if condition
   *
   * @param {int} key
   */
  const removeCondition = (key) => {
    setElseIfInput((prevState) => {
      const newState = [...prevState];
      return newState.filter((state) => state !== key);
    });
  };

  /**
   * Add current step to the parsing steps
   */
  const addStep = () => {
    execStep((data) => {
      const step = getStep(data);
      setParsingSteps((prevState) => {
        let newState = [...prevState];
        if (stepOnEditIndex !== null) {
          newState[stepOnEditIndex] = {
            ...step,
            inputValue: newState[stepOnEditIndex].inputValue,
          };
        } else {
          newState = [...prevState, step];
        }
        return newState;
      });
      resetFields();
      setCurrentStepOutput(data);
      setStepOnEditIndex(null);
    });
  };

  return (
    <Form layout="vertical" form={form} onFieldsChange={handleFieldsChange}>
      <Flex justify="flex-start" align="center">
        <MinusCircleOutlined className="invisible mr-1"></MinusCircleOutlined>
        <Form.Item name={["pattern_cond", 0]} label="If" className="w-1/2">
          <Input />
        </Form.Item>
        <Space className="pr-1 pl-1">
          <Text>=</Text>
        </Space>
        <Form.Item
          name={["pattern_result", 0]}
          label="Then Value is"
          className="w-2/5"
          rules={[alphanumericRule]}
        >
          {fieldsThatRequireSelectComponent.includes(selectedYHField) ? (
            <Select options={predefinedListOptions} showSearch></Select>
          ) : (
            <Input />
          )}
        </Form.Item>
      </Flex>
      {elseIfInput.map((item) => (
        <Flex key={`elseif_${item}`} justify="flex-start" align="center">
          <MinusCircleOutlined
            onClick={() => removeCondition(item)}
            className="mr-1 text-red-500"
          ></MinusCircleOutlined>
          <Form.Item
            name={["pattern_cond", item]}
            label="Else If"
            className="w-1/2"
          >
            <Input />
          </Form.Item>
          <Space className="pr-1 pl-1">
            <Text>=</Text>
          </Space>
          <Form.Item
            name={["pattern_result", item]}
            label="Then Value is"
            className="w-2/5"
            rules={[alphanumericRule]}
          >
            {fieldsThatRequireSelectComponent.includes(selectedYHField) ? (
              <Select options={predefinedListOptions} showSearch></Select>
            ) : (
              <Input />
            )}
          </Form.Item>
        </Flex>
      ))}
      <Flex justify="flex-start" align="center">
        <MinusCircleOutlined className="invisible mr-1"></MinusCircleOutlined>
        <Form.Item name="else_value" label="Else" className="w-1/2">
          {fieldsThatRequireSelectComponent.includes(selectedYHField) ? (
            <Select options={predefinedListOptions} showSearch></Select>
          ) : (
            <Input />
          )}
        </Form.Item>
      </Flex>
      <Flex justify="space-between" align="center">
        <Button
          icon={<PlusCircleOutlined style={{ color: "seagreen" }} />}
          onClick={addCondition}
        >
          Add Another Condition
        </Button>
        <Button onClick={showPreview} disabled={previewBtnDisabled}>
          Show Preview
        </Button>
      </Flex>
      <Form.Item
        name="output_preview"
        label="Output Preview"
        className="w-full mt-1.5"
      >
        <Input disabled />
      </Form.Item>
      <Form.Item name="step_preview" label="Step Preview" className="w-full">
        <Input disabled />
      </Form.Item>
      <Flex justify="flex-end" align="center" className="mt-1.5">
        <Space>
          <Button onClick={resetFields}>Clear All</Button>
          <Button
            type="primary"
            icon={<PlusSquareOutlined />}
            onClick={addStep}
            disabled={previewBtnDisabled}
          >
            Add as Step
          </Button>
        </Space>
      </Flex>
    </Form>
  );
};

export default ConditionalStatement;
