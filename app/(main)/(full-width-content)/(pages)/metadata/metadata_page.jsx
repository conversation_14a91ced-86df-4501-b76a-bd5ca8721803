"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>lap<PERSON>, Flex, theme } from "antd";
import { CheckCircleOutlined, FolderOpenOutlined } from "@ant-design/icons";
import DatalogInfoGrid from "../../../../../src/utils/components/recipe_common/datalog_info_grid";
import Helper from "../../../../../src/utils/helper";
import PagesLayout from "../layout";
import LoadRecipeModal from "../../../../../src/utils/components/recipe_common/load_recipe_modal";
import ApplyRecipeModal from "../../../../../src/utils/components/recipe_common/apply_recipe_modal";
import Api from "../../../../../src/utils/api";
import RecipeInfoGrid from "../../../../../src/utils/components/recipe_common/recipe_info_grid";
import MetadataSimulationOutput from "./metadata_simulation_output";
import MetadataRecipe from "./metadata_recipe";

/**
 * Metadata page component
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
const MetadataPage = ({ pageKey }) => {
  const { token } = theme.useToken();
  const panelStyle = {
    background: token.colorBgTextHover,
    marginBottom: 16,
    borderRadius: token.yhCollapseBorderRadius,
    border: token.yhCollapseBorder,
  };
  const [pageFilters, setPageFilters] = useState({});
  const [selectedParams, setSelectedParams] = useState({});
  const [gridFilters, setGridFilters] = useState({});
  const [simulationGridFilters, setSimulationGridFilters] = useState({});
  const [activeCollapseKey, setActiveCollapseKey] = useState([
    "recipe",
    "dlog_info",
  ]);
  const [simulationIsMade, setSimulationIsMade] = useState(false);
  const [isLoadRecipeModalOpen, setIsLoadRecipeModalOpen] = useState(false);
  const [isApplyRecipeModalOpen, setIsApplyRecipeModalOpen] = useState(false);
  const [recipeInfoPanelStyle, setRecipeInfoPanelStyle] = useState({
    ...panelStyle,
    display: "none",
  });
  const [simulationPanelStyle, setSimulationPanelStyle] = useState({
    ...panelStyle,
    display: "none",
  });
  const [activeRecipeName, setActiveRecipeName] = useState("");
  const [simulationCollapseLabel, setSimulationCollapseLabel] =
    useState("Simulation Output");
  const [recipeData, setRecipeData] = useState({});
  const [recipeVersion, setRecipeVersion] = useState("");

  useEffect(() => {
    try {
      const pageFilters = Helper.getUrlParameters() || {};
      setPageFilters(pageFilters);

      // Convert comma-separated strings to arrays for API endpoints
      const params = Object.fromEntries(
        Object.entries(pageFilters).map(([key, value]) => [
          key,
          value ? value.split(",") : [],
        ]),
      );
      setSelectedParams(params);

      // Set up filters for both grids
      const filters = { [pageKey]: pageFilters };
      setGridFilters(filters);
      setSimulationGridFilters(filters);
      setRecipeVersion(pageFilters.recipe_version || "");
    } catch (error) {
      console.error("Error while setting page filters:", error);
    }
  }, [pageKey]);

  useEffect(() => {
    setRecipeInfoPanelStyle(
      activeRecipeName ? panelStyle : { ...panelStyle, display: "none" },
    );
  }, [activeRecipeName]);

  useEffect(() => {
    setSimulationPanelStyle(
      simulationIsMade ? panelStyle : { ...panelStyle, display: "none" },
    );
  }, [simulationIsMade]);

  /**
   * When user clicks the collapse header
   *
   * @param {string} key
   */
  const handleCollapseChange = (key) => {
    setActiveCollapseKey(key);
  };

  /**
   * Opens the load recipe modal
   */
  const loadRecipe = () => {
    setIsLoadRecipeModalOpen(true);
  };

  /**
   * Opens the apply recipe modal
   */
  const applyRecipe = () => {
    setIsApplyRecipeModalOpen(true);
  };

  return (
    <PagesLayout>
      <LoadRecipeModal
        pageKey="metadata"
        isModalOpen={isLoadRecipeModalOpen}
        setIsModalOpen={setIsLoadRecipeModalOpen}
        selectedParams={pageFilters}
        fromExternalPage={false}
        getRecipeListApi={Api.getMetadataRecipeList}
        getRecipeVersionApi={Api.getMetadataRecipeVersion}
        getRecipeInfoApi={Api.getMetadataRecipeInfo}
        getRecipeDataApi={Api.getRecipeData}
        setRecipeData={setRecipeData}
        recipeType="metadata_header"
        setActiveRecipeName={setActiveRecipeName}
        setRecipeVersion={setRecipeVersion}
      />
      <ApplyRecipeModal
        pageKey="metadata"
        isModalOpen={isApplyRecipeModalOpen}
        setIsModalOpen={setIsApplyRecipeModalOpen}
        selectedParams={selectedParams}
        getRecipeListApi={Api.getMetadataRecipeList}
        getRecipeInfoApi={Api.getMetadataRecipeInfo}
        applyRecipeApi={Api.applyMetadataRecipe}
        recipeType="metadata_header"
        activeRecipeName={activeRecipeName}
        showNote={true}
      />
      <Flex wrap="wrap" gap="middle" className="min-h-10">
        <Button icon={<FolderOpenOutlined />} onClick={loadRecipe}>
          Load a Recipe
        </Button>
        <Button icon={<CheckCircleOutlined />} onClick={applyRecipe}>
          Apply Recipe to Selected Datalog
        </Button>
      </Flex>
      <Collapse
        items={[
          {
            key: "dlog_info",
            label: "Selected Datalogs Information",
            style: panelStyle,
            children: (
              <DatalogInfoGrid
                pageKey={pageKey}
                pageFilters={pageFilters}
                gridId="metadata_dlog_info"
              />
            ),
          },
          {
            key: "recipe_info",
            label: `Recipe Information - ${activeRecipeName}`,
            style: recipeInfoPanelStyle,
            children: (
              <RecipeInfoGrid
                pageKey={pageKey}
                recipeName={activeRecipeName}
                recipeType="metadata_header"
                recipeVersion={recipeVersion}
              />
            ),
          },
          {
            key: "recipe",
            label: "Meta Data Recipe",
            style: panelStyle,
            children: (
              <MetadataRecipe
                pageKey={pageKey}
                gridFilters={gridFilters}
                setSimulationGridFilters={setSimulationGridFilters}
                setActiveCollapseKey={setActiveCollapseKey}
                setSimulationIsMade={setSimulationIsMade}
                recipeData={recipeData}
                activeRecipeName={activeRecipeName}
                setActiveRecipeName={setActiveRecipeName}
                setSimulationCollapseLabel={setSimulationCollapseLabel}
              />
            ),
          },
          {
            key: "simulation_output",
            label: simulationCollapseLabel,
            style: simulationPanelStyle,
            children: (
              <MetadataSimulationOutput
                pageKey={pageKey}
                gridFilters={simulationGridFilters}
              />
            ),
          },
        ]}
        activeKey={activeCollapseKey}
        onChange={handleCollapseChange}
        style={{
          background: token.yhPageColorBg,
          border: token.yhTransparentBorder,
        }}
      ></Collapse>
    </PagesLayout>
  );
};

export default MetadataPage;
