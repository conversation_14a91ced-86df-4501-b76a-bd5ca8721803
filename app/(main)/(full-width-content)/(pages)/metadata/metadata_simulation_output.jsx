"use client";

import { App } from "antd";
import { useEffect, useRef, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import YHGrid from "../../../../../src/utils/grid/yh_grid";
import Api from "../../../../../src/utils/api";
import { QueryKeys } from "../../../../../src/utils/query_keys";

const gridId = "metadata_header_simulation_table";

/**
 * Metadata simulation grid component
 *
 * @param {string} pageKey
 * @param {object} gridFilters
 * @returns {JSX.Element}
 */
const MetadataSimulationOutput = ({ pageKey, gridFilters }) => {
  const [gridComponent, setGridComponent] = useState();
  const [gridKey, setGridKey] = useState(gridId);
  const gridRef = useRef();
  const { message } = App.useApp();

  useEffect(() => {
    // console.log("simulation filters:", gridFilters);
    let stepKey = "";
    const baseKey = Object.values(gridFilters[pageKey])
      .filter((filter) => typeof filter === "string")
      .join("");
    if (typeof gridFilters[pageKey].steps !== "undefined") {
      stepKey = Object.keys(gridFilters[pageKey].steps).join("");
      // .map((step) => step)
      // .join("_");
    }

    setGridKey(baseKey + stepKey);
  }, [gridFilters]);

  /**
   * Gets simulation grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: gridId,
      },
    );

    return abortCtl;
  };

  useQuery({
    queryKey: QueryKeys.grid(pageKey, gridKey),
    queryFn: getGridComponent,
    // refetchOnWindowFocus: false,
    // enabled: false,
  });

  return (
    <div className="h-[35vh]">
      {gridComponent && (
        <YHGrid
          key={gridKey}
          gridRef={gridRef}
          gridId={gridId}
          component={gridComponent}
          pageKey={pageKey}
          filters={gridFilters}
          wrapperClassName="flex grow flex-col h-full"
        />
      )}
    </div>
  );
};

export default MetadataSimulationOutput;
