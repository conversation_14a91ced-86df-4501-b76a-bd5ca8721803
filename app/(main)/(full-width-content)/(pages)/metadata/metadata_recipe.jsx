"use client";

import { App } from "antd";
import { useEffect, useRef, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { capitalize } from "lodash";
import Api from "../../../../../src/utils/api";
import { QueryKeys } from "../../../../../src/utils/query_keys";
import YHGrid from "../../../../../src/utils/grid/yh_grid";
import EditedOutputYHFieldRenderer from "../../../../../src/utils/grid/grid_cell_renderers/editedOutputYHFieldRenderer";
import EditOutputModal from "./edit_output_modal";
import SaveRecipeModal from "./save_recipe_modal";

const gridId = "metadata_header_fields_table";

/**
 * Metadata recipe section component
 *
 * @param {string} pageKey
 * @param {object} gridFilters
 * @param {function} setSimulationGridFilters
 * @param {function} setActiveCollapseKey
 * @param {function} setSimulationIsMade
 * @param {array} recipeData
 * @param {string} activeRecipeName
 * @param {function} setActiveRecipeName
 * @param {function} setSimulationCollapseLabel
 * @returns {JSX.Element}
 */
const MetadataRecipe = ({
  pageKey,
  gridFilters,
  setSimulationGridFilters,
  setActiveCollapseKey,
  setSimulationIsMade,
  recipeData,
  activeRecipeName,
  setActiveRecipeName,
  setSimulationCollapseLabel,
}) => {
  const [gridComponent, setGridComponent] = useState();
  const [isEditOutputModalOpen, setIsEditOutputModalOpen] = useState(false);
  const [isSaveRecipeModalOpen, setIsSaveRecipeModalOpen] = useState(false);
  const [currentStepOutput, setCurrentStepOutput] = useState("yieldHub");
  const [parsingSteps, setParsingSteps] = useState([]);
  const [recipeSteps, setRecipeSteps] = useState({});
  const [initialValues, setInitialValues] = useState({});
  const [fieldData, setFieldData] = useState({});
  const [loadedRecipeData, setLoadedRecipeData] = useState({});
  const [fieldOptions, setFieldOptions] = useState([]);
  const [includedAlgorithmOptions, setIncludedAlgorithmOptions] = useState([]);
  const [selectedYHField, setSelectedYHField] = useState("");
  const gridRef = useRef();
  const { message } = App.useApp();

  useEffect(() => {
    setRecipeData(recipeData);
  }, [recipeData]);

  /**
   * Get the grid component blueprint
   *
   * @returns {AbortController} abortCtl
   */
  const getGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: gridId,
      },
    );

    return abortCtl;
  };

  useQuery({
    queryKey: QueryKeys.grid(pageKey, gridId),
    queryFn: getGridComponent,
  });

  /**
   * Edit the output value
   */
  const editOutputValue = () => {
    const selectedData = gridRef.current.api.getSelectedRows();
    // console.log("Selected Row Data:", selectedData);
    if (selectedData.length === 0) {
      message.open({
        type: "error",
        content: "Select one row to continue.",
      });
      return;
    }
    setCurrentStepOutput(selectedData[0].output);
    setSelectedYHField(selectedData[0].key);
    const initialValues = {
      source_value: selectedData[0].output,
      yh_field: selectedData[0].key,
      source_field: selectedData[0].key,
      yh_field_display: selectedData[0].field,
      simulated_output_value: selectedData[0].output,
    };

    // Load the applied steps if available, else load the initial step
    if (recipeSteps[selectedData[0].key]) {
      setParsingSteps(recipeSteps[selectedData[0].key]);
      initialValues.source_field =
        recipeSteps[selectedData[0].key][0].sourceField;
    } else {
      setParsingSteps([
        {
          label: `Set source field to ${selectedData[0].field}`,
          value: selectedData[0].output,
          inputValue: selectedData[0].output,
          outputValue: selectedData[0].output,
          sourceField: selectedData[0].key,
          collapseKey: "switch_data_source",
        },
      ]);
    }
    setInitialValues(initialValues);
    setIsEditOutputModalOpen(true);
  };

  /**
   * Delete the edited output value (parsing steps if any) of the selected field
   */
  const deleteOutputValue = () => {
    const selectedData = gridRef.current.api.getSelectedRows();
    if (selectedData.length === 0) {
      message.open({
        type: "error",
        content: "Select one row to continue.",
      });
      return;
    }
    setRecipeSteps((prevState) => {
      const newState = { ...prevState };
      delete newState[selectedData[0].key];
      return newState;
    });
    gridRef.current.api.forEachNode((node) => {
      if (node.data.key === selectedData[0].key) {
        node.setData({
          ...node.data,
          output: fieldData[node.data.key],
          outputIsEdited: false,
        });
      }
    });
  };

  /**
   * Simulate the recipe
   *
   * @param {string} simulation_type
   */
  const simulateRecipe = (simulation_type) => {
    setActiveCollapseKey((prevState) => {
      if (prevState.includes("simulation_output")) {
        return prevState;
      }
      return [...prevState, "simulation_output"];
    });
    const filters = {};
    filters[pageKey] = {
      ...gridFilters[pageKey],
      ...{ steps: recipeSteps },
      simulate_by: simulation_type,
    };
    setSimulationGridFilters(filters);
    setSimulationIsMade(true);
    setSimulationCollapseLabel(
      `Simulation Output By ${capitalize(simulation_type)}`,
    );
  };

  /**
   * Opens the save recipe modal
   */
  const openSaveRecipeModal = () => {
    const algoKeys = Object.keys(recipeSteps);
    setIncludedAlgorithmOptions(
      fieldOptions.filter((option) => algoKeys.includes(option.value)),
    );
    setIsSaveRecipeModalOpen(true);
  };

  /**
   * Set the cell values of the edited fields
   *
   * @param {object} recipeData
   */
  const setEditedRowValues = (recipeData) => {
    gridRef.current.api.forEachNode((node) => {
      const algoKeys = Object.keys(recipeData.algos);
      // Reset the edited output columns
      if (node.data.outputIsEdited) {
        const rowData = { ...node.data, output: node.data.field_source };
        delete rowData.outputIsEdited;
        node.updateData(rowData);
      }
      if (algoKeys.includes(node.data.key)) {
        const algo = recipeData.algos[node.data.key];
        const newData = {
          ...node.data,
          output: algo[algo.length - 1].outputValue,
          outputIsEdited: true,
        };
        node.updateData(newData);
      }
    });
  };

  /**
   * Set the data of the loaded recipe
   *
   * @param {object} recipeData
   */
  const setRecipeData = (recipeData) => {
    if (Object.keys(recipeData).length) {
      setRecipeSteps(recipeData.algos);
      setEditedRowValues(recipeData);
      setLoadedRecipeData(recipeData);
    }
  };

  /**
   * Get the recipe data
   */
  const getRecipeData = () => {
    Api.getRecipeData(
      "metadata_header",
      (res) => {
        if (res.success) {
          setRecipeData(res.data);
          setActiveRecipeName(res.data.recipe_name);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      gridFilters[pageKey],
    );
  };

  /**
   * Callback when the grid sets the row data
   *
   * @param {object} data
   */
  const handlePostReload = (data) => {
    const fieldData = {};
    const fieldOptions = [];
    data.table_data.forEach((obj) => {
      const key = obj.key ?? obj.field;
      fieldData[key] = obj.field_source;
      fieldOptions.push({
        value: key,
        label: obj.field,
      });
    });
    setFieldOptions(fieldOptions);
    setFieldData(fieldData);
    // Get the data of the recipe from the url
    if (typeof gridFilters[pageKey].recipe_name !== "undefined") {
      getRecipeData();
    }
  };

  const eventHandlers = {
    simulateMetadataRecipe: simulateRecipe,
    deleteMetadataOutputValue: deleteOutputValue,
    editMetadataOutputValue: editOutputValue,
    saveMetadataRecipe: openSaveRecipeModal,
    handlePostReload: handlePostReload,
  };

  const cellRendererSelectors = {
    editedOutputYHFieldColumn: (params) => {
      if (params.data.outputIsEdited) {
        return {
          component: EditedOutputYHFieldRenderer,
        };
      }
      return undefined;
    },
  };

  return (
    <>
      <EditOutputModal
        isModalOpen={isEditOutputModalOpen}
        setIsModalOpen={setIsEditOutputModalOpen}
        currentStepOutput={currentStepOutput}
        setCurrentStepOutput={setCurrentStepOutput}
        parsingSteps={parsingSteps}
        setParsingSteps={setParsingSteps}
        pageFilters={gridFilters[pageKey]}
        initialValues={initialValues}
        fieldOptions={fieldOptions}
        fieldData={fieldData}
        setRecipeSteps={setRecipeSteps}
        gridRef={gridRef}
        selectedYHField={selectedYHField}
      ></EditOutputModal>
      <SaveRecipeModal
        isModalOpen={isSaveRecipeModalOpen}
        setIsModalOpen={setIsSaveRecipeModalOpen}
        pageFilters={gridFilters[pageKey]}
        recipeType="metadata_header"
        includedAlgorithmOptions={includedAlgorithmOptions}
        algos={recipeSteps}
        loadedRecipeData={loadedRecipeData}
        setLoadedRecipeData={setLoadedRecipeData}
        activeRecipeName={activeRecipeName}
        setActiveRecipeName={setActiveRecipeName}
      ></SaveRecipeModal>
      <div className="h-[75vh]">
        {gridComponent && (
          <YHGrid
            gridRef={gridRef}
            gridId={gridId}
            component={gridComponent}
            pageKey={pageKey}
            filters={gridFilters}
            wrapperClassName="flex grow flex-col h-full"
            eventHandlers={eventHandlers}
            cellRendererSelectors={cellRendererSelectors}
          />
        )}
      </div>
    </>
  );
};

export default MetadataRecipe;
