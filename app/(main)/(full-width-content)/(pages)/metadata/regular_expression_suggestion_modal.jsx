import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from "antd";
const { Text } = Typography;

const columns = [
  {
    title: "Sample Code",
    dataIndex: "code",
    key: "code",
  },
  {
    title: "Description and examples",
    dataIndex: "desc",
    key: "desc",
  },
];

const data = [
  {
    key: "1",
    code: "[abc]",
    desc: "Find any characters between the brackets",
  },
  {
    key: "2",
    code: "[0-9]",
    desc: "Find any of the digits between the brackets",
  },
  {
    key: "3",
    code: "(x|y)",
    desc: "Find any of the alternatives separated with |",
  },
  {
    key: "4",
    code: "\\d",
    desc: "Find a digit",
  },
  {
    key: "5",
    code: "\\s",
    desc: "Find a whitespace character",
  },
  {
    key: "6",
    code: "\\b",
    desc: "Find a match at the beginning of a word e.g. \\bWORD, or at the end of a word e.g. WORD\\b",
  },
  {
    key: "7",
    code: "\\uxxxx",
    desc: "Find the Unicode character specified by the hex number xxxx",
  },
  {
    key: "8",
    code: "n+",
    desc: "Matches any string that contains at least one n",
  },
  {
    key: "9",
    code: "n*",
    desc: "Matches any string that contains zero or more occurrences of n",
  },
  {
    key: "10",
    code: "n?",
    desc: "Matches any string that contains zero or one occurrence of n",
  },
  {
    key: "11",
    code: "i",
    desc: "Makes the whole expression case-insensitive (e.g. /aBc/i  would match AbC)",
  },
  {
    key: "12",
    code: "g",
    desc: "Perform a global match (find all matches rather than stopping after the first match)",
  },
];

/**
 * Regular Expression suggestion modal component
 *
 * @param {boolean} isModalOpen
 * @param {setIsModalOpen} setIsModalOpen
 * @returns {JSX.Element}
 */
const RegularExpressionSuggestionModal = ({ isModalOpen, setIsModalOpen }) => {
  /**
   * When modal is cancelled
   */
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  /**
   * When ok button is clicked
   */
  const handleOk = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal
      title="Pattern Suggestions"
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      width={"50vw"}
      footer={[
        <Button key="submit" type="primary" onClick={handleOk}>
          Ok
        </Button>,
      ]}
    >
      <Text strong>Suggested writing patterns for regular expression</Text>
      <Table columns={columns} dataSource={data} pagination={false} />
    </Modal>
  );
};

export default RegularExpressionSuggestionModal;
