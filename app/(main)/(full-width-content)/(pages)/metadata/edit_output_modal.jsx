"use client";

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Config<PERSON><PERSON><PERSON>,
  Di<PERSON>r,
  Flex,
  Form,
  Input,
  Modal,
  Row,
  Select,
  theme,
  Typography,
} from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
const { Text } = Typography;
import { useEffect, useState } from "react";
import <PERSON><PERSON>yDelimiter from "./split_by_delimiter";
import ConditionalStatement from "./conditional_statement";
import ReplaceCharacters from "./replace_characters";
import RegularExpression from "./regular_expression";
import NoLotId from "./no_lotid";
import MetadataParsingSteps from "./metadata_parsing_steps";
import AddCharsToOutput from "./add_chars_to_output";

/**
 * Edit Output Value modal component
 *
 * @param {boolean} isModalOpen
 * @param {function} setIsModalOpen
 * @param {string} currentStepOutput
 * @param {function} setCurrentStepOutput
 * @param {object} parsingSteps
 * @param {function} setParsingSteps
 * @param {object} pageFilters
 * @param {object} initialValues
 * @param {array} fieldOptions
 * @param {object} fieldData
 * @param {function} setRecipeSteps
 * @param {object} gridRef
 * @param {string} selectedYHField
 * @returns {JSX.Element}
 */
const EditOutputModal = ({
  isModalOpen,
  setIsModalOpen,
  currentStepOutput,
  setCurrentStepOutput,
  parsingSteps,
  setParsingSteps,
  pageFilters,
  initialValues,
  fieldOptions,
  fieldData,
  setRecipeSteps,
  gridRef,
  selectedYHField,
}) => {
  const { token } = theme.useToken();
  const [form] = Form.useForm();
  const [activeCollapseKey, setActiveCollapseKey] = useState([
    "split_by_delimiter",
  ]);
  const [sourceValue, setSourceValue] = useState("");
  const [stepOnEdit, setStepOnEdit] = useState({});
  const [stepOnEditIndex, setStepOnEditIndex] = useState(null);
  const [isFieldSelectDisabled, setIsFieldSelectDisabled] = useState(false);
  const [{ confirm }, contextHolder] = Modal.useModal();

  // Reset all state values when modal opens or closes
  useEffect(() => {
    if (isModalOpen) {
      // Reset form and state values when modal opens
      form.resetFields();
      setSourceValue("");
      setStepOnEdit({});
      setStepOnEditIndex(null);
      setActiveCollapseKey(["split_by_delimiter"]);

      // Then set new values from initialValues
      if (initialValues) {
        form.setFieldsValue(initialValues);
        if (initialValues.source_value) {
          setSourceValue(initialValues.source_value);
        }
      }
    }
  }, [isModalOpen, initialValues]);

  useEffect(() => {
    setIsFieldSelectDisabled(parsingSteps.length > 1);
  }, [parsingSteps.length]);

  /**
   * Apply the edited output
   */
  const handleApplyOutput = () => {
    const steps = {};
    steps[selectedYHField] = parsingSteps;
    steps[selectedYHField].use_source_value =
      form.getFieldValue("use_source_value");
    setRecipeSteps((prevState) => {
      return { ...prevState, ...steps };
    });
    setParsingSteps((prevState) => {
      const newSteps = [...prevState];
      newSteps.length = 1;
      return newSteps;
    });
    gridRef.current.api.forEachNode((node) => {
      if (node.data.key === selectedYHField) {
        // Might not be reliable getting from currentStepOutput
        const newData = {
          ...node.data,
          output: currentStepOutput,
          outputIsEdited: true,
        };
        node.setData(newData);
      }
    });
    setIsModalOpen(false);
  };

  /**
   * Resets the procedures and parsing steps
   */
  const handleReset = () => {
    form.resetFields();
    setParsingSteps((prevState) => {
      const newSteps = [...prevState];
      newSteps.length = 1;
      return newSteps;
    });
  };

  /**
   * Handler when user clicks the collapse header
   *
   * @param {array} key
   */
  const handleCollapseChange = (key) => {
    if (key.length) {
      setActiveCollapseKey(key);
      setStepOnEditIndex(null);
    }
  };

  /**
   * Set the values of the output fields
   *
   * @param {string} value
   * @param {string} sourceField
   */
  const setOutputValues = (value, sourceField) => {
    setSourceValue(value);
    setCurrentStepOutput(value);
    setParsingSteps([
      {
        label: `Set source field to ${sourceField}`,
        value: value,
        inputValue: value,
        outputValue: value,
        collapseKey: "switch_data_source",
        sourceField: form.getFieldValue("source_field"),
        useSourceValue: form.getFieldValue("use_source_value"),
      },
    ]);
  };

  /**
   * Handles the event where a field changes in the form
   *
   * @param {array} changedFields
   */
  const handleFieldsChange = (changedFields) => {
    // If user selects another source field
    if (changedFields[0].name.includes("source_field")) {
      const sourceValue = fieldData[changedFields[0].value];
      setOutputValues(sourceValue, changedFields[0].value);
      // If user checks/unchecks the use source value checkbox
    } else if (changedFields[0].name.includes("use_source_value")) {
      const sourceField = form.getFieldValue("source_field");
      if (changedFields[0].value) {
        setOutputValues(fieldData[sourceField], sourceField);
      } else {
        // Find the edited output of the field
        gridRef.current.api.forEachNode((node) => {
          if (node.data.key === sourceField) {
            setOutputValues(node.data.output, sourceField);
          }
        });
      }
    }
  };

  /**
   * Confirmation modal when user closes the modal without applying
   */
  const confirmCloseDialog = () => {
    const confirmModal = confirm({
      title: "Leave Without Applying Edited Output",
      width: 480,
      icon: <InfoCircleOutlined style={{ color: "rgb(252 211 77)" }} />,
      content:
        "Are you sure you want to leave without applying your edited output value? All unsaved changes will be lost",
      footer: (
        <Flex justify="right" gap="small" className="mt-3">
          <Button
            key="back"
            onClick={() => {
              confirmModal.destroy();
            }}
          >
            No
          </Button>
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              confirmModal.destroy();
              cancelModal();
            }}
          >
            Yes
          </Button>
        </Flex>
      ),
    });
  };

  const cancelModal = () => {
    setParsingSteps((prevState) => {
      const newSteps = [...prevState];
      newSteps.length = 1;
      return newSteps;
    });
    setIsModalOpen(false);
  };

  /**
   * Handle whether to show the confirmation to close the modal or close the modal immediately
   */
  const handleCancel = () => {
    form.getFieldValue("source_field") !== selectedYHField ||
    parsingSteps.length > 1
      ? confirmCloseDialog()
      : cancelModal();
  };

  const panelStyle = {
    background: token.colorPrimaryBg,
    marginBottom: 8,
    borderRadius: token.yhCollapseBorderRadius,
    border: token.yhCollapseBorder,
  };

  const procedureItems = [
    {
      key: "split_by_delimiter",
      label: "Split By Delimiter",
      style: panelStyle,
      children: (
        <SplitByDelimiter
          currentStepOutput={currentStepOutput}
          setCurrentStepOutput={setCurrentStepOutput}
          setParsingSteps={setParsingSteps}
          stepOnEdit={stepOnEdit}
          stepOnEditIndex={stepOnEditIndex}
          setStepOnEditIndex={setStepOnEditIndex}
          pageFilters={pageFilters}
          mainForm={form}
        ></SplitByDelimiter>
      ),
    },
    {
      key: "conditional_statement",
      label: "Conditional Statement",
      style: panelStyle,
      children: (
        <ConditionalStatement
          currentStepOutput={currentStepOutput}
          setCurrentStepOutput={setCurrentStepOutput}
          setParsingSteps={setParsingSteps}
          stepOnEdit={stepOnEdit}
          pageFilters={pageFilters}
          mainForm={form}
          selectedYHField={selectedYHField}
          stepOnEditIndex={stepOnEditIndex}
          setStepOnEditIndex={setStepOnEditIndex}
        ></ConditionalStatement>
      ),
    },
    {
      key: "replace_characters",
      label: "Replace Characters",
      style: panelStyle,
      children: (
        <ReplaceCharacters
          currentStepOutput={currentStepOutput}
          setCurrentStepOutput={setCurrentStepOutput}
          setParsingSteps={setParsingSteps}
          stepOnEdit={stepOnEdit}
          stepOnEditIndex={stepOnEditIndex}
          setStepOnEditIndex={setStepOnEditIndex}
          pageFilters={pageFilters}
          mainForm={form}
        ></ReplaceCharacters>
      ),
    },
    {
      key: "regular_expression",
      label: "Regular Expression",
      style: panelStyle,
      children: (
        <RegularExpression
          currentStepOutput={currentStepOutput}
          setCurrentStepOutput={setCurrentStepOutput}
          setParsingSteps={setParsingSteps}
          stepOnEdit={stepOnEdit}
          pageFilters={pageFilters}
          mainForm={form}
          stepOnEditIndex={stepOnEditIndex}
          setStepOnEditIndex={setStepOnEditIndex}
        ></RegularExpression>
      ),
    },
    {
      key: "no_lotid",
      label: "Invalid Lot ID",
      style: panelStyle,
      children: (
        <NoLotId
          currentStepOutput={currentStepOutput}
          setCurrentStepOutput={setCurrentStepOutput}
          setParsingSteps={setParsingSteps}
          stepOnEdit={stepOnEdit}
          stepOnEditIndex={stepOnEditIndex}
          setStepOnEditIndex={setStepOnEditIndex}
          pageFilters={pageFilters}
          mainForm={form}
        ></NoLotId>
      ),
    },
  ];

  return (
    <ConfigProvider
      theme={{
        token: {
          colorCheckedTagBg: "#d9d9d9", // Custom background for checked tags
          colorUncheckedTagBg: "#F7C19B", // Default background for unchecked tags
        },
        components: {
          Tag: {
            defaultColor: "rgba(0, 0, 0, 0.88)",
            colorPrimaryHover: "#F7C19B", // Hover color for primary tags
          },
        },
      }}
    >
      {contextHolder}
      <Modal
        title="Edit Output Value"
        open={isModalOpen}
        onOk={handleApplyOutput}
        onCancel={handleCancel}
        width={"60vw"}
        destroyOnClose
        footer={[
          <Button key="back" onClick={handleCancel}>
            Cancel
          </Button>,
          <Button key="cancel" onClick={handleReset}>
            Reset
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleApplyOutput}
            disabled={
              form.getFieldValue("source_field") === selectedYHField &&
              parsingSteps.length === 1
            }
          >
            Apply to Output
          </Button>,
        ]}
      >
        <Form
          layout="vertical"
          form={form}
          onFieldsChange={handleFieldsChange}
          // Eventhough this won't affect when the modal opens since its empty initially,
          //  still useful when calling form.resetFields()
          initialValues={initialValues}
          preserve={false}
        >
          <Row gutter={6}>
            <Col span={6}>
              <Form.Item hidden label="yieldHub Field" name="yh_field">
                <Input disabled></Input>
              </Form.Item>
              <Form.Item label="yieldHub Field" name="yh_field_display">
                <Input disabled></Input>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="Source Field" name="source_field">
                <Select
                  showSearch
                  options={fieldOptions}
                  disabled={isFieldSelectDisabled}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="Source Value" name="source_value">
                <Text
                  className="w-full border border-solid border-gray-300 p-0.5 min-h-7 text-gray-400"
                  ellipsis={{ tooltip: { sourceValue } }}
                >
                  {sourceValue}
                </Text>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Simulated Output Value"
                name="simulated_output_value"
              >
                <Text
                  className="w-full border border-solid border-gray-300 p-0.5 min-h-7 text-gray-400"
                  ellipsis={{ tooltip: { currentStepOutput } }}
                >
                  {currentStepOutput}
                </Text>
              </Form.Item>
            </Col>
          </Row>
          {/* <Row gutter={6}>
            <Col>
              <Form.Item name="use_source_value" valuePropName="checked">
                <Checkbox disabled={isFieldSelectDisabled}>
                  Use Original Source Value
                </Checkbox>
              </Form.Item>
            </Col>
          </Row> */}
          <Divider>Add Characters to Output Value</Divider>
          <Row gutter={6}>
            <AddCharsToOutput
              addType="Prepend"
              fieldOptions={fieldOptions}
              fieldData={fieldData}
              form={form}
            ></AddCharsToOutput>
            <AddCharsToOutput
              addType="Append"
              fieldOptions={fieldOptions}
              fieldData={fieldData}
              form={form}
            ></AddCharsToOutput>
          </Row>
        </Form>
        <Divider>Simulate</Divider>
        <Row>
          <Col span={12}>
            <Text strong>Parsing Procedure</Text>
            <Collapse
              accordion
              items={procedureItems}
              activeKey={activeCollapseKey}
              onChange={handleCollapseChange}
              style={{
                background: token.yhPageColorBg,
                border: token.yhTransparentBorder,
              }}
            />
          </Col>
          <Col span={12} className="pl-1">
            <Text strong className="pl-1">
              Steps
            </Text>
            <MetadataParsingSteps
              parsingSteps={parsingSteps}
              setParsingSteps={setParsingSteps}
              setStepOnEdit={setStepOnEdit}
              setActiveProcedure={setActiveCollapseKey}
              pageFilters={pageFilters}
              editForm={form}
              setStepOnEditIndex={setStepOnEditIndex}
              setCurrentStepOutput={setCurrentStepOutput}
            />
          </Col>
        </Row>
      </Modal>
    </ConfigProvider>
  );
};

export default EditOutputModal;
