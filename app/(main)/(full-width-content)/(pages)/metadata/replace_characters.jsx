"use client";

import { useEffect, useState } from "react";
import { App, Button, Input, Flex, Form, Space, Typography } from "antd";
import { PlusSquareOutlined } from "@ant-design/icons";
import { alphanumericRule } from "../../../../../src/utils/antd_validation_rules";
import Api from "../../../../../src/utils/api";
const { Text } = Typography;
const collapseKey = "replace_characters";

/**
 * Replace characters component
 *
 * @param {string} currentStepOutput
 * @param {function} setCurrentStepOutput
 * @param {function} setParsingSteps
 * @param {object} stepOnEdit
 * @param {object} stepOnEditIndex
 * @param {function} setStepOnEditIndex
 * @param {object} pageFilters
 * @param {object} mainForm
 * @returns {JSX.Element}
 */
const ReplaceCharacters = ({
  currentStepOutput = "",
  setCurrentStepOutput,
  setParsingSteps,
  stepOnEdit,
  stepOnEditIndex,
  setStepOnEditIndex,
  pageFilters,
  mainForm,
}) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const [previewBtnDisabled, setPreviewBtnIsDisabled] = useState(true);

  useEffect(() => {
    if (
      Object.keys(stepOnEdit).length &&
      stepOnEdit.collapseKey === "replace_characters"
    ) {
      setEditStep(stepOnEdit);
    }
  }, [stepOnEdit]);

  /**
   * Populate the form
   *
   * @param {object} step
   */
  const setEditStep = (step) => {
    form.setFieldsValue(step.value);
    setPreviewBtnIsDisabled(false);
  };

  /**
   * Execute step thru api
   *
   * @param {function} successCb
   */
  const execStep = (successCb) => {
    Api.executeMetadataParsingSteps(
      (res) => {
        if (res.success) {
          successCb(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        ...pageFilters,
        ...form.getFieldsValue(true),
        ...mainForm.getFieldsValue(true),
        collapseKey: collapseKey,
        inputValue: currentStepOutput,
      },
    );
  };

  /**
   * Get the current step
   *
   * @param {string} output
   * @returns {object}
   */
  const getStep = (ouput) => {
    const chars = form.getFieldsValue(["orig_chars", "char_replacement"]);
    return {
      value: chars,
      label: `Replace Characters: Replace '${chars.orig_chars}' with '${chars.char_replacement}'`,
      inputValue: currentStepOutput,
      outputValue: ouput,
      collapseKey: collapseKey,
    };
  };

  /**
   * When user clicks the Show Preview button
   */
  const showPreview = () => {
    execStep((data) => {
      const step = getStep(data);
      form.setFieldsValue({
        output_preview: step.outputValue,
        step_preview: step.label,
      });
    });
  };

  /**
   * Reset the fields to default
   */
  const resetFields = () => {
    setPreviewBtnIsDisabled(true);
    form.resetFields();
  };

  /**
   * Handles the event where a field changes in a form
   */
  const handleFieldsChange = () => {
    const chars = form.getFieldsValue(["orig_chars", "char_replacement"]);
    setPreviewBtnIsDisabled(
      chars.orig_chars === undefined ||
        chars.orig_chars === "" ||
        !alphanumericRule.pattern.test(chars.char_replacement),
    );
  };

  /**
   * Add current step to the parsing steps
   */
  const addStep = () => {
    execStep((data) => {
      const step = getStep(data);
      setParsingSteps((prevState) => {
        let newState = [...prevState];
        if (stepOnEditIndex !== null) {
          newState[stepOnEditIndex] = {
            ...step,
            inputValue: newState[stepOnEditIndex].inputValue,
          };
        } else {
          newState = [...prevState, step];
        }
        return newState;
      });
      resetFields();
      setCurrentStepOutput(data);
      setStepOnEditIndex(null);
    });
  };

  return (
    <Form
      layout="vertical"
      form={form}
      onFieldsChange={handleFieldsChange}
      initialValues={{ char_replacement: "" }}
    >
      <Flex justify="space-between" align="center">
        <Form.Item
          name="orig_chars"
          label="Replace this"
          style={{ width: "46%" }}
        >
          <Input placeholder="Original Characters" />
        </Form.Item>
        <Text>with</Text>
        <Form.Item
          name="char_replacement"
          label="This characters"
          style={{ width: "46%" }}
          rules={[alphanumericRule]}
        >
          <Input placeholder="Replacement" />
        </Form.Item>
      </Flex>
      <Flex justify="flex-end" align="center" style={{ marginTop: 6 }}>
        <Button onClick={showPreview} disabled={previewBtnDisabled}>
          Show Preview
        </Button>
      </Flex>
      <Form.Item
        name="output_preview"
        label="Output Preview"
        style={{ width: "100%" }}
      >
        <Input disabled />
      </Form.Item>
      <Form.Item
        name="step_preview"
        label="Step Preview"
        style={{ width: "100%" }}
      >
        <Input disabled />
      </Form.Item>
      <Flex justify="flex-end" align="center" style={{ marginTop: 6 }}>
        <Space>
          <Button onClick={resetFields}>Clear All</Button>
          <Button
            type="primary"
            icon={<PlusSquareOutlined />}
            onClick={addStep}
            disabled={previewBtnDisabled}
          >
            Add as Step
          </Button>
        </Space>
      </Flex>
    </Form>
  );
};

export default ReplaceCharacters;
