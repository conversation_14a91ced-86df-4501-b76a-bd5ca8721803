"use client";

import { useEffect, useState } from "react";
import { App, Button, Input, Flex, Form, Tag, Typography, Space } from "antd";
import { CheckOutlined, PlusSquareOutlined } from "@ant-design/icons";
import Api from "../../../../../src/utils/api";
const { Text } = Typography;
const { CheckableTag } = Tag;
const collapseKey = "split_by_delimiter";

/**
 * Split by delimiter component
 *
 * @param {string} currentStepOutput
 * @param {function} setCurrentStepOutput
 * @param {function} setParsingSteps
 * @param {object} stepOnEdit
 * @param {object} stepOnEditIndex
 * @param {function} setStepOnEditIndex
 * @param {object} pageFilters
 * @param {object} mainForm
 * @returns {JSX.Element}
 */
const SplitByDelimiter = ({
  currentStepOutput = "",
  setCurrentStepOutput,
  setParsingSteps,
  stepOnEdit,
  stepOnEditIndex,
  setStepOnEditIndex,
  pageFilters,
  mainForm,
}) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const [splittedItems, setSplittedItems] = useState([]);
  const [selectedSplittedItem, setSelectedSplittedItem] = useState("");
  const [splitBtnIsDisabled, setSplitBtnIsDisabled] = useState(true);
  const [stepPreview, setStepPreview] = useState({});
  const [step, setStep] = useState({});

  useEffect(() => {
    form.setFieldsValue({ step_preview: stepPreview.label ?? "" });
  }, [stepPreview.label]);

  useEffect(() => {
    if (
      Object.keys(stepOnEdit).length &&
      stepOnEdit.collapseKey === collapseKey
    ) {
      setEditStep(stepOnEdit);
    }
  }, [stepOnEdit]);

  /**
   * Populate the form
   *
   * @param {object} step
   */
  const setEditStep = (step) => {
    form.setFieldsValue({ delimiter: step.value });
    splitDelimiter(step.outputValue, step.outputIndex);
  };

  /**
   * Execute step thru api
   *
   * @param {function} successCb
   */
  const execStep = (successCb) => {
    Api.executeMetadataParsingSteps(
      (res) => {
        if (res.success) {
          successCb(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        ...pageFilters,
        ...form.getFieldsValue(true),
        ...mainForm.getFieldsValue(true),
        collapseKey: collapseKey,
        inputValue: currentStepOutput,
      },
    );
  };

  /**
   * When a splitted item is selected
   *
   * @param {string} item The selected splitted item
   * @param {int} idx The index of the selected item
   */
  const setActiveStep = (item, idx) => {
    setSelectedSplittedItem(item);
    const delimiter = form.getFieldValue("delimiter");
    setStep({
      value: delimiter,
      label: `Delimiter: Split by '${delimiter}'`,
      inputValue: currentStepOutput,
      outputValue: item,
      outputIndex: idx,
      collapseKey: collapseKey,
    });
  };

  /**
   * Splits the current step output when user clicks the Split button
   *
   * @param {string} item
   * @param {int} item_idx
   */
  const splitDelimiter = (item, item_idx) => {
    // Check if item is an event, if such a case, set it to null so the default selection will be the first tag
    const select_item = typeof item === "string" ? item : null;
    execStep((data) => {
      setSplittedItems(data);
      if (data.length) {
        // Set first splitted item as default selected tag
        setActiveStep(select_item ?? data[0], item_idx ?? 0);
      } else {
        setSelectedSplittedItem("");
        form.setFieldsValue({ step_preview: "" });
      }
    });
  };

  /**
   * When user clicks a tag item
   *
   * @param {string} item The selected splitted item
   * @param {int} idx The index of the selected item
   */
  const handleSplittedItemsChange = (item, idx) => {
    setActiveStep(item, idx);
  };

  /**
   * When user clicks the Show Preview button
   */
  const showStepPreview = () => {
    setStepPreview({ ...step });
  };

  /**
   * Reset the fields to default
   */
  const resetFields = () => {
    form.setFieldsValue({ delimiter: "" });
    setSplittedItems([]);
    setSelectedSplittedItem("");
    setStepPreview({});
    setSplitBtnIsDisabled(true);
  };

  /**
   * Handles the event where a field changes in a form
   *
   */
  const handleFieldsChange = () => {
    setSplitBtnIsDisabled(
      form.getFieldValue("delimiter") === "" ||
        form.getFieldValue("delimiter") === undefined,
    );
  };

  /**
   * Add current step to the parsing steps
   */
  const addStep = () => {
    setParsingSteps((prevState) => {
      let newState = [...prevState];
      if (stepOnEditIndex !== null) {
        newState[stepOnEditIndex] = {
          ...step,
          inputValue: newState[stepOnEditIndex].inputValue,
        };
      } else {
        newState = [...prevState, step];
      }
      return newState;
    });
    resetFields();
    setCurrentStepOutput(step.outputValue);
    setStepOnEditIndex(null);
  };

  return (
    <Form
      layout="vertical"
      form={form}
      onFieldsChange={handleFieldsChange}
      preserve={false}
    >
      <Flex justify="space-between" align="center">
        <Form.Item name="delimiter" label="Delimiter" className="w-4/5">
          <Input placeholder="Input Delimiter" />
        </Form.Item>
        <Form.Item label=" " className="w-1/6">
          <Button
            className="w-full"
            onClick={splitDelimiter}
            disabled={splitBtnIsDisabled}
          >
            Split
          </Button>
        </Form.Item>
      </Flex>
      <div className="-mt-3 mb-3">
        <Text strong className="text-gray-400">
          Tip:{" "}
        </Text>
        <Text className="text-gray-400">
          Use the vertical bar symbol ( | ) to define multiple delimiters.
          Example: 123|Test|_|=|+
        </Text>
      </div>
      <Text>Select Ouput</Text>
      <div className="border border-solid border-gray-300 p-1 min-h-8">
        {splittedItems.map(
          (item, idx) =>
            item !== "" && (
              <CheckableTag
                key={`${item}_${idx}`}
                checked={selectedSplittedItem === item}
                onChange={() => handleSplittedItemsChange(item, idx)}
                className="text-wrap break-all"
              >
                {item}
                {selectedSplittedItem === item && (
                  <CheckOutlined className="ml-1!"></CheckOutlined>
                )}
              </CheckableTag>
            ),
        )}
      </div>
      <Flex justify="flex-end" align="center" className="mt-1.5!">
        <Button
          disabled={selectedSplittedItem === ""}
          onClick={showStepPreview}
        >
          Show Preview
        </Button>
      </Flex>
      <Form.Item name="step_preview" label="Step Preview" className="w-full">
        <Input disabled />
      </Form.Item>
      <Flex justify="flex-end" align="center" className="mt-1.5">
        <Space>
          <Button onClick={resetFields}>Clear All</Button>
          <Button
            type="primary"
            icon={<PlusSquareOutlined />}
            onClick={addStep}
            disabled={selectedSplittedItem === ""}
          >
            Add as Step
          </Button>
        </Space>
      </Flex>
    </Form>
  );
};

export default SplitByDelimiter;
