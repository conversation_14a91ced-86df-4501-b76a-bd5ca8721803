"use client";

import { theme, ConfigProvider } from "antd";
import { useState } from "react";

const { useToken } = theme;

const defaultData = {
  colorPrimary: "#154495",
  colorPrimaryBg: "#154495",
  colorBgTextHover: "#446AAA",
  controlItemBgActive: "#000000",
  yhColorOrange: "#EF6322",
  yhLightBlue: "#E8EDFC",
  borderRadius: 0,
  yhCollapseBorderRadius: "5px 5px 0 0",
  yhTransparentBorder: "transparent",
  yhCollapseBorder: "1px solid #E8EDFC",
};

/**
 * Layout component of main content
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const MainContentLayout = ({ children }) => {
  const [data] = useState(defaultData);
  const { token } = useToken();

  return (
    <ConfigProvider
      theme={{
        algorithm: [theme.compactAlgorithm],
        token: {
          colorPrimary: data.colorPrimary,
          colorPrimaryBg: data.colorPrimaryBg,
          colorBgTextHover: data.colorBgTextHover,
          controlItemBgActive: token.controlItemBgActiveHover,
          yhColorOrange: data.yhColorOrange,
          borderRadius: data.borderRadius,
          yhCollapseBorderRadius: data.yhCollapseBorderRadius,
          yhTransparentBorder: data.yhTransparentBorder,
          yhCollapseBorder: data.yhCollapseBorder,
        },
        components: {
          Button: {
            colorPrimary: data.colorPrimaryBg,
          },
          Collapse: {
            colorFillAlter: data.colorPrimaryBg,
            colorTextHeading: token.colorWhite,
            borderRadiusLG: data.yhCollapseBorderRadius,
            headerBg: data.colorBgTextHover,
            colorBorder: data.yhLightBlue,
          },
          Tabs: {
            cardBg: token.yhPageColorBg,
            inkBarColor: data.yhColorOrange,
            itemHoverColor: data.yhColorOrange,
            itemSelectedColor: data.yhColorOrange,
            titleFontSize: 14,
          },
        },
      }}
    >
      {children}
    </ConfigProvider>
  );
};
export default MainContentLayout;
