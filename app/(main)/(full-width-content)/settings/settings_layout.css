.settings-layout {
  /* overflow-y: hidden; */
}

/* .settings-layout .main-content {
  padding: 24px 24px 0px 0px;
} */

/* .settings-layout .main-content .breadcrumb {
  margin-left: 24px;
} */

.settings-layout .content {
  /* padding: 24px 0 0 0; */
  /* height: 100%;
  overflow-y: hidden; */
}

.settings-layout .ant-tabs {
  height: 100%;
}

.settings-layout .ant-tabs-nav {
  display: flex;
  align-items: flex-start;
  background: #e8edfc;
  width: 20%;
}

.settings-layout .ant-tabs-nav .ant-tabs-tab {
  padding-left: 24px;
}

.settings-layout .ant-tabs-extra-content {
  padding: 16px 24px 8px 24px;
}

.settings-layout .ant-tabs-content {
  padding-top: 16px;
}

/* .settings-layout .page-title {
  margin-left: 24px;
  margin-bottom: 36px;
} */

.settings-layout .ant-tabs-tab-active {
  background: #f7c19b;
}

/* .settings-layout .ant-menu .ant-menu-sub {
  background: #D2DBF6 !important;
  color: #021d4d;
} */

/* .settings-layout .ant-menu-submenu {
  background: #e8edfc;
} */
