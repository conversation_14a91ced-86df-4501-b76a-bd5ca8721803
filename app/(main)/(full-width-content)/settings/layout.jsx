"use client";

import { ConfigProvider, theme } from "antd";
import dynamic from "next/dynamic";
import { useState } from "react";
import PageLoading from "../../loading";

const SettingsLayout = dynamic(() => import("./settings_layout"), {
  ssr: false,
  loading: () => <PageLoading />,
});

const { useToken } = theme;
const defaultData = {
  yhMenuColorBg: "#D2DBF6",
};

/**
 * Layout component of account settings page
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const AccountSettingsLayout = ({ children }) => {
  const [data] = useState(defaultData);
  const { token } = useToken();

  return (
    <ConfigProvider
      theme={{
        algorithm: [theme.compactAlgorithm],
        token: {
          yhMenuColorBg: data.yhMenuColorBg,
        },
        components: {
          Menu: {
            subMenuItemBg: data.yhMenuColorBg,
            itemSelectedBg: token.yhColorLightOrange,
            itemMarginInline: 0,
            activeBarWidth: 3,
          },
        },
      }}
    >
      <SettingsLayout>{children}</SettingsLayout>
    </ConfigProvider>
  );
};
export default AccountSettingsLayout;
