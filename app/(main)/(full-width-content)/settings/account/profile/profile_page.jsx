"use client";

import ProfileForm from "../../../../../../src/utils/forms/profile_form";
import AccountSettingsLayout from "../../layout";
// NOTE: Comment out for now. DO NOT remove.
// import { PushNotificationManager } from "../../../../../../src/utils/push_notification";

/**
 * Account profile page component
 *
 * @returns {JSX.Element}
 */
export default function AccountProfile() {
  return (
    <AccountSettingsLayout>
      <ProfileForm />
    </AccountSettingsLayout>
  );
}
