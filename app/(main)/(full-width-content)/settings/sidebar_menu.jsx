"use client";

import { UserOutlined, SettingOutlined } from "@ant-design/icons";
import { theme, Menu } from "antd";
import { useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useBoundStore } from "../../../../src/store/store";
import Helper from "../../../../src/utils/helper";

const { useToken } = theme;

/**
 * Sidebar menu component
 *
 * @returns {JSX.Element}
 */
const SidebarMenu = () => {
  const currentPageData = useBoundStore((state) => state.currentPageData);
  const { token } = useToken();
  const queryClient = useQueryClient();

  /**
   * Get menu state
   *
   * @returns {object} menuState
   */
  const getMenuState = () => {
    let menuState = {};
    const pageState = window.history.state;
    if (pageState.pageKey) {
      switch (pageState.pageKey) {
        case "my_profile":
          menuState.openKeys = ["account"];
          menuState.selectedKeys = ["my_profile"];
          break;
        case "change_password":
          menuState.openKeys = ["account"];
          menuState.selectedKeys = ["change_password"];
          break;
        case "general":
          menuState.openKeys = ["preferences"];
          menuState.selectedKeys = ["general"];
          break;
        case "notifications":
          menuState.openKeys = ["preferences"];
          menuState.selectedKeys = ["notifications"];
          break;
        case "appearance":
          menuState.openKeys = ["preferences"];
          menuState.selectedKeys = ["appearance"];
          break;
      }
    }

    return menuState;
  };

  /**
   * Get menu for settings sidebar
   *
   * @returns {JSX.Element}
   */
  const getMenu = useCallback(() => {
    const menuState = getMenuState();

    return (
      <Menu
        mode="inline"
        defaultOpenKeys={menuState.openKeys}
        selectedKeys={menuState.selectedKeys}
        style={{
          background: token.yhLightBlue,
        }}
        onSelect={({ key }) => Helper.navigatePage(key, queryClient)}
        items={[
          {
            key: "preferences",
            icon: <SettingOutlined />,
            label: "Preferences",
            theme: "light",
            children: [
              {
                key: "general",
                label: "General",
              },
              {
                key: "notifications",
                label: "Notifications",
              },
              {
                key: "appearance",
                label: "Appearance",
              },
            ],
          },
          {
            key: "account",
            icon: <UserOutlined />,
            label: "Account",
            theme: "light",
            children: [
              {
                key: "my_profile",
                label: "My Profile",
              },
              {
                key: "change_password",
                label: "Change Password",
              },
            ],
          },
        ]}
      />
    );
  }, [currentPageData.key]);

  return getMenu();
};

export default SidebarMenu;
