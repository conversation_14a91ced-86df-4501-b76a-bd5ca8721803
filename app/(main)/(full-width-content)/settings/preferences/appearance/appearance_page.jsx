"use client";

import AppearanceForm from "../../../../../../src/utils/forms/settings/preferences/preferences_appearance_form";
import AccountSettingsLayout from "../../layout";

/**
 * User settings appearance preferences page component
 *
 * @returns {JSX.Element}
 */
export default function AppearanceSettings({ pageKey }) {
  return (
    <AccountSettingsLayout>
      <AppearanceForm pageKey={pageKey} />
    </AccountSettingsLayout>
  );
}
