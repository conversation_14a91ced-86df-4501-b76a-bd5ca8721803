import "./settings_layout.css";
import { Layout, theme, Typography } from "antd";
import SidebarMenu from "./sidebar_menu";

const { Content, Sider } = Layout;
const { useToken } = theme;
const { Title } = Typography;

/**
 * Layout component of settings page
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const SettingsLayout = ({ children }) => {
  const { token } = useToken();

  return (
    <Layout className="h-full overflow-hidden">
      <Content
        className="pt-4 pr-4 flex flex-col grow"
        style={{
          background: token.colorBgContainer,
        }}
      >
        <div className="h-full">
          <div className="h-full">
            <Title level={4} className="page-title ml-6 mb-4!">
              Settings
            </Title>
            <Layout
              className="h-full"
              style={{
                background: token.colorBgContainer,
              }}
              hasSider
            >
              <Sider
                style={{
                  background: token.yhLightBlue,
                }}
              >
                <SidebarMenu />
              </Sider>
              <Content>
                <div className="px-6 py-4">{children}</div>
              </Content>
            </Layout>
          </div>
        </div>
      </Content>
    </Layout>
  );
};
export default SettingsLayout;
