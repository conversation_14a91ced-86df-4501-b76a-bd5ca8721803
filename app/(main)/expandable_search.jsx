import { SearchOutlined } from "@ant-design/icons";
import { Button, Input, Space } from "antd";
import { useState } from "react";

/**
 * Search bar that is expandable
 *
 * @returns {JSX.Element}
 */
const ExpandableSearch = () => {
  const [searchBoxClass, setSearchBoxClass] = useState("hidden");
  const [searched, setSearched] = useState("");

  /**
   * Set search value
   *
   * @param {Event} e
   */
  const setSearchValue = (e) => {
    setSearched(e.target.value);
  };

  /**
   * Trigger when clicking the search button
   */
  const handleSearch = () => {
    toggleSearchBox();
  };

  /**
   * Show/hide search box
   */
  const toggleSearchBox = () => {
    if (
      searchBoxClass === "hidden" ||
      searchBoxClass === "animate__slideOutRight"
    ) {
      setSearchBoxClass("animate__slideInRight");
    } else if (searchBoxClass === "animate__slideInRight" && searched === "") {
      setSearchBoxClass("animate__slideOutRight");
    }
  };

  return (
    <Space size={0} className="expandable-search-wrapper">
      <Space className="overflow-hidden">
        <Input
          className={`animate__animated animate__faster rounded-r-none ${searchBoxClass}`}
          placeholder="input search text"
          defaultValue={searched}
          onChange={setSearchValue}
          allowClear
        />
      </Space>
      <Button
        type="primary"
        className="menu-item menu-item-btn"
        size="large"
        onClick={handleSearch}
      >
        <SearchOutlined />
      </Button>
    </Space>
  );
};

export default ExpandableSearch;
