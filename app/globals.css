@import "tailwindcss";

* {
  box-sizing: border-box;
}

html,
body {
  max-width: 100vw;
  max-height: 100vh;
  overflow: hidden;
}

.flex-center {
  display: flex;
  justify-content: center;
}

.notifications-dropdown {
  width: 500px;
  background: #ffffff;
  box-shadow:
    0 6px 16px 0 rgb(0 0 0 / 8%),
    0 3px 6px -4px rgb(0 0 0 / 12%),
    0 9px 28px 8px rgb(0 0 0 / 5%);
}

.text-error {
  color: #ff0000;
}

.rotate-90 {
  transform: rotate(90deg);
}

.rotate-180 {
  transform: rotate(180deg);
}

.rotate-270 {
  transform: rotate(270deg);
}

.rotate-0.flip-x {
  transform: rotateY(180deg);
}

.rotate-90.flip-x {
  transform: rotate(270deg) rotateX(180deg);
}

.rotate-180.flip-x {
  transform: rotate(180deg) rotateY(180deg);
}

.rotate-270.flip-x {
  transform: rotate(90deg) rotateX(180deg);
}

.rotate-0.flip-y {
  transform: rotateX(180deg);
}

.rotate-90.flip-y {
  transform: rotate(270deg) rotateY(180deg);
}

.rotate-180.flip-y {
  transform: rotate(180deg) rotateX(180deg);
}

.rotate-270.flip-y {
  transform: rotate(90deg) rotateY(180deg);
}

.color-swatch {
  width: 16px;
  height: 16px;
  border: none;
  padding: 0;
  border-radius: 4px;
  cursor: pointer;
  outline: none;
}

/* parent container css style for children to fill remaining height */
/* use together with .fill-height for child */
.flex-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
}

/* child will fill the remaining height */
/* use together with .flex-container for parent */
.fill-height {
  flex: 1;
}

/***** ANT DESIGN OVERRIDES *****/

.ant-menu .ant-menu-item,
.ant-menu .ant-menu-submenu,
.ant-menu-submenu > .ant-menu,
.ant-menu-submenu > .ant-menu .ant-menu-item,
.ant-menu .ant-menu-submenu-title {
  border-radius: 0;
}

.sidebar-menu .ant-menu-submenu .ant-menu-item:hover {
  color: #ef6322 !important;
  background-color: #021d4d !important;
}

.ant-dropdown-menu {
  border-radius: 0 !important;
}

.anticon-close:hover.ant-tag-close-icon:hover {
  color: rgba(0, 0, 0, 0.88);
}

/***** AGGRID OVERRIDES *****/

.ag-paging-panel {
  border-top: 0px !important;
}

.ag-details-grid .ag-header {
  background-color: #adb3c6 !important;
}

.ag-root-wrapper,
.ag-header {
  border: none !important;
}

/***** AGGRID STYLES *****/

.row-invalid {
  background-color: #ffe5d2 !important;
}

/****** REACT COLORFUL (COLORPICKER) ******/

.picker-swatch {
  width: 24px;
  height: 24px;
  margin: 4px;
  border: none;
  padding: 0;
  border-radius: 4px;
  cursor: pointer;
  outline: none;
}

/***** REACT FULLSCREEN OVERRIDE *****/

.fullscreen-enabled {
  background-color: #ffffff;
  padding: 30px;
}

/** Shows asterisk for labels that is not inside a required Form.item **/
.required-label::before {
  display: inline-block;
  margin-inline-end: 4px;
  color: #ff4d4f;
  font-size: 12px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: "*";
}
