import Head from "next/head";

/**
 * Header of site page
 *
 * @returns {JSX.Element}
 */
export default function Header() {
  const title = process.env.NEXT_PUBLIC_APP_TITLE;
  const description = process.env.NEXT_PUBLIC_APP_DESCRIPTION;

  return (
    <Head>
      <title>{title}</title>
      <meta property="og:title" content={title} key="title" />
      <meta content="width=device-width, initial-scale=1" name="viewport" />
      <meta name="description" content={description} />

      <link
        rel="icon"
        type="image/png"
        href="/favicon-48x48.png"
        sizes="48x48"
      />
      <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
      <link rel="shortcut icon" href="/favicon.ico" />
      <link
        rel="apple-touch-icon"
        sizes="180x180"
        href="/apple-touch-icon.png"
      />
      <meta
        name="apple-mobile-web-app-title"
        content={process.env.NEXT_PUBLIC_APP_TITLE}
      />
      <link rel="manifest" href="/site.webmanifest" />
    </Head>
  );
}
