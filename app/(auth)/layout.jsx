"use client";

import { useState } from "react";
import { ConfigProvider, theme } from "antd";
import dynamic from "next/dynamic";
import PageLoading from "../loading";

const LoginLayout = dynamic(() => import("./login_layout"), {
  ssr: false,
  loading: () => <PageLoading />,
});

const defaultData = {
  _bgFormContainer: "#ffffff",
  colorPrimary: "#154495",
  borderRadius: 0,
  controlHeight: 40,
  fontSize: 14,
};

/**
 * Login page layout wrapper component
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const Layout = ({ children }) => {
  const [data] = useState(defaultData);

  return (
    <ConfigProvider
      theme={{
        algorithm: [theme.compactAlgorithm],
        token: {
          _bgFormContainer: data._bgFormContainer,
          colorPrimary: data.colorPrimary,
          borderRadius: data.borderRadius,
          controlHeight: data.controlHeight,
          fontSize: data.fontSize,
        },
      }}
    >
      <LoginLayout>{children}</LoginLayout>
    </ConfigProvider>
  );
};
export default Layout;
