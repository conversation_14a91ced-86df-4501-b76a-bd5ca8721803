"use client";

import { Layout, theme, Col, <PERSON>, Button } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { useBoundStore } from "../../src/store/store";
import styles from "./styles.module.css";

const { useToken } = theme;
const { Content } = Layout;

/**
 * Login page layout component
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const LoginLayout = ({ children }) => {
  const hasBackRoute = useBoundStore((state) => state.hasBackRoute);
  const router = useRouter();
  const { token } = useToken();

  return (
    <div className={styles.loginLayoutWrapper}>
      <Layout className={styles.loginLayout}>
        <Content>
          <Row className={styles.loginWrapper} align="middle">
            <Col flex="auto" className={styles.loginLeft}>
              <div className={styles.logo}>
                <div className={styles.text}>yieldHUB</div>
              </div>
            </Col>
            <Col
              className={styles.loginRight}
              xs={24}
              sm={24}
              md={10}
              lg={10}
              xl={10}
            >
              <div
                className={styles.loginBox}
                style={{
                  background: token._bgFormContainer,
                }}
              >
                {hasBackRoute && (
                  <div className={styles.backRouteWrapper}>
                    <Button type="link" onClick={() => router.back()}>
                      <ArrowLeftOutlined className={styles.backRouteIcon} />
                    </Button>
                  </div>
                )}
                <div className={styles.loginBoxContent}>{children}</div>
              </div>
            </Col>
          </Row>
        </Content>
      </Layout>
    </div>
  );
};
export default LoginLayout;
