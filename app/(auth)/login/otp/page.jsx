"use client";

import React, { useState, useEffect } from "react";
import { Button, Form, Input, Typography, Statistic, message } from "antd";
import { useBoundStore } from "../../../../src/store/store";
import Validator from "../../../../src/utils/validator";
import Api from "../../../../src/utils/api";
import Helper from "../../../../src/utils/helper";
import styles from "./styles.module.css";

const { Text, Title } = Typography;
const { Countdown } = Statistic;

/**
 * Get OTP expiry
 *
 * @returns {int}
 */
const getExpiry = () => {
  return Date.now() + 1000 * 60 * 5;
};

/**
 * One Time Password page component to enter OTP
 *
 * @returns {JSX.Element}
 */
export default function OTP() {
  const [allowSubmit, setAllowSubmit] = useState(false);
  const [allowInput, setAllowInput] = useState(true);
  const [expiry, setExpiry] = useState(getExpiry());
  const [expired, setExpired] = useState(false);
  const [loading, setLoading] = useState(false);
  const [resendOTPLoading, setResendOTPLoading] = useState(false);
  const [transitionEffect, setTransitionEffect] = useState("");
  const [validateStatus, setValidateStatus] = useState();
  const [validateMessage, setValidateMessage] = useState();
  const hasBackRoute = useBoundStore((state) => state.hasBackRoute);
  const setHasBackRoute = useBoundStore((state) => state.setHasBackRoute);
  const loginData = useBoundStore((state) => state.loginData);
  const [messageApi, contextHolder] = message.useMessage();
  const [form] = Form.useForm();

  useEffect(() => {
    setHasBackRoute(true);
  }, []);

  useEffect(() => {
    if (hasBackRoute) {
      setTransitionEffect("animate__slideInRight");
    } else {
      setTransitionEffect("animate__slideOutRight");
    }
  }, [hasBackRoute]);

  /**
   * Trigger after submitting OTP form and verifying data successfully
   *
   * @param {object} values
   */
  const onFinish = (values) => {
    setLoading(true);
    submitOTP(values.otp);
  };

  /**
   * Check OTP validity
   *
   * @param {number} otp
   */
  const submitOTP = (otp) => {
    Api.submitOTP(
      (res) => {
        if (res.success) {
          Helper.loginUser(res);
        } else {
          setValidateStatus("error");
          setValidateMessage(res.message);
        }
        setLoading(false);
      },
      (err) => {
        setValidateStatus("error");
        setValidateMessage(err);
        setLoading(false);
      },
      {
        otp: otp,
        jwt: loginData.jwt,
      },
    );
  };

  /**
   * Trigger when OTP value expires
   */
  const onExpiry = () => {
    setExpired(true);
    setAllowInput(false);
    setAllowSubmit(false);
    setValidateStatus("error");
    setValidateMessage("");
  };

  /**
   * Trigger when user enters an OTP
   *
   * @param {Event} e
   */
  const handleChange = (e) => {
    setAllowSubmit(e.target.value.length === e.target.maxLength);
    setValidateMessage("");
  };

  /**
   * Resend OTP
   */
  const resendOTP = () => {
    setResendOTPLoading(true);
    Api.resendOTP(
      (res) => {
        if (res.success) {
          messageApi.success(
            "A One-Time-Password (OTP) has been sent to your email.",
          );
          setExpired(false);
          setExpiry(getExpiry());
          setAllowInput(true);
          setAllowSubmit(
            form.getFieldValue("otp") !== undefined &&
              form.getFieldValue("otp").length ===
                form.getFieldInstance("otp").input.maxLength,
          );
        } else {
          messageApi.warning(res.message, 10);
        }
        setResendOTPLoading(false);
      },
      (err) => {
        messageApi.error(err, 10);
        setResendOTPLoading(false);
      },
      {
        jwt: loginData.jwt,
      },
    );
  };

  return (
    <>
      {contextHolder}
      <div className={`animate__animated animate__faster ${transitionEffect}`}>
        <Form
          form={form}
          name="otp"
          className={styles.otpForm}
          layout="vertical"
          requiredMark={false}
          onFinish={onFinish}
          autoComplete="off"
        >
          <Title level={4}>Please enter the OTP to Verify your Account</Title>
          <Text type="secondary">
            A One-Time-Password (OTP) has been sent to your email
          </Text>
          <Form.Item
            name="otp"
            className="mt-6"
            help={validateMessage}
            validateStatus={validateStatus}
            rules={[
              {
                required: true,
                message: "Please input your OTP!",
              },
            ]}
          >
            <Input
              id="otp"
              placeholder="Enter 6 digit OTP"
              type="text"
              maxLength={6}
              disabled={!allowInput}
              onKeyDown={Validator.onKeyPress}
              onPaste={Validator.onPaste}
              onChange={handleChange}
            />
          </Form.Item>
          {!expired ? (
            <Text className={styles.otpExpiry}>
              OTP will expire in
              <Countdown
                className={styles.otpTimer}
                value={expiry}
                onFinish={onExpiry}
                prefix=" "
                suffix=" "
                format="mm:ss"
                valueStyle={{
                  fontSize: 12,
                  color: "#40A9FF",
                }}
              />
              minutes
            </Text>
          ) : (
            <div className="text-error">OTP expired!</div>
          )}
          <Form.Item className="my-6">
            <Button
              type="primary"
              htmlType="submit"
              block
              disabled={!allowSubmit}
              loading={loading}
            >
              Verify OTP
            </Button>
          </Form.Item>
          <Text type="secondary">
            Didn&apos;t Receive the Code?
            <Button type="link" onClick={resendOTP} loading={resendOTPLoading}>
              <Text type="secondary" underline>
                Resend OTP
              </Text>
            </Button>
          </Text>
        </Form>
      </div>
    </>
  );
}
