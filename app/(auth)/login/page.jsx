"use client";

import React, { useState, useEffect } from "react";
import { Tabs } from "antd";
import { useBoundStore } from "../../../src/store/store";
import LoginForm from "./login_form";
import styles from "./styles.module.css";

/**
 * Login page component
 *
 * @returns {JSX.Element}
 */
export default function LoginPage() {
  const [transitionEffect, setTransitionEffect] = useState("");
  const hasBackRoute = useBoundStore((state) => state.hasBackRoute);
  const setHasBackRoute = useBoundStore((state) => state.setHasBackRoute);

  useEffect(() => {
    setHasBackRoute(false);
  }, []);

  useEffect(() => {
    if (hasBackRoute) {
      setTransitionEffect("animate__slideOutLeft");
    } else {
      setTransitionEffect("animate__slideInLeft");
    }
  }, [hasBackRoute]);

  return (
    <Tabs
      className={`${styles.loginTabs} animate__animated animate__faster ${transitionEffect}`}
      defaultActiveKey="login-tab"
      size="medium"
      tabBarGutter={72}
      centered
      items={[
        {
          label: <span className={styles.loginTabLabel}>Sign Up</span>,
          key: "signup-tab",
          children: "",
          disabled: true,
        },
        {
          label: <span className={styles.loginTabLabel}>Login</span>,
          key: "login-tab",
          children: <LoginForm />,
        },
      ]}
    />
  );
}
