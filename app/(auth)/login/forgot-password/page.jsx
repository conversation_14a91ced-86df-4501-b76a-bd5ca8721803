"use client";

import React, { useState, useEffect } from "react";
import { Button, Form, Input, message, Typography } from "antd";
import { useBoundStore } from "../../../../src/store/store";
import Validator from "../../../../src/utils/validator";
import Api from "../../../../src/utils/api";
import styles from "./styles.module.css";

const { Text, Title } = Typography;

/**
 * Forgot password page component
 *
 * @returns {JSX.Element}
 */
export default function ForgotPassword() {
  const [allowSubmit, setAllowSubmit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [transitionEffect, setTransitionEffect] = useState("");
  const [validateStatus, setValidateStatus] = useState();
  const [validateMessage, setValidateMessage] = useState();
  const hasBackRoute = useBoundStore((state) => state.hasBackRoute);
  const setHasBackRoute = useBoundStore((state) => state.setHasBackRoute);
  const [form] = Form.useForm();

  useEffect(() => {
    setHasBackRoute(true);
  }, []);

  useEffect(() => {
    if (hasBackRoute) {
      setTransitionEffect("animate__slideInRight");
    } else {
      setTransitionEffect("animate__slideOutRight");
    }
  }, [hasBackRoute]);

  /**
   * Handler function when submitting reset password request form
   *
   * @param {object} values
   */
  const onFinish = (values) => {
    setLoading(true);
    requestResetPassword(values.username);
  };

  /**
   * Request for reset password
   *
   * @param {string} username
   */
  const requestResetPassword = (username) => {
    Api.requestResetPassword(
      (res) => {
        if (res.success) {
          setValidateStatus("success");
          setValidateMessage("");
          message.success(res.message, 10);
        } else {
          setValidateStatus("error");
          setValidateMessage(res.message);
        }
        setLoading(false);
      },
      (err) => {
        setValidateStatus("error");
        setValidateMessage(err);
        setLoading(false);
      },
      {
        username,
      },
    );
  };

  /**
   * Handler function to handle failure when submitting reset password request form
   *
   * @param {string} errorInfo
   */
  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  /**
   * Triggers when username field value changes
   * Allow form submit when value is not empty
   *
   * @param {Event} e
   */
  const handleChange = (e) => {
    setAllowSubmit(e.target.value.length > 0);
    setValidateMessage("");
  };

  return (
    <div className={`animate__animated animate__faster ${transitionEffect}`}>
      <Form
        form={form}
        name="forgot_password"
        className={styles.forgotPasswordForm}
        layout="vertical"
        requiredMark={false}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
      >
        <Title level={4}>Reset Password</Title>
        <Text type="secondary">
          Please enter your username to reset your password.
        </Text>
        <Form.Item
          name="username"
          className="mt-6"
          help={validateMessage}
          validateStatus={validateStatus}
          rules={[
            {
              required: true,
              message: "Please input your username!",
            },
          ]}
        >
          <Input
            placeholder="Enter your username"
            onKeyPress={Validator.onKeyPress}
            onPaste={Validator.onPaste}
            onChange={handleChange}
          />
        </Form.Item>
        <Form.Item className="my-6">
          <Button
            type="primary"
            htmlType="submit"
            block
            disabled={!allowSubmit}
            loading={loading}
          >
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}
