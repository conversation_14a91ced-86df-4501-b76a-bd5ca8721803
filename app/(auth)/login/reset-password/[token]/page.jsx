"use client";

import React, { useState } from "react";
import { CloseCircleOutlined } from "@ant-design/icons";
import { Form, message, Typography } from "antd";
import Image from "next/image";
import { useRouter } from "next/navigation";
import Api from "../../../../../src/utils/api";
import { useEffectApiFetch } from "../../../../../src/hooks";
import loadingSpinner from "../../../../../public/images/loading-spinner.gif";
import ResetPasswordForm from "../../../../../src/utils/forms/reset_password_form";
import Helper from "../../../../../src/utils/helper";
import { useBoundStore } from "../../../../../src/store/store";

const { Text, Title } = Typography;

/**
 * Reset password page component
 *
 * @param {object} params
 *
 * @returns {JSX.Element}
 */
export default function ResetPassword({ params }) {
  const [isTokenValid, setIsTokenValid] = useState(false);
  const [errorMessage, setErrorMessage] = useState();
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [allowSubmit, setAllowSubmit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [validateStatus, setValidateStatus] = useState();
  const [validateMessage, setValidateMessage] = useState();
  const setUserData = useBoundStore((state) => state.setUserData);
  const [form] = Form.useForm();
  const router = useRouter();
  const token = params.token;

  useEffectApiFetch(
    () => {
      return validateResetPasswordToken(token);
    },
    () => {
      setIsTokenValid(false);
      setErrorMessage();
    },
  );

  /**
   * Validate reset password token
   *
   * @param {string} token
   * @returns {AbortController} abortCtl
   */
  const validateResetPasswordToken = (token) => {
    const abortCtl = Api.validateResetPasswordToken(
      (res) => {
        if (res.success) {
          setIsTokenValid(true);
          setErrorMessage();
        } else {
          setIsTokenValid(false);
          setErrorMessage(res.message);
        }
      },
      (err) => {
        setIsTokenValid(false);
        setErrorMessage(err);
      },
      {
        token,
      },
    );

    return abortCtl;
  };

  /**
   * Handler function when submitting reset password form
   *
   * @param {object} values
   */
  const onFinish = (values) => {
    setLoading(true);
    resetPassword(values.password, values.password_confirmation, token);
  };

  /**
   * Reset password
   *
   * @param {string} password
   * @param {string} password_confirmation
   * @param {string} token
   */
  const resetPassword = (password, password_confirmation, token) => {
    Api.resetPassword(
      (res) => {
        if (res.success) {
          setValidateStatus("success");
          setValidateMessage("");
          message.success(res.message, 5);
          Helper.updateUserDataStorage(
            "initial_password_change",
            true,
            setUserData,
          );
        } else {
          setValidateStatus("error");
          setValidateMessage(res.message);
        }
        setLoading(false);
      },
      (err) => {
        setValidateStatus("error");
        setValidateMessage(err);
        setLoading(false);
      },
      {
        password,
        password_confirmation,
        token,
      },
    );
  };

  /**
   * Handler function to handle failure when submitting reset password form
   *
   * @param {string} errorInfo
   */
  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  /**
   * Redirect to login page
   */
  const redirectToLoginPage = () => {
    setIsRedirecting(true);
    router.push("/login");
  };

  return (
    <div>
      {isTokenValid ? (
        <ResetPasswordForm
          form={form}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          validateMessage={validateMessage}
          validateStatus={validateStatus}
          allowSubmit={allowSubmit}
          setAllowSubmit={setAllowSubmit}
          isRedirectToLoginPage={true}
          isRedirecting={isRedirecting}
          redirectToLoginPage={redirectToLoginPage}
          loading={loading}
          formTitle="Reset Password"
        />
      ) : errorMessage ? (
        <div className="text-center">
          <Title level={4}>Reset Password</Title>
          <Text type="danger">
            <CloseCircleOutlined /> {errorMessage}
          </Text>
        </div>
      ) : (
        <div className="text-center">
          <Image
            src={loadingSpinner}
            alt="Loading..."
            width={50}
            unoptimized={true}
          />
          <div>Validating reset password token...</div>
        </div>
      )}
    </div>
  );
}
