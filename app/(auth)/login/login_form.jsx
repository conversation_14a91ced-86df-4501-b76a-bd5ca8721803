import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Divider, Form, Input } from "antd";
import {
  UserOutlined,
  LoginOutlined,
  WindowsFilled,
  GoogleOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Api from "../../../src/utils/api";
import Helper from "../../../src/utils/helper";
import { useBoundStore } from "../../../src/store/store";
import styles from "./styles.module.css";

/**
 * Login form component
 *
 * @returns {JSX.Element}
 */
const LoginForm = () => {
  const router = useRouter();
  const loginData = useBoundStore((state) => state.loginData);
  const setLoginData = useBoundStore((state) => state.setLoginData);
  const [allowSubmit, setAllowSubmit] = useState(loginData.allowSubmit);
  const [loading, setLoading] = useState(false);
  const [validateStatus, setValidateStatus] = useState();
  const [validateMessage, setValidateMessage] = useState();
  const [form] = Form.useForm();

  /**
   * Check for browser auto fill event and enable login button if username and password fields were autofilled.
   * The :autofill CSS pseudo-class matches when an element has its value autofilled by the browser.
   */
  const checkBrowserAutoFillEvent = () => {
    const usernameField = form.getFieldInstance("username");
    const passwordField = form.getFieldInstance("password");

    if (
      usernameField.input.matches(":autofill") &&
      passwordField.input.matches(":autofill")
    ) {
      setAllowSubmit(true);
    }

    passwordField.input.removeEventListener(
      "transitionend",
      checkBrowserAutoFillEvent,
    );
  };

  useEffect(() => {
    const passwordField = form.getFieldInstance("password");
    passwordField.input.addEventListener(
      "transitionend",
      checkBrowserAutoFillEvent,
    );

    return () => {
      passwordField.input.removeEventListener(
        "transitionend",
        checkBrowserAutoFillEvent,
      );
    };
  }, []);

  /**
   * Triggers when submitting login form
   *
   * @param {object} values
   */
  const onFinish = (values) => {
    setLoading(true);
    login(values.username, values.password);
  };

  /**
   * Validate user login
   *
   * @param {string} username
   * @param {string} password
   */
  const login = (username, password) => {
    Api.login(
      (res) => {
        if (res.success) {
          if (res.data.token) {
            Helper.loginUser(res);
          } else {
            setLoginData({
              ...res.data,
              ...{ username, password, allowSubmit },
            });
            router.push("/login/otp");
          }
        } else {
          setValidateStatus("error");
          setValidateMessage(res.message);
        }
        setLoading(false);
      },
      (err) => {
        setValidateStatus("error");
        setValidateMessage(err);
        setLoading(false);
      },
      {
        name: username,
        password: password,
      },
    );
  };

  /**
   * Trigger when form values change
   *
   * @param {object} allValues
   */
  const onValuesChange = (_, allValues) => {
    setAllowSubmit(
      allValues.username !== undefined &&
        allValues.password !== undefined &&
        allValues.username.length > 0 &&
        allValues.password.length > 0,
    );
    setValidateMessage("");
  };

  return (
    <Form
      form={form}
      name="login"
      className={styles.loginForm}
      layout="vertical"
      requiredMark={false}
      onFinish={onFinish}
      onValuesChange={onValuesChange}
      autoComplete="off"
    >
      <Form.Item
        label="Username"
        name="username"
        initialValue={loginData.username}
        validateStatus={validateStatus}
        rules={[
          {
            required: true,
            message: "Please input your username!",
          },
        ]}
      >
        <Input prefix={<UserOutlined />} />
      </Form.Item>

      <Form.Item
        label="Password"
        name="password"
        className="mb-0"
        initialValue={loginData.password}
        validateStatus={validateStatus}
        rules={[
          {
            required: true,
            message: "Please input your password!",
          },
        ]}
      >
        <Input.Password />
      </Form.Item>

      <Form.Item>
        <Link href="/login/forgot-password">Forgot Password?</Link>
        {validateStatus === "error" && (
          <div className="text-error text-center m-0 mb-2">
            {validateMessage}
          </div>
        )}
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          icon={<LoginOutlined />}
          block
          disabled={!allowSubmit}
          loading={loading}
        >
          Login
        </Button>
      </Form.Item>

      <Divider className={`${styles.orDivider} pt-2 pb-3`} plain>
        OR
      </Divider>

      <Form.Item>
        <Button type="primary" htmlType="submit" icon={<WindowsFilled />} block>
          Login with Microsoft
        </Button>
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          icon={<GoogleOutlined />}
          block
        >
          Login with Google
        </Button>
      </Form.Item>
    </Form>
  );
};
export default LoginForm;
