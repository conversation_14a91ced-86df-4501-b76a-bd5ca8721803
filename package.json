{"name": "yh2-ui", "version": "0.1.0", "private": true, "author": "<PERSON> <<EMAIL>> (https://yieldhub.com)", "contributors": ["<PERSON> <<EMAIL>> (https://yieldhub.com)"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "start-dev": "next start -p 3001", "start-prod": "next start", "test": "", "lint": "eslint . --config ./eslint.config.mjs", "execute-husky": "npx husky init && echo \"npm test\nnpx lint-staged\nnpm run build || exit 1\" > .husky/pre-commit", "prepare": "husky"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "dependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@tanstack/react-query": "^5.85.5", "ag-grid-enterprise": "^34.1.2", "ag-grid-react": "^34.1.2", "animate.css": "^4.1.1", "antd": "^5.27.1", "eslint-config-next": "^15.5.2", "highcharts": "^12.3.0", "highcharts-react-official": "^3.2.2", "jsonwebtoken": "^9.0.2", "jstat": "^1.9.6", "libarchive.js": "^2.0.2", "lodash": "^4.17.21", "next": "^15.5.2", "react": "^19.1.1", "react-colorful": "^5.6.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.1", "react-full-screen": "^1.1.1", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.16.0", "server-only": "^0.0.1", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.34.0", "@next/eslint-plugin-next": "^15.5.2", "@tailwindcss/postcss": "^4.1.12", "@tanstack/eslint-plugin-query": "^5.83.1", "autoprefixer": "^10.4.21", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.3.0", "husky": "^9.1.7", "ignore-loader": "^0.1.2", "laravel-echo": "^2.2.0", "lint-staged": "^16.1.5", "postcss": "^8.5.6", "prettier": "^3.6.2", "pusher-js": "^8.4.0", "stylelint-prettier": "^5.0.3", "tailwindcss": "^4.1.12"}, "lint-staged": {"**/*.{js,jsx}": ["eslint . --config ./eslint.config.mjs --fix --quiet", "prettier --write --ignore-unknown"], "**/*.{css,scss}": ["stylelint *.css --fix --quiet", "prettier --write --ignore-unknown"]}}