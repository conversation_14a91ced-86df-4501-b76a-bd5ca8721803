"use client";

import { MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";
import { Layout, theme } from "antd";
import React, { useEffect, useState } from "react";
import SidebarMenu from "./sidebar_menu";
import HeaderMenu from "./header_menu";

const { useToken } = theme;
const { Header, Footer, Sider, Content } = Layout;

/**
 * NOT BEING USED ANYMORE
 * Main layout component
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const MainLayout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [showLogoText, setShowLogoText] = useState(true);
  const { token } = useToken();

  useEffect(() => {
    setShowLogoText(!collapsed);
  }, [collapsed]);

  useEffect(() => {
    setTimeout(() => {}, 300);
  }, [showLogoText]);

  return (
    <Layout className="main-layout">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          backgroundColor: token.colorPrimary,
        }}
      >
        <div className="logo">
          <div
            className="text"
            style={{
              visibility: showLogoText ? "visible" : "hidden",
              opacity: showLogoText ? 1 : 0,
            }}
          >
            yieldHUB
          </div>
        </div>
        <SidebarMenu />
      </Sider>
      <Layout className="site-layout">
        <Header
          className="header"
          style={{
            background: token.colorPrimary,
          }}
        >
          <div>
            {React.createElement(
              collapsed ? MenuUnfoldOutlined : MenuFoldOutlined,
              {
                className: "side-menu-collapse-toogle",
                onClick: () => {
                  setCollapsed(!collapsed);
                },
                style: {
                  color: token.colorWhite,
                },
              },
            )}
          </div>
          <div className="header-center-wrapper"></div>
          <div className="header-right-wrapper">
            <HeaderMenu />
          </div>
        </Header>
        <Content
          className="content"
          style={{
            background: token.colorBgContainer,
          }}
        >
          {children}
        </Content>
        <Footer
          className="footer"
          style={{ background: token.colorPrimary }}
        ></Footer>
      </Layout>
    </Layout>
  );
};
export default MainLayout;
