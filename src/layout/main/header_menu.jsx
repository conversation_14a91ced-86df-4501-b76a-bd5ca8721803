"use client";

import {
  SearchOutlined,
  QuestionCircleOutlined,
  <PERSON>Outlined,
} from "@ant-design/icons";
import { Col, Row, theme } from "antd";

const { useToken } = theme;

/**
 * NOT BEING USED ANYMORE
 * Header menu component
 *
 * @returns {JSX.Element}
 */
const HeaderMenu = () => {
  const { token } = useToken();
  const style = {
    color: token.colorWhite,
  };

  return (
    <Row gutter={24}>
      <Col className="items">
        <SearchOutlined className="icon" style={style} />
      </Col>
      <Col className="items">
        <QuestionCircleOutlined className="icon" style={style} />
      </Col>
      <Col className="items">
        <BellOutlined className="icon" style={style} />
      </Col>
    </Row>
  );
};
export default HeaderMenu;
