"use client";

import { FileOutlined } from "@ant-design/icons";
import { Menu, theme } from "antd";

const { useToken } = theme;

/**
 * NOT BEING USED ANYMORE
 * Sidebar menu component
 *
 * @returns {JSX.Element}
 */
const SidebarMenu = () => {
  const { token } = useToken();

  return (
    <Menu
      theme="dark"
      mode="inline"
      defaultSelectedKeys={["1"]}
      style={{
        backgroundColor: token.colorPrimary,
      }}
      items={[
        {
          key: "1",
          icon: <FileOutlined />,
          label: "Home",
        },
        {
          key: "2",
          icon: <FileOutlined />,
          label: "Page 1",
        },
        {
          key: "3",
          icon: <FileOutlined />,
          label: "Page 2",
        },
      ]}
    />
  );
};
export default SidebarMenu;
