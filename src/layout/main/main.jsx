"use client";

import { useState } from "react";
import { ConfigProvider, theme } from "antd";
import MainLayout from "./main_layout";

const defaultData = {
  colorPrimary: "#154495",
};

/**
 * NOT BEING USED ANYMORE
 * Main layout wrapper
 *
 * @param {JSX.Element} children
 * @returns {JSX.Element}
 */
const Main = ({ children }) => {
  const [data] = useState(defaultData);

  return (
    <ConfigProvider
      theme={{
        algorithm: [theme.compactAlgorithm],
        token: {
          colorPrimary: data.colorPrimary,
        },
        components: {
          Layout: {
            Sider: {
              colorPrimary: data.colorPrimary,
            },
          },
        },
      }}
    >
      <MainLayout>{children}</MainLayout>
    </ConfigProvider>
  );
};
export default Main;
