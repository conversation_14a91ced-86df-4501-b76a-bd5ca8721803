const initialState = {
  mfgProcess: "",
  filters: {},
};

/**
 * Create page data store
 *
 * @param {function} set
 * @param {function} get
 * @returns {object}
 */
export const createPageData = (set, get) => ({
  ...initialState,
  pageOpenKeys: [],
  pageSelectedKeys: [],
  reloadQuantityData: Date.now(),
  pageTitle: "",
  analysisName: "",
  pageMeta: {},
  renderedPages: {},
  currentPageData: {},
  cacheData: {},
  OEEFilters: {
    category: "tester_node",
    category_value: [],
    subcon: [],
    period: "today",
    period_label: "Today",
    limit: 90,
    comparison_period: "yesterday",
    comparison_period_label: "Yesterday",
    display_period_format: "daily",
    exclude_hours: [],
  },
  mainContentRef: null,
  isEditRolePermissionTeamModalOpen: false,
  rolePermissionTeamData: {},
  isGenealogyLinkEditButtonDisabled: false,
  isGenealogyLinkInEditMode: false,
  genealogyLinkEditedLots: {},
  galleryCharts: {},
  galleryChartsUpdateTime: Date.now(),
  selectedStatusOption: {},
  selectedDatalogProcessingSubconOption: undefined,
  isNpiReportsModalOpen: false,
  isRecipeVersionHistoryModalOpen: false,
  isRecipeSelectedDatalogsModalOpen: false,
  isRecipeOwnerNotesModalOpen: false,
  isDataIntegrityModalOpen: false,
  activeRecipeData: {},
  consolidationConfig: {},
  dataIntegrityGridFilter: {},
  tabsData: {},
  rawDataTestNumber: undefined,
  chartKeys: {},
  gridIds: {},
  consolidationRecipeParams: {},
  processYhGridSocketEventQueue: Date.now(),
  renderedComponents: {}, // For now only grids are being added
  yhGridSocketEventQueue: {},
  renderComponentActions: {},
  pageElements: {},
  pageTabs: {},
  pendingLocalChartData: {},
  searchGridId: undefined,
  setMfgProcess: (mfgProcess) => set({ mfgProcess }),
  setFilters: (filters) => set({ filters }),
  setPageOpenKeys: (pageOpenKeys) => set({ pageOpenKeys }),
  setPageSelectedKeys: (pageSelectedKeys) => set({ pageSelectedKeys }),
  setReloadQuantityData: (reloadQuantityData) => set({ reloadQuantityData }),
  setPageTitle: (pageTitle) => set({ pageTitle }),
  setAnalysisName: (analysisName) => set({ analysisName }),
  setPageMeta: (pageMeta) => set({ pageMeta }),
  setRenderedPages: (renderedPages) => set({ renderedPages }),
  setCurrentPageData: (currentPageData) => set({ currentPageData }),
  setCacheData: (cacheData) => set({ cacheData }),
  setOEEFilters: (OEEFilters) => set({ OEEFilters }),
  setMainContentRef: (mainContentRef) => set({ mainContentRef }),
  setIsEditRolePermissionTeamModalOpen: (isEditRolePermissionTeamModalOpen) =>
    set({ isEditRolePermissionTeamModalOpen }),
  setRolePermissionTeamData: (rolePermissionTeamData) =>
    set({ rolePermissionTeamData }),
  setIsGenealogyLinkEditButtonDisabled: (isGenealogyLinkEditButtonDisabled) =>
    set({ isGenealogyLinkEditButtonDisabled }),
  setIsGenealogyLinkInEditMode: (isGenealogyLinkInEditMode) =>
    set({ isGenealogyLinkInEditMode }),
  setGenealogyLinkEditedLots: (genealogyLinkEditedLots) =>
    set({ genealogyLinkEditedLots }),
  setGalleryCharts: (galleryCharts) => set({ galleryCharts }),
  setGalleryChartsUpdateTime: (galleryChartsUpdateTime) =>
    set({ galleryChartsUpdateTime }),
  setSelectedStatusOption: (selectedStatusOption) =>
    set({ selectedStatusOption }),
  setSelectedDatalogProcessingSubconOption: (
    selectedDatalogProcessingSubconOption,
  ) => set({ selectedDatalogProcessingSubconOption }),
  setIsNpiReportsModalOpen: (isNpiReportsModalOpen) =>
    set({ isNpiReportsModalOpen }),
  setIsRecipeVersionHistoryModalOpen: (isRecipeVersionHistoryModalOpen) =>
    set({ isRecipeVersionHistoryModalOpen }),
  setIsRecipeSelectedDatalogsModalOpen: (isRecipeSelectedDatalogsModalOpen) =>
    set({ isRecipeSelectedDatalogsModalOpen }),
  setIsRecipeOwnerNotesModalOpen: (isRecipeOwnerNotesModalOpen) =>
    set({ isRecipeOwnerNotesModalOpen }),
  setIsDataIntegrityModalOpen: (isDataIntegrityModalOpen) =>
    set({ isDataIntegrityModalOpen }),
  setActiveRecipeData: (activeRecipeData) => set({ activeRecipeData }),
  setConsolidationConfig: (consolidationConfig) => set({ consolidationConfig }),
  setDataIntegrityFilter: (dataIntegrityGridFilter) =>
    set({ dataIntegrityGridFilter }),
  setTabsData: (tabsData) => set({ tabsData }),
  setRawDataTestNumber: (rawDataTestNumber) => set({ rawDataTestNumber }),
  setChartKeys: (chartKeys) => set({ chartKeys }),
  setGridIds: (gridIds) => set({ gridIds }),
  setConsolidationRecipeParams: (consolidationRecipeParams) =>
    set({ consolidationRecipeParams }),
  setProcessYhGridSocketEventQueue: (processYhGridSocketEventQueue) =>
    set({ processYhGridSocketEventQueue }),
  setRenderedComponents: (renderedComponents) => set({ renderedComponents }),
  registerRenderComponentAction: (key, fn) =>
    set((state) => ({
      renderComponentActions: {
        ...state.renderComponentActions,
        [key]: fn,
      },
    })),
  updatePageElement: (id, updates) =>
    set((state) => ({
      pageElements: {
        ...state.pageElements,
        [id]: {
          ...state.pageElements[id],
          ...updates,
        },
      },
    })),
  getPageElement: (id) => get().pageElements[id],
  updatePageTabs: (id, updates) =>
    set((state) => ({
      pageTabs: {
        ...state.pageTabs,
        [id]: {
          ...state.pageTabs[id],
          ...updates,
        },
      },
    })),
  getPageTabs: (id) => get().pageTabs[id],
  setPendingLocalChartData: (id, data) =>
    set((state) => ({
      pendingLocalChartData: {
        ...state.pendingLocalChartData,
        [id]: data,
      },
    })),
  popPendingLocalChartData: (id) => {
    const data = get().pendingLocalChartData[id];
    set((state) => {
      const copy = { ...state.pendingLocalChartData };
      delete copy[id];
      return { pendingLocalChartData: copy };
    });
    return data;
  },
  setSearchGridId: (searchGridId) => set({ searchGridId }),
  resetGlobalState: () => set(initialState),
});
