/**
 * Create analysis template data store
 *
 * @param {function} set
 * @returns {object}
 */
export const createAnalysisTemplateData = (set) => ({
  templateId: null,
  templateType: null,
  templateName: null,
  templateRows: [],
  previousTemplateRows: [],
  templateAnalysis: null,
  seletedTemplateId: null,
  presetAnalysisTemplates: [],
  userAnalysisTemplates: [],
  disableEditing: false,
  disableDeletion: true,
  rerenderTemplateDropdown: false,
  setTemplateId: (templateId) => set({ templateId }),
  setTemplateType: (templateType) => set({ templateType }),
  setTemplateName: (templateName) => set({ templateName }),
  setTemplateRows: (templateRows) => set({ templateRows }),
  setPreviousTemplateRows: (previousTemplateRows) =>
    set({ previousTemplateRows }),
  setTemplateAnalysis: (templateAnalysis) => set({ templateAnalysis }),
  setSelectedTemplateId: (selectedTemplateId) => set({ selectedTemplateId }),
  setPresetAnalysisTemplates: (presetAnalysisTemplates) =>
    set({ presetAnalysisTemplates }),
  setUserAnalysisTemplates: (userAnalysisTemplates) =>
    set({ userAnalysisTemplates }),
  setDisableEditing: (disableEditing) => set({ disableEditing }),
  setDisableDeletion: (disableDeletion) => set({ disableDeletion }),
  setRerenderTemplateDropdown: (rerenderTemplateDropdown) =>
    set({ rerenderTemplateDropdown }),
});
