/**
 * Create chart data store
 *
 * @param {function} set
 * @returns {object}
 */
export const createChartData = (set) => ({
  isChartOptionsOpen: false,
  currentChart: {},
  currentChartSettings: {},
  currentChartComponent: {},
  reloadChart: {},
  reloadChartFilters: {},
  reloadChartKeys: [],
  chartComponentRefs: {},
  testStatsInfo: {},
  activeTestNumber: "",
  retainedChartOptions: {},
  setTestStatsInfo: (testStatsInfo) => set({ testStatsInfo }),
  setActiveTestNumber: (activeTestNumber) => set({ activeTestNumber }),
  setIsChartOptionsOpen: (isChartOptionsOpen) => set({ isChartOptionsOpen }),
  setCurrentChart: (currentChart) => set({ currentChart }),
  setCurrentChartSettings: (currentChartSettings) =>
    set({ currentChartSettings }),
  setCurrentChartComponent: (currentChartComponent) =>
    set({ currentChartComponent }),
  setReloadChart: (reloadChart) => set({ reloadChart }),
  setReloadChartFilters: (reloadChartFilters) => set({ reloadChartFilters }),
  setReloadChartKeys: (reloadChartKeys) => set({ reloadChartKeys }),
  setChartComponentRefs: (chartComponentRefs) => set({ chartComponentRefs }),
  setRetainedChartOptions: (key, value) =>
    set(() => ({
      [key]: value,
    })),
});
