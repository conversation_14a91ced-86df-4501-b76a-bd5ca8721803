/**
 * Creates a state slice for NPI data.
 *
 * @param {function} set - The state setter function.
 * @returns {object} The state slice.
 *
 * The state slice will contain the following properties:
 *
 * - `selectedRowsForNpiSimulation`: Array of selected rows for NPI simulation.
 * - `selectedConditions`: Array of selected conditions for NPI simulation.
 * - `availableConditions`: Array of available conditions for NPI simulation.
 * - `filteredConditions`: Array of filtered conditions for NPI simulation.
 * - `npiCharSimulationTestListGridRef`: Ref to the NPI Char Simulation Test List grid.
 *
 * The state slice will also contain the following methods:
 *
 * - `setSelectedRowsForNpiSimulation`: Sets the selected rows for NPI simulation.
 * - `setSelectedConditions`: Sets the selected conditions for NPI simulation.
 * - `setAvailableConditions`: Sets the available conditions for NPI simulation.
 * - `setFilteredConditions`: Sets the filtered conditions for NPI simulation.
 * - `setNpiCharSimulationTestListGridRef`: Sets the ref to the NPI Char Simulation Test List grid.
 */
export const createNPIData = (set) => ({
  selectedRowsForNpiSimulation: [],
  selectedConditions: [],
  availableConditions: [],
  filteredConditions: [],
  npiCharSimulationTestListGridRef: "",
  npiReportOptionsTestFilters: {},
  npiRecipeData: {},
  npiRecipeInfo: {},
  npiSimulationTestTabsRef: null,
  npiRecipeProcessingStatus: {},
  setSelectedRowsForNpiSimulation: (updater) =>
    set((prev) => ({
      selectedRowsForNpiSimulation: updater(prev.selectedRowsForNpiSimulation),
    })),
  setSelectedConditions: (updater) =>
    set((prev) => ({ selectedConditions: updater(prev.selectedConditions) })),
  setAvailableConditions: (updater) =>
    set((prev) => ({ availableConditions: updater(prev.availableConditions) })),
  setFilteredConditions: (updater) =>
    set((prev) => ({ filteredConditions: updater(prev.filteredConditions) })),
  setNpiCharSimulationTestListGridRef: (npiCharSimulationTestListGridRef) =>
    set({ npiCharSimulationTestListGridRef }),
  setNpiSimulationTestTabsRef: (npiSimulationTestTabsRef) =>
    set({ npiSimulationTestTabsRef }),
  setNpiReportOptionsTestFilters: (npiReportOptionsTestFilters) =>
    set({ npiReportOptionsTestFilters }),
  setNpiRecipeData: (updater) =>
    set((prev) => ({ npiRecipeData: updater(prev.npiRecipeData) })),
  setNpiRecipeInfo: (npiRecipeInfo) => set({ npiRecipeInfo }),
  setNpiRecipeProcessingStatus: (npiRecipeProcessingStatus) =>
    set({ npiRecipeProcessingStatus }),
});
