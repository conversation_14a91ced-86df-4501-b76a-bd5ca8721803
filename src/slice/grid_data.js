/**
 * Create grid selection data store
 *
 * @param {function} set
 * @returns {object}
 */
export const createGridData = (set) => ({
  gridSelectionData: {},
  disableRowSelection: false,
  reloadGrid: Date.now(),
  reloadGrids: [],
  reloadGridFilters: {},
  detailGridComponents: {},
  gridComponentRefs: {},
  selectedPartIdData: [],
  setGridSelectionData: (gridSelectionData) => set({ gridSelectionData }),
  setDisableRowSelection: (disableRowSelection) => set({ disableRowSelection }),
  setReloadGrid: (reloadGrid) => set({ reloadGrid }),
  setReloadGrids: (reloadGrids) => set({ reloadGrids }),
  setReloadGridFilters: (reloadGridFilters) => set({ reloadGridFilters }),
  setDetailGridComponents: (detailGridComponents) =>
    set({ detailGridComponents }),
  setGridComponentRefs: (gridComponentRefs) => set({ gridComponentRefs }),
  setSelectedPartIdData: (selectedPartIdData) => set({ selectedPartIdData }),
});
