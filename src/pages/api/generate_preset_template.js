import jwt from "jsonwebtoken";
import Helper from "../../utils/helper";

const totalColspan = 24;

export const config = {
  api: {
    bodyParser: {
      sizeLimit: "4gb",
    },
  },
};

/**
 * Endpoint handler
 *
 * @param {http.IncomingMessage} req
 * @param {http.ServerResponse} res
 */
export default function handler(req, res) {
  let status = 401;
  let data = [];

  if (validateJwt(req) && req.method === "POST") {
    status = 200;
    const presetTemplateBlueprints = req.body.preset_template_blueprint;
    const analysisComponentPropsBlueprint =
      req.body.analysis_component_props_blueprint;

    if (presetTemplateBlueprints && Array.isArray(presetTemplateBlueprints)) {
      presetTemplateBlueprints.forEach((blueprint) => {
        let templateData = {};
        let templateRows = [];
        Object.keys(blueprint.template_data).forEach((dataKey) => {
          switch (dataKey) {
            case "template_rows":
              blueprint.template_data[dataKey].forEach((row, rowKey) => {
                const {
                  columns,
                  options,
                  classname,
                  prerender_process,
                  postrender_process,
                  required_data,
                } = row;
                templateRows[rowKey] = {};
                templateRows[rowKey]["columns"] = [];

                assignIfPresent(templateRows[rowKey], {
                  options,
                  classname,
                  prerender_process,
                  postrender_process,
                  required_data,
                });

                columns.forEach((columnComponent, colKey) => {
                  templateRows[rowKey]["columns"][colKey] = {
                    colspan:
                      columnComponent.colspan ??
                      totalColspan / row.columns.length,
                    component: null,
                    container: columnComponent.container,
                    render_conditions: columnComponent.render_conditions,
                    invert_render_conditions:
                      columnComponent.invert_render_conditions,
                    required_data: columnComponent.required_data,
                  };

                  if (
                    columnComponent.container &&
                    columnComponent.container.type === "tabs"
                  ) {
                    // tabs
                    templateRows[rowKey]["columns"][colKey].tabs = {};
                    generateTabs(
                      analysisComponentPropsBlueprint,
                      templateRows,
                      columnComponent,
                      templateRows[rowKey]["columns"][colKey],
                      rowKey,
                      colKey,
                    );
                  } else if (columnComponent.rows) {
                    // rows inside column
                    templateRows[rowKey]["columns"][colKey]["rows"] = [];
                    columnComponent.rows.forEach((colRow, colRowKey) => {
                      templateRows[rowKey]["columns"][colKey]["rows"][
                        colRowKey
                      ] = {};
                      templateRows[rowKey]["columns"][colKey]["rows"][
                        colRowKey
                      ]["columns"] = [];

                      assignIfPresent(
                        templateRows[rowKey]["columns"][colKey]["rows"][
                          colRowKey
                        ],
                        {
                          options: colRow.options,
                          classname: colRow.classname,
                          prerender_process: colRow.prerender_process,
                          postrender_process: colRow.postrender_process,
                          required_data: colRow.required_data,
                          title: colRow.title,
                          info_message: colRow.info_message,
                        },
                      );

                      colRow["columns"].forEach(
                        (colRowColumnComponent, colRowColKey) => {
                          templateRows[rowKey]["columns"][colKey]["rows"][
                            colRowKey
                          ]["columns"][colRowColKey] = {
                            colspan:
                              colRowColumnComponent.colspan ??
                              totalColspan / colRow["columns"].length,
                            component: null,
                            container: colRowColumnComponent.container,
                            render_conditions:
                              colRowColumnComponent.render_conditions,
                            invert_render_conditions:
                              colRowColumnComponent.invert_render_conditions,
                            required_data: colRowColumnComponent.required_data,
                          };
                          if (
                            colRowColumnComponent.container &&
                            colRowColumnComponent.container.type === "tabs"
                          ) {
                            templateRows[rowKey]["columns"][colKey]["rows"][
                              colRowKey
                            ]["columns"][colRowColKey].tabs = {};

                            generateTabs(
                              analysisComponentPropsBlueprint,
                              templateRows,
                              colRowColumnComponent,
                              templateRows[rowKey]["columns"][colKey]["rows"][
                                colRowKey
                              ]["columns"][colRowColKey],
                              rowKey,
                              colKey,
                              colRowKey,
                              colRowColKey,
                            );
                          } else {
                            templateRows = addComponentToTemplate(
                              templateRows[rowKey]["columns"][colKey]["rows"][
                                colRowKey
                              ]["columns"][colRowColKey],
                              colRowColumnComponent,
                              analysisComponentPropsBlueprint,
                              templateRows,
                              rowKey,
                              colKey,
                              colRowKey,
                              colRowColKey,
                            );
                          }
                        },
                      );
                    });
                  } else {
                    Object.values(analysisComponentPropsBlueprint).forEach(
                      (analysisComponents) => {
                        const components = analysisComponents.filter(
                          (analysisComponent) => {
                            return (
                              analysisComponent.name === columnComponent.name
                            );
                          },
                        );
                        components.forEach((component) => {
                          templateRows = Helper.addTemplateComponent(
                            templateRows[rowKey]["columns"][colKey],
                            component,
                            templateRows,
                            null,
                            rowKey,
                            colKey,
                          );
                        });
                      },
                    );
                  }
                });
              });
              templateData[dataKey] = templateRows;
              break;
            default:
              templateData[dataKey] = blueprint.template_data[dataKey];
          }
        });
        data.push({
          key: blueprint.key,
          name: blueprint.name,
          title: blueprint.title,
          template_data: templateData,
        });
      });
    }
  }

  res.status(status).json(data);
}

/**
 * Assign value in target object if present
 *
 * @param {object} target
 * @param {object} fields
 */
function assignIfPresent(target, fields) {
  Object.entries(fields).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      target[key] = value;
    }
  });
}

/**
 *
 * @param {object} analysisComponentPropsBlueprint
 * @param {array} templateRows
 * @param {object} colRowColumnComponent
 * @param {object} templateRowColumn
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {string} colRowColKey
 */
function generateTabs(
  analysisComponentPropsBlueprint,
  templateRows,
  colRowColumnComponent,
  templateRowColumn,
  rowKey,
  colKey,
  colRowKey,
  colRowColKey,
) {
  Object.keys(colRowColumnComponent.tabs).forEach((tabIndex) => {
    const tab = colRowColumnComponent.tabs[tabIndex];
    const tabComponent = tab.component;
    const tabRows = tab.rows;
    const tabKey = tab.tab_key;

    templateRowColumn.tabs[tabIndex] = {
      label: tab.label,
    };

    assignIfPresent(templateRowColumn.tabs[tabIndex], {
      label_tooltip: tab.label_tooltip,
      close_icon: tab.close_icon,
      tab_key: tab.tab_key,
      trigger_key: tab.trigger_key,
      is_hidden: tab.is_hidden,
      content_process: tab.content_process,
    });

    if (tabComponent) {
      templateRows = addComponentToTemplate(
        templateRowColumn.tabs[tabIndex],
        tabComponent,
        analysisComponentPropsBlueprint,
        templateRows,
        rowKey,
        colKey,
        colRowKey,
        colRowColKey,
        undefined,
        undefined,
        undefined,
        undefined,
        tabKey,
      );
    }

    if (tabRows) {
      if (!templateRowColumn.tabs[tabIndex].rows) {
        templateRowColumn.tabs[tabIndex].rows = {};
      }

      tabRows.forEach((tabRow, colRowColRowKey) => {
        const {
          columns,
          options,
          classname,
          prerender_process,
          postrender_process,
          required_data,
        } = tabRow;
        if (!templateRowColumn.tabs[tabIndex].rows[colRowColRowKey]) {
          templateRowColumn.tabs[tabIndex].rows[colRowColRowKey] = {};
        }

        assignIfPresent(
          templateRowColumn.tabs[tabIndex].rows[colRowColRowKey],
          {
            options,
            classname,
            prerender_process,
            postrender_process,
            required_data,
          },
        );

        columns.forEach((column, colRowColRowColKey) => {
          if (!templateRowColumn.tabs[tabIndex].rows[colRowColRowKey].columns) {
            templateRowColumn.tabs[tabIndex].rows[colRowColRowKey].columns = {};
          }
          if (
            !templateRowColumn.tabs[tabIndex].rows[colRowColRowKey].columns[
              colRowColRowColKey
            ]
          ) {
            templateRowColumn.tabs[tabIndex].rows[colRowColRowKey].columns[
              colRowColRowColKey
            ] = {
              colspan: column.colspan ?? totalColspan / columns.length,
              component: null,
              container: column.container,
              render_conditions: column.render_conditions,
              invert_render_conditions: column.invert_render_conditions,
              required_data: column.required_data,
            };
          }

          if (column.container && column.container.type === "tabs") {
            // tabs
            templateRowColumn.tabs[tabIndex].rows[colRowColRowKey].columns[
              colRowColRowColKey
            ].tabs = {};

            generateTabs(
              analysisComponentPropsBlueprint,
              templateRows,
              column,
              templateRowColumn.tabs[tabIndex].rows[colRowColRowKey].columns[
                colRowColRowColKey
              ],
              rowKey,
              colKey,
              colRowKey,
              colRowColKey,
            );
          } else if (column.rows) {
            column.rows.forEach((row, colRowColRowColRowKey) => {
              const {
                columns,
                options,
                classname,
                prerender_process,
                postrender_process,
                required_data,
              } = row;

              if (
                !templateRowColumn.tabs[tabIndex].rows[colRowColRowKey][
                  "columns"
                ][colRowColRowColKey].rows
              ) {
                templateRowColumn.tabs[tabIndex].rows[colRowColRowKey][
                  "columns"
                ][colRowColRowColKey].rows = {};
              }

              if (
                !templateRowColumn.tabs[tabIndex].rows[colRowColRowKey][
                  "columns"
                ][colRowColRowColKey].rows[colRowColRowColRowKey]
              ) {
                templateRowColumn.tabs[tabIndex].rows[colRowColRowKey][
                  "columns"
                ][colRowColRowColKey].rows[colRowColRowColRowKey] = {};
              }

              assignIfPresent(
                templateRowColumn.tabs[tabIndex].rows[colRowColRowKey][
                  "columns"
                ][colRowColRowColKey].rows[colRowColRowColRowKey],
                {
                  options,
                  classname,
                  prerender_process,
                  postrender_process,
                  required_data,
                },
              );

              columns.forEach((column, colRowColRowColRowColKey) => {
                if (
                  !templateRowColumn.tabs[tabIndex].rows[colRowColRowKey]
                    .columns[colRowColRowColKey].rows[colRowColRowColRowKey]
                    .columns
                ) {
                  templateRowColumn.tabs[tabIndex].rows[
                    colRowColRowKey
                  ].columns[colRowColRowColKey].rows[
                    colRowColRowColRowKey
                  ].columns = {};
                }
                if (
                  !templateRowColumn.tabs[tabIndex].rows[colRowColRowKey]
                    .columns[colRowColRowColKey].rows[colRowColRowColRowKey]
                    .columns[colRowColRowColRowColKey]
                ) {
                  templateRowColumn.tabs[tabIndex].rows[
                    colRowColRowKey
                  ].columns[colRowColRowColKey].rows[
                    colRowColRowColRowKey
                  ].columns[colRowColRowColRowColKey] = {
                    colspan: column.colspan ?? totalColspan / columns.length,
                    component: null,
                    container: column.container,
                    render_conditions: column.render_conditions,
                    invert_render_conditions: column.invert_render_conditions,
                    required_data: column.required_data,
                  };
                }

                if (column.container && column.container.type === "tabs") {
                  // tabs
                  templateRowColumn.tabs[tabIndex].rows[
                    colRowColRowKey
                  ].columns[colRowColRowColKey].rows[
                    colRowColRowColRowKey
                  ].columns[colRowColRowColRowColKey].tabs = {};

                  generateTabs(
                    analysisComponentPropsBlueprint,
                    templateRows,
                    column,
                    templateRowColumn.tabs[tabIndex].rows[colRowColRowKey]
                      .columns[colRowColRowColKey].rows[colRowColRowColRowKey]
                      .columns[colRowColRowColRowColKey],
                    rowKey,
                    colKey,
                    colRowKey,
                    colRowColKey,
                  );
                } else if (column.rows) {
                  column.rows.forEach((row, colRowColRowColRowColRowKey) => {
                    const {
                      columns,
                      options,
                      classname,
                      prerender_process,
                      postrender_process,
                      required_data,
                    } = row;

                    if (
                      !templateRowColumn.tabs[tabIndex].rows[colRowColRowKey][
                        "columns"
                      ][colRowColRowColKey].rows[colRowColRowColRowKey][
                        "columns"
                      ][colRowColRowColRowColKey].rows
                    ) {
                      templateRowColumn.tabs[tabIndex].rows[colRowColRowKey][
                        "columns"
                      ][colRowColRowColKey].rows[colRowColRowColRowKey][
                        "columns"
                      ][colRowColRowColRowColKey].rows = {};
                    }
                    if (
                      !templateRowColumn.tabs[tabIndex].rows[colRowColRowKey][
                        "columns"
                      ][colRowColRowColKey].rows[colRowColRowColRowKey][
                        "columns"
                      ][colRowColRowColRowColKey].rows[
                        colRowColRowColRowColRowKey
                      ]
                    ) {
                      templateRowColumn.tabs[tabIndex].rows[colRowColRowKey][
                        "columns"
                      ][colRowColRowColKey].rows[colRowColRowColRowKey][
                        "columns"
                      ][colRowColRowColRowColKey].rows[
                        colRowColRowColRowColRowKey
                      ] = {};
                    }

                    assignIfPresent(
                      templateRowColumn.tabs[tabIndex].rows[colRowColRowKey][
                        "columns"
                      ][colRowColRowColKey].rows[colRowColRowColRowKey][
                        "columns"
                      ][colRowColRowColRowColKey].rows[
                        colRowColRowColRowColRowKey
                      ],
                      {
                        options,
                        classname,
                        prerender_process,
                        postrender_process,
                        required_data,
                      },
                    );

                    columns.forEach(
                      (column, colRowColRowColRowColRowColKey) => {
                        if (
                          !templateRowColumn.tabs[tabIndex].rows[
                            colRowColRowKey
                          ].columns[colRowColRowColKey].rows[
                            colRowColRowColRowKey
                          ]["columns"][colRowColRowColRowColKey].rows[
                            colRowColRowColRowColRowKey
                          ].columns
                        ) {
                          templateRowColumn.tabs[tabIndex].rows[
                            colRowColRowKey
                          ].columns[colRowColRowColKey].rows[
                            colRowColRowColRowKey
                          ]["columns"][colRowColRowColRowColKey].rows[
                            colRowColRowColRowColRowKey
                          ].columns = {};
                        }
                        if (
                          !templateRowColumn.tabs[tabIndex].rows[
                            colRowColRowKey
                          ].columns[colRowColRowColKey].rows[
                            colRowColRowColRowKey
                          ].columns[colRowColRowColRowColKey].rows[
                            colRowColRowColRowColRowKey
                          ].columns[colRowColRowColRowColRowColKey]
                        ) {
                          templateRowColumn.tabs[tabIndex].rows[
                            colRowColRowKey
                          ].columns[colRowColRowColKey].rows[
                            colRowColRowColRowKey
                          ].columns[colRowColRowColRowColKey].rows[
                            colRowColRowColRowColRowKey
                          ].columns[colRowColRowColRowColRowColKey] = {
                            colspan:
                              column.colspan ?? totalColspan / columns.length,
                            component: null,
                            container: column.container,
                            render_conditions: column.render_conditions,
                            invert_render_conditions:
                              column.invert_render_conditions,
                            required_data: column.required_data,
                          };
                        }

                        if (
                          column.container &&
                          column.container.type === "tabs"
                        ) {
                          // tabs
                          templateRowColumn.tabs[tabIndex].rows[
                            colRowColRowKey
                          ].columns[colRowColRowColKey].rows[
                            colRowColRowColRowKey
                          ].columns[colRowColRowColRowColKey].rows[
                            colRowColRowColRowColRowKey
                          ].columns[colRowColRowColRowColRowColKey].tabs = {};

                          generateTabs(
                            analysisComponentPropsBlueprint,
                            templateRows,
                            column,
                            templateRowColumn.tabs[tabIndex].rows[
                              colRowColRowKey
                            ].columns[colRowColRowColKey].rows[
                              colRowColRowColRowKey
                            ].columns[colRowColRowColRowColKey].rows[
                              colRowColRowColRowColRowKey
                            ].columns[colRowColRowColRowColRowColKey],
                            rowKey,
                            colKey,
                            colRowKey,
                            colRowColKey,
                          );
                        } else {
                          templateRows = addComponentToTemplate(
                            templateRowColumn.tabs[tabIndex].rows[
                              colRowColRowKey
                            ].columns[colRowColRowColKey].rows[
                              colRowColRowColRowKey
                            ].columns[colRowColRowColRowColKey].rows[
                              colRowColRowColRowColRowKey
                            ].columns[colRowColRowColRowColRowColKey],
                            column,
                            analysisComponentPropsBlueprint,
                            templateRows,
                            rowKey,
                            colKey,
                            colRowKey,
                            colRowColKey,
                            colRowColRowKey,
                            colRowColRowColKey,
                            colRowColRowColRowKey,
                            colRowColRowColRowColKey,
                            colRowColRowColRowColRowKey,
                            colRowColRowColRowColRowColKey,
                            tabKey,
                          );
                        }
                      },
                    );
                  });
                } else {
                  templateRows = addComponentToTemplate(
                    templateRowColumn.tabs[tabIndex].rows[colRowColRowKey]
                      .columns[colRowColRowColKey].rows[colRowColRowColRowKey]
                      .columns[colRowColRowColRowColKey],
                    column,
                    analysisComponentPropsBlueprint,
                    templateRows,
                    rowKey,
                    colKey,
                    colRowKey,
                    colRowColKey,
                    colRowColRowKey,
                    colRowColRowColKey,
                    colRowColRowColRowKey,
                    colRowColRowColRowColKey,
                    undefined,
                    undefined,
                    tabKey,
                  );
                }
              });
            });
          } else {
            templateRows = addComponentToTemplate(
              templateRowColumn.tabs[tabIndex].rows[colRowColRowKey]["columns"][
                colRowColRowColKey
              ],
              column,
              analysisComponentPropsBlueprint,
              templateRows,
              rowKey,
              colKey,
              colRowKey,
              colRowColKey,
              colRowColRowKey,
              colRowColRowColKey,
              undefined,
              undefined,
              undefined,
              undefined,
              tabKey,
            );
          }
        });
      });
    }
  });
}

/**
 * Insert component blueprint data to template data
 *
 * @param {object} target
 * @param {object} colRowColumnComponent
 * @param {array} analysisComponentPropsBlueprint
 * @param {array} templateRows
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {string} colRowColKey
 * @param {string} colRowColRowKey
 * @param {string} colRowColRowColKey
 * @param {string} colRowColRowColRowKey
 * @param {string} colRowColRowColRowColKey
 * @param {string} colRowColRowColRowColRowKey
 * @param {string} colRowColRowColRowColRowColKey
 * @param {string} tabKey
 * @returns {array} templateRows
 */
function addComponentToTemplate(
  target,
  colRowColumnComponent,
  analysisComponentPropsBlueprint,
  templateRows,
  rowKey,
  colKey,
  colRowKey,
  colRowColKey,
  colRowColRowKey,
  colRowColRowColKey,
  colRowColRowColRowKey,
  colRowColRowColRowColKey,
  colRowColRowColRowColRowKey,
  colRowColRowColRowColRowColKey,
  tabKey,
) {
  Object.values(analysisComponentPropsBlueprint).forEach(
    (analysisComponents) => {
      const components = analysisComponents.filter((analysisComponent) => {
        return analysisComponent.name === colRowColumnComponent.name;
      });
      components.forEach((component) => {
        templateRows = Helper.addTemplateComponent(
          target,
          component,
          templateRows,
          null,
          rowKey,
          colKey,
          colRowKey,
          colRowColKey,
          colRowColRowKey,
          colRowColRowColKey,
          colRowColRowColRowKey,
          colRowColRowColRowColKey,
          colRowColRowColRowColRowKey,
          colRowColRowColRowColRowColKey,
          tabKey,
        );
      });
    },
  );

  return templateRows;
}

/**
 * Validate the JWT token
 *
 * @param {http.IncomingMessage} req
 * @returns {boolean}
 */
function validateJwt(req) {
  const tokenStr = req.headers["authorization"];
  const token = tokenStr.replace("Bearer ", "");
  const decoded = jwt.verify(token, process.env.YH2_UI_API_SECRET);

  return decoded?.trigger === "yh2_api_generate_presets";
}
