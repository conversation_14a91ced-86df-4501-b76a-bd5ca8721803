"use client";

import { App, Typography } from "antd";
import { useRef, useState } from "react";
import Api from "../../api";
import { useEffectApiFetch } from "../../../hooks";
import { useBoundStore } from "../../../store/store";
import YHGrid from "../yh_grid";

const { Text } = Typography;

/**
 * Data Integrity grid component
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function DataIntegrityGrid({ pageKey }) {
  const [dataIntegrityGridComponent, setDataIntegrityGridComponent] =
    useState();
  const filters = useBoundStore((state) => state.filters);
  const dataIntegrityGridFilter = useBoundStore(
    (state) => state.dataIntegrityGridFilter,
  );
  const { message } = App.useApp();
  const dataIntegrityGridRef = useRef();

  useEffectApiFetch(
    () => {
      return getDataIntegrityGridComponent();
    },
    () => {
      setDataIntegrityGridComponent();
    },
  );

  /**
   * Get and set recipe version data integrity grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getDataIntegrityGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setDataIntegrityGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: "data_integrity_table",
      },
    );

    return abortCtl;
  };

  return (
    <div className="h-[50vh] mb-5 my-2">
      <Text>Lot ID: </Text>
      <Text strong>{dataIntegrityGridFilter.lot_id}</Text>
      {dataIntegrityGridComponent && (
        <YHGrid
          gridRef={dataIntegrityGridRef}
          gridId={`${pageKey}_${dataIntegrityGridComponent.name}`}
          component={dataIntegrityGridComponent}
          pageKey={pageKey}
          filters={filters}
          initialGridFilters={dataIntegrityGridFilter}
          rowGroups={[]}
          wrapperClassName="flex grow flex-col h-full"
        />
      )}
    </div>
  );
}
