"use client";

import { message, Select, Space } from "antd";
import { useState } from "react";
import { useEffectApiFetch } from "../../../hooks";
import { useBoundStore } from "../../../store/store";
import Api from "../../api";

/**
 * Status select component
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function FilterStatusSelect(props) {
  const [messageApi] = message.useMessage();
  let selectedStatusOption = useBoundStore(
    (state) => state.selectedStatusOption,
  );
  let [statusOptions, setStatusOptions] = useState([]);

  useEffectApiFetch(
    () => {
      return getStatusOptions();
    },
    () => {
      setStatusOptions();
    },
  );

  /**
   * Get and set datalog processing updates status options
   *
   * @returns {AbortController} abortCtl
   */
  const getStatusOptions = () => {
    const abortCtl = Api.getSelectOptions(
      (res) => {
        if (res.success) {
          setStatusOptions(res.data);
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      {
        field: "file_status",
        cache_it: 0,
      },
    );

    return abortCtl;
  };

  return (
    <Space key={`select_${props.componentKey}_wrapper`}>
      <Select
        mode={props.mode ? props.mode : null}
        key={`select_${props.componentKey}`}
        className={props.className ?? "min-w-32"}
        showSearch
        value={selectedStatusOption[props.gridId]}
        placeholder={props.placeholder}
        onChange={props.onChange}
        optionFilterProp="children"
        filterOption={(input, option) =>
          (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
        }
        options={statusOptions}
      />
    </Space>
  );
}
