import { useGridFilter } from "ag-grid-react";
import { Select } from "antd";
import React, { useCallback, useRef } from "react";

export default function RecipeAutoTriggerStatusFilter({
  model,
  onModelChange,
}) {
  const refInput = useRef(null);

  /**
   * The grid will ask each active filter, in turn, whether each row in the grid passes.
   */
  const doesFilterPass = useCallback(
    (params) => {
      const { data } = params;
      const filterValue = model.value;
      const autoTriggerData = data.auto_trigger.filter((data) => {
        return data.trigger_name === "auto_trigger_production";
      })[0];
      if (autoTriggerData) {
        return autoTriggerData.trigger_value === filterValue;
      } else {
        return false;
      }
    },
    [model],
  );

  /**
   * Gets called every time the popup is shown, after the GUI returned in getGui is attached to the DOM.
   *
   * @param {object} params
   */
  const afterGuiAttached = useCallback((params) => {
    if (!params || !params.suppressFocus) {
      /**
       * Focus the input element for keyboard navigation.
       * Can't do this in an effect, as the component is not recreated when hidden and then shown again
       */
      refInput.current.focus();
    }
  }, []);

  /**
   * Register filter handlers with the grid
   */
  useGridFilter({
    doesFilterPass,
    afterGuiAttached,
  });

  return (
    <div className="p-1">
      <Select
        ref={refInput}
        key={`select_auto_trigger_status`}
        className="w-full rounded-sm!"
        defaultValue=""
        size="small"
        optionFilterProp="children"
        onChange={(value) => onModelChange(value === "" ? null : { value })}
        options={[
          {
            value: "",
            label: "All Trigger Status",
          },
          {
            value: 1,
            label: "Auto-Trigger On",
          },
          {
            value: 0,
            label: "Auto-Trigger Off",
          },
        ]}
      />
    </div>
  );
}
