"use client";

import { message, Select, Flex } from "antd";
import { useState } from "react";
import { useEffectApiFetch } from "../../../hooks";
import { useBoundStore } from "../../../store/store";
import Api from "../../api";
import Helper from "../../../../src/utils/helper";

/**
 * Subcon select component
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function FilterSubconSelect(props) {
  const [manufacturingProcessOptions, setManufacturingProcessOptions] =
    useState([]);
  const [messageApi] = message.useMessage();
  let selectedDatalogProcessingSubconOption = useBoundStore(
    (state) => state.selectedDatalogProcessingSubconOption,
  );
  let [subconOptions, setSubconOptions] = useState([]);

  useEffectApiFetch(
    () => {
      return Helper.getManufacturingProcessOptions(
        setManufacturingProcessOptions,
      );
    },
    () => {
      setManufacturingProcessOptions([]);
    },
  );

  useEffectApiFetch(
    () => {
      if (manufacturingProcessOptions.length > 0) return getSubconOptions();
    },
    () => {
      setSubconOptions();
    },
    [manufacturingProcessOptions],
  );

  /**
   * Get and set datalog processing updates subcon options
   *
   * @returns {AbortController} abortCtl
   */
  const getSubconOptions = () => {
    const abortCtl = Api.getSelectOptions(
      (res) => {
        if (res.success) {
          setSubconOptions(res.data);
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      {
        field: "subcon",
        mfg_process: manufacturingProcessOptions
          .map((mfgProcess) => {
            return mfgProcess.value;
          })
          .join(),
        cache_it: 0,
      },
    );

    return abortCtl;
  };

  return (
    <Flex
      key={`select_${props.componentKey}_wrapper`}
      flex={props.flex ?? "normal"}
    >
      <Select
        mode={props.mode ? props.mode : null}
        key={`select_${props.componentKey}`}
        className={props.className ?? "min-w-32"}
        showSearch={props.showSearch ?? true}
        value={selectedDatalogProcessingSubconOption}
        placeholder={props.placeholder}
        onChange={props.onChange}
        optionFilterProp="children"
        filterOption={(input, option) =>
          (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
        }
        options={subconOptions}
      />
    </Flex>
  );
}
