"use client";

import { AgGridReact } from "ag-grid-react";
import Tag<PERSON>ell<PERSON>enderer from "../grid_cell_renderers/tagCellRenderer";

/**
 * Simple aggrid wrapper component
 *
 * @param {array} rowData
 * @param {array} columnDefs
 * @param {string} wrapperClassName
 * @returns {JSX.Element}
 */
const YHGridSimple = ({
  rowData = [],
  columnDefs = [],
  wrapperClassName = "h-24",
}) => {
  columnDefs.forEach((colDef) => {
    if (colDef.field === "condition_value") {
      colDef.cellRenderer = TagCellRenderer;
    }
  });

  return (
    <div className={`ag-theme-balham ${wrapperClassName}`}>
      <AgGridReact rowData={rowData} columnDefs={columnDefs} />
    </div>
  );
};

export default YHGridSimple;
