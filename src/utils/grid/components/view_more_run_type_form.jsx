"use client";

import { App, <PERSON><PERSON>, Form, Select } from "antd";
import { useState } from "react";
import { useEffectApiFetch } from "../../../hooks";
import { useBoundStore } from "../../../store/store";
import Api from "../../api";

/**
 * View more run type form component
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function ViewMoreRunTypeForm({ pageKey }) {
  const [runTypeOptions, setRunTypeOptions] = useState([]);
  const urlParams = useBoundStore((state) => state.urlParams);
  const { message } = App.useApp();

  useEffectApiFetch(
    () => {
      return getRunTypeOptions();
    },
    () => {
      setRunTypeOptions([]);
    },
  );

  /**
   * Get run type options and set it to run type selection
   *
   * @returns {AbortController} abortCtl
   */
  const getRunTypeOptions = () => {
    const abortCtl = Api.getRunTypeOptions(
      (res) => {
        if (res.success) {
          setRunTypeOptions(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        src_type: urlParams[pageKey].src_type,
        src_value: urlParams[pageKey].src_value,
        mfg_process: urlParams[pageKey].mfg_process,
      },
    );

    return abortCtl;
  };

  return (
    <div>
      <Form layout="inline" name={`${pageKey}_view_more_run_type_form`}>
        <Form.Item name="run_type" label="View More Run Type">
          <Select
            className="w-40!"
            mode="multiple"
            maxTagCount="responsive"
            showSearch
            allowClear
            placeholder="Please select Run Type"
            optionFilterProp="children"
            popupMatchSelectWidth={false}
            filterOption={(input, option) =>
              (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
            }
            options={runTypeOptions}
          />
        </Form.Item>
        <Form.Item>
          <Button htmlType="submit">Apply Filter</Button>
        </Form.Item>
      </Form>
    </div>
  );
}
