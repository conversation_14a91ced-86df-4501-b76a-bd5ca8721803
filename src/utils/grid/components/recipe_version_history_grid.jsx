"use client";

import { App } from "antd";
import { useRef, useState } from "react";
import Api from "../../api";
import { useEffectApiFetch } from "../../../hooks";
import { useBoundStore } from "../../../store/store";
import YHGrid from "../yh_grid";

/**
 * Recipe version history grid component
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function RecipeVersionHistoryGrid({ pageKey }) {
  const [historyGridComponent, setHistoryGridComponent] = useState();
  const filters = useBoundStore((state) => state.filters);
  const setFilters = useBoundStore((state) => state.setFilters);
  const activeRecipeData = useBoundStore((state) => state.activeRecipeData);
  const { message } = App.useApp();
  const searchGridRef = useRef();

  useEffectApiFetch(
    () => {
      return getHistoryGridComponent();
    },
    () => {
      setHistoryGridComponent();
    },
  );

  /**
   * Get and set recipe version history grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getHistoryGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setHistoryGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: "recipe_version_history_table",
      },
    );

    return abortCtl;
  };

  return (
    <>
      {historyGridComponent && (
        <YHGrid
          gridRef={searchGridRef}
          gridId={`${pageKey}_${historyGridComponent.name}`}
          component={historyGridComponent}
          pageKey={pageKey}
          filters={filters}
          setFilters={setFilters}
          initialGridFilters={{
            recipe_key:
              activeRecipeData[pageKey] && activeRecipeData[pageKey].recipe_key
                ? activeRecipeData[pageKey].recipe_key
                : undefined,
          }}
          rowGroups={[]}
          wrapperClassName="flex grow flex-col h-full"
        />
      )}
    </>
  );
}
