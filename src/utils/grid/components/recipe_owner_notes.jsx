"use client";

import { Descriptions, Typography } from "antd";
import { useEffect, useState } from "react";
import { useBoundStore } from "../../../store/store";

const { Text } = Typography;
const dataMapper = {
  recipe_name: "Recipe Name",
  full_name: "Version Created By",
  date_created: "Date Updated",
  version: "Version number",
  notes: "",
};
/**
 * Recipe owner notes component
 *
 * @param {object} props
 * @param {string} props.pageKey
 * @returns {JSX.Element}
 */
export default function RecipeOwnerNotes({ pageKey }) {
  const [items, setItems] = useState([]);
  const activeRecipeData = useBoundStore((state) => state.activeRecipeData);
  const currentPageData = useBoundStore((state) => state.currentPageData);

  useEffect(() => {
    if (pageKey === currentPageData.key) {
      setItems(
        Object.keys(dataMapper).map((itemKey, i) => {
          return {
            key: i,
            label: <Text strong>{dataMapper[itemKey]}</Text>,
            children: (
              <Text className="overflow-auto max-h-60">
                {activeRecipeData[pageKey][itemKey]}
              </Text>
            ),
          };
        }),
      );
    }
  }, [activeRecipeData]);

  return (
    <>
      {activeRecipeData && (
        <Descriptions colon={false} column={1} items={items} />
      )}
    </>
  );
}
