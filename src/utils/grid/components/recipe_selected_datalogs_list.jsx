"use client";

import { List, Typography } from "antd";
import { useEffect, useState } from "react";
import { useBoundStore } from "../../../store/store";

/**
 * Recipe applied datalogs list component
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function RecipeSelectedDatalogsList({ pageKey }) {
  const [listData, setListData] = useState([]);
  const activeRecipeData = useBoundStore((state) => state.activeRecipeData);
  const currentPageData = useBoundStore((state) => state.currentPageData);

  useEffect(() => {
    if (pageKey === currentPageData.key) {
      setListData(
        activeRecipeData[pageKey].selected_datalogs.map((data) => {
          return data.file_name;
        }),
      );
    }
  }, [activeRecipeData]);

  return (
    <>
      {activeRecipeData && (
        <List
          className="rounded-none mt-4 mb-8"
          header={<Typography.Text strong>Datalogs</Typography.Text>}
          bordered
          dataSource={listData}
          renderItem={(item) => (
            <List.Item key={item}>
              <span className="break-all">{item}</span>
            </List.Item>
          )}
        />
      )}
    </>
  );
}
