"use client";

import { Flex, message, Select } from "antd";
import { useEffect, useState } from "react";
import { useEffectApiFetch } from "../../../hooks";
import Api from "../../api";
import Helper from "../../helper";

/**
 * Selection component with options from API
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function FilterSelect(props) {
  const [messageApi] = message.useMessage();
  const [options, setOptions] = useState([]);
  const selectProps = {};
  if (props.defaultValue !== undefined) {
    selectProps.defaultValue = props.params.userSettings?.key
      ? (Helper.getUserSettings(props.params.userSettings.key) ??
        props.defaultValue)
      : props.defaultValue;
  }
  if (props.onChange) {
    selectProps.onChange = props.onChange;
  }
  if (props.maxTagCount) {
    selectProps.maxTagCount = "responsive";
  }
  if (props.value !== undefined && props.value !== null) {
    selectProps.value = props.value;
  }

  useEffectApiFetch(
    () => {
      return getOptions();
    },
    () => {
      setOptions();
    },
    props.deps,
  );

  useEffect(() => {
    if (
      selectProps.defaultValue !== undefined &&
      selectProps.defaultValue !== props.defaultValue &&
      typeof selectProps.onChange === "function"
    ) {
      selectProps.onChange(selectProps.defaultValue);
    }
  }, []);

  useEffect(() => {
    if (typeof props.setOptions === "function") {
      if (options) {
        props.setOptions(options);
      }
    }
  }, [options]);

  /**
   * Get and set selection options
   *
   * @returns {AbortController} abortCtl
   */
  const getOptions = () => {
    const abortCtl = Api.getSelectOptions(
      (res) => {
        if (res.success) {
          let data = res.data;
          if (props.optionPrefix) {
            data.forEach((option) => {
              option.label = `${props.optionPrefix}${option.label}`;
            });
          }
          if (props.params.allOption) {
            data.unshift({
              label: props.params.allOption.label,
              value: props.params.allOption.value,
            });
          }
          setOptions(data);
          if (props.selectFirstOption && data.length) {
            selectFirstOption(data, props.form, props.fieldName);
            props.successCbk?.(data?.[0]);
          }
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      props.params.api,
    );

    return abortCtl;
  };

  /**
   * Auto-select the first option in a <Select>.
   *
   * @param {array}   data
   * @param {object}  form
   * @param {string}  fieldName
   */
  const selectFirstOption = (data, form, fieldName) => {
    const firstVal = data[0];

    if (form) {
      form.setFieldValue(fieldName, firstVal);
    }
  };

  return (
    <Flex key={`${props.componentKey}_wrapper`} flex={props.flex ?? "normal"}>
      <Select
        mode={props.mode ?? null}
        key={props.componentKey}
        className={props.className ? props.className : "!w-40"}
        labelInValue={props.labelInValue ?? false}
        disabled={props.disabled ?? false}
        popupMatchSelectWidth={false}
        showSearch={props.showSearch ?? true}
        allowClear={props.allowClear ?? true}
        placeholder={props.placeholder}
        optionFilterProp="children"
        filterOption={(input, option) =>
          (option?.label.toString() ?? "")
            .toLowerCase()
            .includes(input.toLowerCase())
        }
        options={options}
        {...selectProps}
      />
    </Flex>
  );
}
