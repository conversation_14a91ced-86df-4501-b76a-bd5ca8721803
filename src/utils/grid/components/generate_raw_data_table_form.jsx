"use client";

import { Button, Checkbox, Form, Select } from "antd";
import { useEffect, useState } from "react";
import { useBoundStore } from "../../../store/store";
import TestListSelect from "../../components/test_list_select";
import Helper from "../../helper";
import SearchInput from "../../forms/fields/search_input";
import Api from "../../api";
import FilterSelect from "./filter_select";

/**
 * View more run type form component
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function GenerateRawDataTableForm({ pageKey }) {
  const [testListOptions, setTestListOptions] = useState([]);
  const [disablePerPinCheckbox, setDisablePerPinCheckbox] = useState(true);
  const [disablePinSelection, setDisablePinSelection] = useState(true);
  const [disableViewRawDataBtn, setDisableViewRawDataBtn] = useState(true);
  const [shouldClearTestList, setShouldClearTestList] = useState(false);
  const [shouldClearPinNumbers, setShouldClearPinNumbers] = useState(false);
  const urlParams = useBoundStore((state) => state.urlParams);
  const [apiParams, setApiParams] = useState({
    ...urlParams[pageKey],
    sort_by: "fails_overall",
  });
  const rawDataTestNumber = useBoundStore((state) => state.rawDataTestNumber);
  const setRawDataTestNumber = useBoundStore(
    (state) => state.setRawDataTestNumber,
  );
  const [rawDataFilterForm] = Form.useForm();

  useEffect(() => {
    if (rawDataTestNumber && testListOptions.length > 0) {
      const options = testListOptions.filter((option) => {
        return option.value.indexOf(`|${rawDataTestNumber}|`) !== -1;
      });
      if (options.length > 0) {
        rawDataFilterForm.setFieldsValue({
          test_type: options[0].value?.split("|")[2] ?? "",
          tnum: options[0],
        });
        onChangeTest(options[0]);
        rawDataFilterForm.submit();
        setRawDataTestNumber();
      }
    }
  }, [rawDataTestNumber, testListOptions]);

  /**
   * Handle test list options change event
   *
   * @param {array} options
   */
  const onChangeTestListOptions = (options) => {
    setTestListOptions(options);
    onChangeTest(rawDataFilterForm.getFieldValue("tnum"));
  };

  /**
   * Triggers when test selection change
   * Disable view raw data button when there is no test selected
   *
   * @param {object} valueObj
   */
  const onChangeTest = (valueObj) => {
    const testType = valueObj?.value?.split("|")[2];
    setDisablePerPinCheckbox(testType !== "m");
    setDisableViewRawDataBtn(valueObj === undefined || valueObj === null);
    setShouldClearPinNumbers(true);
  };

  /**
   * Handle test list options change event
   *
   * @param {array} options
   */
  const onChangePinNumberOptions = (options) => {
    if (options.length === 0) {
      rawDataFilterForm.setFieldValue("pin_numbers", []);
    }
  };

  /**
   * Trigger when form values change
   *
   * @param {object} allValues
   */
  const onValuesChange = (_, allValues) => {
    setDisableViewRawDataBtn(
      allValues.tnum === undefined ||
        allValues.tnum === null ||
        (allValues.per_pin === true &&
          (!Array.isArray(allValues.pin_numbers) ||
            (Array.isArray(allValues.pin_numbers) &&
              allValues.pin_numbers.length === 0))),
    );
  };

  /**
   * Trigger when test type change
   *
   * @param {string} value
   */
  const handleTestTypeChange = (value) => {
    rawDataFilterForm.setFieldValue("tnum", null);
    setShouldClearTestList(true);
    const apiParamsCopy = Helper.cloneObject(apiParams);
    apiParamsCopy.test_type = value;
    setApiParams(apiParamsCopy);
  };

  /**
   * Trigger when per pin value change
   *
   * @param {CheckboxChangeEvent} event
   */
  const handlePerPinChange = (event) => {
    const checked = event.target.checked;
    if (!checked) {
      rawDataFilterForm.setFieldValue("pin_numbers", null);
    }
    setDisablePinSelection(!checked);
  };

  return (
    <div>
      <Form
        form={rawDataFilterForm}
        layout="inline"
        name={`${pageKey}_generate_raw_data_table_form`}
        initialValues={{
          test_type: "",
          site: "",
          per_pin: false,
        }}
        onValuesChange={onValuesChange}
      >
        <Form.Item name="test_type" label="">
          <Select
            className="w-32"
            defaultValue=""
            popupMatchSelectWidth={false}
            onChange={handleTestTypeChange}
            options={[
              {
                label: "All Test Types",
                value: "",
              },
              {
                label: "MPR",
                value: "m",
              },
              {
                label: "PTR",
                value: "p",
              },
              {
                label: "FTR",
                value: "f",
              },
            ]}
          />
        </Form.Item>
        <Form.Item name="tnum" label="">
          <TestListSelect
            labelInValue
            placeholder="Test Name/Number"
            className="min-w-[500px]!"
            apiParams={apiParams}
            searchValue={rawDataTestNumber?.toString()}
            shouldClearTestList={shouldClearTestList}
            setShouldClearTestList={setShouldClearTestList}
            onChange={onChangeTest}
            onChangeTestListOptions={onChangeTestListOptions}
          ></TestListSelect>
        </Form.Item>
        <Form.Item name="site" label="" className="w-48!">
          <FilterSelect
            placeholder="Site Number"
            maxTagCount="responsive"
            defaultValue=""
            showSearch={false}
            allowClear={false}
            params={{
              api: {
                url: "api/v1/internal/options/list/sites",
                src_type: urlParams[pageKey].src_type,
                src_value: urlParams[pageKey].src_value,
                exclude_overall: true,
                cache_it: 0,
              },
              allOption: {
                label: "All Test Sites",
                value: "",
              },
            }}
            optionPrefix="Site "
          />
        </Form.Item>
        <Form.Item name="per_pin" valuePropName="checked">
          <Checkbox
            disabled={disablePerPinCheckbox}
            onChange={handlePerPinChange}
          >
            Show Breakdown Per Pin
          </Checkbox>
        </Form.Item>
        <Form.Item name="pin_numbers" label="" className="w-48!">
          <SearchInput
            placeholder="Select Pin Numbers"
            apiFunction={Api.getSelectOptions}
            apiParams={{
              field: "pin_numbers",
              src_type: urlParams[pageKey].src_type,
              src_value: urlParams[pageKey].src_value,
              tnum: rawDataFilterForm
                .getFieldValue("tnum")
                ?.value?.split("|")[1],
            }}
            disabled={disablePinSelection}
            onOptionsChange={onChangePinNumberOptions}
            shouldClearOptions={shouldClearPinNumbers}
            setShouldClearOptions={setShouldClearPinNumbers}
          />
        </Form.Item>
        <Form.Item>
          <Button
            type="primary"
            disabled={disableViewRawDataBtn}
            onClick={() => rawDataFilterForm.submit()}
          >
            View Raw Data
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}
