import {
  createOptimizedFetchRowsForRange,
  clearServerDataCache,
  prefetchAdjacentRanges,
} from "../../../server_data_fetcher";

/**
 * Takes the original context and returns a new context with an optimized fetch
 * function that caches results. Also adds utility functions to the context.
 *
 * @param {object} originalContext - Context object containing the following
 * properties:
 *   - gridRequestParams: object with request parameters
 *   - params: object with grid parameters
 *   - filters: object with current filters
 *   - pageKey: string with page key
 *   - gridFilters: object with grid filters
 *   - prerenderData: object with prerender data
 *   - reloadGridFilters: object with reload grid filters
 *   - component: object with component information
 *   - Helper: object with helper functions
 *   - Api: object with API functions
 *
 * @returns {object} Context object with optimized fetch function and added
 * utility functions
 */
export function setupGridContext(originalContext) {
  // Create the optimized fetch function with all the context it needs
  const optimizedFetchRowsForRange = createOptimizedFetchRowsForRange({
    gridRequestParams: originalContext.gridRequestParams,
    params: originalContext.params,
    filters: originalContext.filters,
    pageKey: originalContext.pageKey,
    gridFilters: originalContext.gridFilters,
    prerenderData: originalContext.prerenderData,
    reloadGridFilters: originalContext.reloadGridFilters,
    component: originalContext.component,
    Helper: originalContext.Helper,
    Api: originalContext.Api,
  });

  // Return the context with the optimized fetch function
  return {
    ...originalContext,
    fetchRowsForRange: optimizedFetchRowsForRange,
    // Add utility functions to context if needed
    clearServerDataCache,
    prefetchAdjacentRanges: (start, end, pageSize) =>
      prefetchAdjacentRanges(optimizedFetchRowsForRange, start, end, pageSize),
  };
}

/**
 * Triggered when the pagination page size changes. Will update the chart data
 * for charts that are associated with the current page and have a key that
 * matches the pattern "dice_analysis_boxplot_<tab_key>".
 *
 * @param {object} ctx - Context object containing the following properties:
 *   - {React.RefObject<AgGridReact>} gridRef - Reference to the AgGridReact
 *     component
 *   - {string} pageKey - Key for the current page
 *   - {object} chartKeys - Object mapping page keys to arrays of chart keys
 *   - {object} chartComponentRefs - Object mapping chart keys to React refs
 *     for the chart components
 *   - {object} prerenderData - Object containing prerendered data for the
 *     current page
 *   - {function} isServerSide - Function to determine if the current page is
 *     rendered server-side
 *   - {function} getCurrentPageRows - Function to retrieve the current page
 *     rows
 *   - {function} fetchRowsForRange - Function to fetch rows for a given range
 *   - {function} setPendingLocalChartData - Function to set pending local
 *     chart data
 *   - {React.RefObject<{startRow: number, endRow: number}>}
 *     lastChartSliceRef - Reference to the last page slice that triggered an
 *     update
 *   - {React.RefObject<number>} lastChartUpdateAtRef - Reference to the last
 *     time an update was triggered
 *   - {React.RefObject<boolean>} chartPageFetchInFlightRef - Reference to a
 *     boolean indicating whether a page fetch is currently in flight
 */
export async function diceAnalysisUpdateBoxplot(ctx) {
  // If ctx doesn't have the optimized version, set it up
  const context = ctx.fetchRowsForRange ? ctx : setupGridContext(ctx);

  const {
    gridRef,
    pageKey,
    chartKeys,
    chartComponentRefs,
    prerenderData,
    isServerSide,
    getCurrentPageRows,
    fetchRowsForRange,
    setPendingLocalChartData,
    lastChartSliceRef,
    lastChartUpdateAtRef,
    chartPageFetchInFlightRef,
  } = context;

  // Determine page slice
  const pageSize = gridRef.current.api.paginationGetPageSize();
  const currentPage = gridRef.current.api.paginationGetCurrentPage();
  const startRow = currentPage * pageSize;
  const endRow = startRow + pageSize;

  // Target charts for this tab
  const diceKeys = chartKeys?.[pageKey]?.["dice_analysis_boxplot"] || [];
  const tabKeyStr = prerenderData?.tab_key;
  const targetChartKeys = (Array.isArray(diceKeys) ? diceKeys : []).filter(
    (k) => typeof k === "string" && (tabKeyStr ? k.includes(tabKeyStr) : true),
  );

  // Debounce same-slice re-entries (e.g., scroll)
  const prev = lastChartSliceRef.current || { startRow: -1, endRow: -1 };
  const now = Date.now();
  const sameSlice = prev.startRow === startRow && prev.endRow === endRow;
  const recentlyUpdated = now - (lastChartUpdateAtRef.current || 0) < 800;
  if (
    isServerSide() &&
    sameSlice &&
    (chartPageFetchInFlightRef.current || recentlyUpdated)
  ) {
    return;
  }

  let rows = [];

  // Show chart overlays when we fetch server-side
  if (isServerSide()) {
    console.log(
      "diceAnalysisUpdateBoxplot: Server-side mode, fetching fresh data",
    );
    chartPageFetchInFlightRef.current = true;
    lastChartSliceRef.current = { startRow, endRow };
    targetChartKeys.forEach((key) => {
      const ref = chartComponentRefs[key];
      if (ref && typeof ref.current?.showLoading === "function") {
        ref.current.showLoading("Loading page data...");
      }
    });
    // Always fetch fresh data for server-side grids to ensure we get updated sort/filter results
    rows = await fetchRowsForRange(startRow, endRow);
    console.log(
      "diceAnalysisUpdateBoxplot: Fetched",
      rows.length,
      "rows from server",
    );
  } else {
    console.log(
      "diceAnalysisUpdateBoxplot: Client-side mode, getting current page rows",
    );
    // For client-side grids, try multiple methods to get the most up-to-date data
    const currentPageRows = getCurrentPageRows();
    rows = currentPageRows.map((n) => n.data || {});

    // Alternative method: get data directly from grid API
    if (rows.length === 0) {
      console.log(
        "diceAnalysisUpdateBoxplot: Trying alternative method to get rows",
      );
      const allRows = [];
      gridRef.current.api.forEachNodeAfterFilterAndSort((node, index) => {
        if (index >= startRow && index < endRow) {
          allRows.push(node.data || {});
        }
      });
      rows = allRows;
    }

    console.log(
      "diceAnalysisUpdateBoxplot: Got",
      rows.length,
      "rows from current page",
    );
  }

  // If nothing came back, clear overlays and exit
  if (!Array.isArray(rows) || rows.length === 0) {
    if (isServerSide()) {
      targetChartKeys.forEach((key) => {
        const ref = chartComponentRefs[key];
        if (ref && typeof ref.current?.hideLoading === "function") {
          ref.current.hideLoading();
        }
      });
      chartPageFetchInFlightRef.current = false;
    }
    return;
  }

  /**
   * Iterate over keys to find the first number value in the row
   * @param {Object} row - grid row object
   * @param {Array<string>} keys - keys to search
   * @param {number} [fallback] - value to return if no number is found
   * @returns {number} first number found, or fallback if nothing found
   */
  const getNum = (row, keys, fallback = undefined) => {
    for (const k of keys) {
      if (row[k] !== undefined && row[k] !== null && row[k] !== "") {
        const v =
          typeof row[k] === "string" ? row[k].replace(/,/g, "") : row[k];
        const num = Number(v);
        if (!Number.isNaN(num)) return num;
      }
    }
    return fallback;
  };

  /**
   * Iterate over keys to find the first string value in the row
   * @param {Object} row - grid row object
   * @param {Array<string>} keys - keys to search
   * @param {string} [fallback] - value to return if no string is found
   * @returns {string} first string found, or fallback if nothing found
   */
  const getStr = (row, keys, fallback = "") => {
    for (const k of keys) {
      if (row[k] !== undefined && row[k] !== null && row[k] !== "") {
        return String(row[k]);
      }
    }
    return fallback;
  };

  const categories = [];
  const boxplotData = [];
  const normLoList = [];
  const normHiList = [];
  // Will hold data for dynamically discovered scatter series coming from
  // columns named: norm_results_{number1}_{number2}
  const scatterSeriesMap = new Map();
  const scatterLegendLabels = new Map();
  let globalMin = Number.POSITIVE_INFINITY;
  let globalMax = Number.NEGATIVE_INFINITY;
  let loLimit = Number.POSITIVE_INFINITY;
  let hiLimit = Number.NEGATIVE_INFINITY;

  console.log(
    "diceAnalysisUpdateBoxplot: Processing",
    rows.length,
    "rows for boxplot",
  );
  if (rows.length > 0) {
    console.log("diceAnalysisUpdateBoxplot: First row sample:", rows[0]);
  }

  rows.forEach((row) => {
    const testNum =
      getStr(row, ["actual_test_number", "test_number", "tnum"]) || "";
    const testName = getStr(row, ["test_name", "tname"]) || "";
    categories.push(testNum ? `${testNum}: ${testName}` : testName);

    const mean = getNum(row, ["mean", "avg", "average"], 0) ?? 0;
    const stdev = getNum(row, ["stdev", "stddev", "std_dev"], 0) ?? 0;
    // Prefer normalized min/max when provided for whiskers
    const defMin =
      getNum(row, ["min_result", "min", "min_value"], mean - 2 * stdev) ??
      mean - 2 * stdev;
    const defMax =
      getNum(row, ["max_result", "max", "max_value"], mean + 2 * stdev) ??
      mean + 2 * stdev;
    const nMin = getNum(row, ["norm_min_result"], null);
    const nMax = getNum(row, ["norm_max_result"], null);
    const min = Number.isFinite(nMin) ? nMin : defMin;
    const max = Number.isFinite(nMax) ? nMax : defMax;
    const ll = getNum(row, ["lo_limit", "lo_lim", "ltl", "LTL"]);
    const hl = getNum(row, ["hi_limit", "hi_lim", "utl", "UTL"]);

    // Normalized limits per test (to be drawn as red short lines)
    const nll = getNum(
      row,
      ["norm_lo_lim", "normalized_lo_lim", "norm_lo"],
      null,
    );
    const nhl = getNum(
      row,
      ["norm_hi_lim", "normalized_hi_lim", "norm_hi"],
      null,
    );
    normLoList.push(Number.isFinite(nll) ? nll : null);
    normHiList.push(Number.isFinite(nhl) ? nhl : null);

    if (typeof ll === "number") loLimit = Math.min(loLimit, ll);
    if (typeof hl === "number") hiLimit = Math.max(hiLimit, hl);
    globalMin = Math.min(globalMin, min, typeof ll === "number" ? ll : min);
    globalMax = Math.max(globalMax, max, typeof hl === "number" ? hl : max);

    const median = getNum(row, ["norm_median"], null);
    const q1 = getNum(row, ["norm_iqr_lo_lim"], null);
    const q3 = getNum(row, ["norm_iqr_hi_lim"], null);
    boxplotData.push([min, q1, median, q3, max, mean, stdev]);

    // Discover and collect scatter data for any keys matching norm_results_{num1}_{num2}.
    Object.keys(row).forEach((key) => {
      const m = /^norm_results_(\d+)_(\d+)$/.exec(key);
      if (!m) return;
      const label = `${m[2]} - ${m[1]} |Value|`;
      if (!scatterSeriesMap.has(key)) {
        scatterSeriesMap.set(key, []);
        scatterLegendLabels.set(key, label);
      }
      const raw = row[key];
      let val = null;
      if (raw !== undefined && raw !== null && raw !== "") {
        const num = Number(
          typeof raw === "string" ? raw.replace(/,/g, "") : raw,
        );
        val = Number.isFinite(num) ? num : null;
      }
      scatterSeriesMap.get(key).push(val);
      if (val !== null) {
        globalMin = Math.min(globalMin, val);
        globalMax = Math.max(globalMax, val);
      }
    });
  });

  // Sort scatter keys by their numeric parts for a stable series order
  const scatterKeysSorted = Array.from(scatterSeriesMap.keys()).sort((a, b) => {
    const [a1, a2] = a
      .match(/(\d+)_(\d+)$/)
      .slice(1)
      .map((n) => Number(n));
    const [b1, b2] = b
      .match(/(\d+)_(\d+)$/)
      .slice(1)
      .map((n) => Number(n));
    return a1 - b1 || a2 - b2;
  });
  const scatterData = scatterKeysSorted.map((k) => scatterSeriesMap.get(k));
  const scatterLegend = scatterKeysSorted.map((k) =>
    scatterLegendLabels.get(k),
  );

  const localChartData = {
    boxplot_data: [boxplotData],
    x_categories: categories,
    min_result: globalMin === Number.POSITIVE_INFINITY ? 0 : globalMin,
    max_result: globalMax === Number.NEGATIVE_INFINITY ? 0 : globalMax,
    // No overall limits for dice analysis; rely on result min/max for scaling
    lo_limit: undefined,
    hi_limit: undefined,
    // Custom per-category limits to be rendered as short red lines
    extra_series: {
      norm_lo: normLoList,
      norm_hi: normHiList,
    },
    scatter_data: scatterData.length ? scatterData : undefined,
    legend: {
      boxplot: ["Boxplot (IQR, Median, Whiskers)"],
      outliers: scatterLegend.length ? scatterLegend : undefined,
    },
  };

  /**
   * Update local chart data in the grid, or save it to pending charts to update later
   * @param {Array<string>} keys - keys to update, can be a subset of keys in chartComponentRefs
   * @returns {void}
   */
  const pushToCharts = (keys) => {
    if (!Array.isArray(keys)) return;
    const tabKey = prerenderData?.tab_key;
    const targetKeys2 = tabKey
      ? keys.filter((k) => String(k).includes(tabKey))
      : keys;
    let updated = false;
    targetKeys2.forEach((key) => {
      const ref = chartComponentRefs[key];
      if (ref && typeof ref.current?.setLocalChartData === "function") {
        ref.current.setLocalChartData(localChartData);
        updated = true;
      }
    });
    if (!updated) {
      const id = `${pageKey}_dice_analysis_boxplot_${prerenderData?.tab_key ?? ""}`;
      setPendingLocalChartData(id, localChartData);
    }
  };

  if (chartKeys?.[pageKey]) {
    pushToCharts(chartKeys[pageKey]["dice_analysis_boxplot"]);
  }

  if (isServerSide()) {
    targetChartKeys.forEach((key) => {
      const ref = chartComponentRefs[key];
      if (ref && typeof ref.current?.hideLoading === "function") {
        ref.current.hideLoading();
      }
    });
    chartPageFetchInFlightRef.current = false;
    lastChartUpdateAtRef.current = Date.now();
  }
}
