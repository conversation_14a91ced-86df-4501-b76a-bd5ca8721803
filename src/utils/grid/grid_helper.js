import { notification } from "antd";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import { uniq } from "lodash";
import Api from "../api";
import Helper from "../helper";

dayjs.extend(duration);

/**
 * Grid helper object
 */
const GridHelper = {
  /**
   * Convert value to number format
   *
   * @param {object} params
   * @returns {string} formattedValue
   */
  numberFormatter: (params) => {
    const value =
      typeof params.value === "object" && params.value !== null
        ? params.value.toString()
        : params.value;
    let formattedValue =
      value !== null && value !== undefined
        ? Helper.descriptiveFormatting(value, 4, 4)
        : "";

    return formattedValue;
  },
  /**
   * Convert value to percent format
   *
   * @param {object} params
   * @returns {string} formattedValue
   */
  percentageFormatter: (params) => {
    let formattedValue = params.value;

    if (
      formattedValue == "n/a" ||
      formattedValue === "" ||
      formattedValue == null ||
      formattedValue == "-"
    ) {
      formattedValue = "-";
    } else {
      formattedValue = Helper.descriptiveFormatting(
        formattedValue,
        2,
        2,
        false,
      );
    }
    formattedValue = isNaN(formattedValue) ? "" : formattedValue + "%";

    return formattedValue;
  },
  /**
   * Convert value to date format (e.g. `Jan 1, 2023`)
   *
   * @param {object} params
   * @returns {string} formattedValue
   */
  dateFormatter: (params) => {
    let formattedValue = params.value;
    if (formattedValue !== "") {
      const options = {
        year: "numeric",
        month: "short",
        day: "numeric",
      };
      formattedValue = new Date(formattedValue).toLocaleDateString(
        "en-US",
        options,
      );
    }

    return formattedValue;
  },
  /**
   * Convert boolean value to Yes/No format
   *
   * @param {object} params
   * @returns {string} formattedValue
   */
  booleanYesNoFormatter: (params) => {
    let formattedValue = params.value === true ? "Yes" : "No";

    return formattedValue;
  },
  /**
   * Truncate very long string value
   *
   * @param {object} params
   * @param {int} width
   * @returns {string} formattedValue
   */
  stringFormatter: (params, width) => {
    const value =
      typeof params.value === "object" && params.value !== null
        ? params.value.toString()
        : params.value;
    let formattedValue = value ?? "";
    if (width && formattedValue) {
      const maxLength = Helper.getStringMaxLength(width);
      formattedValue = Helper.truncateString(value, maxLength, "middle");
    }

    return formattedValue;
  },
  /**
   * Formats the test type value
   *
   * @param {object} params
   * @returns {string} The formatted test type value
   */
  testTypeFormatter: (params) => {
    const testTypes = {
      f: "FTR",
      m: "MPR",
      p: "PTR",
    };

    return testTypes[params.value] ?? params.value;
  },
  /**
   * Convert value to readable size format (e.g. B, KB, MB, GB)
   *
   * @param {object} params
   * @returns {string} formattedSize
   */
  fileSizeFormatter: (params) => {
    let formattedSize = "";
    if (params.value < 1024) formattedSize = `${params.value} B`;
    else if (params.value < 1024 * 1024)
      formattedSize = `${(params.value / 1024).toFixed(2)} KB`;
    else if (params.value < 1024 * 1024 * 1024)
      formattedSize = `${(params.value / (1024 * 1024)).toFixed(2)} MB`;
    else
      formattedSize = `${(params.value / (1024 * 1024 * 1024)).toFixed(2)} GB`;

    return formattedSize;
  },
  /**
   * Convert value to readable duration format (e.g. `1h 20m 3s`)
   *
   * @param {object} params
   * @returns {string} formattedDuration
   */
  durationFormatter: (params) => {
    if (!isNaN(params.value)) {
      const durationObject = dayjs.duration(params.value, "seconds");
      let formattedDuration = "";
      if (durationObject.hours() > 0) {
        formattedDuration += `${durationObject.hours()}h `;
      }
      if (durationObject.minutes() > 0 || durationObject.hours() > 0) {
        formattedDuration += `${durationObject.minutes()}m `;
      }
      formattedDuration += `${durationObject.seconds()}s`;
      return formattedDuration.trim();
    }
  },
  /**
   * Sort date in string format
   *
   * @param {string} date1
   * @param {string} date2
   * @returns {int}
   */
  dateStringComparator: (date1, date2) => {
    const date1Obj = new Date(date1);
    const date2Obj = new Date(date2);

    return date1Obj < date2Obj ? -1 : date1Obj > date2Obj ? 1 : 0;
  },
  /**
   * Sort numeric columns
   *
   * @param {int|float} valueA
   * @param {int|float} valueB
   * @returns {int}
   */
  numberComparator: (valueA, valueB) => {
    return valueA - valueB;
  },
  /**
   * Get selected nodes for server side row model
   *
   * @param {object} gridOptions - either from gridRef.current or SelectionChangedEvent
   * @returns {array} selectedNodes
   */
  getServerSideSelectedNodes: (gridOptions) => {
    const selectionState = gridOptions.api.getServerSideSelectionState();
    const selectedNodes = selectionState.toggledNodes.map((rowId) => {
      return gridOptions.api.getRowNode(rowId);
    });

    return selectedNodes;
  },
  /**
   * Get selected row data for server side row model
   *
   * @param {object} gridInstance
   * @returns {array} selectedRows
   */
  getServerSideSelectedRows: (gridInstance) => {
    const selectionState = gridInstance.api.getServerSideSelectionState();
    const selectedRows = selectionState.toggledNodes.map((rowId) => {
      return gridInstance.api.getRowNode(rowId).data;
    });

    return selectedRows;
  },
  /**
   * Download table data in CSV format
   *
   * @param {string} csvDownloadUrl
   * @param {string} filename
   * @param {object} exportParams
   * @param {object} filters
   * @param {object} loadingStatus
   * @param {function} setLoadingStatus
   * @param {string} loadingStatusKey
   */
  downloadTableAsCSV: (
    csvDownloadUrl,
    filename,
    exportParams,
    filters,
    loadingStatus,
    setLoadingStatus,
    loadingStatusKey,
  ) => {
    Api.downloadTableAsCSV(
      (res) => {
        Helper.downloadBlobAsFile(res, filename);
        if (typeof setLoadingStatus === "function") {
          Helper.updateLoadingStatus(
            loadingStatus,
            setLoadingStatus,
            loadingStatusKey,
            false,
          );
        }
      },
      (err) => {
        notification.error({
          message: "Download Table",
          description: err,
        });
        if (typeof setLoadingStatus === "function") {
          Helper.updateLoadingStatus(
            loadingStatus,
            setLoadingStatus,
            loadingStatusKey,
            false,
          );
        }
      },
      {
        ...filters,
        ...exportParams,
        csv_download_url: csvDownloadUrl,
      },
    );
  },
  /**
   * Get column fields based on mfg process filter
   *
   * @param {array} columnDefs
   * @param {array} mfgProcesses
   * @returns {array} mfgProcessColumnFields
   */
  getMfgProcessColumnFields: (columnDefs, mfgProcesses) => {
    const mfgProcessColumnFields = columnDefs
      .filter((columnDef) => {
        return (
          !columnDef.filterProps ||
          !columnDef.filterProps.mfgProcess ||
          mfgProcesses.length === 0 ||
          (columnDef.filterProps &&
            columnDef.filterProps.mfgProcess &&
            mfgProcesses.some((value) =>
              columnDef.filterProps.mfgProcess.includes(value),
            ))
        );
      })
      .map((columnDef) => {
        return columnDef.field;
      });

    return mfgProcessColumnFields;
  },
  /**
   * Get selected keys grouped by row group if present
   *
   * @param {array} selectedNodes
   * @param {object} gridOptions
   * @param {object} gridComponent
   * @returns {object} selectedKeys
   */
  getSelectedKeys: (selectedNodes, gridOptions, gridComponent) => {
    const groups = gridOptions.api.getRowGroupColumns().map((column) => {
      return column.colId;
    });
    let groupKeys = {};
    let groupUniqueKeys = {};
    let selKeys = {};
    let lotIds = [];
    const tableUniqueCols = gridComponent.table_unique_cols;
    const tableUniqueGroupCols = gridComponent.table_unique_group_cols;

    selectedNodes.forEach((node) => {
      if (node.group) {
        const group = node.field;
        if (groupKeys[group] === undefined) {
          groupKeys[group] = [];
        }
        groupKeys[group].push(node.data[group]);

        tableUniqueGroupCols?.forEach((colId) => {
          if (groupUniqueKeys[colId] === undefined) {
            groupUniqueKeys[colId] = [];
          }
          groupUniqueKeys[colId].push(node.data[colId]);
        });
      } else {
        tableUniqueCols?.forEach((colId) => {
          if (selKeys[colId] === undefined) {
            selKeys[colId] = [];
          }
          selKeys[colId].push(node.data[colId]);
          if (node.data.lot_id) {
            lotIds.push(node.data.lot_id);
          }
        });
      }
    });
    const selectedKeys = {
      groups: groups,
      groupKeys: groupKeys,
      groupUniqueKeys: groupUniqueKeys,
      selectedKeys: selKeys,
      lotIds: lotIds,
    };

    return selectedKeys;
  },
  /**
   * Get all rows
   *
   * @param {object} gridOptions
   * @returns {array} rows
   */
  getAllRows: (gridOptions) => {
    let rows = [];
    gridOptions.api.forEachNode((node) => {
      if (node.data) {
        rows.push(node);
      }
    });
    return rows;
  },
  /**
   * Get all rows data
   *
   * @param {object} gridOptions
   * @returns {array} data
   */
  getAllRowsData: (gridOptions) => {
    let data = [];
    gridOptions.api.forEachNode((node) => {
      if (node.data) {
        data.push(node.data);
      }
    });
    return data;
  },
  /**
   * Get sorting
   *
   * @param {object} gridOptions
   * @returns {array} sortState
   */
  getSorting: (gridOptions) => {
    var sortState = gridOptions.api
      .getColumnState()
      .filter(function (state) {
        return state.sort !== null;
      })
      .map(function (state) {
        return {
          field: state.colId,
          sort: state.sort,
          sortIndex: state.sortIndex,
        };
      });
    sortState.sort((a, b) => a.sortIndex - b.sortIndex);

    return sortState;
  },
  /**
   * Get selected rows including selection from breakdown table
   *
   * @param {object} gridSelectionData
   * @param {string} selectionStoreKey
   * @param {object} detailGridComponents
   * @returns {array} selectedRows
   */
  getSelectedRows: (
    gridSelectionData,
    selectionStoreKey,
    detailGridComponents,
  ) => {
    let selectedRows = gridSelectionData[selectionStoreKey] ?? [];
    if (typeof detailGridComponents === "object") {
      Object.values(detailGridComponents).forEach((detailGridComponent) => {
        if (
          Array.isArray(
            gridSelectionData[detailGridComponent.selection_store_key],
          )
        ) {
          selectedRows = selectedRows.concat(
            gridSelectionData[detailGridComponent.selection_store_key],
          );
        }
      });
    }

    return selectedRows;
  },
  /**
   * Get selected nodes
   *
   * @param {object} gridOptions
   * @param {boolean} isServerSide
   * @returns {array} selectedNodes
   */
  getSelectedNodes: (gridOptions, isServerSide) => {
    const selectedNodes = isServerSide
      ? GridHelper.getServerSideSelectedNodes(gridOptions)
      : gridOptions.api.getSelectedNodes();

    return selectedNodes;
  },
  /**
   * Store grid id to grid ids global state
   *
   * @param {string} gridId
   * @param {object} gridIds
   * @param {string} pageKey
   * @param {string} componentName
   */
  storeGridId: (gridId, gridIds, pageKey, componentName) => {
    if (gridIds[pageKey] === undefined) {
      gridIds[pageKey] = {};
    }
    if (Array.isArray(gridIds[pageKey][componentName])) {
      gridIds[pageKey][componentName].push(gridId);
      gridIds[pageKey][componentName] = uniq(gridIds[pageKey][componentName]);
    } else {
      gridIds[pageKey][componentName] = [gridId];
    }
  },
};

export default GridHelper;
