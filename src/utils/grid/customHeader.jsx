import {
  QuestionCircleOutlined,
  MenuOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import { Space, Tooltip } from "antd";
import React, { useEffect, useRef, useState } from "react";

const icons = {
  QuestionCircleOutlined: <QuestionCircleOutlined />,
};

/**
 * Custom header component
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
const CustomHeader = (props) => {
  const [sortDirection, setSortDirection] = useState(null);
  const [isFilterActive, setIsFilterActive] = useState(false);
  const [sortOrder, setSortOrder] = useState();
  const [hasMultiSort, setHasMultiSort] = useState(false);
  const eMenu = useRef("eMenu");
  const eLabel = useRef("eLabel");
  const eText = useRef("eText");
  const eFilter = useRef("eFilter");
  const eSortOrder = useRef("eSortOrder");
  const eSortDir = useRef("eSortDir");

  useEffect(() => {
    props.column.addEventListener("sortChanged", onSortChanged);
    props.column.addEventListener("filterChanged", onFilterChanged);
    return () => {
      props.column.removeEventListener("sortChanged", onSortChanged);
      props.column.removeEventListener("filterChanged", onFilterChanged);
    };
  }, []);

  useEffect(() => {
    const sortIndex = props.sortModel.findIndex((sortData) => {
      return sortData.colId === props.column.colId;
    });
    setSortOrder(sortIndex !== -1 ? parseInt(sortIndex) + 1 : null);
    setHasMultiSort(props.sortModel.length > 1);
  }, [props.sortModel]);

  /**
   * Triggers when menu clicked
   * Show menu options
   */
  const onMenuClicked = () => {
    props.showColumnMenu(eMenu.current);
  };

  /**
   * Triggers when column filter applied
   */
  const onFilterChanged = () => {
    setIsFilterActive(props.column.isFilterActive());
  };

  /**
   * Triggers when column sorting change
   */
  const onSortChanged = () => {
    setSortDirection(props.column.getSort());
  };

  /**
   * Tell the grid when you want to progress the sorting
   *
   * @param {Event} event
   */
  const onSortClicked = (event) => {
    if (props.enableSorting) {
      props.progressSort(event.shiftKey);
    }
  };

  /**
   * Insert break lines to label
   *
   * @param {string} label
   * @returns {string} newLabel
   */
  const insertBreakLines = (label) => {
    let indexes = [];
    const spaceRegex = / /g;

    let match;
    while ((match = spaceRegex.exec(label)) !== null) {
      indexes.push(match.index);
    }
    indexes.sort((a, b) => b - a);
    let newLabel = label;
    indexes.forEach((index) => {
      newLabel = newLabel.slice(0, index) + "<br>" + newLabel.slice(index + 1);
    });

    return newLabel;
  };

  return (
    <div className="ag-cell-label-container" role="presentation">
      {props.enableMenu && (
        <span
          ref={eMenu}
          className="ag-header-icon ag-header-cell-menu-button"
          aria-hidden="true"
          onClick={() => onMenuClicked()}
        >
          <MenuOutlined />
        </span>
      )}
      <div
        ref={eLabel}
        className="ag-header-cell-label"
        role="presentation"
        onClick={(event) => onSortClicked(event)}
      >
        <span ref={eText} className="ag-header-cell-text">
          <Space>
            <span
              dangerouslySetInnerHTML={{
                __html:
                  props.wrap !== false
                    ? insertBreakLines(props.displayName)
                    : props.displayName,
              }}
            />
            {props.icon && props.iconTooltip && (
              <Tooltip title={props.iconTooltip}>
                <span>{icons[props.icon]}</span>
              </Tooltip>
            )}
          </Space>
        </span>
        {isFilterActive && (
          <span
            ref={eFilter}
            className="ag-header-icon ag-header-label-icon ag-filter-icon"
            aria-hidden="true"
          >
            <FilterOutlined />
          </span>
        )}
        {hasMultiSort && sortOrder && (
          <span
            ref={eSortOrder}
            className="ag-header-icon ag-header-label-icon ag-sort-order"
            aria-hidden="true"
          >
            {sortOrder}
          </span>
        )}
        {sortDirection && (
          <span
            ref={eSortDir}
            className="ag-header-icon ag-header-label-icon"
            aria-hidden="true"
          >
            {sortDirection === "asc" ? (
              <ArrowUpOutlined />
            ) : (
              <ArrowDownOutlined />
            )}
          </span>
        )}
      </div>
    </div>
  );
};

export default CustomHeader;
