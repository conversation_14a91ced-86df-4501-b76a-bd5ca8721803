import { Popconfirm, Tag, message } from "antd";
import { useState } from "react";
import { useSaveAnalysisSet } from "../../../hooks/useSaveAnalysisSet";

/**
 * Renderer for cell that has comma separated values which is removable
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function ClosableTagCellRenderer(props) {
  const [openId, setOpenId] = useState();
  const [filters, setFilters] = useState({ recipe_type: "" });
  const [messageApi, contextHolder] = message.useMessage();

  /**
   * Remove analysis set tag
   *
   * @param {object} filters
   */
  const removeAnalysisSetTag = (filters) => {
    props.data.analysis_set = props.value
      .split(",")
      .filter((item) => item !== filters.analysis_set_name)
      .join(",");
    props.api.refreshCells({
      rowNodes: [props.node],
      columns: [props.column],
      suppressFlash: true,
    });

    messageApi.success("Analysis set was removed successfully.");
  };

  useSaveAnalysisSet(filters, "remove", removeAnalysisSetTag);

  /**
   * Trigger when tag is removed
   * Open confirmation popup to remove analysis set from test
   *
   * @param {React.MouseEvent} e
   * @param {string} id
   */
  const handleClose = (e, id) => {
    e.preventDefault();
    setOpenId(id);
  };

  /**
   * Remove analysis set from test
   *
   * @param {string} analysisSet
   */
  const removeAnalysisSet = (analysisSet) => {
    setOpenId();
    setFilters({
      recipe_type: "npi",
      analysis_set_name: analysisSet,
      ...props.filters,
      test_info: [
        {
          actual_test_number: props.data.actual_test_number,
          test_type: props.data.test_type,
          test_name: props.data.test_name,
        },
      ],
    });
  };

  return (
    <>
      {contextHolder}
      {props.value !== "" &&
        props.value.split(",").map((value) => (
          <Popconfirm
            key={`submit_${props.data.test_number}_${value}`}
            title="Remove analysis set"
            description="Are you sure to remove this analysis set from the test?"
            okText="Remove"
            cancelText="Cancel"
            onConfirm={() => removeAnalysisSet(value)}
            onCancel={() => setOpenId()}
            open={openId === `${props.data.test_number}_${value}`}
          >
            <Tag
              key={value}
              closable
              onClose={(e) =>
                handleClose(e, `${props.data.test_number}_${value}`)
              }
            >
              {value}
            </Tag>
          </Popconfirm>
        ))}
    </>
  );
}
