import { UserOutlined } from "@ant-design/icons";
import { Avatar, Space, Tag, Typography } from "antd";

const { Text } = Typography;

/**
 * Renderer for username cell
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function UsernameCellRenderer(props) {
  return (
    <Space size="middle">
      <Avatar icon={<UserOutlined />} />
      <Text>{props.value}</Text>
      {props.data.is_admin && <Tag color="gold">Admin</Tag>}
    </Space>
  );
}
