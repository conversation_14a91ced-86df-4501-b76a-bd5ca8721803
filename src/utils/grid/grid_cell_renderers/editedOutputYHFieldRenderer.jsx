"use client";
import { Space, Typography } from "antd";
const { Text } = Typography;

/**
 * Render<PERSON> for yh field edited cells in metadata
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
const EditedOutputYHFieldRenderer = (props) => {
  return (
    <Space size="small">
      <Text strong style={{ color: "#EF6322" }}>
        {props.value}
      </Text>
      <Text italic className="text-black/25">
        edited
      </Text>
    </Space>
  );
};
export default EditedOutputYHFieldRenderer;
