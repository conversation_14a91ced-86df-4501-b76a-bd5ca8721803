import { Modal, Tag } from "antd";
import { useEffect, useState } from "react";
import {
  closestCenter,
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  arrayMove,
  horizontalListSortingStrategy,
  SortableContext,
  useSortable,
} from "@dnd-kit/sortable";
import { Flex } from "antd";
import { useBoundStore } from "../../../store/store";
import TagCellRenderer from "./tagCellRenderer";

const commonStyle = {
  cursor: "move",
  transition: "unset", // Prevent element from shaking after drag
};

/**
 * Renderer for cell that has comma separated values and supports reordering
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function ReorderableTagCellRenderer(props) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [csvText, setCsvText] = useState(props.value);
  const setSelectedConditions = useBoundStore(
    (state) => state.setSelectedConditions,
  );

  /**
   * Opens the modal by setting the `isModalOpen` state to true.
   */
  const showModal = () => {
    setIsModalOpen(true);
  };

  /**
   * Handles the cancel event on the reorder tags modal.
   * When the cancel button is clicked, it will set the `isModalOpen` state to false.
   */
  const handleModalCancel = () => {
    setIsModalOpen(false);
  };

  /**
   * Handles the ok event on the reorder tags modal.
   * When the ok button is clicked, it will set the `isModalOpen` state to false.
   */
  const handleModalOk = () => {
    setIsModalOpen(false);
  };

  /**
   * Handles the order change event on the reorder tags modal.
   * When the order of the tags is changed, it will update the `csvText` state with the new order.
   *
   * @param {string} reorderedCsv The new order of tags as a comma separated string.
   */
  const handleOrderChange = (reorderedCsv) => {
    setCsvText(reorderedCsv);
    props.node.setDataValue("condition_value_display", reorderedCsv);
    setSelectedConditions((prevState) =>
      prevState.map((condition) => {
        if (condition.condition_name === props.data.condition_name) {
          condition.condition_value_display = reorderedCsv;
        }
        return condition;
      }),
    );
  };

  return (
    <>
      <div onClick={showModal}>{TagCellRenderer({ value: csvText })}</div>
      <ReorderTagsModal
        title={"Reorder Values"}
        csvText={csvText}
        isOpen={isModalOpen}
        handleOkCallback={handleModalOk}
        handleCancelCallback={handleModalCancel}
        handleOrderChange={handleOrderChange}
      />
    </>
  );
}

/**
 * Modal component for reordering comma separated values.
 *
 * @param {string} title The title of the modal.
 * @param {string} csvText The comma separated values.
 * @param {boolean} isOpen Whether the modal is open or not.
 * @param {function} handleOkCallback The callback function when the ok button is clicked.
 * @param {function} handleCancelCallback The callback function when the cancel button is clicked.
 * @param {function} handleOrderChange The callback function when the order of the tags is changed.
 *
 * @returns {JSX.Element}
 */
function ReorderTagsModal({
  title,
  csvText,
  isOpen,
  handleOkCallback,
  handleCancelCallback,
  handleOrderChange,
}) {
  const [items, setItems] = useState([]);
  const [itemsAtInit, setItemsAtInit] = useState([]);

  useEffect(() => {
    if (csvText) {
      let splitVals = csvText
        .split(",")
        .map((text, index) => ({ id: index, text }));

      setItems(splitVals);
      setItemsAtInit(splitVals);
    }
  }, [csvText]);

  const sensors = useSensors(useSensor(PointerSensor));

  /**
   * Handles the drag end event on the reorder tags modal.
   * When the drag is ended, if the dragged item is not the same as the over item,
   * it will reorder the items in the list.
   *
   * @param {object} event The drag end event.
   */
  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (!over) {
      return;
    }
    if (active.id !== over.id) {
      setItems(() => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  /**
   * Handles the ok button click event on the reorder tags modal.
   * When the ok button is clicked, it will reorder the items in the list
   * and call the handleOrderChange callback with the new order of the tags
   * as a comma separated string and then call the handleOkCallback.
   */
  const handleOk = () => {
    const reorderedCsv = items.map((item) => item.text).join(",");
    handleOrderChange(reorderedCsv);
    handleOkCallback();
  };

  /**
   * Handles the cancel button click event on the reorder tags modal.
   * When the cancel button is clicked, it will reset the items to their initial state
   * and call the handleCancelCallback.
   */
  const handleCancel = () => {
    setItems(itemsAtInit);
    handleCancelCallback();
  };

  return (
    <Modal
      title={title}
      open={isOpen}
      onCancel={handleCancel}
      onOk={handleOk}
      destroyOnClose
    >
      <DndContext
        sensors={sensors}
        onDragEnd={handleDragEnd}
        collisionDetection={closestCenter}
      >
        <SortableContext items={items} strategy={horizontalListSortingStrategy}>
          <Flex gap="4px 0" wrap>
            {items.map((item) => (
              <DraggableTag tag={item} key={item.id} />
            ))}
          </Flex>
        </SortableContext>
      </DndContext>
    </Modal>
  );
}

/**
 * Draggable tag component that can be reordered using drag-and-drop.
 *
 * @param {object} props
 * @returns {JSX.Element} A draggable tag element.
 */
const DraggableTag = (props) => {
  const { tag } = props;
  const { listeners, transform, transition, isDragging, setNodeRef } =
    useSortable({
      id: tag.id,
    });
  const style = transform
    ? {
        ...commonStyle,
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
        transition: isDragging ? "unset" : transition, // Improve performance/visual effect when dragging
      }
    : commonStyle;
  return (
    <Tag style={style} ref={setNodeRef} {...listeners}>
      {tag.text}
    </Tag>
  );
};
