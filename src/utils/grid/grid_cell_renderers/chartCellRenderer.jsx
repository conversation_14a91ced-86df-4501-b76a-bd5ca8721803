import { LoadingOutlined } from "@ant-design/icons";
import { Image, Spin } from "antd";
import { useEffect, useMemo, useState } from "react";
import { merge } from "lodash";
import Helper from "../../helper";
import { getAnalysisSetFilters } from "../../components/recipe_common/helpers";
import ChartHelper from "../../charts/chart_helper";

/**
 * Renderer for cell that contains chart image
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function ChartCellRenderer(props) {
  const [chart, setChart] = useState(<></>);
  const [highchartsChart, setHighchartsChart] = useState();
  const [chartImageData, setChartImageData] = useState();
  const component = useMemo(() => {
    return JSON.parse(props.value);
  }, [props.value]);
  const prerenderData = useMemo(() => {
    return merge(
      {},
      props.prerenderData,
      props.data,
      getAnalysisSetFilters(props.prerenderData?.analysis_set_data?.[0] ?? {}),
    );
  }, [props.prerenderData, props.data]);

  useEffect(() => {
    setChart(generateChart(component));
  }, [component]);

  useEffect(() => {
    if (ChartHelper.isHighchartsChart(highchartsChart)) {
      generateChartThumbnail(highchartsChart);
    }
  }, [highchartsChart]);

  /**
   * Create chart component
   *
   * @param {object} component
   * @returns {object}
   */
  const generateChart = (component) => {
    component.id = `component_${component.name}_${props.data.unique}`;
    component.props.settings.plotarea_only = true;
    component.props.settings.should_use_limits_scaling = false;
    component.props.settings.boost_mode = false;

    return Helper.createComponent(
      component,
      undefined,
      props.pageKey,
      undefined,
      props.filters,
      undefined,
      false,
      undefined,
      prerenderData,
      {},
      {},
      false,
      setHighchartsChart,
    );
  };

  /**
   * Create chart image data
   *
   * @param {object} chart
   */
  const generateChartThumbnail = (chart) => {
    ChartHelper.getHighchartsImageData(chart, updateChartImageData);
  };

  /**
   * Set chart image data
   *
   * @param {string} imageData
   */
  const updateChartImageData = (imageData) => {
    setChartImageData(imageData);
  };

  return chartImageData ? (
    <a onClick={() => Helper.openTestTab(props)}>
      <Image
        key={`${component.name}_image_${props.data.unique}`}
        src={chartImageData}
        preview={false}
      />
    </a>
  ) : (
    <div
      className={`relative w-full`}
      style={{ height: `${props.column.actualWidth - 20}px` }}
    >
      <div className={`invisible w-full h-full`}>{chart}</div>
      <div className={`absolute inset-0 flex items-center justify-center z-10`}>
        <Spin indicator={<LoadingOutlined spin />} />
      </div>
    </div>
  );
}
