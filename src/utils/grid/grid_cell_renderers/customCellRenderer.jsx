import React, { useState } from "react";
import {
  PushpinOutlined,
  // BellOutlined,
  // MessageOutlined,
  // BoxPlotOutlined,
  AreaChartOutlined,
  PushpinFilled,
  // BellFilled,
  ExclamationCircleOutlined,
  StarOutlined,
  StarFilled,
  UnlockOutlined,
  LockOutlined,
} from "@ant-design/icons";
import {
  App,
  Badge,
  Button,
  List,
  Modal,
  Space,
  Switch,
  Typography,
  Tooltip,
  Flex,
  Dropdown,
  Descriptions,
} from "antd";
import { useBoundStore } from "../../../store/store";
import Api from "../../api";
import Helper from "../../helper";
import RecipeInfo from "../../../../src/utils/components/recipe_common/recipe_info";
import RecipeUpdatesMade from "../../../../src/utils/components/recipe_common/recipe_updates_made";
import FileInformationModal from "../../components/file_information_modal";

const onlineStatusData = {
  online: {
    text: "Online",
    status: "success",
  },
  offline: {
    text: "Offline",
    status: "default",
  },
  idle: {
    text: "Idle",
    status: "warning",
  },
};
const accountStatusData = {
  active: {
    text: "Active",
    status: "success",
  },
  pending: {
    text: "Pending Account",
    status: "processing",
  },
  suspended: {
    text: "Suspended",
    status: "error",
  },
};

/**
 * Renderer for custom cell
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
const CustomCellRenderer = (props) => {
  const [favourite, setFavourite] = useState(false);
  const [pinned, setPinned] = useState(props.value);
  // const [followed, setFollowed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isUpdatesMadeModalOpen, setIsUpdatesMadeModalOpen] = useState(false);
  const [isFileInformationModalOpen, setIsFileInformationModalOpen] =
    useState(false);
  const setIsNpiReportsModalOpen = useBoundStore(
    (state) => state.setIsNpiReportsModalOpen,
  );
  const setIsRecipeVersionHistoryModalOpen = useBoundStore(
    (state) => state.setIsRecipeVersionHistoryModalOpen,
  );
  const setIsRecipeSelectedDatalogsModalOpen = useBoundStore(
    (state) => state.setIsRecipeSelectedDatalogsModalOpen,
  );
  const setIsRecipeOwnerNotesModalOpen = useBoundStore(
    (state) => state.setIsRecipeOwnerNotesModalOpen,
  );
  const setActiveRecipeData = useBoundStore(
    (state) => state.setActiveRecipeData,
  );
  const setIsDataIntegrityModalOpen = useBoundStore(
    (state) => state.setIsDataIntegrityModalOpen,
  );
  const setDataIntegrityFilter = useBoundStore(
    (state) => state.setDataIntegrityFilter,
  );
  const setConsolidationRecipeParams = useBoundStore(
    (state) => state.setConsolidationRecipeParams,
  );

  const setIsEditRolePermissionTeamModalOpen = useBoundStore(
    (state) => state.setIsEditRolePermissionTeamModalOpen,
  );
  const setRolePermissionTeamData = useBoundStore(
    (state) => state.setRolePermissionTeamData,
  );

  const presetAnalysisTemplates = useBoundStore(
    (state) => state.presetAnalysisTemplates,
  );
  const { confirm } = Modal;
  const [modalApi, contextHolder] = Modal.useModal();
  const { message } = App.useApp();

  /**
   * Get quick actions icons
   *
   * @returns {JSX.Element}
   */
  const getQuickActionsIcons = () => {
    return (
      <Space.Compact>
        {/* <Button
          type="link"
          icon={pinned ? <PushpinFilled /> : <PushpinOutlined />}
          onClick={() => togglePinned()}
        ></Button>
        <Button
          type="link"
          icon={followed ? <BellFilled /> : <BellOutlined />}
          onClick={() => toggleFollowed()}
        ></Button>
        <Button
          type="link"
          icon={<MessageOutlined />}
          onClick={() => togglePinned()}
          disabled
        ></Button>
        <Button
          type="link"
          icon={<BoxPlotOutlined />}
          onClick={() => togglePinned()}
          disabled
        ></Button> */}
        <Tooltip title="Opens in Selected Test">
          <Button
            type="link"
            icon={<AreaChartOutlined />}
            onClick={() => openSelectedTestAnalysis(props.data)}
          ></Button>
        </Tooltip>
      </Space.Compact>
    );
  };

  /**
   * Open selected test analysis in a new browser tab
   *
   * @param {object} data
   */
  const openSelectedTestAnalysis = (data) => {
    const page = "selected_test";
    const analysisInput = {
      dsk: data.dsk,
      lot_id: Array.isArray(data.lot_id) ? data.lot_id.join(",") : null,
      mfg_process: Array.isArray(data.mfg_process)
        ? data.mfg_process.join(",")
        : null,
      src_type: "dsk",
      src_value: data.dsk,
      tnum: data.tnum,
      tnum_dsk: data.dsk,
    };
    Helper.openAnalysis(
      page,
      analysisInput,
      presetAnalysisTemplates,
      props.queryClient,
      message,
      false,
      true,
    );
  };

  /**
   * Get favourite icon button
   *
   * @returns {JSX.Element}
   */
  const getFavouriteIconBtn = () => {
    return (
      <Button
        type="link"
        icon={favourite ? <StarFilled /> : <StarOutlined />}
        onClick={() => toggleFavourite()}
      ></Button>
    );
  };

  /**
   * Get pin icon button
   *
   * @returns {JSX.Element}
   */
  const getPinIconBtn = () => {
    return (
      <Button
        type="link"
        icon={pinned ? <PushpinFilled /> : <PushpinOutlined />}
        onClick={() => togglePinned()}
      ></Button>
    );
  };

  /**
   * Toggle auto trigger value of a recipe
   *
   * @param {boolean} value
   * @param {Event} event
   * @param {array} data
   * @param {object} rowData
   */
  const toggleRecipeAutoTrigger = (value, event, data, rowData) => {
    confirm({
      title: `Auto Trigger ${value ? "On" : "Off"}?`,
      icon: <ExclamationCircleOutlined />,
      content: `Are you sure you want to Turn ${value ? "on" : "off"} the Auto-Trigger for this Recipe?`,
      onOk() {
        updateRecipeAutoTrigger(value, data, rowData);
      },
      onCancel() {
        event.preventDefault();
      },
    });
  };

  /**
   * Update the auto trigger value of a recipe
   *
   * @param {boolean} value
   * @param {object} triggerData
   * @param {object} rowData
   */
  const updateRecipeAutoTrigger = (value, triggerData, rowData) => {
    setLoading(true);
    Api.updateRecipeAutoTrigger(
      (res) => {
        if (res.success) {
          message.success(res.message);
          triggerData.trigger_value = value;
        } else {
          message.warning(res.message, 10);
        }
        setLoading(false);
      },
      (err) => {
        message.error(err, 10);
        setLoading(false);
      },
      {
        recipe_key: rowData.recipe_key,
        name: triggerData.trigger_name,
        value: value,
      },
    );
  };

  /**
   * Get recipe auto trigger switches
   *
   * @returns {JSX.Element}
   */
  const getRecipeAutoTriggerSwitches = () => {
    return (
      <Space.Compact direction="vertical">
        {props.value.map((data) => {
          return (
            <Space
              key={`${props.data.recipe_key}_${data.trigger_name}_wrapper`}
            >
              <Switch
                key={`switch_${props.data.recipe_key}_${data.trigger_name}`}
                size="small"
                checked={data.trigger_value}
                loading={loading}
                onChange={(value, event) =>
                  toggleRecipeAutoTrigger(value, event, data, props.data)
                }
                disabled={data.disabled}
              />
              {data.trigger_label}
            </Space>
          );
        })}
      </Space.Compact>
    );
  };

  /**
   * Get link element to load a recipe
   *
   * @param {string} label
   * @param {string} type
   * @param {boolean} newTab
   * @returns {JSX.Element} element
   */
  const getRecipeLink = (label, type = "link", newTab = false) => {
    let element = label;
    switch (props.data.recipe_category_value) {
      case "traceability":
      case "metadata_header":
      case "npi_for_char":
      case "npi_for_gage":
      case "npi_for_die_drift":
        element = (
          <Button
            type={type}
            onClick={() => {
              props.node.suppressRowSelection = true;
              setIsUpdatesMadeModalOpen(false);
              renderRecipePage(props.data.recipe_category_value, props, newTab);
            }}
          >
            {label}
          </Button>
        );
        break;
      case "consolidation":
        element = (
          <Button
            type={type}
            onClick={() => {
              props.node.suppressRowSelection = true;
              setIsUpdatesMadeModalOpen(false);
              setConsolidationRecipeParams({
                recipe_name: props.data.recipe_name,
                recipe_version: props.data.version,
              });
            }}
          >
            {label}
          </Button>
        );
        break;
    }

    return element;
  };

  /**
   * Load recipe page
   *
   * @param {string} category
   * @param {object} props
   * @param {boolean} newTab
   */
  const renderRecipePage = (category, props, newTab) => {
    const params = {
      recipe_name: props.data.recipe_name,
      recipe_version: props.data.version,
      dsk: props.data.recipe_data.source_dsks,
    };
    if (props.data.recipe_data.mfg_process) {
      params.mfg_process = props.data.recipe_data.mfg_process;
    }
    if (
      typeof props.data.recipe_category_value !== "undefined" &&
      props.data.recipe_category_value.indexOf("npi_") !== -1
    ) {
      category = "analysis";
      params.template_key = "npi_recipe";
      params.recipe_category = props.data.recipe_category_value;
    }
    Helper.renderRecipePage(category, params, props.queryClient, true, newTab);
    setIsRecipeVersionHistoryModalOpen(false);
  };

  /**
   * Get element to display recipe input methods
   *
   * @returns {JSX.Element}
   */
  const getRecipeInputMethodsInfo = () => {
    return (
      <List
        itemLayout="horizontal"
        size="small"
        split={false}
        locale={{ emptyText: <></> }}
        dataSource={Object.values(props.value)}
        renderItem={(item) => (
          <List.Item>
            <List.Item.Meta
              title={item.label}
              description={
                Array.isArray(item.value)
                  ? item.value.map((value, i) => {
                      return (
                        <div key={`input_method_${i}`}>
                          {(value && value.label) ?? value}
                        </div>
                      );
                    })
                  : item.value
              }
            />
          </List.Item>
        )}
      />
    );
  };

  /**
   * Get element to display recipe version history
   *
   * @returns {JSX.Element}
   */
  const getRecipeVersionHistory = () => {
    return (
      <Button
        type="link"
        onClick={() => {
          props.node.suppressRowSelection = true;
          setActiveRecipeData({
            [props.pageKey]: props.data,
          });
          setIsRecipeVersionHistoryModalOpen(true);
        }}
      >
        {props.value}
      </Button>
    );
  };

  /**
   * Get element to display datalogs where recipe is applied
   *
   * @returns {JSX.Element}
   */
  const getRecipeSelectedDatalogs = () => {
    return (
      props.value.length > 0 && (
        <Button
          type="link"
          onClick={() => {
            props.node.suppressRowSelection = true;
            setActiveRecipeData({
              [props.pageKey]: props.data,
            });
            setIsRecipeSelectedDatalogsModalOpen(true);
          }}
        >
          View
        </Button>
      )
    );
  };

  /**
   * Get cell element of reports column
   *
   * @returns {JSX.Element}
   */
  const getReports = () => {
    return props.data?.recipe_category_value?.includes("npi_") ? (
      <Button
        type="link"
        onClick={() => {
          setActiveRecipeData({
            [props.pageKey]: props.data,
          });
          setIsNpiReportsModalOpen(true);
        }}
      >
        View
      </Button>
    ) : (
      <></>
    );
  };

  /**
   * Get element to display multi line info
   *
   * @returns {JSX.Element}
   */
  const getMultiLineInfo = () => {
    const items = JSON.parse(props.value).map((item) => {
      return {
        label: item.label,
        children:
          typeof item.value === "number"
            ? Helper.numberFormat(item.value, 4)
            : item.value,
        span: "filled",
      };
    });

    return (
      <div>
        <Descriptions
          bordered
          size="small" // Optional: compact spacing
          className="[&_.ant-descriptions-item-content]:!p-0 [&_.ant-descriptions-item-label]:!p-0"
          items={items}
        />
      </div>
    );
  };

  /**
   * Get element to display available recipe actions
   *
   * @returns {JSX.Element}
   */
  const getRecipeActions = () => {
    return getRecipeLink("Edit");
  };

  /**
   * Get element to display recipe notes
   *
   * @returns {JSX.Element}
   */
  const getRecipeNotesInfo = () => {
    const maxLength = Helper.getStringMaxLength(
      props.column.getActualWidth() * 2 - 60,
    );
    return (
      <Typography.Text className="text-ellipsis">
        {Helper.truncateString(
          props.value,
          maxLength,
          "end",
          <Button
            key={`show_more_recipe_notes_info_${props.data.recipe_history_key}`}
            type="link"
            onClick={() => {
              setActiveRecipeData({
                [props.pageKey]: props.data,
              });
              setIsRecipeOwnerNotesModalOpen(true);
            }}
          >
            show more
          </Button>,
        )}
      </Typography.Text>
    );
  };

  /**
   * Get element to display online status info
   *
   * @returns {JSX.Element}
   */
  const getOnlineStatusInfo = () => {
    const statusData = onlineStatusData[props.value]
      ? onlineStatusData[props.value]
      : {
          text: props.value,
          status: "default",
        };
    return <Badge status={statusData.status} text={statusData.text} />;
  };

  /**
   * Get element to display account status info
   *
   * @returns {JSX.Element}
   */
  const getAccountStatusInfo = () => {
    const statusData = accountStatusData[props.value]
      ? accountStatusData[props.value]
      : {
          text: props.value,
          status: "default",
        };
    return <Badge status={statusData.status} text={statusData.text} />;
  };

  /**
   * Get element to display lot integrity index breakdown info
   *
   * @returns {JSX.Element}
   */
  const getDataIntegrityIndexInfo = () => {
    return (
      <Button
        type="link"
        onClick={() => {
          props.node.suppressRowSelection = true;
          setIsDataIntegrityModalOpen(true);
          setDataIntegrityFilter({
            lot_id: props.data.lot_id,
            mfg_process: props.data.manufacturing_process,
          });
        }}
      >
        {props.value !== null && props.value !== undefined
          ? Helper.numberFormat(props.value, 2)
          : ""}
      </Button>
    );
  };

  /**
   * Toggle favourite value
   */
  const toggleFavourite = () => {
    setFavourite(!favourite);
    // TODO: Make api call to toggle favourite value
  };

  /**
   * Toggle pinned value
   */
  const togglePinned = () => {
    setPinned(!pinned);
    // TODO: Make api call to toggle pinned value
  };

  // /**
  //  * Toggle followed value
  //  */
  // const toggleFollowed = () => {
  //   setFollowed(!followed);
  //   // TODO: Make api call to toggle followed value on datalog
  // };

  /**
   * Get element to display file information
   *
   * @returns {JSX.Element}
   */
  const getFileInfo = () => {
    const data = props.node.data;
    const fileInfo = {
      fileName: data.file_name,
      lotId: data.lot_id,
      dataloggedParts: data.datalogged_parts,
      dataloggedGood: data.datalogged_good,
      mainOperation: data.main_operation,
      destinationDir: data.destination_dir,
      dataStrucKey: data.data_struc_key,
      datalogType: data.datalog_type,
    };
    return props.node.data.datalog_type === "non_datalog" ? (
      <Typography.Text className="ml-3">{props.value}</Typography.Text>
    ) : (
      <>
        {isFileInformationModalOpen && (
          <FileInformationModal
            isFileInformationModalOpen={isFileInformationModalOpen}
            setIsFileInformationModalOpen={setIsFileInformationModalOpen}
            fileInfo={fileInfo}
          />
        )}
        <Tooltip
          title="View File Information"
          placement="topLeft"
          mouseEnterDelay={0.5}
        >
          <Button
            type="link"
            onClick={() => {
              props.node.suppressRowSelection = true;
              setIsFileInformationModalOpen(true);
            }}
          >
            {props.value}
          </Button>
        </Tooltip>
      </>
    );
  };

  /**
   * Get element to display role/permission/team actions
   *
   * @returns {JSX.Element}
   */
  const getRolePermissionTeamActions = () => {
    const editAllowed = false;
    const deleteAllowed = false;
    const reloadData = props.reloadData;
    let endpoint = null;
    let label = null;
    switch (props.gridId) {
      case "roles_grid":
        endpoint = "role";
        label = "Role";
        break;
      case "permissions_grid":
        endpoint = "permission";
        label = "Permission";
        break;
      case "teams_grid":
        endpoint = "team";
        label = "Team";
        break;
    }

    /**
     * Delete selected role/permission/team on confirm
     */
    const handleDeleteRolePermissionTeamOnConfirm = () => {
      Api.getData(
        `/api/v1/internal/user_role_permission_team/${endpoint}/${props.data.id}`,
        "delete",
        () => {
          message.success(`Successfully deleted ${label}`, 5);
          reloadData();
        },
        (err) => {
          message.error(err, 5);
        },
        {},
      );
    };

    return (
      <>
        {contextHolder}
        <Dropdown.Button
          placement="bottomLeft"
          type="link"
          trigger="hover"
          menu={{
            items: [
              {
                key: "edit",
                label: `Edit ${label}`,
                disabled: !editAllowed,
                onClick: () => {
                  setIsEditRolePermissionTeamModalOpen(true);
                  setRolePermissionTeamData(props.data);
                },
              },
              {
                key: "delete",
                label: `Delete ${label}`,
                disabled: !deleteAllowed,
                danger: true,
                onClick: () => {
                  modalApi.confirm({
                    title: "Confirm Delete",
                    icon: <ExclamationCircleOutlined />,
                    content: (
                      <Typography.Text>
                        Delete {`${label} `}
                        <span className="font-semibold">{props.data.name}</span>
                      </Typography.Text>
                    ),
                    okText: "Delete",
                    okButtonProps: {
                      danger: true,
                    },
                    onOk: handleDeleteRolePermissionTeamOnConfirm,
                  });
                },
              },
            ],
          }}
        />
      </>
    );
  };

  /**
   * Get visibility switch
   *
   * @returns {JSX.Element}
   */
  const getVisibilitySwitch = () => {
    const { datalog_type, data_struc_key, main_operation, main_status } =
      props.data;

    return (
      <Switch
        size="default"
        defaultChecked={props.value}
        loading={main_operation === "Processing" && main_status === "Ongoing"}
        disabled={
          datalog_type === "non_datalog" ||
          main_operation !== "Processed" ||
          main_status !== "Done"
        }
        checkedChildren={<UnlockOutlined />}
        unCheckedChildren={<LockOutlined />}
        onChange={(value) =>
          Api.updateDatalogVisibility(
            (res) => {
              !res.success && message.warning(res.message, 5);
            },
            (err) => {
              message.error(err, 5);
            },
            {
              dsk: data_struc_key,
              value: Number(value),
            },
          )
        }
      />
    );
  };

  /**
   * Get element to display recipe version updates made
   *
   * @returns {JSX.Element}
   */
  const getVersionUpdatesMade = () => {
    const { recipe_name, recipe_category, full_name, date_created, version } =
      props.data;

    const recipeInfo = [
      { label: "Recipe Name", value: recipe_name },
      { label: "Recipe Type", value: recipe_category },
      { label: "Version Created By", value: full_name },
      { label: "Date Updated", value: date_created },
      { label: "Version Number", value: version },
    ];

    const updatesMade = JSON.parse(props.data.updates_made);
    return (
      <>
        <Button
          type="link"
          onClick={() => {
            setIsUpdatesMadeModalOpen(true);
          }}
        >
          View
        </Button>

        {isUpdatesMadeModalOpen && (
          <Modal
            width="50%"
            open={isUpdatesMadeModalOpen}
            title="Updates Made"
            onCancel={() => setIsUpdatesMadeModalOpen(false)}
            destroyOnClose
            centered
            footer={[
              getRecipeLink("View Recipe", "primary", true),
              <Button
                key="close"
                onClick={() => setIsUpdatesMadeModalOpen(false)}
              >
                Close
              </Button>,
            ]}
          >
            <RecipeInfo recipeInfo={recipeInfo} />
            <div className="mt-4">
              <RecipeUpdatesMade updatesMade={updatesMade} />
            </div>
          </Modal>
        )}
      </>
    );
  };

  /**
   * Get duplicate action element
   *
   * @returns {JSX.Element}
   */
  const getDuplicateAction = () => {
    const { componentColumnDefs, column, data, cellActions } = props;
    const filterProps = componentColumnDefs.find(
      (columnDef) => columnDef.field === column.colDef.field,
    )?.filterProps;
    // Determine the element based on action value
    const element = data.action ? (
      <Switch
        size="default"
        defaultChecked={false}
        onChange={() => cellActions[filterProps.action](data.file_name)}
      />
    ) : (
      <span>Cannot Overwrite</span>
    );

    return element;
  };

  /**
   * Get bin program element
   *
   * @returns {JSX.Element}
   */
  const getBinProgram = () => {
    const str = props.value.split(",");
    return (
      <Flex vertical>
        <Typography.Text>{str[0]}</Typography.Text>
        <Typography.Text type="secondary">{str[1]}</Typography.Text>
      </Flex>
    );
  };

  /**
   * Get action/input element based on column props
   *
   * @param {object} props
   * @returns {JSX.Element} element
   */
  const getActionElement = (props) => {
    let element = <></>;
    switch (props.column.colId) {
      case "quick_actions":
        element = getQuickActionsIcons();
        break;
      case "favourite":
        element = getFavouriteIconBtn();
        break;
      case "pin":
        element = getPinIconBtn();
        break;
      case "auto_trigger":
        element = getRecipeAutoTriggerSwitches();
        break;
      case "recipe_name":
        element = getRecipeLink(props.value);
        break;
      case "selected_datalogs":
        element = getRecipeSelectedDatalogs();
        break;
      case "apply_to":
        element = getRecipeInputMethodsInfo();
        break;
      case "version":
        if (props.data.trigger_data) {
          element = getRecipeVersionHistory();
        } else {
          element = getRecipeLink(props.value);
        }
        break;
      case "actions":
        element = getRecipeActions();
        break;
      case "role_permission_team_actions":
        element = getRolePermissionTeamActions();
        break;
      case "notes":
        element = getRecipeNotesInfo();
        break;
      case "presence_status":
        element = getOnlineStatusInfo();
        break;
      case "account_status":
        element = getAccountStatusInfo();
        break;
      case "data_integrity_index":
        element = getDataIntegrityIndexInfo();
        break;
      case "file_name":
        element = getFileInfo();
        break;
      case "is_public":
        element = getVisibilitySwitch();
        break;
      case "updates_made":
        element = getVersionUpdatesMade();
        break;
      case "duplicate_upload_action":
        element = getDuplicateAction();
        break;
      case "bin_program":
        element = getBinProgram();
        break;
      case "reports":
        element = getReports();
        break;
      case "worst_case":
      case "limits":
      case "statistics":
      case "parts":
        element = getMultiLineInfo();
        break;
    }

    return element;
  };

  return getActionElement(props);
};

export default CustomCellRenderer;
