"use client";

import ColorPicker from "../../../../src/utils/components/color_picker";

/**
 * Renderer for color picker cell
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function ColorPickerCellRenderer(props) {
  /**
   * Set node data color value
   *
   * @param {string} selectedColor
   */
  const setColor = (selectedColor) => {
    props.node.updateData({
      ...props.node.data,
      [props.colDef.colId]: selectedColor,
    });
  };

  return <ColorPicker color={props.value} onClickColor={setColor} />;
}
