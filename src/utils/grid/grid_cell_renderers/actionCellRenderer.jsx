import React, { useState, useEffect } from "react";
import { Switch } from "antd";

/**
 * Renderer for action cell
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
const ActionCellRenderer = (props) => {
  useEffect(() => {
    setActionElement(getActionElement(props));
  }, [props.value]);

  /**
   * Get action/input element based on column props
   *
   * @param {object} props
   * @returns {JSX.Element} element
   */
  const getActionElement = (props) => {
    let element = <></>;
    const filterProps = props.componentColumnDefs
      .filter((columnDef) => {
        return columnDef.field === props.column.colDef.field;
      })
      .map((columnDef) => {
        return columnDef.filterProps;
      })[0];

    switch (filterProps && filterProps.inputType) {
      case "switch":
        element = (
          <Switch
            size="small"
            defaultChecked={getSwitchDefaultValue(filterProps, props)}
            onChange={(value) =>
              props.cellActions[filterProps.action](value, props.data)
            }
          />
        );
        break;
    }

    return element;
  };

  /**
   * Get default value of switch element based on current grid filters
   *
   * @param {object} filterProps
   * @param {object} props
   * @returns {boolean} defaultValue
   */
  const getSwitchDefaultValue = (filterProps, props) => {
    let defaultValue = filterProps.defaultValue;
    if (
      filterProps.filterDataKey &&
      props.filters &&
      props.filters[filterProps.filterDataKey] !== undefined
    ) {
      const filterValue = props.filters[filterProps.filterDataKey].split(",");
      const filtered = filterValue.includes(
        props.data[filterProps.rowDataKey].toString(),
      );
      if (filtered) {
        defaultValue = !filterProps.defaultValue;
      }
    }

    return defaultValue;
  };

  const [actionElement, setActionElement] = useState(getActionElement(props));

  return actionElement;
};

export default ActionCellRenderer;
