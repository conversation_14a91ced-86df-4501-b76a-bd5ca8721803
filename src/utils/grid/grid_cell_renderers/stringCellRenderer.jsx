import { useEffect, useState } from "react";
import <PERSON>rid<PERSON>el<PERSON> from "../grid_helper";

/**
 * Format cell value and truncate at the middle if needed
 *
 * @param {object} props
 * @returns {string} formattedValue
 */
const formatCellValue = (props) => {
  const truncate = shouldTruncate(props);
  const formattedValue = GridHelper.stringFormatter(
    props,
    truncate ? props.column.getActualWidth() : null,
  );

  return formattedValue;
};

/**
 * Determine if cell value should be truncated or not
 *
 * @param {object} props
 * @returns {boolean} truncate
 */
const shouldTruncate = (props) => {
  let truncate = false;
  if (props.componentColumnDefs) {
    const filterProps = props.componentColumnDefs
      .filter((columnDef) => {
        return columnDef.field === props.column.colDef.field;
      })
      .map((columnDef) => {
        return columnDef.filterProps;
      })[0];

    truncate = filterProps && filterProps.shouldTruncate === true;
  }

  return truncate;
};

/**
 * Renderer for string cell with possible truncation on text
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function StringCellRenderer(props) {
  const [cellValue, setCellValue] = useState(formatCellValue(props));

  useEffect(() => {
    props.api.addEventListener("columnResized", onColumnResized);
  }, []);

  useEffect(() => {
    setCellValue(formatCellValue(props));
  }, [props.value]);

  /**
   * A column was resized.
   *
   * @param {ColumnResizedEvent} event
   */
  const onColumnResized = (event) => {
    if (event.column && shouldTruncate(props) === true) {
      setCellValue(formatCellValue(props));
    }
  };

  return cellValue ?? "";
}
