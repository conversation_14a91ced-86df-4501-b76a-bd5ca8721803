"use client";
import React from "react";
import { Space } from "antd";

/**
 * Render<PERSON> for cell with label and value
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
const LabelCellRenderer = (props) => {
  return (
    <Space size="small" direction="vertical">
      {props.value.map((item, index) => (
        <span key={index}>
          {item.label} : {item.value}
        </span>
      ))}
    </Space>
  );
};
export default LabelCellRenderer;
