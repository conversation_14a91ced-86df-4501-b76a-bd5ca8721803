import Helper from "../../helper";

/**
 * Renderer for link cell
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function LinkCellRenderer(props) {
  /**
   * Generate test link
   *
   * @param {object} props
   * @returns {JSX.Element}
   */
  const getTestLink = (props) => {
    return <a onClick={() => Helper.openTestTab(props)}>{props.value}</a>;
  };

  /**
   * Generate clickable link element
   *
   * @param {object} props
   * @returns {JSX.Element} element
   */
  const getLinkElement = (props) => {
    let element = <>{props.value}</>;
    switch (props.column.colId) {
      case "actual_test_number":
      case "test_name":
        element = getTestLink(props);
        break;
    }

    return element;
  };

  return getLinkElement(props);
}
