import { useEffect } from "react";
import { Badge, Space, Typography } from "antd";

const { Text } = Typography;

/**
 * Renderer for status cell
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
export default function StatusCellRenderer(props) {
  useEffect(() => {
    getActionElement(props);
  }, [props.value]);

  /**
   * Get corresponding preset color
   *
   * @param {string} columnStatus
   * @returns {string}
   */
  const getPresetColor = (columnStatus) => {
    let color = "red";
    if (columnStatus === "Done") color = "green";
    else if (columnStatus === "Ongoing") color = "orange";
    return color;
  };

  /**
   * Get action/input element
   *
   * @param {object} props
   * @returns {JSX.Element} element
   */
  const getActionElement = (props) => {
    let element = <></>;
    let columnStatus =
      props.column.colId === "main_operation"
        ? props.node.data.main_status
        : props.node.data.post_status;

    if (props.column.colId === "main_operation") {
      element = (
        <Space size="middle">
          <div>
            {props.value && <Badge color={getPresetColor(columnStatus)} dot />}
            <Text className="ml-1">{props.value}</Text>
          </div>
        </Space>
      );
    } else {
      if (Array.isArray(columnStatus)) {
        element = (
          <Space size="middle">
            {columnStatus.map(({ operation, status }, index) => (
              <div key={index}>
                {operation && <Badge color={getPresetColor(status)} dot />}
                {operation && <Text className="mx-1">{operation}</Text>}
              </div>
            ))}
          </Space>
        );
      }
    }

    return element;
  };

  return getActionElement(props);
}
