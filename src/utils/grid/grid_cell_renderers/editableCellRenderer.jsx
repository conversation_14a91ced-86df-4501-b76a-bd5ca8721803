"use client";
import { useEffect } from "react";
import { <PERSON><PERSON><PERSON>, Button, Input } from "antd";
import { EditOutlined, CopyOutlined } from "@ant-design/icons";
import FilterSelect from "../../../../src/utils/grid/components/filter_select";
import StringCellRenderer from "../../../../src/utils/grid/grid_cell_renderers/stringCellRenderer";
import GridHelper from "../../../../src/utils/grid/grid_helper";
import { useBoundStore } from "../../../store/store";
const { Text } = Typography;

/**
 * Renderer for editable columns
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
const EditableCellRenderer = (props) => {
  const setIsGenealogyLinkEditButtonDisabled = useBoundStore(
    (state) => state.setIsGenealogyLinkEditButtonDisabled,
  );
  const isGenealogyLinkInEditMode = useBoundStore(
    (state) => state.isGenealogyLinkInEditMode,
  );
  const genealogyLinkEditedLots = useBoundStore(
    (state) => state.genealogyLinkEditedLots,
  );

  const filterProps = props.componentColumnDefs
    ?.filter((columnDef) => {
      return columnDef.field === props.column.colDef.field;
    })
    .map((columnDef) => {
      return columnDef.filterProps;
    })[0];

  useEffect(() => {
    props.node[`${props.colDef.colId}_original_value`] = props.value;
  }, []);

  useEffect(() => {
    // Update edited cell original value during saving to match the server side value
    if (props.updateEditedCellOriginalValue && props.node.isSelected()) {
      props.setUpdateEditedCellOriginalValue(false);
      props.node[`${props.colDef.colId}_original_value`] = props.value;
    }
  }, [props.updateEditedCellOriginalValue]);

  /**
   * Copy cell value to all corresponding selected column cells
   *
   * @param {object} columnProps
   */
  const copyCellValueToAllSelectedRows = (columnProps) => {
    const selectedNodes = GridHelper.getServerSideSelectedNodes(
      props.gridRef.current,
    );
    selectedNodes.forEach((node) => {
      props.updateEditedData(node, columnProps.colDef.colId, columnProps.value);
    });
  };

  /*
   * Display genealogy linking grid cells
   *
   * @returns {JSX.Element} element
   * */
  const handleGenealogyLinking = () => {
    const colId = props.column.colId;
    const rowIndex = props.node.rowIndex;
    const value = props.data[colId];
    let element = <></>;

    if (value === undefined || value === null || value === "") {
      element = (
        <Button
          disabled={isGenealogyLinkInEditMode}
          color="blue"
          variant="text"
          onClick={() => {
            setIsGenealogyLinkEditButtonDisabled(true);
            props.api.startEditingCell({
              rowIndex: rowIndex,
              colKey: colId,
            });
          }}
        >
          Add Data
        </Button>
      );
    } else {
      element = genealogyLinkEditedLots[`${colId}_${rowIndex}`] ? (
        isGenealogyLinkInEditMode ? (
          <>
            <span className="text-red-800">{value}</span>
            <Button
              className="!border-none !shadow-none"
              variant="text"
              icon={<EditOutlined />}
              onClick={() => {
                props.api.startEditingCell({
                  rowIndex: rowIndex,
                  colKey: colId,
                });
              }}
            />
          </>
        ) : (
          <span className="text-red-800">{value}</span>
        )
      ) : (
        <>{value}</>
      );
    }
    return <>{element}</>;
  };

  /**
   * Get action/input element based on column props
   *
   * @param {object} props
   * @returns {JSX.Element} element
   */
  const getActionElement = (props) => {
    let element = <>{props.value}</>;
    if (props.data.editable !== false) {
      const inputType = filterProps?.inputType;
      switch (inputType) {
        case "genealogy_linking_input":
          element = handleGenealogyLinking();
          break;
        case "select":
          element =
            props.isEditMode && props.node.isSelected() ? (
              <div className="flex m-0 p-0">
                <FilterSelect
                  className="w-20"
                  value={props.value}
                  params={{
                    api: {
                      field: filterProps.requestParams.field,
                      cache_it: 1,
                      method: filterProps.requestParams.method,
                    },
                  }}
                  allowClear={false}
                  onChange={(newValue) => {
                    props.updateEditedData(
                      props.node,
                      props.colDef.colId,
                      newValue,
                    );
                  }}
                />
                <Button
                  type="primary"
                  icon={<CopyOutlined className="m-0 p-0" />}
                  onClick={() => copyCellValueToAllSelectedRows(props)}
                  className="w-4!"
                />
              </div>
            ) : (
              <StringCellRenderer {...props} />
            );
          break;
        case "input":
          element =
            props.isEditMode && props.node.isSelected() ? (
              <div className="flex m-0 p-0">
                <Input
                  className="min-w-20"
                  value={props.value}
                  onChange={(e) => {
                    props.updateEditedData(
                      props.node,
                      props.colDef.colId,
                      e.target.value,
                    );
                  }}
                />
                <Button
                  type="primary"
                  icon={<CopyOutlined className="m-0 p-0" />}
                  onClick={() => copyCellValueToAllSelectedRows(props)}
                  className="w-4!"
                />
              </div>
            ) : (
              <StringCellRenderer {...props} />
            );
          break;
        default:
          element = (
            <>
              <Text>{props.value}</Text>
              <EditOutlined
                style={{ color: "#1677ff" }}
                className="ml-1"
              ></EditOutlined>
            </>
          );
          break;
      }
    }

    return element;
  };

  return getActionElement(props);
};

export default EditableCellRenderer;
