"use client";
import { Typography } from "antd";
import { EditOutlined } from "@ant-design/icons";
const { Text } = Typography;

/**
 * Renderer for the variable name column
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
const VariableNameCellRenderer = (props) => {
  const style = { color: "#1677ff" };

  return props.value ? (
    <>
      <Text>{props.value}</Text>
      <EditOutlined style={style} className="ml-1"></EditOutlined>
    </>
  ) : (
    <>
      <Text style={style}>Set Variable</Text>
    </>
  );
};

export default VariableNameCellRenderer;
