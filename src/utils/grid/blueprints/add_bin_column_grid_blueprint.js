/**
 * Blueprint for add bin column grid
 */
export const AddBinColumnGridBlueprint = {
  name: "add_hardware_bin_column",
  component: "table",
  type: "table",
  display_name: "Add Hardware Bin Column",
  preview_image:
    "https://yh2-api-dev.yieldhub.net/images/components/table-preview.png",
  props: {
    settings: {
      title: "",
      column_defs: [
        {
          field: "checkbox",
          headerName: "",
          type: "checkboxColumn",
        },
        {
          field: "binning",
          headerName: "Binning",
          type: "stringColumn",
        },
        {
          field: "minimum_percentage_value",
          headerName: "Minimum % Value",
          type: "numericColumn",
          editable: true,
        },
        {
          field: "maximum_percentage_value",
          headerName: "Maximum % Value",
          type: "numericColumn",
          editable: true,
        },
      ],
      width: "100%",
      height: 200,
      pagination: false,
    },
    params: {
      data: [
        {
          binning: "Bin 1",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 2",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 3",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 4",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 5",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 6",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 7",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 8",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 9",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 10",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 11",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 12",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 13",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 14",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
        {
          binning: "Bin 15",
          minimum_percentage_value: null,
          maximum_percentage_value: null,
        },
      ],
    },
  },
};
