/**
 * Blueprint for add parametric column grid
 */
export const AddParametricColumnGridBlueprint = {
  name: "add_parametric_column",
  component: "table",
  type: "table",
  display_name: "Add Parametric Column",
  preview_image:
    "https://yh2-api-dev.yieldhub.net/images/components/table-preview.png",
  props: {
    settings: {
      title: "",
      column_defs: [
        {
          field: "checkbox",
          headerName: "",
          type: "checkboxColumn",
        },
        {
          field: "performance_metrics",
          headerName: "Performance Metrics",
          type: "stringColumn",
        },
        {
          field: "minimum_value",
          headerName: "Minimum Value",
          type: "numericColumn",
          editable: true,
        },
        {
          field: "maximum_value",
          headerName: "Maximum Value",
          type: "numericColumn",
          editable: true,
        },
      ],
      width: "100%",
      height: "auto",
      pagination: false,
    },
    params: {
      data: [
        {
          performance_metrics: "Mean",
          minimum_value: null,
          maximum_value: null,
        },
        {
          performance_metrics: "Median",
          minimum_value: null,
          maximum_value: null,
        },
        {
          performance_metrics: "Stdev",
          minimum_value: null,
          maximum_value: null,
        },
        {
          performance_metrics: "Cpk",
          minimum_value: null,
          maximum_value: null,
        },
        {
          performance_metrics: "Cpu",
          minimum_value: null,
          maximum_value: null,
        },
        {
          performance_metrics: "Fails",
          minimum_value: null,
          maximum_value: null,
        },
        {
          performance_metrics: "% Fails per Unit",
          minimum_value: null,
          maximum_value: null,
        },
        {
          performance_metrics: "Minimum",
          minimum_value: null,
          maximum_value: null,
        },
        {
          performance_metrics: "Maximum",
          minimum_value: null,
          maximum_value: null,
        },
        {
          performance_metrics: "Range",
          minimum_value: null,
          maximum_value: null,
        },
      ],
    },
  },
};
