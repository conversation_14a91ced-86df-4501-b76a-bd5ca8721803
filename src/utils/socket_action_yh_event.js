import { message } from "antd";
import { debounce } from "lodash";
import { useBoundStore } from "../../src/store/store";
import Helper from "./helper";

let debouncedProcessYhGridSocketEventQueue;

/**
 * <PERSON><PERSON> on updating the yh grid socket event queue and triggering the process socket event hook.
 *
 * @param {undefined|array} payload
 * @param {undefined|string} component
 * @param {undefined|string} action
 * @param {boolean} compressed
 */
const handleYhGridSocketEvent = async (
  payload,
  component,
  action,
  compressed,
) => {
  if (compressed) {
    payload = await Helper.decompressWithCompressionStream(payload);
  }
  const {
    renderedComponents,
    yhGridSocketEventQueue,
    setProcessYhGridSocketEventQueue,
  } = useBoundStore.getState();
  const isComponentRendered = Object.values(renderedComponents).some((array) =>
    array.includes(component),
  );

  // add event to queue if component is rendered
  if (isComponentRendered) {
    yhGridSocketEventQueue[component] = [
      ...(yhGridSocketEventQueue[component] || []),
      {
        payload,
        action: action,
      },
    ];

    // cancel the previous debounce if it exists
    if (debouncedProcessYhGridSocketEventQueue?.cancel) {
      debouncedProcessYhGridSocketEventQueue.cancel();
    }

    // create a new debounced function to process yh grid socket event queue
    debouncedProcessYhGridSocketEventQueue = debounce(() => {
      setProcessYhGridSocketEventQueue(Date.now());
    }, 1000);

    // call the debounced function
    debouncedProcessYhGridSocketEventQueue();
  }
};

const Action = {
  /**
   * Basic show message action
   *
   * @param {undefined|object} payload
   */
  showMessage: (payload) => {
    message.info({
      content: payload?.message ?? "Show message",
    });
  },

  /**
   * Add and update data from datalog_table
   *
   * @param {undefined|array} payload
   * @param {undefined|string} component
   * @param {boolean} compressed
   */
  addUpdateTableRowData: (payload, component, compressed) => {
    // - check if component is currenly shown
    // -- yes: perform data add/update
    // -- no: add to task queue (to update component data later, when component is active/visible)
    // -- NOTE: if component is not loaded initially, then it's okay not to update data via websocket, since updated data will be loaded from normal http request, so in this case, implement a logic to check the component being not loaded initially when this socket data is received, then release from task queue
    console.log("addUpdateTableRowData", payload, component); // for debugging purpose
    handleYhGridSocketEvent(payload, component, "add_update", compressed);
  },

  /**
   * Update data from table
   *
   * @param {undefined|array} payload
   * @param {undefined|string} component
   * @param {boolean} compressed
   */
  updateTableRowData: (payload, component, compressed) => {
    // - check if component is currenly shown
    // -- yes: perform data update
    // -- no: add to task queue (to update component data later, when component is active/visible)
    // -- NOTE: if component is not loaded initially, then it's okay not to update data via websocket, since updated data will be loaded from normal http request, so in this case, implement a logic to check the component being not loaded initially when this socket data is received, then release from task queue
    handleYhGridSocketEvent(payload, component, "update", compressed);
  },

  /**
   * Delete data from datalog_table
   *
   * @param {undefined|array} payload
   * @param {undefined|string} component
   * @param {boolean} compressed
   */
  deleteTableRowData: (payload, component, compressed) => {
    // - check if component is currenly shown
    // -- yes: perform data update/delete
    // -- no: add to task queue (to update component data later, when component is active/visible)
    // -- NOTE: if component is not loaded initially, then it's okay not to update data via websocket, since updated data will be loaded from normal http request, so in this case, implement a logic to check the component being not loaded initially when this socket data is received, then release from task queue
    console.log("deleteTableRowData", payload, component); // for debugging purpose
    handleYhGridSocketEvent(payload, component, "delete", compressed);
  },

  /**
   * Update user pdb storage info
   *
   * @param {undefined|object} payload
   */
  updatePDBStorageInfo: (payload) => {
    console.log("updatePDBStorageInfo", payload); // for debugging purpose
    const { userData, setUserPdbStorageInfo } = useBoundStore.getState();

    if (userData.id === payload.user_id) {
      setUserPdbStorageInfo(payload);
    }
  },

  /**
   * Update NPI recipe info
   *
   * @param {object} payload
   */
  updateNpiRecipeInfo: (payload) => {
    const { setNpiRecipeInfo } = useBoundStore.getState();
    setNpiRecipeInfo(payload);
  },

  /**
   * Update NPI table loading value
   *
   * @param {undefined|array} payload
   * @param {undefined|string} component
   * @param {boolean} compressed
   */
  updateNpiTestSummaryTableLoadingValue: (payload, component, compressed) => {
    // - check if component is currenly shown
    // -- yes: perform loading data update
    // -- no: add to task queue (to update component loading data later, when component is active/visible)
    handleYhGridSocketEvent(payload, component, "update_loading", compressed);
  },

  /**
   * Update NPI recipe processing status
   *
   * @param {object} payload
   */
  updateNpiRecipeProcessingStatus: (payload) => {
    const { setNpiRecipeProcessingStatus } = useBoundStore.getState();
    setNpiRecipeProcessingStatus(payload);
  },
};

export default Action;
