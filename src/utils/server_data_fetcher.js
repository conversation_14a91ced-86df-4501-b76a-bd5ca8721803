import { merge } from "lodash";

// Cache for storing request configurations and results
const requestCache = new Map();
const pendingRequests = new Map();

// Batch configuration
const BATCH_CONFIG = {
  DELAY_MS: 50,
  MAX_BATCH_SIZE: 10,
  CACHE_TTL_MS: 5 * 60 * 1000,
};

// Queue for batching requests
let batchQueue = [];
let batchTimer = null;

/**
 * Creates an optimized fetch function for server-side row data
 * This factory function creates a closure over the component context
 */
export function createOptimizedFetchRowsForRange(context) {
  const {
    gridRequestParams,
    params,
    filters,
    pageKey,
    gridFilters,
    prerenderData,
    reloadGridFilters,
    component,
    Helper,
    Api,
  } = context;

  /**
   * Generate a cache key for a request by combining the URL and the serialized payload
   *
   * @param {string} url
   * @param {object} payload
   * @returns {string} cache key
   */
  const generateCacheKey = (url, payload) => {
    return `${url}_${JSON.stringify(payload)}`;
  };

  /**
   * Check if a cached entry is valid. A valid entry is one whose cache time is
   * not older than the configured cache TTL.
   *
   * @param {object} cachedEntry - The cached entry to check
   * @returns {boolean} true if the cache is valid, false otherwise
   */
  const isCacheValid = (cachedEntry) => {
    if (!cachedEntry) return false;
    const now = Date.now();
    return now - cachedEntry.timestamp < BATCH_CONFIG.CACHE_TTL_MS;
  };

  /**
   * Builds a request configuration for fetching data from the server.
   *
   * @returns {object} The request configuration
   */
  const buildRequestConfig = () => {
    if (!gridRequestParams?.request) return null;

    const pageFilters = Helper.getPageFilters(filters, pageKey);
    let allFilters = { ...pageFilters, ...gridFilters, ...prerenderData };

    if (reloadGridFilters[component.reload_grid_key]) {
      allFilters = {
        ...allFilters,
        ...reloadGridFilters[component.reload_grid_key],
      };
    }

    const url =
      typeof params.url_endpoint === "string"
        ? Helper.parseUrlEndpoint(params.url_endpoint, allFilters)
        : "";

    if (!url) return null;

    const paramKeys = Object.keys(
      merge(params.body_params ?? {}, params.query_params ?? {}),
    );

    const queryParams =
      paramKeys.length > 0
        ? Helper.filterObjectByKeys(allFilters, paramKeys)
        : allFilters;

    return {
      url,
      queryParams,
      method: params.method,
      bodyParams: params.body_params,
      gridRequestParams: gridRequestParams.request,
    };
  };

  /**
   * Execute an API call for fetching data from the server.
   *
   * If the call returns a "loading" response, it will be retried up to 10 times
   * with an increasing delay (starting from 200ms, and increasing by 50% each
   * time). If all retries fail, or if the call returns a non-"loading" response,
   * the returned promise will be resolved with the received data (if successful)
   * or rejected on terminal failure/invalid payload.
   * @param {string} url - The URL to call
   * @param {string} method - The HTTP method to use
   * @param {object} payload - The request payload
   * @param {object} bodyParams - The request body parameters
   * @returns {Promise} A promise that resolves with the received data
   */
  const executeApiCall = (url, method, payload, bodyParams) => {
    return new Promise((resolve, reject) => {
      const attemptRequest = (retryCount = 0) => {
        Api.getData(
          url,
          method,
          (res) => {
            if (res.success && Array.isArray(res.data?.table_data)) {
              resolve(res.data.table_data);
            } else if (res?.data?.loading && retryCount < 10) {
              const delay = Math.min(200 * Math.pow(1.5, retryCount), 5000);
              setTimeout(() => attemptRequest(retryCount + 1), delay);
            } else {
              const message =
                res?.error ||
                res?.message ||
                (!Array.isArray(res?.data?.table_data) &&
                  "Invalid payload: table_data is not an array") ||
                "Server returned non-success";
              reject(new Error(message));
            }
          },
          () => reject(new Error("API call failed")),
          payload,
          bodyParams,
        );
      };

      attemptRequest();
    });
  };

  /**
   * Executes a batch of data requests. The requests are grouped by URL and
   * similar parameters, and each group is executed as a single API call.
   *
   * The function takes the current batch of requests, empties the batch queue,
   * and sets the batch timer to null. It then iterates over the current batch
   * and groups the requests by URL and similar parameters. For each group,
   * it executes an API call and caches the results. Finally, it resolves each
   * request in the batch with the corresponding range of data from the cache.
   *
   * If an error occurs during execution, all requests in the batch are resolved
   * with an empty array.
   */
  const executeBatchedRequests = async () => {
    if (batchQueue.length === 0) return;

    const currentBatch = [...batchQueue];
    batchQueue = [];
    batchTimer = null;

    // Group requests by URL and similar parameters
    const groupedRequests = new Map();

    currentBatch.forEach((request) => {
      const { url, method, bodyParams } = request.config;
      const baseKey = `${url}_${method}_${JSON.stringify(bodyParams)}`;

      if (!groupedRequests.has(baseKey)) {
        groupedRequests.set(baseKey, {
          config: request.config,
          ranges: [],
          callbacks: [],
        });
      }

      const group = groupedRequests.get(baseKey);
      group.ranges.push({
        startRow: request.startRow,
        endRow: request.endRow,
      });
      group.callbacks.push(request.resolve);
    });

    // Execute grouped requests
    for (const group of groupedRequests.values()) {
      const { config, ranges, callbacks } = group;

      const minStart = Math.min(...ranges.map((r) => r.startRow));
      const maxEnd = Math.max(...ranges.map((r) => r.endRow));

      const payload = {
        stream: 0,
        ...config.queryParams,
        grid_request_params: {
          ...config.gridRequestParams,
          startRow: minStart,
          endRow: maxEnd,
          requestedRanges: ranges,
        },
      };

      try {
        const data = await executeApiCall(
          config.url,
          config.method,
          payload,
          config.bodyParams,
        );

        const cacheKey = generateCacheKey(config.url, payload);
        requestCache.set(cacheKey, {
          data,
          timestamp: Date.now(),
        });

        ranges.forEach((range, index) => {
          const rangeData = extractRangeData(
            data,
            range.startRow,
            range.endRow,
            minStart,
          );
          callbacks[index](rangeData);
        });
      } catch (error) {
        console.error("Error executing grouped request:", error);
        callbacks.forEach((callback) => callback([]));
      }
    }
  };

  /**
   * Extracts a range of data from a full dataset.
   * @param {Array} fullData - The full dataset to extract from.
   * @param {number} startRow - The starting row of the range to extract.
   * @param {number} endRow - The ending row of the range to extract.
   * @param {number} dataStartRow - The starting row of the full dataset.
   * @returns {Array} The extracted range of data.
   */
  const extractRangeData = (fullData, startRow, endRow, dataStartRow) => {
    if (!Array.isArray(fullData)) return [];
    const relativeStart = startRow - dataStartRow;
    const relativeEnd = endRow - dataStartRow;
    return fullData.slice(relativeStart, relativeEnd);
  };

  return function fetchRowsForRange(startRow, endRow) {
    return new Promise((resolve) => {
      try {
        const requestConfig = buildRequestConfig();

        if (!requestConfig) {
          return resolve([]);
        }

        // Check cache first
        const cacheKey = generateCacheKey(requestConfig.url, {
          ...requestConfig.queryParams,
          grid_request_params: {
            ...requestConfig.gridRequestParams,
            startRow,
            endRow,
          },
        });

        const cachedEntry = requestCache.get(cacheKey);
        if (isCacheValid(cachedEntry)) {
          return resolve(cachedEntry.data);
        }

        // Check for pending requests
        const pendingKey = `${cacheKey}_${startRow}_${endRow}`;
        if (pendingRequests.has(pendingKey)) {
          return pendingRequests.get(pendingKey).then(resolve);
        }

        // Create new batched request
        const requestPromise = new Promise((resolveRequest) => {
          batchQueue.push({
            config: requestConfig,
            startRow,
            endRow,
            resolve: resolveRequest,
          });

          if (!batchTimer) {
            batchTimer = setTimeout(
              executeBatchedRequests,
              BATCH_CONFIG.DELAY_MS,
            );
          }

          if (batchQueue.length >= BATCH_CONFIG.MAX_BATCH_SIZE) {
            clearTimeout(batchTimer);
            executeBatchedRequests();
          }
        });

        pendingRequests.set(pendingKey, requestPromise);
        requestPromise.finally(() => {
          pendingRequests.delete(pendingKey);
        });

        requestPromise.then(resolve);
      } catch (error) {
        console.error("Error in fetchRowsForRange:", error);
        resolve([]);
      }
    });
  };
}

/**
 * Clears the cache of server data fetched by `fetchRowsForRange`.
 * This can be used to invalidate the cache when the underlying data changes.
 */
export const clearServerDataCache = () => {
  requestCache.clear();
  pendingRequests.clear();
  batchQueue = [];
  if (batchTimer) {
    clearTimeout(batchTimer);
    batchTimer = null;
  }
};

/**
 * Prefetches data for the adjacent ranges of the given current range.
 *
 * The prefetch is done by calling the given `fetchRowsForRange` function with
 * the start and end of each adjacent range. If the start of the range is negative
 * (i.e. the range is before the current start), the range is skipped. The
 * prefetch is done in parallel, and any errors that occur are caught and
 * ignored.
 *
 * @param {function} fetchRowsForRange - Function that fetches data for a given
 * range. It takes two parameters: `start` and `end`, which are the start and end
 * indices of the range.
 * @param {number} currentStart - Start index of the current range.
 * @param {number} currentEnd - End index of the current range.
 * @param {number} rowsPerPage - Number of rows per page.
 */
export const prefetchAdjacentRanges = (
  fetchRowsForRange,
  currentStart,
  currentEnd,
  rowsPerPage,
) => {
  const ranges = [
    { start: currentStart - rowsPerPage, end: currentStart },
    { start: currentEnd, end: currentEnd + rowsPerPage },
  ];

  ranges.forEach((range) => {
    if (range.start >= 0) {
      fetchRowsForRange(range.start, range.end).catch(() => {});
    }
  });
};
