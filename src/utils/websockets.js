import { message } from "antd";
import Echo from "laravel-echo";
import Action from "./socket_action_yh_event";

export default class WebSockets {
  connected = false;
  static apiUrl = process.env.NEXT_PUBLIC_APP_API_URL;
  static broadcaster = process.env.NEXT_PUBLIC_WEBSOCKET_BROADCASTER;
  static pusherKey = process.env.NEXT_PUBLIC_PUSHER_KEY;
  static wsHost = process.env.NEXT_PUBLIC_WEBSOCKET_HOST;
  static wsPort = process.env.NEXT_PUBLIC_WEBSOCKET_PORT;
  static wssPort = this.wsPort;
  static wsSsl =
    JSON.parse(process.env.NEXT_PUBLIC_WEBSOCKET_SSL || false) || false;
  static env = process.env.NEXT_PUBLIC_APP_ENV || "production";
  static notificationMsgKey = "websocket_msg";

  /**
   * Initialize and authenticate current user
   */
  static connect() {
    if (!window.Pusher) {
      window.Pusher = require("pusher-js");
    }

    if (!window.Echo) {
      window.Echo = new Echo({
        broadcaster: this.broadcaster,
        key: this.pusherKey,
        wsHost: this.wsHost,
        wsPort: this.wsPort,
        wssPort: this.wssPort,
        forceTLS: this.wsSsl,
        // cluster: "ap1",
        // disableStats: true,
        authEndpoint: `${this.apiUrl}/api/broadcasting/auth`,
        auth: {
          headers: {
            Accept: "application/json",
            Authorization: `Bearer ${window.localStorage.getItem(
              "accessToken",
            )}`,
          },
        },
        enabledTransports: ["ws", "wss"],
      });

      // let hide = message["info"]({
      //   ...{ duration: 5 },
      //   ...{ content: "API Connection: Initialized!" },
      // });
      let hide;

      // window.Echo.connector.pusher.connection.bind('state_change', function (states) {
      //     console.log(states);
      // });

      // window.Echo.connector.pusher.connection.bind("initialized", (payload) => {
      //   hide = this.notifyMessage(hide, "initialized", payload);
      // });

      // window.Echo.connector.pusher.connection.bind("connecting", (payload) => {
      //   /**
      //    * All dependencies have been loaded and Channels is trying to connect.
      //    * The connection will also enter this state when it is trying to reconnect after a connection failure.
      //    */
      //   hide = this.notifyMessage(hide, "connecting", payload, { duration: 0 });
      // });

      window.Echo.connector.pusher.connection.bind("connected", () => {
        /**
         * The connection to Channels is open and authenticated with your app.
         */
        this.connected = true;
        // hide = this.notifyMessage(hide, "connected", payload);
        message.destroy(this.notificationMsgKey);
      });

      window.Echo.connector.pusher.connection.bind("unavailable", (payload) => {
        /**
         *  The connection is temporarily unavailable. In most cases this means that there is no internet connection.
         *  It could also mean that Channels is down, or some intermediary is blocking the connection. In this state,
         *  pusher-js will automatically retry the connection every 15 seconds.
         */
        this.connected = false;
        hide = this.notifyMessage(hide, "unavailable", payload, {
          duration: 0,
        });
      });

      window.Echo.connector.pusher.connection.bind("failed", (payload) => {
        /**
         * Channels is not supported by the browser.
         * This implies that WebSockets are not natively available and an HTTP-based transport could not be found.
         */
        this.connected = false;
        hide = this.notifyMessage(hide, "failed", payload, { duration: 0 });
      });

      window.Echo.connector.pusher.connection.bind(
        "disconnected",
        (payload) => {
          /**
           * The Channels connection was previously connected and has now intentionally been closed
           */
          this.connected = false;
          hide = this.notifyMessage(hide, "disconnected", payload, {
            duration: 0,
          });
        },
      );

      window.Echo.connector.pusher.connection.bind("error", (event) => {
        /**
         * An error occured
         */
        this.connected = false;
        hide = this.notifyMessage(hide, "error", event, { duration: 0 });
      });

      window.Echo.connector.pusher.connection.bind("message", () => {
        /**
         * Ping received from server
         */
        // console.log('ws: message', payload);
      });

      this.listenApiBroadcast();
    }
  }

  /**
   * Disconnects the current Echo instance connection
   */
  static disconnect() {
    if (window.Echo) {
      window.Echo.disconnect();
      window.Echo = null;
    }
  }

  /**
   * Initialize listener of license management api broadcast
   */
  static listenApiBroadcast() {
    /**
     * The callback for public event broadcast
     *
     * @param {string} e - The event name
     * @param {object} data - The event data
     */
    const publicBroadcastCallback = (e, data) => {
      if (data?.data && data?.data?.event) {
        const eventData = data.data;

        switch (eventData.event) {
          case "yh_event":
            if (typeof Action[eventData?.payload?.action] === "function") {
              const components = eventData?.payload?.component ?? [];
              components.forEach((set) => {
                if (set?.ch_type === "public") {
                  Action[eventData?.payload?.action](
                    eventData?.payload?.data,
                    set?.component,
                    eventData?.payload?.compressed ?? false,
                  );
                }
              });
            }
            break;
          default:
            break;
        }
      }
    };

    /**
     * The callback for private event broadcast
     *
     * @param {object} e - The event object
     */
    const privateBroadcastCallback = (e) => {
      if (e.data && e.data.event) {
        const eventData = e.data;

        switch (eventData.event) {
          case "yh_event":
            if (typeof Action[eventData?.payload?.action] === "function") {
              const components = eventData?.payload?.component ?? [];
              components.forEach((set) => {
                if (set?.ch_type === "private") {
                  Action[eventData?.payload?.action](
                    eventData?.payload?.data,
                    set?.component,
                  );
                }
              });
            }
            break;
          default:
            break;
        }
      }
    };

    /**
     * The callback for notification broadcast
     *
     * @param {object} data - The notification object data
     */
    const userNotificationCallback = (data) => {
      console.log(data);
      /**
       * Example object data
       */
      // {
      //   "avatar": "http://yh2-api-url.com/images/avatar.jpg",
      //   "name": "jdoe",
      //   "full_name": "John Doe",
      //   "email": "<EMAIL>",
      //   "title": "System Notification",
      //   "message": "This is a system notification test message.",
      //   "id": "5f49e019-b824-4f65-7d0f-df9d229820d5",
      //   "type": "notification.broadcast.system"
      // }
    };

    // Public broadcast

    window.Echo.channel("public_channel")
      .stopListeningToAll(publicBroadcastCallback)
      .listenToAll(publicBroadcastCallback);

    // Private broadcast

    const userData = window.localStorage.getItem("userData")
      ? JSON.parse(window.localStorage.getItem("userData"))
      : {};

    window.Echo.private(`private_channel_${userData.id}`)
      .stopListening(".yh_event")
      .listen(".yh_event", privateBroadcastCallback);

    window.Echo.private(
      `broadcast.notification.user.${userData.id}`,
    ).notification(userNotificationCallback);
  }

  /**
   * UI message notification
   *
   * @param {MessageType} hide
   * @param {string} event
   * @param {object} payload
   * @param {object} msgConfig
   * @returns {MessageType}
   */
  static notifyMessage(hide, event, payload, msgConfig = {}) {
    const config = {
      key: this.notificationMsgKey,
      duration: 5,
    };

    const events = {
      initialized: {
        level: "info",
        message: "Initialized!",
      },
      connecting: {
        level: "info",
        message: "Connecting...",
      },
      connected: {
        level: "success",
        message: "Connected!",
      },
      unavailable: {
        level: "warning",
        message: "Unavailable!",
      },
      failed: {
        level: "error",
        message: "Failed!",
      },
      disconnected: {
        level: "error",
        message: "Disconnected!",
      },
      error: {
        level: "error",
        message: "Error!",
      },
    };

    if (this.env !== "production") {
      console.log(`ws: ${events[event].message}`, payload);
    }

    hide = message[events[event].level]({
      ...config,
      ...{ content: `API Connection: ${events[event].message}` },
      ...msgConfig,
    });

    return hide;
  }
}
