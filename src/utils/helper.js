"use client";

import {
  Col,
  Flex,
  Form,
  notification,
  Radio,
  Row,
  Spin,
  Tooltip,
  Typography,
} from "antd";
import { ExclamationCircleFilled, LoadingOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import jstat from "jstat";
import React from "react";
import { last, remove } from "lodash";
import { TemplateComponent } from "../../app/(main)/(full-width-content)/(pages)/analysis/template_component";
import { ComponentNameMapper } from "../../src/utils/grid/component_name_mapper";
import { RowOptionsFields } from "../../app/(main)/(full-width-content)/(pages)/template/template_content/row_options/form_fields/row_options_fields";
import { PageMapper } from "../../app/(main)/page_mapper";
import { useQueueStore } from "../store/queue_store";
import { useBoundStore } from "../store/store";
import Api from "./api";
import { QueryKeys } from "./query_keys";
import GridHelper from "./grid/grid_helper";
import { OptionsList } from "./forms/options_list";
import ChartLoading from "./charts/loading";

const { Title, Text } = Typography;

const NoValue = parseFloat("1.7E+308");
const NoValue2 = parseFloat("1.0E+34");

const urlParamMapper = {
  lot_id: "lotId",
  src_type: "srcType",
  src_value: "srcValue",
  tnum: "tNum",
  analysis_type: "analysisType",
};

/**
 * Set the application UI to maintenance mode
 *
 * @param {boolean} value
 * @param {boolean} redirect
 * @param {string} path
 */
const setSiteMaintenance = (
  value = true,
  redirect = true,
  path = "/maintenance",
) => {
  window.localStorage.setItem("isMaintenance", value ? "true" : "false");
  if (redirect) {
    window.location.href = path;
  }
};

/**
 * Log user out
 *
 * @param {boolean} shouldLogin
 */
const logoutUser = (shouldLogin = true) => {
  window.localStorage.removeItem("isLoggedIn");
  window.localStorage.removeItem("accessToken");
  window.localStorage.removeItem("userData");
  if (shouldLogin) {
    window.location.href = "/login";
  }
};

/**
 * Get logged in user data
 *
 * @returns {json}
 */
const getUserData = () => {
  const userDataStr = window.localStorage.getItem("userData");
  return userDataStr && JSON.parse(userDataStr);
};

/**
 * Clone object to avoid mutation on original object
 *
 * @param {object} obj
 * @returns {object} objCopy
 */
const cloneObject = (obj) => {
  const objCopy = JSON.parse(JSON.stringify(obj));

  return objCopy;
};

/**
 * Generate row columns object
 *
 * @param {array} colspans
 * @param {object|null} component
 * @returns {object} columns
 */
const createRowColumns = (colspans, component) => {
  let columns = [];
  colspans.forEach((colspan) => {
    columns.push({
      colspan: colspan,
      component: component,
    });
  });

  return columns;
};

/**
 * Get component row and column key based on index
 *
 * @param {string} index
 * @returns {array}
 */
const getTemplateComponentPlacement = (index) => {
  return index.split("-");
};

/**
 * Render page content
 *
 * @param {string} pageKey
 * @param {string} pageName
 * @param {string} pageUrl
 * @param {QueryClient} queryClient
 * @param {boolean} shouldSaveHistory
 * @param {boolean} newTab
 */
const renderPage = (
  pageKey,
  pageName,
  pageUrl,
  queryClient,
  shouldSaveHistory = true,
  newTab = false,
) => {
  const renderedPages = useBoundStore.getState().renderedPages;
  const setRenderedPages = useBoundStore.getState().setRenderedPages;
  const currentPageData = useBoundStore.getState().currentPageData;
  const setCurrentPageData = useBoundStore.getState().setCurrentPageData;

  if (newTab === true) {
    const urlObj = new URL(window.location.href);
    window.open(`${urlObj.origin}${urlObj.pathname}${pageUrl}`, "_blank");
  } else {
    let isFetching = false;
    if (queryClient && queryClient.isFetching !== undefined) {
      isFetching = queryClient.isFetching() > 0;
    }
    if (isFetching) {
      queryClient.cancelQueries({
        queryKey: QueryKeys.page(currentPageData.key),
      });
    }
    setCurrentPageData({
      key: pageKey,
      name: pageName,
      title: PageMapper[pageName].pageTitle,
    });
    let renderPages = {};
    if (PageMapper[pageName].cache === true) {
      if (Object.keys(renderedPages).includes(pageKey)) {
        refetchCancelledQueries(queryClient, pageKey);
      } else {
        renderPages[pageKey] = {
          name: pageName,
          active: true,
        };
      }
    }
    Object.keys(renderedPages).forEach((key) => {
      renderPages[key] = renderedPages[key];
      renderPages[key].active = key === pageKey;
    });
    setRenderedPages(renderPages);

    // set page history navigation data
    const pageHistory = JSON.parse(
      window.sessionStorage.getItem("pageHistory"),
    );
    if (Array.isArray(pageHistory) && PageMapper[pageName].isLanding !== true) {
      const index = pageHistory.indexOf(pageName);
      if (index !== -1) {
        pageHistory.splice(index);
      }
      window.sessionStorage.setItem(
        "pageHistory",
        JSON.stringify(Helper.arrayUnique(pageHistory.concat(pageName))),
      );
    } else {
      window.sessionStorage.setItem("pageHistory", JSON.stringify([pageName]));
    }

    if (shouldSaveHistory === true) {
      const historyState = {
        pageKey: pageKey,
        pageUrl: pageUrl,
      };
      if (Object.keys(renderedPages).length === 0) {
        window.history.replaceState(historyState, "", pageUrl);
      } else {
        window.history.pushState(historyState, "", pageUrl);
      }
    }
  }
};

/**
 * Refetch cancelled page queries
 *
 * @param {QueryClient} queryClient
 * @param {string} pageKey
 */
const refetchCancelledQueries = (queryClient, pageKey) => {
  // Get all queries from the query client
  const queries = queryClient.getQueryCache().findAll();
  // Filter queries of current page that are in the 'pending' status
  const cancelledQueries = queries.filter(
    (query) =>
      query.state.status === "pending" &&
      query.queryKey.indexOf(pageKey) !== -1,
  );
  // Refetch the cancelled queries
  cancelledQueries.forEach((query) => {
    queryClient.refetchQueries({
      queryKey: query.queryKey,
      exact: true,
    });
  });
};

/**
 * Get page map by key value pair
 *
 * @param {string} key
 * @param {string} value
 * @param {string} queryString
 * @param {string} hash
 * @returns {object} pageMap
 */
const getPageMapByKeyValue = (key, value, queryString = "", hash = "") => {
  const urlParams = Helper.getUrlParamsFromHash(hash);
  const pageMap = Object.keys(PageMapper).reduce((result, mapKey) => {
    if (
      PageMapper[mapKey][key] === value &&
      (!urlParams.template_key ||
        (urlParams.template_key && urlParams.template_key === mapKey))
    ) {
      result[mapKey] = PageMapper[mapKey];
      // Retain the query string if page is reloaded
      if (
        queryString !== "" &&
        result[mapKey].pageUrl &&
        result[mapKey].pageUrl.indexOf("?") === -1
      ) {
        result[mapKey].pageUrl = `${result[mapKey].pageUrl}?${queryString}`;
      }
    }
    return result;
  }, {});

  return pageMap;
};

/**
 * Get template rows by index
 *
 * @param {array} templateRows
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {string} colRowColKey
 * @param {string} colRowColRowKey
 * @param {string} colRowColRowColKey
 * @param {string} colRowColRowColRowKey
 * @param {string} colRowColRowColRowColKey
 * @param {string} colRowColRowColRowColRowKey
 * @param {string} colRowColRowColRowColRowColKey
 * @param {string} tabKey
 * @param {string} subTabKey
 * @returns {object} rows
 */
const getTemplateRowsByIndex = (
  templateRows,
  rowKey,
  colKey,
  colRowKey,
  colRowColKey,
  colRowColRowKey,
  colRowColRowColKey,
  colRowColRowColRowKey,
  colRowColRowColRowColKey,
  colRowColRowColRowColRowKey,
  colRowColRowColRowColRowColKey,
  tabKey,
  subTabKey,
) => {
  let rows;
  if (tabKey !== undefined && subTabKey !== undefined) {
    if (colRowColRowColRowColRowKey !== undefined) {
      if (
        templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
          colRowColRowKey
        ]["columns"][colRowColRowColKey]["tabs"]
      ) {
        rows =
          templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
            colRowColRowKey
          ]["columns"][colRowColRowColKey]["tabs"][subTabKey]["rows"][
            colRowColRowColRowKey
          ]["columns"][colRowColRowColRowColKey]["rows"];
      }
      if (
        templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
          colRowColRowKey
        ]["columns"][colRowColRowColKey]["rows"][colRowColRowColRowKey][
          "columns"
        ][colRowColRowColRowColKey]["rows"]
      ) {
        rows =
          templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
            colRowColRowKey
          ]["columns"][colRowColRowColKey]["rows"][colRowColRowColRowKey][
            "columns"
          ][colRowColRowColRowColKey]["rows"];
      }
      if (
        templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
          colRowColRowKey
        ]["columns"][colRowColRowColKey]["rows"][colRowColRowColRowKey][
          "columns"
        ][colRowColRowColRowColKey]["component"]
      ) {
        rows =
          templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
            colRowColRowKey
          ]["columns"][colRowColRowColKey]["rows"];
      }
    } else if (colRowColRowColRowKey !== undefined) {
      if (
        templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
          colRowColRowKey
        ]["columns"][colRowColRowColKey]["tabs"]
      ) {
        rows =
          templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
            colRowColRowKey
          ]["columns"][colRowColRowColKey]["tabs"][subTabKey]["rows"];
      }
      if (
        templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
          colRowColRowKey
        ]["columns"][colRowColRowColKey]["rows"]
      ) {
        rows =
          templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
            colRowColRowKey
          ]["columns"][colRowColRowColKey]["rows"];
      }
      if (
        templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
          colRowColRowKey
        ]["columns"][colRowColRowColKey]["component"]
      ) {
        rows = templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"];
      }
    } else if (colRowColRowKey !== undefined) {
      rows =
        templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
          colRowColRowKey
        ]["columns"][colRowColRowColKey]["tabs"][subTabKey]["rows"];
    }
  } else if (tabKey !== undefined) {
    if (colRowColRowColRowColRowKey !== undefined) {
      rows =
        templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
          colRowColRowKey
        ]["columns"][colRowColRowColKey]["rows"][colRowColRowColRowKey][
          "columns"
        ][colRowColRowColRowColKey]["rows"];
    } else if (colRowColRowColRowKey !== undefined) {
      rows =
        templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"][
          colRowColRowKey
        ]["columns"][colRowColRowColKey]["rows"];
    } else if (colRowColRowKey !== undefined) {
      rows = templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"];
    } else if (colRowKey !== undefined) {
      rows = templateRows[rowKey]["columns"][colKey]["tabs"][tabKey]["rows"];
    }
  } else {
    if (colRowColRowColRowColRowKey !== undefined) {
      rows =
        templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
          colRowColKey
        ]["rows"][colRowColRowKey]["columns"][colRowColRowColKey]["rows"][
          colRowColRowColRowKey
        ]["columns"][colRowColRowColRowColKey]["rows"];
    } else if (colRowColRowColRowKey !== undefined) {
      rows =
        templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
          colRowColKey
        ]["rows"][colRowColRowKey]["columns"][colRowColRowColKey]["rows"];
    } else if (colRowColRowKey !== undefined) {
      rows =
        templateRows[rowKey]["columns"][colKey]["rows"][colRowKey]["columns"][
          colRowColKey
        ]["rows"];
    } else if (colRowKey !== undefined) {
      rows = templateRows[rowKey]["columns"][colKey]["rows"];
    } else {
      rows = templateRows;
    }
  }

  return rows;
};

/**
 * Get template rows key
 *
 * @param {string} rowKey
 * @param {string} colRowKey
 * @param {string} colRowColRowKey
 * @param {string} colRowColRowColRowKey
 * @param {string} colRowColRowColRowColRowKey
 * @returns {string} key
 */
const getTemplateRowsKey = (
  rowKey,
  colRowKey,
  colRowColRowKey,
  colRowColRowColRowKey,
  colRowColRowColRowColRowKey,
) => {
  const key =
    colRowColRowColRowColRowKey !== undefined
      ? colRowColRowColRowColRowKey
      : colRowColRowColRowKey !== undefined
        ? colRowColRowColRowKey
        : colRowColRowKey !== undefined
          ? colRowColRowKey
          : colRowKey !== undefined
            ? colRowKey
            : rowKey;

  return key;
};

/**
 * Get template row by index
 *
 * @param {array} templateRows
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {string} colRowColKey
 * @param {string} colRowColRowKey
 * @param {string} colRowColRowColKey
 * @param {string} colRowColRowColRowKey
 * @param {string} colRowColRowColRowColKey
 * @param {string} colRowColRowColRowColRowKey
 * @param {string} colRowColRowColRowColRowColKey
 * @param {string} tabKey
 * @param {string} subTabKey
 * @returns {object} templateRow
 */
const getTemplateRowByIndex = (
  templateRows,
  rowKey,
  colKey,
  colRowKey,
  colRowColKey,
  colRowColRowKey,
  colRowColRowColKey,
  colRowColRowColRowKey,
  colRowColRowColRowColKey,
  colRowColRowColRowColRowKey,
  colRowColRowColRowColRowColKey,
  tabKey,
  subTabKey,
) => {
  let rows = getTemplateRowsByIndex(
    templateRows,
    rowKey,
    colKey,
    colRowKey,
    colRowColKey,
    colRowColRowKey,
    colRowColRowColKey,
    colRowColRowColRowKey,
    colRowColRowColRowColKey,
    colRowColRowColRowColRowKey,
    colRowColRowColRowColRowColKey,
    tabKey,
    subTabKey,
  );
  const rowsKey = getTemplateRowsKey(
    rowKey,
    colRowKey,
    colRowColRowKey,
    colRowColRowColRowKey,
    colRowColRowColRowColRowKey,
  );

  const templateRow = rows[rowsKey];

  return templateRow;
};

/**
 * Get template column by index
 *
 * @param {array} templateRows
 * @param {string} rowKey
 * @param {string} colKey
 * @param {string} colRowKey
 * @param {string} colRowColKey
 * @param {string} colRowColRowKey
 * @param {string} colRowColRowColKey
 * @param {string} colRowColRowColRowKey
 * @param {string} colRowColRowColRowColKey
 * @param {string} colRowColRowColRowColRowKey
 * @param {string} colRowColRowColRowColRowColKey
 * @param {string} tabKey
 * @param {string} subTabKey
 * @returns {object} templateColumn
 */
const getTemplateColumnByIndex = (
  templateRows,
  rowKey,
  colKey,
  colRowKey,
  colRowColKey,
  colRowColRowKey,
  colRowColRowColKey,
  colRowColRowColRowKey,
  colRowColRowColRowColKey,
  colRowColRowColRowColRowKey,
  colRowColRowColRowColRowColKey,
  tabKey,
  subTabKey,
) => {
  const row = getTemplateRowByIndex(
    templateRows,
    rowKey,
    colKey,
    colRowKey,
    colRowColKey,
    colRowColRowKey,
    colRowColRowColKey,
    colRowColRowColRowKey,
    colRowColRowColRowColKey,
    tabKey,
    subTabKey,
  );
  const columnKey =
    colRowColRowColRowColRowColKey !== undefined
      ? colRowColRowColRowColRowColKey
      : colRowColRowColRowColKey !== undefined
        ? colRowColRowColRowColKey
        : colRowColRowColKey !== undefined
          ? colRowColRowColKey
          : colRowColKey !== undefined
            ? colRowColKey
            : colKey !== undefined
              ? colKey
              : undefined;
  const templateColumn = row["columns"][columnKey];

  return templateColumn;
};

/**
 * Get absolute value of a number
 *
 * @param {number} mixedNumber
 * @returns {number}
 */
const abs = (mixedNumber) => {
  return Math.abs(mixedNumber) || 0;
};

/**
 * Check if value is integer or not
 *
 * @param {*} mixedVar
 * @returns {boolean}
 */
const isInteger = (mixedVar) => {
  return mixedVar === ~~mixedVar;
};

/**
 * Convert value to number format
 *
 * @param {number} number
 * @param {int} decimals
 * @returns {string}
 */
const numberFormat = (number, decimals) => {
  let formatted;
  if (!Helper.hasValue(number)) {
    formatted = number;
  } else {
    const minDecimal = 1 / Math.pow(10, decimals);
    if (
      (parseInt(number) !== 0 && Math.abs(number) < minDecimal) ||
      parseInt(number) > 1000000
    ) {
      formatted = number.toExponential(decimals);
    } else {
      const integerPart = Math.trunc(number);
      const decimalPart = (number - integerPart).toFixed(decimals);
      const result = integerPart + parseFloat(decimalPart);
      formatted = result.toLocaleString("en-US", {
        maximumFractionDigits: decimals,
      });
    }
  }

  return formatted;
};

/**
 * Get preset analysis templates
 *
 * @param {function} successCbk - success callback function
 * @param {*} successCbkParams
 * @returns {AbortController}
 */
const getPresetAnalysisTemplates = (successCbk, successCbkParams) => {
  return Api.getPresetAnalysisTemplates(
    (res) => {
      if (res.success) {
        successCbk(res.data, successCbkParams);
      } else {
        notification.warning({
          message: "Preset Analysis Templates",
          description: res.message,
        });
      }
    },
    (err) => {
      notification.error({
        message: "Preset Analysis Templates",
        description: err,
      });
    },
  );
};

/**
 * Get template data by key
 *
 * @param {object} templates
 * @param {string} templateKey
 * @returns {object} template
 */
const getTemplateByKey = (templates, templateKey) => {
  const template = templates.filter((templateData) => {
    return templateData.key === templateKey;
  })[0];

  return template;
};

/**
 * Generate analysis
 *
 * @param {string} key
 * @param {object} input
 * @param {array} presetAnalysisTemplates
 * @param {QueryClient} queryClient
 * @param {object} message
 * @param {boolean} shouldSaveHistory
 * @param {boolean} newTab
 */
const generateAnalysis = (
  key,
  input,
  presetAnalysisTemplates,
  queryClient,
  message,
  shouldSaveHistory = true,
  newTab = false,
) => {
  const template = getTemplateByKey(presetAnalysisTemplates, key);
  const invalidMessages = validateAnalysisInput(input, template, message);
  if (invalidMessages.length === 0) {
    delete input.search_selection;
    let urlParams = {};
    let surlParams = {};
    Object.keys(input).forEach((param) => {
      if (Array.isArray(input[param])) {
        if (input[param].length > 1) {
          surlParams[param] = input[param].join();
        } else {
          urlParams[param] = input[param].join();
        }
      } else {
        urlParams[param] = input[param];
      }
    });
    urlParams.template_key = key;

    if (Object.keys(surlParams).length > 0) {
      getShortUrl(
        surlParams,
        urlParams,
        goToAnalysis,
        {
          key,
          pageName: template.key,
          queryClient,
          shouldSaveHistory,
          newTab,
        },
        message,
      );
    } else {
      goToAnalysis(urlParams, undefined, {
        key,
        pageName: template.key,
        queryClient,
        shouldSaveHistory,
        newTab,
      });
    }
  }
};

/**
 * Get template by key id
 *
 * @param {string} templateKeyId
 * @param {array} presetAnalysisTemplates
 * @param {array} userAnalysisTemplates
 * @returns {object} template
 */
const getTemplateByKeyId = (
  templateKeyId,
  presetAnalysisTemplates,
  userAnalysisTemplates,
) => {
  const templateKeyArr = templateKeyId.split("_");
  const type = templateKeyArr[0];
  const id = templateKeyArr[1];
  const templates =
    type === "preset" ? presetAnalysisTemplates : userAnalysisTemplates;
  const templateArr = templates.filter((template) => {
    return template.id == id;
  });
  const template = templateArr.length > 0 ? templateArr[0] : {};

  return template;
};

/**
 * Format grid selection to analysis input
 *
 * @param {array} selectedNodes
 * @param {arrayy} gridSelectionData
 * @param {object} gridOptions
 * @param {object} gridComponent
 * @returns {object} input
 */
const formatAnalysisInput = (
  selectedNodes,
  gridSelectionData,
  gridOptions,
  gridComponent,
) => {
  let input = {};

  const selectedKeys = GridHelper.getSelectedKeys(
    selectedNodes,
    gridOptions,
    gridComponent,
  );
  if (gridSelectionData.length) {
    if (selectedKeys.selectedKeys.data_struc_key) {
      input.dsk = Helper.arrayUnique(selectedKeys.selectedKeys.data_struc_key);
      input.lot_id = Helper.arrayUnique(selectedKeys.lotIds);
    }
    if (selectedKeys.groupKeys.lot_id) {
      input.lot_id = Helper.arrayUnique(
        Array.isArray(input.lot_id)
          ? input.lot_id.concat(selectedKeys.groupKeys.lot_id)
          : selectedKeys.groupKeys.lot_id,
      );
    }
    input.mfg_process = Helper.arrayUnique(
      gridSelectionData.map((data) => {
        return data.manufacturing_process;
      }),
    );
    input.search_selection = selectedKeys;
  }

  return input;
};

/**
 * Get short url and execute callback function if defined
 *
 * @param {object} surlParams - short url parameters
 * @param {object} urlParams - url parameters
 * @param {function} callbackFn - callback function to be executed after getting short url value
 * @param {*} callbackParams - callback function parameters
 * @param {object} message
 */
const getShortUrl = (
  surlParams,
  urlParams,
  callbackFn,
  callbackParams,
  message,
) => {
  Api.getShortUrl(
    (res) => {
      if (res.success) {
        if (typeof callbackFn === "function") {
          callbackFn(urlParams, res.data, callbackParams);
        }
      } else {
        displayMessage(
          message,
          res.message,
          "warning",
          5,
          "get_short_url_message",
        );
      }
    },
    (err) => {
      displayMessage(message, err, "error", 5, "get_short_url_message");
    },
    surlParams,
  );
};

/**
 * Validate input data based on analysis template validation rules
 *
 * @param {object} input
 * @param {object} template
 * @param {object} message
 * @returns {array} invalidMessages
 */
const validateAnalysisInput = (input, template, message) => {
  let invalidMessages = [];
  const templateData = JSON.parse(template.template_data);
  if (Object.keys(templateData).length > 0) {
    const datalogSelectionRules =
      templateData.datalog_selection_rules !== undefined
        ? templateData.datalog_selection_rules
        : {};

    const conditions = datalogSelectionRules.conditions;
    if (conditions) {
      let conditionParams = [];
      Object.keys(conditions).forEach((condition) => {
        invalidMessages = invalidMessages.concat(
          validateRulesByCondition(
            datalogSelectionRules.rules,
            conditions[condition],
            input,
            condition,
          ),
        );
        conditionParams = Helper.arrayUnique(
          conditionParams.concat(conditions[condition]),
        );
      });
      conditionParams.forEach((param) => {
        delete datalogSelectionRules.rules[param];
      });
    }
    if (datalogSelectionRules.rules) {
      invalidMessages = invalidMessages.concat(
        validateRulesByCondition(
          datalogSelectionRules.rules,
          Object.keys(datalogSelectionRules.rules),
          input,
        ),
      );
    }
  }

  invalidMessages = invalidMessages.filter((messages) => {
    return messages.length > 0;
  });
  const invalidMsgKey = "invalid_msg";
  if (message) {
    if (invalidMessages.length === 0) {
      message.destroy(invalidMsgKey);
    } else {
      message.warning({
        key: invalidMsgKey,
        content: invalidMessages,
        duration: 10,
        icon: <></>,
      });
    }
  }

  return invalidMessages;
};

/**
 * Validate rules by condition logic
 *
 * @param {object} rules
 * @param {array} params
 * @param {object} input
 * @param {string} condition
 * @returns {array} invalidMessages
 */
const validateRulesByCondition = (rules, params, input, condition = "and") => {
  let ruleStatus = {};

  Object.keys(rules).forEach((param) => {
    if (params.indexOf(param) !== -1) {
      ruleStatus[param] = {
        messages: [],
        valid: true,
      };
      const { min, max, label } = rules[param];
      if (max && input[param] && input[param].length > max) {
        ruleStatus[param].messages.push(
          <Row key={`${param}_max`} wrap={false} gutter={8}>
            <Col>
              <ExclamationCircleFilled className="text-orange-400" />
            </Col>
            <Col>{`You can select up to ${max} ${label} only.`}</Col>
          </Row>,
        );
        ruleStatus[param].valid = false;
      }
      if (min && (!input[param] || input[param].length < min)) {
        ruleStatus[param].messages.push(
          <Row key={`${param}_min`} wrap={false} gutter={8}>
            <Col>
              <ExclamationCircleFilled className="text-orange-400" />
            </Col>
            <Col>{`You should select at least ${min} ${label}.`}</Col>
          </Row>,
        );
        ruleStatus[param].valid = false;
      }
    }
  });
  let validCount = 0;
  Object.values(ruleStatus).forEach((status) => {
    if (status.valid) {
      validCount++;
    }
  });

  let invalidMessages = [];
  if (
    (condition === "or" && validCount === 0) ||
    (condition === "and" && validCount !== params.length)
  ) {
    invalidMessages = Object.values(ruleStatus)
      .filter((status) => {
        return status.valid === false;
      })
      .map((status) => {
        return status.messages;
      });
  } else if (condition === "not" && validCount === params.length) {
    let paramLabels = params.map((param) => {
      return rules[param].label;
    });
    const lastParam = paramLabels.pop();
    const firstParams = paramLabels.join(", ");
    const invalidParamsStr = `${firstParams} and ${lastParam}`;
    invalidMessages.push(`You can not select both ${invalidParamsStr}.`);
  }

  return invalidMessages;
};

/**
 * Redirect to analysis page
 *
 * @param {object} urlParams - url parameters
 * @param {string} surl - short url value
 * @param {object} analysisParams
 */
const goToAnalysis = (urlParams, surl, analysisParams) => {
  if (surl !== undefined) {
    urlParams = { ...urlParams, ...{ surl: surl } };
  }
  const queryString = createQueryString(urlParams);
  const pageMap = PageMapper[analysisParams.pageName];
  const pageKey = pageMap?.pageKey ?? `#analysis?${queryString}`;
  const pageName = pageMap?.pageName ?? "analysis";
  const pageUrl = pageMap?.pageUrl ?? `#analysis?${queryString}`;
  renderPage(
    pageKey,
    pageName,
    pageUrl,
    analysisParams.queryClient,
    analysisParams.shouldSaveHistory,
    analysisParams.newTab,
  );
};

/**
 * Generate query string to be passed as url parameters
 * Exclude parameters with object and empty values
 *
 * @param {object} params
 * @returns {string} queryString
 */
const createQueryString = (params) => {
  const urlParams = new URLSearchParams();
  Object.keys(params).forEach((key) => {
    const isNotEmpty =
      (typeof params[key] === "string" || Array.isArray(params[key])) &&
      params[key].length;
    if (isNotEmpty || typeof params[key] !== "object") {
      urlParams.set(key, params[key]);
    }
  });
  const queryString = urlParams.toString();

  return queryString;
};

/**
 * Render page by page key
 *
 * @param {string} key
 * @param {QueryClient} queryClient
 * @param {object} message
 */
const renderPageByPageKey = (key, queryClient, message) => {
  getPresetAnalysisTemplates(initAnalysis, {
    key,
    queryClient,
    message,
  });
};

/**
 * Initialize analysis
 *
 * @param {array} presetAnalysisTemplates
 * @param {object} analysisParams
 */
const initAnalysis = (presetAnalysisTemplates, analysisParams) => {
  const { key, queryClient, message } = { ...analysisParams };
  const template = getTemplateByKey(presetAnalysisTemplates, key);
  const templateKey = template.key;
  const urlParameters = getUrlParameters();
  delete urlParameters.template_key;

  if (urlParameters["surl"] !== undefined) {
    const surl = urlParameters["surl"];
    delete urlParameters["surl"];
    analysisParams.presetAnalysisTemplates = presetAnalysisTemplates;
    analysisParams.key = templateKey;
    return Helper.getLongUrlParams(
      surl,
      urlParameters,
      prepareAnalysis,
      analysisParams,
    );
  } else {
    const input = convertObjectValuesToArray(urlParameters);
    generateAnalysis(
      templateKey,
      input,
      presetAnalysisTemplates,
      queryClient,
      message,
    );
  }
};

/**
 * Convert object values to array
 *
 * @param {object} obj
 * @returns {object} convertedObj
 */
const convertObjectValuesToArray = (obj) => {
  let convertedObj = {};
  Object.keys(obj).forEach((key) => {
    convertedObj[key] = obj[key].split(",");
  });

  return convertedObj;
};

/**
 * Prepare data for generating analysis
 *
 * @param {object} urlParameters
 * @param {object} surlParams
 * @param {object} analysisParams
 */
const prepareAnalysis = (urlParameters, surlParams, analysisParams) => {
  if (surlParams !== undefined) {
    urlParameters = { ...urlParameters, ...surlParams };
  }
  const { key, presetAnalysisTemplates, queryClient, message } = {
    ...analysisParams,
  };
  const input = convertObjectValuesToArray(urlParameters);
  generateAnalysis(key, input, presetAnalysisTemplates, queryClient, message);
};

/**
 * Get url parameters based on history state
 *
 * @returns {object} urlParameters
 */
const getUrlParameters = () => {
  let urlParameters = {};
  const pageState = window.history.state;
  if (pageState.pageUrl) {
    const [, queryString] = pageState.pageUrl.split("?");
    if (queryString) {
      const searchParams = new URLSearchParams(queryString);
      searchParams.forEach((value, key) => {
        urlParameters[key] = value;
      });
    }
  }

  return urlParameters;
};

/**
 * Get long url parameters and execute callback function if defined
 *
 * @param {string} surl - short url value
 * @param {object} urlParams - url parameters
 * @param {function} callbackFn - callback function to be executed after getting long url value
 * @param {*} callbackParams
 * @param {object} message
 * @returns {AbortController}
 */
const getLongUrlParams = (
  surl,
  urlParams,
  callbackFn,
  callbackParams,
  message,
) => {
  return Api.getLongUrlParams(
    (res) => {
      if (res.success) {
        if (typeof callbackFn === "function") {
          callbackFn(urlParams, res.data, callbackParams);
        }
      } else {
        displayMessage(
          message,
          res.message,
          "warning",
          5,
          "get_long_url_params_message",
        );
      }
    },
    (err) => {
      displayMessage(message, err, "error", 5, "get_long_url_params_message");
    },
    surl,
  );
};

/**
 * Display of message for user action
 *
 * @param {object} message
 * @param {string} content
 * @param {string} type
 * @param {number} duration
 * @param {string} key
 */
const displayMessage = (
  message,
  content = "",
  type = "info",
  duration = 3,
  key = "",
) => {
  if (message) {
    message.open({
      key,
      type,
      content,
      duration,
    });
  }
};

/**
 * Count number of lowercase/uppercase letters in a string
 *
 * @param {string} str
 * @param {string} charCase
 * @returns {int} count
 */
const countCharCaseLetters = (str, charCase = "lowercase") => {
  let count = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str[i];
    if (/[a-zA-Z]/.test(char)) {
      const casedChar =
        charCase === "uppercase" ? char.toUpperCase() : char.toLowerCase();
      if (char === casedChar) {
        count++;
      }
    }
  }
  return count;
};

/**
 * Count number of numbers in a string
 *
 * @param {string} str
 * @returns {int}
 */
const countNumbers = (str) => {
  const numericRegex = /\d/g;
  const matches = str.match(numericRegex);
  return matches ? matches.length : 0;
};

/**
 * Count number of special characters in a string
 *
 * @param {string} str
 * @returns {int}
 */
const countSpecialChars = (str) => {
  const specialRegex = /[^a-zA-Z0-9]/g;
  const matches = str.match(specialRegex);
  return matches ? matches.length : 0;
};

/**
 * Create user settings key based on main key and component
 *
 * @param {string} key
 * @param {string} type
 * @param {object} component
 * @param {object} filters
 * @returns {string}
 */
const generateUserSettingsKey = (key, type, component, filters = {}) => {
  let keyArr = [key];
  switch (type) {
    case "chart":
    case "grid":
      keyArr.push(...[component.component, component.name]);
      switch (component.name) {
        case ComponentNameMapper.home_search_table:
          if (
            Array.isArray(filters.rowGroups) &&
            filters.rowGroups.length > 0
          ) {
            keyArr.push(...filters.rowGroups);
          }
      }
      break;
  }

  return keyArr.join("_");
};

/**
 * Get row option item element based on input type
 *
 * @param {object} option
 * @param {object} rowOption
 * @param {string} pageKey
 * @param {object} filters
 * @param {function} setFilters
 * @param {object} prerenderData
 * @param {function} setPrerenderData
 * @param {object} rowOptionActions
 * @param {function} setShouldGenerateRow
 * @returns {JSX Element} item
 */
const getRowOptionItem = (
  option,
  rowOption,
  pageKey,
  filters,
  setFilters,
  prerenderData,
  setPrerenderData,
  rowOptionActions,
  setShouldGenerateRow,
) => {
  let item = <></>;
  if (option) {
    switch (option.inputType) {
      case "form":
        item = (
          <Form.Provider
            key={`form_${rowOption.key}`}
            onFormFinish={(name, { values }) => {
              if (
                name &&
                name.indexOf("top_failing_test_selection_form") !== -1
              ) {
                rowOptionActions.analyseTest(values);
              } else if (
                name &&
                name.indexOf("selected_test_analysis_filter_form") !== -1
              ) {
                rowOptionActions.analyseTest(values);
              } else if (
                name &&
                name.indexOf("binning_bin_pattern_form") !== -1
              ) {
                rowOptionActions.plotBinPatterns(values);
              } else if (
                name &&
                name.indexOf("grouping_selection_form") !== -1
              ) {
                rowOptionActions.changeAnalysisGrouping(values);
              } else if (
                name &&
                name.indexOf("per_group_wafer_chart_options_form") !== -1
              ) {
                rowOptionActions.plotPerGroupWaferCharts(values);
              } else if (
                name &&
                name.indexOf("binning_pareto_group_filter_form") !== -1
              ) {
                rowOptionActions.reloadGroupParetoCharts(values);
              } else if (
                name &&
                name.indexOf("scatter_chart_options_form") !== -1
              ) {
                rowOptionActions.filterScatterChart(values, rowOption.data);
              } else if (
                name &&
                name.indexOf("mpr_xy_wafer_chart_options_form") !== -1
              ) {
                rowOptionActions.plotMPRXYWaferCharts(values);
              } else if (
                name &&
                name.indexOf("selected_test_trend_analysis_filter_form") !== -1
              ) {
                rowOptionActions.plotTrendAnalysis(values);
              } else if (
                name &&
                name.indexOf("trend_analysis_chart_options_form") !== -1
              ) {
                rowOptionActions.reloadTrendAnalysisChartsTable(
                  values,
                  rowOption.data,
                );
              }
            }}
          >
            {React.createElement(option.component, {
              pageKey: pageKey,
              key: `form_component_${rowOption.key}`,
              filters: filters,
              setFilters: setFilters,
              prerenderData: prerenderData,
              setPrerenderData: setPrerenderData,
              rowOptionData: rowOption.data,
              setShouldGenerateRow: setShouldGenerateRow,
            })}
          </Form.Provider>
        );
        break;
      case "radioButton":
        item = (
          <Flex
            key={`radio_group_${rowOption.key}_wrapper`}
            justify={option.position ?? "normal"}
          >
            <Radio.Group
              key={`radio_group_${rowOption.key}`}
              options={option.options}
              onChange={rowOptionActions[option.onChange]}
              defaultValue={
                option.defaultValue !== undefined ? option.defaultValue : null
              }
              optionType="button"
              buttonStyle="solid"
            />
          </Flex>
        );
        break;
    }
  }

  return item;
};

/**
 * Decode base64 and then convert it to a Uint8Array
 *
 * @param {string} base64
 * @returns {Uint8Array} bytes
 */
const base64ToUint8Array = (base64) => {
  const binaryString = window.atob(base64);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  return bytes;
};

const Helper = {
  /**
   * Navigate to other page
   *
   * @param {string} key - selected page menu item key
   * @param {QueryClient} queryClient
   * @param {object} message
   */
  navigatePage: (key, queryClient, message) => {
    if (PageMapper[key]) {
      if (PageMapper[key].pageUrl) {
        renderPage(
          PageMapper[key].pageKey,
          PageMapper[key].pageName,
          PageMapper[key].pageUrl,
          queryClient,
        );
      } else {
        renderPageByPageKey(key, queryClient, message);
      }
    }
  },
  /**
   * Render page content
   *
   * @param {string} pageKey
   * @param {string} pageName
   * @param {string} pageUrl
   * @param {QueryClient} queryClient
   * @param {boolean} shouldSaveHistory
   * @param {boolean} newTab
   */
  renderPage: (
    pageKey,
    pageName,
    pageUrl,
    queryClient,
    shouldSaveHistory = true,
    newTab = false,
  ) => {
    renderPage(
      pageKey,
      pageName,
      pageUrl,
      queryClient,
      shouldSaveHistory,
      newTab,
    );
  },
  /**
   * Get url parameters from hash segment of the url
   *
   * @param {string} hash
   * @returns {object} urlParameters
   */
  getUrlParamsFromHash: (hash) => {
    let urlParameters = {};
    if (hash) {
      const [, queryString] = hash.split("?");
      if (queryString) {
        const searchParams = new URLSearchParams(queryString);
        searchParams.forEach((value, key) => {
          urlParameters[key] = value;
        });
      }
    }

    return urlParameters;
  },
  /**
   * Get page map by key value pair
   *
   * @param {string} key
   * @param {string} value
   * @param {string} queryString
   * @param {string} hash
   * @returns {object} pageMap
   */
  getPageMapByKeyValue: (key, value, queryString = "", hash = "") => {
    const pageMap = getPageMapByKeyValue(key, value, queryString, hash);

    return pageMap;
  },
  /**
   * Get page map data from hash segment of the url
   *
   * @param {string} hash
   * @returns {object} pageMap
   */
  getPageMapFromHash: (hash) => {
    let pageMap = {};
    if (hash) {
      const [hashSegment, queryString] = hash.split("?");
      if (hashSegment) {
        pageMap = getPageMapByKeyValue(
          "pageSegment",
          hashSegment,
          queryString,
          hash,
        );
      }
    }

    return pageMap;
  },
  /**
   * Get the header depending on content type
   *
   * @param {string} contentType
   * @returns {object} header
   */
  getHeaders: (contentType = "application/json") => {
    let header = {
      Authorization: `Bearer ${window.localStorage.getItem("accessToken")}`,
      Accept: "application/json",
    };

    switch (contentType) {
      case "application/json":
        header["Content-Type"] = "application/json";
        break;
    }

    return header;
  },
  /**
   * The fetch response common callback
   *
   * @param {object} response
   * @returns {object} responseData
   */
  responseCallback: (response) => {
    let responseData;
    if (response.ok) {
      setSiteMaintenance(false, false);
      const contentType = response.headers.get("Content-Type");
      if (
        contentType.indexOf("text/csv") !== -1 ||
        contentType.indexOf("application/gzip") !== -1 ||
        contentType.indexOf("application/zip") !== -1
      ) {
        responseData = response.blob();
      } else {
        responseData = response.json();
      }
    } else {
      responseData = Promise.reject(response);
    }

    return responseData;
  },
  /**
   * The fetch error common callback
   *
   * @param {Function} callback
   */
  errorCallback: (callback) => (err) => {
    if (err.status === 401) {
      logoutUser();
    } else if (err.status === 503) {
      setSiteMaintenance(
        true,
        window.location.pathname.indexOf("/maintenance") === -1,
      );
    } else {
      if (err instanceof Response) {
        try {
          err.json().then((response) => {
            callback(response.message, err.status);
          });
        } catch (error) {
          console.error(error);
          if (err.name === "AbortError") {
            // request aborted by user
            // nothing to do for now
          }
        }
      } else {
        console.log("Fetch failed:", err.message);
      }
    }
  },
  /**
   * The login error callback
   *
   * @param {Function} callback
   */
  loginErrorCallback: (callback) => (err) => {
    if (err.status === 503) {
      setSiteMaintenance(
        true,
        window.location.pathname.indexOf("/maintenance") === -1,
      );
    } else {
      err.json().then((response) => {
        callback(response.message);
      });
    }
  },
  /**
   * Log in user
   *
   * @param {object} res
   */
  loginUser: (res) => {
    window.localStorage.setItem("accessToken", res.data.token);
    window.localStorage.setItem("userData", JSON.stringify(res.data.user));
    window.localStorage.setItem("isLoggedIn", "true");
    if (res.data.device_id) {
      window.localStorage.setItem("yhDeviceId", res.data.device_id);
    }
    window.location.href = "/page";
  },
  /**
   * Log user out
   *
   * @param {boolean} shouldLogin
   */
  logoutUser: (shouldLogin = true) => {
    logoutUser(shouldLogin);
  },
  /**
   * Set the application UI to maintenance mode
   *
   * @param {boolean} value
   * @param {boolean} redirect
   * @param {string} path
   */
  setSiteMaintenance: (
    value = true,
    redirect = true,
    path = "/maintenance",
  ) => {
    setSiteMaintenance(value, redirect, path);
  },
  /**
   * Validate input max length
   *
   * @param {Event} e
   * @param {string} value
   * @returns {boolean} isValid
   */
  validateMaxLength: (e, value) => {
    let isValid = true;
    if (e.target.maxLength !== undefined && value.length > e.target.maxLength) {
      if (typeof e.preventDefault === "function") {
        e.preventDefault();
      }
      isValid = false;
    }
    return isValid;
  },
  /**
   * Validate input min length
   *
   * @param {Event} e
   * @param {string} value
   * @returns {boolean} isValid
   */
  validateMinLength: (e, value) => {
    let isValid = true;
    if (e.target.minLength !== undefined && value.length < e.target.minLength) {
      if (typeof e.preventDefault === "function") {
        e.preventDefault();
      }
      isValid = false;
    }
    return isValid;
  },
  /**
   * Validate lowercase min count
   *
   * @param {Event} e
   * @param {string} value
   * @returns {boolean} isValid
   */
  validateLowercaseCount: (e, value) => {
    let isValid = true;
    if (
      e.target.getAttribute("lowercasecount") !== undefined &&
      countCharCaseLetters(value, "lowercase") <
        parseInt(e.target.getAttribute("lowercasecount"))
    ) {
      if (typeof e.preventDefault === "function") {
        e.preventDefault();
      }
      isValid = false;
    }
    return isValid;
  },
  /**
   * Validate uppercase min count
   *
   * @param {Event} e
   * @param {string} value
   * @returns {boolean} isValid
   */
  validateUppercaseCount: (e, value) => {
    let isValid = true;
    if (
      e.target.getAttribute("uppercasecount") !== undefined &&
      countCharCaseLetters(value, "uppercase") <
        parseInt(e.target.getAttribute("uppercasecount"))
    ) {
      if (typeof e.preventDefault === "function") {
        e.preventDefault();
      }
      isValid = false;
    }
    return isValid;
  },
  /**
   * Validate number min count
   *
   * @param {Event} e
   * @param {string} value
   * @returns {boolean} isValid
   */
  validateNumberCount: (e, value) => {
    let isValid = true;
    if (
      e.target.getAttribute("numbercount") !== undefined &&
      countNumbers(value) < parseInt(e.target.getAttribute("numbercount"))
    ) {
      if (typeof e.preventDefault === "function") {
        e.preventDefault();
      }
      isValid = false;
    }
    return isValid;
  },
  /**
   * Validate special character min count
   *
   * @param {Event} e
   * @param {string} value
   * @returns {boolean} isValid
   */
  validateSpecialCharCount: (e, value) => {
    let isValid = true;
    if (
      e.target.getAttribute("specialcharcount") !== undefined &&
      countSpecialChars(value) <
        parseInt(e.target.getAttribute("specialcharcount"))
    ) {
      if (typeof e.preventDefault === "function") {
        e.preventDefault();
      }
      isValid = false;
    }
    return isValid;
  },
  /**
   * Validate character input by regular expression
   * @param {Event} e
   * @param {string} value
   * @param {string} regex
   * @returns {boolean}
   */
  validateKeyPressed: (e, value, regex) => {
    const isValid = regex.test(value);
    if (isValid === false) {
      if (typeof e.preventDefault === "function") {
        e.preventDefault();
      }
      return false;
    }
  },
  /**
   * Get logged in user data
   *
   * @returns {json} userData
   */
  getUserData: () => {
    const userData = getUserData();
    return userData;
  },
  /**
   * Update user data client storage
   *
   * @param {string} key
   * @param {*} value
   * @param {function} setUserData
   */
  updateUserDataStorage: (key, value, setUserData) => {
    const userData = getUserData();
    userData[key] = value;
    window.localStorage.setItem("userData", JSON.stringify(userData));
    if (typeof setUserData === "function") {
      setUserData(userData);
    }
  },
  /**
   * Replace underscores with spaces and capitalize words
   *
   * @param {string} str
   * @returns {string}
   */
  titlelize: (str) => {
    const words = str.split("_");
    words.forEach((word, i) => {
      words[i] = words[i].charAt(0).toUpperCase() + words[i].slice(1);
    });

    return words.join(" ");
  },
  /**
   * Get all unique values of an array
   *
   * @param {array} arr
   * @returns {array} unique
   */
  arrayUnique: (arr) => {
    const unique = [...new Set(arr)];

    return unique;
  },
  /**
   * Rearrange array based on given order of indexes
   *
   * @param {array} arr
   * @param {array} indexes
   * @returns {array} result
   */
  rearrangeArray: (arr, indexes) => {
    let result = [];
    indexes.forEach((index) => {
      result.push(arr[index]);
    });

    return result;
  },
  /**
   * Clone array to avoid mutation on original array
   *
   * @param {array} sourceArr
   * @returns {array} newArr
   */
  cloneArray: (sourceArr) => {
    const newArr = [...sourceArr];

    return newArr;
  },
  /**
   * Clone object to avoid mutation on original object
   *
   * @param {object} obj
   * @returns {object} objCopy
   */
  cloneObject: (obj) => {
    const objCopy = cloneObject(obj);

    return objCopy;
  },
  /**
   * Filter object by allowed keys
   *
   * @param {object} sourceObj
   * @param {array} allowedKeys
   * @returns {object} filteredObj
   */
  filterObjectByKeys: (sourceObj, allowedKeys) => {
    const filteredObj = Object.keys(sourceObj)
      .filter((key) => {
        return allowedKeys.includes(key);
      })
      .reduce((obj, key) => {
        obj[key] = sourceObj[key];
        return obj;
      }, {});

    return filteredObj;
  },
  /**
   * filter array by key and get matched element
   *
   * @param {array} arr
   * @param {string} key
   * @param {*} value
   * @param {boolean} firstMatchOnly
   * @returns {*}
   */
  filterArrayAndFind: (arr, key, value, firstMatchOnly = true) => {
    const filteredArray = arr.filter((item) => {
      // do not use strict comparison since there are instances that array value and searched value has different type
      return Array.isArray(value)
        ? value.indexOf(item[key]) !== -1
        : item[key] == value;
    });

    return filteredArray.length > 0
      ? firstMatchOnly
        ? filteredArray[0]
        : filteredArray
      : null;
  },
  /**
   * Get elements before and after a specific element in an array based on its index,
   * while prioritizing the "before" elements
   *
   * @param {array} arr
   * @param {int} index
   * @param {int} count
   * @returns {array}
   */
  getArrayElementsAroundIndex: (arr, index, count) => {
    const halfCount = Math.ceil((count - 1) / 2); // half count for elements before and after
    let startIndex = Math.max(0, index - halfCount); // start index prioritizing before the element
    let endIndex = Math.min(arr.length, startIndex + count); // end index based on the count

    // adjust the startIndex if there aren't enough elements after the index
    if (endIndex - startIndex < count) {
      startIndex = Math.max(0, endIndex - count);
    }

    return arr.slice(startIndex, endIndex);
  },
  /**
   * Sort object by key value in ascending order
   *
   * @param {object} obj
   * @param {string} key
   * @returns {object} sortedObj
   */
  sortObjectByKeyValue: (obj, key) => {
    const entries = Object.entries(obj);
    entries.sort((a, b) =>
      (a[1]?.[key] ?? "")
        .toLowerCase()
        .localeCompare((b[1]?.[key] ?? "").toLowerCase()),
    );
    const sortedObj = Object.fromEntries(entries);
    return sortedObj;
  },
  /**
   * Sort an object in descending order and maintain index association
   * JS objects do not guarantee a specific order for their properties, so returning array of key-value pair here instead
   *
   * @param {object} sourceObj
   * @returns {array} keyValueArr
   */
  arsort: (sourceObj) => {
    // Convert the object to an array of key-value pairs
    let keyValueArr = Object.entries(sourceObj);
    // Sort the array based on values in descending order
    keyValueArr.sort((a, b) => b[1] - a[1]);

    return keyValueArr;
  },
  /**
   * Sort an object by key in descending order
   * JS objects do not guarantee a specific order for their properties, so returning array of key-value pair here instead
   *
   * @param {object} sourceObj
   * @returns {array} keyValueArr
   */
  krsort: (sourceObj) => {
    // Get the keys of the object as an array
    let keysArray = Object.keys(sourceObj);
    // Sort the array of keys in reverse order
    keysArray.sort().reverse();
    let keyValueArr = [];
    keysArray.forEach((key) => {
      keyValueArr.push([key, sourceObj[key]]);
    });

    return keyValueArr;
  },
  /**
   * Get minimum value of an array
   * This is suitable for very large arrays to avoid RangeError when using Math.min
   *
   * @param {array} arr
   * @returns {*} min
   */
  getArrayMin: (arr) => {
    const min = arr.length > 0 ? arr.reduce((a, b) => Math.min(a, b)) : 0;
    return min;
  },
  /**
   * Get maximum value of an array
   * This is suitable for very large arrays to avoid RangeError when using Math.max
   *
   * @param {array} arr
   * @returns {*} max
   */
  getArrayMax: (arr) => {
    const max = arr.length > 0 ? arr.reduce((a, b) => Math.max(a, b)) : 0;
    return max;
  },
  /**
   * Disable all future dates
   *
   * @param {Object} current
   * @returns {boolean}
   */
  disabledFutureDate: (current) => {
    return current && current > dayjs().endOf("day");
  },
  /**
   * Format period value to user readable form
   *
   * @param {object|array} value
   * @returns {string} label
   */
  formatPeriodLabel: (value) => {
    let label = "";
    if (Array.isArray(value)) {
      const startDate = value[0];
      const endDate = value[1];
      const isSameDay = startDate.isSame(endDate, "day");
      const isSameMonth = startDate.isSame(endDate, "month");
      const isSameYear = startDate.isSame(endDate, "year");

      if (isSameDay && isSameMonth && isSameYear) {
        label = `${startDate.format("MMM")} ${startDate.format(
          "D",
        )}, ${startDate.format("YYYY")}`;
      } else if (isSameMonth && isSameYear) {
        label = `${startDate.format("MMM")} ${startDate.format(
          "D",
        )}-${endDate.format("D")}, ${endDate.format("YYYY")}`;
      } else if (isSameYear) {
        label = `${startDate.format("MMM")} ${startDate.format(
          "D",
        )}-${endDate.format("MMM")} ${endDate.format("D")}, ${endDate.format(
          "YYYY",
        )}`;
      } else {
        label = `${startDate.format("MMM")} ${startDate.format(
          "D",
        )}, ${startDate.format("YYYY")} - ${endDate.format(
          "MMM",
        )} ${endDate.format("D")}, ${endDate.format("YYYY")}`;
      }
    } else {
      label = `${value.format("MMM")} ${value.format("D")}, ${value.format(
        "YYYY",
      )}`;
    }

    return label;
  },
  /**
   * Set show invalid filter value
   *
   * @param {object} newFilters
   * @param {boolean} isShowInvalid
   * @returns {object} newFilters
   */
  setShowInvalidFilter: (currentFilters, newFilters) => {
    newFilters.show_invalid = currentFilters.show_invalid
      ? currentFilters.show_invalid
      : false;

    return newFilters;
  },
  /**
   * Get page filters by page and filters key
   *
   * @param {object} filters
   * @param {string} pageKey
   * @param {string} filtersKey
   * @returns {object}
   */
  getPageFilters: (filters, pageKey, filtersKey) => {
    return filtersKey && filters[pageKey][filtersKey]
      ? { ...filters[pageKey][filtersKey], ...filters[pageKey] }
      : filters[pageKey]
        ? filters[pageKey]
        : {};
  },
  /**
   * Get url parameters based on history state
   *
   * @returns {object}
   */
  getUrlParameters: () => {
    return getUrlParameters();
  },
  /**
   * Get short url and execute callback function if defined
   *
   * @param {object} surlParams - short url parameters
   * @param {object} urlParams - url parameters
   * @param {function} callbackFn - callback function to be executed after getting short url value
   * @param {*} callbackParams - callback function parameters
   */
  getShortUrl: (surlParams, urlParams, callbackFn, callbackParams) => {
    getShortUrl(surlParams, urlParams, callbackFn, callbackParams);
  },
  /**
   * Get long url parameters and execute callback function if defined
   *
   * @param {string} surl - short url value
   * @param {object} urlParams - url parameters
   * @param {function} callbackFn - callback function to be executed after getting long url value
   * @param {*} callbackParams
   * @param {object} message
   * @returns {AbortController}
   */
  getLongUrlParams: (surl, urlParams, callbackFn, callbackParams, message) => {
    return getLongUrlParams(
      surl,
      urlParams,
      callbackFn,
      callbackParams,
      message,
    );
  },
  /**
   * Replace url endpoint variable parameter with actual value
   *
   * @param {string} url
   * @param {object} params
   * @returns {string} url
   */
  parseUrlEndpoint: (url, params) => {
    Object.keys(params).forEach((key) => {
      const urlParam = urlParamMapper[key] ? urlParamMapper[key] : key;
      const regex = new RegExp(`{${urlParam}}`, "g");
      url = url.replace(regex, params[key]);
    });

    return url;
  },
  /**
   * Get preset analysis templates
   *
   * @param {function} successCbk - success callback function
   * @param {*} successCbkParams
   * @returns {AbortController}
   */
  getPresetAnalysisTemplates: (successCbk, successCbkParams) => {
    return getPresetAnalysisTemplates(successCbk, successCbkParams);
  },
  /**
   * Get user analysis templates for editing
   *
   * @param {function} successCbk - success callback function
   * @returns {AbortController}
   */
  getUserAnalysisTemplates: (successCbk) => {
    return Api.getUserAnalysisTemplates(
      (res) => {
        if (res.success) {
          successCbk(res.data);
        } else {
          notification.warning({
            message: "User Analysis Templates",
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: "User Analysis Templates",
          description: err,
        });
      },
    );
  },
  /**
   * Get template by key id
   *
   * @param {string} templateKeyId
   * @param {array} presetAnalysisTemplates
   * @param {array} userAnalysisTemplates
   * @returns {object}
   */
  getTemplateByKeyId: (
    templateKeyId,
    presetAnalysisTemplates,
    userAnalysisTemplates,
  ) => {
    return getTemplateByKeyId(
      templateKeyId,
      presetAnalysisTemplates,
      userAnalysisTemplates,
    );
  },
  /**
   * Get analysis input data based on grid selection
   *
   * @param {object} gridOptions
   * @param {object} gridComponent
   * @param {string} selectionType
   * @param {function} successCbk
   * @param {*} successCbkParams
   * @param {function} warningCbk
   * @param {function} errorCbk
   * @returns {object} input
   */
  getAnalysisInput: (
    gridOptions,
    gridComponent,
    selectionType,
    successCbk,
    successCbkParams,
    warningCbk,
    errorCbk,
  ) => {
    const isServerSide =
      gridOptions.api.getGridOption("rowModelType") === "serverSide";
    const selectedNodes = isServerSide
      ? GridHelper.getServerSideSelectedNodes(gridOptions)
      : gridOptions.api.getSelectedNodes();
    const gridSelectionData = isServerSide
      ? GridHelper.getServerSideSelectedRows(gridOptions)
      : gridOptions.api.getSelectedRows();

    const input = formatAnalysisInput(
      selectedNodes,
      gridSelectionData,
      gridOptions,
      gridComponent,
    );

    if (
      selectionType === "datalog" &&
      typeof successCbk === "function" &&
      Array.isArray(input.search_selection.groupKeys.lot_id)
    ) {
      Api.getLotDsks(
        (res) => {
          if (res.success) {
            input.src_type = "dsk";
            input.src_value = Helper.arrayUnique(
              res.data.concat(input.dsk ?? []),
            );
            successCbk(input, successCbkParams);
          } else {
            warningCbk(res.message, 5);
          }
        },
        (err) => {
          errorCbk(err, 5);
        },
        {
          src_type: "lotid",
          src_value: input.search_selection.groupKeys.lot_id.join(),
          mfg_process: input.mfg_process.join(),
        },
      );
    } else {
      input.src_type =
        Array.isArray(input.dsk) && input.dsk.length > 0 ? "dsk" : "lotid";
      input.src_value =
        input.src_type === "lotid" ? input.lot_id : input[input.src_type];

      if (typeof successCbk === "function") {
        successCbk(input, successCbkParams);
      }
    }

    return input;
  },
  /**
   * Format grid selection to analysis input
   *
   * @param {array} selectedNodes
   * @param {arrayy} gridSelectionData
   * @param {object} gridOptions
   * @param {object} gridComponent
   * @returns {object} input
   */
  formatAnalysisInput: (
    selectedNodes,
    gridSelectionData,
    gridOptions,
    gridComponent,
  ) => {
    const input = formatAnalysisInput(
      selectedNodes,
      gridSelectionData,
      gridOptions,
      gridComponent,
    );

    return input;
  },
  /**
   * Validate input data based on analysis template validation rules
   *
   * @param {object} input
   * @param {object} template
   * @param {object} message
   * @returns {array} invalidMessages
   */
  validateAnalysisInput: (input, template, message) => {
    const invalidMessages = validateAnalysisInput(input, template, message);

    return invalidMessages;
  },
  /**
   * Validate children item based on template children render rules
   *
   * @param {object} inputCount
   * @param {object} template
   * @returns {array} children
   */
  validateChildrenRulesByCondition: (inputCount, template) => {
    let children = [];
    const templateData = JSON.parse(template.template_data);
    if (Object.keys(templateData).length > 0) {
      const childrenRenderRules =
        templateData.menu_item_children_render_rules !== undefined
          ? templateData.menu_item_children_render_rules.rules
          : {};
      const condition =
        templateData?.menu_item_children_render_rules?.condition ?? "and";

      let ruleStatus = {};
      let valid = true;
      if (Object.keys(childrenRenderRules).length) {
        Object.keys(childrenRenderRules).forEach((param) => {
          ruleStatus[param] = {
            valid: true,
          };

          const { min, max } = childrenRenderRules[param];
          if (
            max &&
            Helper.hasValue(inputCount[param]) &&
            inputCount[param] > max
          ) {
            ruleStatus[param].valid = false;
          }
          if (
            min &&
            (!Helper.hasValue(inputCount[param]) || inputCount[param] < min)
          ) {
            ruleStatus[param].valid = false;
          }
        });

        valid = Object.values(ruleStatus)
          .map((rule) => rule.valid)
          .reduce((acc, current) =>
            condition === "or" ? acc || current : acc && current,
          );

        if (valid) {
          children =
            templateData.menu_item_children_render_rules !== undefined
              ? templateData.menu_item_children_render_rules.items
              : [];
        }
      }
    }

    return children;
  },
  /**
   * Generate analysis
   *
   * @param {string} key
   * @param {object} input
   * @param {array} presetAnalysisTemplates
   * @param {QueryClient} queryClient
   * @param {object} message
   * @param {boolean} shouldSaveHistory
   * @param {boolean} newTab
   */
  generateAnalysis: (
    key,
    input,
    presetAnalysisTemplates,
    queryClient,
    message,
    shouldSaveHistory = true,
    newTab = false,
  ) => {
    generateAnalysis(
      key,
      input,
      presetAnalysisTemplates,
      queryClient,
      message,
      shouldSaveHistory,
      newTab,
    );
  },
  /**
   * Open analysis in current or new tab
   *
   * @param {string} pageKey
   * @param {object} input
   * @param {array} presetAnalysisTemplates
   * @param {QueryClient} queryClient
   * @param {object} message
   * @param {boolean} shouldSaveHistory
   * @param {boolean} newTab
   */
  openAnalysis: (
    pageKey,
    input,
    presetAnalysisTemplates,
    queryClient,
    message,
    shouldSaveHistory = true,
    newTab = false,
  ) => {
    const template = getTemplateByKey(presetAnalysisTemplates, pageKey);
    const templateKey = template.key;
    generateAnalysis(
      templateKey,
      input,
      presetAnalysisTemplates,
      queryClient,
      message,
      shouldSaveHistory,
      newTab,
    );
  },
  /**
   * Get manufacturing process options
   *
   * @param {function} successCbk - success callback function
   * @returns {AbortController}
   */
  getManufacturingProcessOptions: (successCbk) => {
    return Api.getManufacturingProcessOptions(
      (res) => {
        if (res.success) {
          successCbk(res.data);
        } else {
          notification.warning({
            message: "Manufacturing Process Options",
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: "Manufacturing Process Options",
          description: err,
        });
      },
    );
  },
  /**
   * Remove empty rows
   *
   * @param {array} rows
   */
  removeEmptyTemplateRows: (rows) => {
    rows.forEach((row, rowKey) => {
      if (row.columns && Object.keys(row.columns).length === 0) {
        delete rows[rowKey];
      }
      row.columns.forEach((column) => {
        if (column.rows) {
          Helper.removeEmptyTemplateRows(column.rows);
        }
        if (column.tabs) {
          Object.values(column.tabs).forEach((tab) => {
            if (tab.rows) {
              Helper.removeEmptyTemplateRows(tab.rows);
            }
          });
        }
      });
    });
  },
  /**
   * Add row to template
   *
   * @param {object} rowLayout
   * @param {array} templateRows
   * @param {function} setTemplateRows
   */
  addTemplateRow: (rowLayout, templateRows, setTemplateRows) => {
    const component = rowLayout.component ? rowLayout.component : null;
    const rowColumns = createRowColumns(rowLayout.colspans, component);
    const templateRowsCopy = cloneObject(templateRows);

    templateRowsCopy.push({
      columns: rowColumns,
      index: templateRows.length,
      type: rowLayout.type ? rowLayout.type : null,
    });
    setTemplateRows(templateRowsCopy);
  },
  /**
   * Add row to column
   *
   * @param {string} rowKey
   * @param {string} colKey
   * @param {string} colRowKey
   * @param {string} colRowColKey
   * @param {string} colRowColRowKey
   * @param {string} colRowColRowColKey
   * @param {object} rowLayout
   * @param {array} templateRows
   * @param {function} setTemplateRows
   * @param {string} tabKey
   * @param {string} subTabKey
   */
  addColumnRow: (
    rowKey,
    colKey,
    colRowKey,
    colRowColKey,
    colRowColRowKey,
    colRowColRowColKey,
    rowLayout,
    templateRows,
    setTemplateRows,
    tabKey,
    subTabKey,
  ) => {
    const component = rowLayout.component ? rowLayout.component : null;
    const rowColumns = createRowColumns(rowLayout.colspans, component);
    let templateRowsCopy = cloneObject(templateRows);

    let templateColumn = getTemplateColumnByIndex(
      templateRows,
      rowKey,
      colKey,
      colRowKey,
      colRowColKey,
      colRowColRowKey,
      colRowColRowColKey,
      undefined,
      undefined,
      tabKey,
      subTabKey,
    );

    if (!templateColumn.rows) {
      templateColumn.rows = [];
    }

    templateColumn.rows.push({
      columns: rowColumns,
      index: templateColumn.rows.length,
      type: rowLayout.type ? rowLayout.type : null,
    });
    setTemplateRows(templateRowsCopy);
  },
  /**
   * Add component to template
   *
   * @param {object} targetColumn
   * @param {object} component
   * @param {array} templateRows
   * @param {function} setTemplateRows
   * @param {string} rowKey
   * @param {string} colKey
   * @param {string} colRowKey
   * @param {string} colRowColKey
   * @param {string} colRowColRowKey
   * @param {string} colRowColRowColKey
   * @param {string} colRowColRowColRowKey
   * @param {string} colRowColRowColRowColKey
   * @param {string} colRowColRowColRowColRowKey
   * @param {string} colRowColRowColRowColRowColKey
   * @param {string} tabKey
   * @param {string} subTabKey
   * @returns {array|void} templateRowsCopy
   */
  addTemplateComponent: (
    targetColumn,
    component,
    templateRows,
    setTemplateRows,
    rowKey,
    colKey,
    colRowKey,
    colRowColKey,
    colRowColRowKey,
    colRowColRowColKey,
    colRowColRowColRowKey,
    colRowColRowColRowColKey,
    colRowColRowColRowColRowKey,
    colRowColRowColRowColRowColKey,
    tabKey,
    subTabKey,
  ) => {
    if (
      colRowColRowColRowColRowKey !== undefined &&
      colRowColRowColRowColRowColKey !== undefined
    ) {
      if (tabKey !== undefined) {
        component.id =
          component.index = `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}-${colRowColRowColRowKey}-${colRowColRowColRowColKey}-${colRowColRowColRowColRowKey}-${colRowColRowColRowColRowColKey}-${tabKey}`;
      } else {
        component.id =
          component.index = `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}-${colRowColRowColRowKey}-${colRowColRowColRowColKey}-${colRowColRowColRowColRowKey}-${colRowColRowColRowColRowColKey}`;
      }
    } else if (
      colRowColRowColRowKey !== undefined &&
      colRowColRowColRowColKey !== undefined
    ) {
      if (tabKey !== undefined) {
        component.id =
          component.index = `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}-${colRowColRowColRowKey}-${colRowColRowColRowColKey}-${tabKey}`;
      } else {
        component.id =
          component.index = `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}-${colRowColRowColRowKey}-${colRowColRowColRowColKey}`;
      }
    } else if (
      colRowColRowKey !== undefined &&
      colRowColRowColKey !== undefined
    ) {
      if (tabKey !== undefined) {
        component.id =
          component.index = `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}-${tabKey}`;
      } else {
        component.id =
          component.index = `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}`;
      }
    } else if (colRowKey !== undefined && colRowColKey !== undefined) {
      if (tabKey !== undefined) {
        component.id =
          component.index = `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${tabKey}`;
      } else {
        component.id =
          component.index = `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}`;
      }
    } else {
      if (tabKey !== undefined) {
        component.id = component.index = `${rowKey}-${colKey}-${tabKey}`;
      } else {
        component.id = component.index = `${rowKey}-${colKey}`;
      }
    }
    if (targetColumn === undefined) {
      targetColumn = getTemplateColumnByIndex(
        templateRows,
        rowKey,
        colKey,
        colRowKey,
        colRowColKey,
        colRowColRowKey,
        colRowColRowColKey,
        colRowColRowColRowKey,
        colRowColRowColRowColKey,
        colRowColRowColRowColRowKey,
        colRowColRowColRowColRowColKey,
        tabKey,
        subTabKey,
      );
    }
    targetColumn.component = component;

    if (setTemplateRows) {
      setTemplateRows(templateRows);
    } else {
      return templateRows;
    }
  },
  /**
   * Get template rows by index
   *
   * @param {array} templateRows
   * @param {string} rowKey
   * @param {string} colKey
   * @param {string} colRowKey
   * @param {string} colRowColKey
   * @param {string} colRowColRowKey
   * @param {string} colRowColRowColKey
   * @param {string} colRowColRowColRowKey
   * @param {string} colRowColRowColRowColKey
   * @param {string} colRowColRowColRowColRowKey
   * @param {string} colRowColRowColRowColRowColKey
   * @param {string} tabKey
   * @param {string} subTabKey
   * @returns {object} rows
   */
  getTemplateRowsByIndex: (
    templateRows,
    rowKey,
    colKey,
    colRowKey,
    colRowColKey,
    colRowColRowKey,
    colRowColRowColKey,
    colRowColRowColRowKey,
    colRowColRowColRowColKey,
    colRowColRowColRowColRowKey,
    colRowColRowColRowColRowColKey,
    tabKey,
    subTabKey,
  ) => {
    const rows = getTemplateRowsByIndex(
      templateRows,
      rowKey,
      colKey,
      colRowKey,
      colRowColKey,
      colRowColRowKey,
      colRowColRowColKey,
      colRowColRowColRowKey,
      colRowColRowColRowColKey,
      colRowColRowColRowColRowKey,
      colRowColRowColRowColRowColKey,
      tabKey,
      subTabKey,
    );

    return rows;
  },
  /**
   * Get template rows key
   *
   * @param {array} templateRows
   * @param {string} rowKey
   * @param {string} colRowKey
   * @param {string} colRowColRowKey
   * @param {string} colRowColRowColRowKey
   * @param {string} colRowColRowColRowColRowKey
   * @returns {string} key
   */
  getTemplateRowsKey: (
    rowKey,
    colRowKey,
    colRowColRowKey,
    colRowColRowColRowKey,
    colRowColRowColRowColRowKey,
  ) => {
    const key = getTemplateRowsKey(
      rowKey,
      colRowKey,
      colRowColRowKey,
      colRowColRowColRowKey,
      colRowColRowColRowColRowKey,
    );

    return key;
  },
  /**
   * Get template row by index
   *
   * @param {array} templateRows
   * @param {string} rowKey
   * @param {string} colKey
   * @param {string} colRowKey
   * @param {string} colRowColKey
   * @param {string} colRowColRowKey
   * @param {string} colRowColRowColKey
   * @param {string} colRowColRowColRowKey
   * @param {string} colRowColRowColRowColKey
   * @param {string} colRowColRowColRowColRowKey
   * @param {string} tabKey
   * @param {string} subTabKey
   * @returns {object} row
   */
  getTemplateRowByIndex: (
    templateRows,
    rowKey,
    colKey,
    colRowKey,
    colRowColKey,
    colRowColRowKey,
    colRowColRowColKey,
    colRowColRowColRowKey,
    colRowColRowColRowColKey,
    colRowColRowColRowColRowKey,
    colRowColRowColRowColRowColKey,
    tabKey,
    subTabKey,
  ) => {
    const row = getTemplateRowByIndex(
      templateRows,
      rowKey,
      colKey,
      colRowKey,
      colRowColKey,
      colRowColRowKey,
      colRowColRowColKey,
      colRowColRowColRowKey,
      colRowColRowColRowColKey,
      colRowColRowColRowColRowKey,
      colRowColRowColRowColRowColKey,
      tabKey,
      subTabKey,
    );

    return row;
  },
  /**
   * Get template column by index
   *
   * @param {array} templateRows
   * @param {string} rowKey
   * @param {string} colKey
   * @param {string} colRowKey
   * @param {string} colRowColKey
   * @param {string} colRowColRowKey
   * @param {string} colRowColRowColKey
   * @param {string} colRowColRowColRowKey
   * @param {string} colRowColRowColRowColKey
   * @param {string} colRowColRowColRowColRowKey
   * @param {string} colRowColRowColRowColRowColKey
   * @param {string} tabKey
   * @param {string} subTabKey
   * @returns {object} column
   */
  getTemplateColumnByIndex: (
    templateRows,
    rowKey,
    colKey,
    colRowKey,
    colRowColKey,
    colRowColRowKey,
    colRowColRowColKey,
    colRowColRowColRowKey,
    colRowColRowColRowColKey,
    colRowColRowColRowColRowKey,
    colRowColRowColRowColRowColKey,
    tabKey,
    subTabKey,
  ) => {
    const column = getTemplateColumnByIndex(
      templateRows,
      rowKey,
      colKey,
      colRowKey,
      colRowColKey,
      colRowColRowKey,
      colRowColRowColKey,
      colRowColRowColRowKey,
      colRowColRowColRowColKey,
      colRowColRowColRowColRowKey,
      colRowColRowColRowColRowColKey,
      tabKey,
      subTabKey,
    );

    return column;
  },
  /**
   * Get tab key
   *
   * @param {string} tabKey
   * @param {string} contentTabKey
   * @returns {string} key
   */
  getTabKey: (tabKey, contentTabKey) => {
    const key = tabKey !== undefined ? tabKey : contentTabKey;
    return key;
  },
  /**
   * Get sub tab key
   *
   * @param {string} tabKey
   * @param {string} contentTabKey
   * @param {string} subTabKey
   * @returns {string} key
   */
  getSubTabKey: (tabKey, subTabKey, contentTabKey) => {
    const key =
      subTabKey !== undefined
        ? subTabKey
        : tabKey !== undefined && tabKey !== contentTabKey
          ? contentTabKey
          : undefined;
    return key;
  },
  /**
   * Move/Swap template component
   *
   * @param {object} component
   * @param {array} templateRows
   * @param {function} setTemplateRows
   * @param {string} rowKey
   * @param {string} colKey
   * @param {string} colRowKey
   * @param {string} colRowColKey
   * @param {string} colRowColRowKey
   * @param {string} colRowColRowColkey
   * @param {string} colRowColRowColRowKey
   * @param {string} colRowColRowColRowColkey
   */
  moveTemplateComponent: (
    component,
    templateRows,
    setTemplateRows,
    rowKey,
    colKey,
    colRowKey,
    colRowColKey,
    colRowColRowKey,
    colRowColRowColKey,
    colRowColRowColRowKey,
    colRowColRowColRowColKey,
  ) => {
    const templateRowsCopy = Helper.cloneObject(templateRows);
    const dragIndex = component.index;
    const dragPlacement = getTemplateComponentPlacement(dragIndex);
    const dragComponent =
      dragPlacement.length === 8
        ? templateRowsCopy[dragPlacement[0]]["columns"][dragPlacement[1]][
            "rows"
          ][dragPlacement[2]]["columns"][dragPlacement[3]]["rows"][
            dragPlacement[4]
          ]["columns"][dragPlacement[5]]["rows"][dragPlacement[6]]["columns"][
            dragPlacement[7]
          ]["component"]
        : dragPlacement.length === 6
          ? templateRowsCopy[dragPlacement[0]]["columns"][dragPlacement[1]][
              "rows"
            ][dragPlacement[2]]["columns"][dragPlacement[3]]["rows"][
              dragPlacement[4]
            ]["columns"][dragPlacement[5]]["component"]
          : dragPlacement.length === 4
            ? templateRowsCopy[dragPlacement[0]]["columns"][dragPlacement[1]][
                "rows"
              ][dragPlacement[2]]["columns"][dragPlacement[3]]["component"]
            : templateRowsCopy[dragPlacement[0]]["columns"][dragPlacement[1]][
                "component"
              ];
    let hoverComponent;
    if (
      colRowColRowColRowKey !== undefined &&
      colRowColRowColRowColKey !== undefined
    ) {
      hoverComponent =
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["rows"][colRowColRowKey]["columns"][colRowColRowColKey][
          "rows"
        ][colRowColRowColRowKey]["columns"][colRowColRowColRowColKey][
          "component"
        ];
    } else if (
      colRowColRowKey !== undefined &&
      colRowColRowColKey !== undefined
    ) {
      hoverComponent =
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["rows"][colRowColRowKey]["columns"][colRowColRowColKey][
          "component"
        ];
    } else if (colRowKey !== undefined && colRowColKey !== undefined) {
      hoverComponent =
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["component"];
    } else {
      hoverComponent = templateRowsCopy[rowKey]["columns"][colKey]["component"];
    }
    if (hoverComponent) {
      // swap components
      dragComponent.id = dragComponent.index =
        colRowColRowColRowKey !== undefined &&
        colRowColRowColRowColKey !== undefined
          ? `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}-${colRowColRowColRowKey}-${colRowColRowColRowColKey}`
          : colRowColRowKey !== undefined && colRowColRowColKey !== undefined
            ? `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}`
            : colRowKey !== undefined && colRowColKey !== undefined
              ? `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}`
              : `${rowKey}-${colKey}`;
      hoverComponent.id = hoverComponent.index = dragPlacement.join("-");
      if (
        colRowColRowColRowKey !== undefined &&
        colRowColRowColRowColKey !== undefined
      ) {
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["rows"][colRowColRowKey]["columns"][colRowColRowColKey][
          "rows"
        ][colRowColRowColRowKey]["columns"][colRowColRowColRowColKey][
          "component"
        ] = dragComponent;
      } else if (
        colRowColRowKey !== undefined &&
        colRowColRowColKey !== undefined
      ) {
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["rows"][colRowColRowKey]["columns"][colRowColRowColKey][
          "component"
        ] = dragComponent;
      } else if (colRowKey !== undefined && colRowColKey !== undefined) {
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["component"] = dragComponent;
      } else {
        templateRowsCopy[rowKey]["columns"][colKey]["component"] =
          dragComponent;
      }
      if (dragPlacement.length === 8) {
        templateRowsCopy[dragPlacement[0]]["columns"][dragPlacement[1]]["rows"][
          dragPlacement[2]
        ]["columns"][dragPlacement[3]]["rows"][dragPlacement[4]]["columns"][
          dragPlacement[5]
        ]["rows"][dragPlacement[6]]["columns"][dragPlacement[7]]["component"] =
          hoverComponent;
      } else if (dragPlacement.length === 6) {
        templateRowsCopy[dragPlacement[0]]["columns"][dragPlacement[1]]["rows"][
          dragPlacement[2]
        ]["columns"][dragPlacement[3]]["rows"][dragPlacement[4]]["columns"][
          dragPlacement[5]
        ]["component"] = hoverComponent;
      } else if (dragPlacement.length === 4) {
        templateRowsCopy[dragPlacement[0]]["columns"][dragPlacement[1]]["rows"][
          dragPlacement[2]
        ]["columns"][dragPlacement[3]]["component"] = hoverComponent;
      } else {
        templateRowsCopy[dragPlacement[0]]["columns"][dragPlacement[1]][
          "component"
        ] = hoverComponent;
      }
    } else {
      // move component
      dragComponent.id = dragComponent.index =
        colRowColRowColRowKey !== undefined &&
        colRowColRowColRowColKey !== undefined
          ? `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}-${colRowColRowColRowKey}-${colRowColRowColRowColKey}`
          : colRowColRowKey !== undefined && colRowColRowColKey !== undefined
            ? `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}-${colRowColRowKey}-${colRowColRowColKey}`
            : colRowKey !== undefined && colRowColKey !== undefined
              ? `${rowKey}-${colKey}-${colRowKey}-${colRowColKey}`
              : `${rowKey}-${colKey}`;
      if (dragPlacement.length === 8) {
        templateRowsCopy[dragPlacement[0]]["columns"][dragPlacement[1]]["rows"][
          dragPlacement[2]
        ]["columns"][dragPlacement[3]]["rows"][dragPlacement[4]]["columns"][
          dragPlacement[5]
        ]["rows"][dragPlacement[6]]["columns"][dragPlacement[7]]["component"] =
          null;
      } else if (dragPlacement.length === 6) {
        templateRowsCopy[dragPlacement[0]]["columns"][dragPlacement[1]]["rows"][
          dragPlacement[2]
        ]["columns"][dragPlacement[3]]["rows"][dragPlacement[4]]["columns"][
          dragPlacement[5]
        ]["component"] = null;
      } else if (dragPlacement.length === 4) {
        templateRowsCopy[dragPlacement[0]]["columns"][dragPlacement[1]]["rows"][
          dragPlacement[2]
        ]["columns"][dragPlacement[3]]["component"] = null;
      } else {
        templateRowsCopy[dragPlacement[0]]["columns"][dragPlacement[1]][
          "component"
        ] = null;
      }
      if (
        colRowColRowColRowKey !== undefined &&
        colRowColRowColRowColKey !== undefined
      ) {
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["rows"][colRowColRowKey]["columns"][colRowColRowColKey][
          "rows"
        ][colRowColRowColRowKey]["columns"][colRowColRowColRowColKey][
          "component"
        ] = dragComponent;
      } else if (
        colRowColRowKey !== undefined &&
        colRowColRowColKey !== undefined
      ) {
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["rows"][colRowColRowKey]["columns"][colRowColRowColKey][
          "component"
        ] = dragComponent;
      } else if (colRowKey !== undefined && colRowColKey !== undefined) {
        templateRowsCopy[rowKey]["columns"][colKey]["rows"][colRowKey][
          "columns"
        ][colRowColKey]["component"] = dragComponent;
      } else {
        templateRowsCopy[rowKey]["columns"][colKey]["component"] =
          dragComponent;
      }
    }
    setTemplateRows(templateRowsCopy);
  },
  /**
   * Generate text element based on type
   *
   * @param {string} value
   * @param {string} type
   * @returns {JSX.Element} element
   */
  getTextElement: (value, type) => {
    let element = <></>;
    switch (type) {
      case "h1":
        element = <Title level={1}>{value}</Title>;
        break;
      case "h2":
        element = <Title level={2}>{value}</Title>;
        break;
      case "h3":
        element = <Title level={3}>{value}</Title>;
        break;
      case "h4":
        element = <Title level={4}>{value}</Title>;
        break;
      case "h5":
        element = <Title level={5}>{value}</Title>;
        break;
      default:
        element = <Text>{value}</Text>;
    }

    return element;
  },
  /**
   * Save user analysis template as draft
   *
   * @param {int|null} templateId
   * @param {array} templateRows
   */
  draftUserAnalysisTemplate: (templateId, templateRows) => {
    Api.draftUserAnalysisTemplate(
      (res) => {
        if (!res.success) {
          notification.warning({
            message: "Draft Template",
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: "Draft Template",
          description: err,
        });
      },
      {
        id: templateId,
        template_data: templateRows,
      },
    );
  },
  /**
   * Generate query string to be passed as url parameters
   * Exclude parameters with object value
   *
   * @param {object} params
   * @returns {string}
   */
  createQueryString: (params) => {
    return createQueryString(params);
  },
  /**
   * Set required API parameters with default value
   *
   * @param {object} payload
   * @param {object} bodyParams
   * @returns {object} payload
   */
  setRequiredApiParameters: (payload, bodyParams) => {
    const requiredParamKeys = Object.keys(bodyParams).filter((key) => {
      return (
        bodyParams[key]?.required === true &&
        bodyParams[key]?.default_value !== undefined
      );
    });
    requiredParamKeys.forEach((key) => {
      if (payload[key] === undefined) {
        payload[key] = bodyParams[key].default_value;
      }
    });

    return payload;
  },
  /**
   * Get default value by component blueprint
   *
   * @param {object} component
   * @param {string} dataKey
   * @returns {*} defaultValue
   */
  getDefaultValueByComponent: (component, dataKey) => {
    const bodyParams = component.props.params.body_params;
    const defaultValue =
      bodyParams[dataKey] && bodyParams[dataKey].default_value !== undefined
        ? bodyParams[dataKey].default_value
        : undefined;

    return defaultValue;
  },
  /**
   * Download blob data as file
   *
   * @param {Blob} blob
   * @param {string} filename
   */
  downloadBlobAsFile: (blob, filename) => {
    const link = document.createElement("a");
    link.href = window.URL.createObjectURL(blob);
    link.download = filename;
    link.click();
  },
  /**
   * Truncate very long string and add tooltip with original string value
   *
   * @param {string} str
   * @param {int} maxLength
   * @param {string} position
   * @param {*} showMore
   * @returns {string} formattedValue
   */
  truncateString: (str, maxLength = 50, position = "end", showMore) => {
    const truncateLength = Math.floor(maxLength / 2) - 3;
    let formattedValue = str;
    if (str?.length > maxLength) {
      if (position === "middle") {
        const first = str.substring(0, truncateLength);
        const last = str.substring(str.length - (truncateLength + 1));
        const truncatedStr = `${first}...${last}`;
        formattedValue = (
          <span key={str} title={str}>
            {truncatedStr}
          </span>
        );
      } else {
        const truncatedStr = `${str.substring(0, maxLength)}...`;
        formattedValue = (
          <span key={str} title={str}>
            {truncatedStr}
          </span>
        );
      }
      if (showMore) {
        formattedValue = [formattedValue, showMore];
      }
    }

    return formattedValue;
  },
  /**
   * Calculate string max length based on given available width
   *
   * @param {int} width
   * @returns {int} maxLength
   */
  getStringMaxLength: (width) => {
    const charWidth = 8;
    const maxLength = Math.floor(width / charWidth);
    return maxLength;
  },
  /**
   * Generate component node to be rendered
   *
   * @param {object} component
   * @param {object} cellActions
   * @param {string} pageKey
   * @param {function} testStatsInfoHandler Handler of the setting the test stats info
   * @param {object} filters
   * @param {function} setFilters
   * @param {boolean} isChartBoosted
   * @param {function} setIsChartBoosted
   * @param {object} prerenderData
   * @param {object} requiredData
   * @param {object} shouldRenderComponent
   * @param {boolean} showBoostWarning
   * @param {function} setHighchartsChart
   * @returns {JSX.Element}
   */
  createComponent: (
    component,
    cellActions,
    pageKey,
    testStatsInfoHandler,
    filters,
    setFilters,
    isChartBoosted,
    setIsChartBoosted,
    prerenderData = {},
    requiredData,
    shouldRenderComponent = {},
    showBoostWarning = true,
    setHighchartsChart,
  ) => {
    return (
      <TemplateComponent
        key={`template_component_${component.id}_${shouldRenderComponent.version ?? ""}`}
        component={component}
        cellActions={cellActions}
        pageKey={pageKey}
        testStatsInfoHandler={testStatsInfoHandler}
        filters={filters}
        setFilters={setFilters}
        isChartBoosted={isChartBoosted}
        setIsChartBoosted={setIsChartBoosted}
        prerenderData={prerenderData}
        requiredData={requiredData}
        showBoostWarning={showBoostWarning}
        setHighchartsChart={setHighchartsChart}
      />
    );
  },
  /**
   * Create tab item
   *
   * @param {string} tabKey
   * @param {string} tabLabel
   * @param {JSX.Element} tabContent
   * @param {boolean|JSX.Element|undefined} tabCloseIconOrClosable
   *        - boolean: controls closability on editable-card tabs
   *        - ReactNode: custom close icon (also marks as closable)
   * @returns {object} tabItem
   */
  createTabItem: (tabKey, tabLabel, tabContent, tabCloseIconOrClosable) => {
    const tabItem = {
      key: tabKey,
      label: tabLabel,
      children: tabContent,
    };
    if (typeof tabCloseIconOrClosable === "boolean") {
      // Treat boolean as the 'closable' flag rather than the icon itself.
      tabItem.closable = tabCloseIconOrClosable;
    } else if (tabCloseIconOrClosable !== undefined) {
      // Custom icon node: ensure the tab is closable and use provided icon.
      tabItem.closable = true;
      tabItem.closeIcon = tabCloseIconOrClosable;
    }
    return tabItem;
  },
  /**
   * Generate tab key based on provided data
   *
   * @param {string} tabKey
   * @param {object} data
   * @returns {string} newTabKey
   */
  generateTabKey: (tabKey, data) => {
    let newTabKey = tabKey;
    if (newTabKey) {
      Object.keys(data).forEach((key) => {
        if (data[key]) {
          const regex = new RegExp(`{${key}}`, "g");
          newTabKey = newTabKey.replace(regex, data[key]);
        }
      });
    }

    return newTabKey;
  },
  /**
   * Generate tab label based on provided data
   *
   * @param {string} tabLabel
   * @param {string} tabLabelTooltip
   * @param {object} data
   * @returns {string} newTabLabel
   */
  generateTabLabel: (tabLabel, tabLabelTooltip, data) => {
    let newTabLabel = Helper.truncateString(
      Helper.generateLabelByData(tabLabel, data),
      50,
    );
    const newTabLabelTooltip = Helper.generateLabelByData(
      tabLabelTooltip,
      data,
    );
    if (newTabLabelTooltip) {
      newTabLabel = (
        <Tooltip
          title={
            <div dangerouslySetInnerHTML={{ __html: newTabLabelTooltip }} />
          }
        >
          {newTabLabel}
        </Tooltip>
      );
    }

    return newTabLabel;
  },
  /**
   * Generate label based on provided data
   *
   * @param {string} template
   * @param {object} data
   * @returns {string} label
   */
  generateLabelByData: (template, data) => {
    let label = template;
    if (label) {
      Object.keys(data).forEach((key) => {
        let value = data[key];
        if (OptionsList[key]) {
          const option = Helper.filterArrayAndFind(
            OptionsList[key],
            "value",
            value,
          );
          if (option) {
            value = option.label;
          }
        }
        const regex = new RegExp(`{${key}}`, "g");
        label = label.replace(regex, value);
      });
    }

    return label;
  },
  /**
   * Remove column if render conditions is not met
   *
   * @param {array} renderConditions
   * @param {boolean} invertRenderConditions
   * @param {object} conditionValues
   * @param {array} target
   * @param {int} targetKey
   * @param {array} targetParent
   * @param {int} targetParentKey
   * @returns {boolean} shouldRender
   */
  filterByRenderConditions: (
    renderConditions,
    invertRenderConditions = false,
    conditionValues,
    target,
    targetKey,
    targetParent,
    targetParentKey,
  ) => {
    const shouldRender = renderConditions.some((condition) =>
      Object.entries(condition).every(([key, allowedValues]) => {
        const value = conditionValues?.[key];
        if (value === undefined) return true;

        const matches = Helper.containsValue(allowedValues, value);
        return invertRenderConditions ? !matches : matches;
      }),
    );

    if (!shouldRender && target) {
      delete target[targetKey];
      if (Object.keys(target).length === 0 && Array.isArray(targetParent)) {
        delete targetParent[targetParentKey];
      }
    }

    return shouldRender;
  },
  /**
   * Get analysis type
   *
   * @param {string} srcType
   * @param {string} srcValue
   * @param {string} mfgProcess
   * @param {string} groupBy
   * @param {function} successCbk
   * @param {function} warningCbk
   * @param {function} errorCbk
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getAnalysisType: (
    srcType,
    srcValue,
    mfgProcess,
    groupBy,
    successCbk,
    warningCbk,
    errorCbk,
    cacheData,
  ) => {
    const abortCtl = Api.getAnalysisType(
      (res) => {
        if (res.success) {
          successCbk(res.data);
        } else {
          warningCbk(res.message, 5);
        }
      },
      (err) => {
        errorCbk(err, 5);
      },
      {
        src_type: srcType,
        src_value: srcValue,
        mfg_process: mfgProcess,
        group_by: groupBy,
      },
      cacheData,
    );

    return abortCtl;
  },
  /**
   * Get page meta
   *
   * @param {string} srcType
   * @param {string} srcValue
   * @param {string} mfgProcess
   * @param {string} groupBy
   * @param {function} successCbk
   * @param {function} warningCbk
   * @param {function} errorCbk
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getPageMeta: (
    srcType,
    srcValue,
    mfgProcess,
    groupBy,
    successCbk,
    warningCbk,
    errorCbk,
    cacheData,
  ) => {
    const abortCtl = Api.getPageMeta(
      (res) => {
        if (res.success) {
          successCbk(res.data);
        } else {
          warningCbk(res.message, 5);
        }
      },
      (err) => {
        errorCbk(err, 5);
      },
      {
        src_type: srcType,
        src_value: srcValue,
        mfg_process: mfgProcess,
        group_by: groupBy,
      },
      cacheData,
    );

    return abortCtl;
  },
  /**
   * Get lot top failing test data
   *
   * @param {string} srcType
   * @param {string} srcValue
   * @param {string} mfgProcess
   * @param {function} successCbk
   * @param {function} warningCbk
   * @param {function} errorCbk
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getLotTopFailingTestData: (
    srcType,
    srcValue,
    mfgProcess,
    successCbk,
    warningCbk,
    errorCbk,
    cacheData,
  ) => {
    const abortCtl = Api.getLotTopFailingTestData(
      (res) => {
        if (res.success) {
          successCbk(res.data);
        } else {
          warningCbk(res.message, 5);
        }
      },
      (err) => {
        errorCbk(err, 5);
      },
      {
        src_type: srcType,
        src_value: srcValue,
        mfg_process: mfgProcess,
      },
      cacheData,
    );

    return abortCtl;
  },
  /**
   * Create cache key from request url and parameters
   *
   * @param {string} url
   * @param {object} params
   * @returns {string} cacheKey
   */
  generateCacheKey: (url, params) => {
    const cacheKey = `${url}_${params.method}_${JSON.stringify(params.body)}`;
    return cacheKey;
  },
  /**
   * Convert value to number format
   *
   * @param {number} number
   * @param {int} decimals
   * @returns {string}
   */
  numberFormat: (number, decimals) => {
    return numberFormat(number, decimals);
  },
  /**
   * Convert value to percent format
   *
   * @param {number} number
   * @param {int} decimals
   * @returns {string}
   */
  percentFormat: (number, decimals) => {
    return `${numberFormat(number, decimals)}%`;
  },
  /**
   * Calculate normal CDF
   * The calculation is not 100% correct as it is just an approximation
   *
   * @param {number} mean
   * @param {number} sigma
   * @param {number} to
   * @returns {number} normalCdf
   */
  calculateNormalCdf: (mean, sigma, to) => {
    var z = (to - mean) / Math.sqrt(2 * sigma * sigma);
    var t = 1 / (1 + 0.3275911 * Math.abs(z));
    var a1 = 0.254829592;
    var a2 = -0.284496736;
    var a3 = 1.421413741;
    var a4 = -1.453152027;
    var a5 = 1.061405429;
    var erf =
      1 - ((((a5 * t + a4) * t + a3) * t + a2) * t + a1) * t * Math.exp(-z * z);
    var sign = 1;
    if (z < 0) {
      sign = -1;
    }
    const normalCdf = (1 / 2) * (1 + sign * erf);

    return normalCdf;
  },
  /**
   * Calculate the standard quartile value corresponding to the given probability
   *
   * @param {float} probability
   * @param {float} mean
   * @param {float} stdev
   * @returns {float} quartile
   */
  getStandardQuartileFromProbability: (probability, mean, stdev) => {
    // Using a probability of exactly 0 or 1 in a normal distribution's inverse CDF results in undefined or extreme values
    // To handle this, adjust extreme probabilities slightly to get usable quantiles
    switch (probability) {
      case 0:
        probability = 0.0001;
        break;
      case 1:
        probability = 0.9999;
        break;
    }
    let quartile = jstat.normal.inv(probability, mean, stdev);
    // Check if it's effectively zero, allowing for a tiny tolerance
    if (Math.abs(quartile) < 1e-10) {
      quartile = 0;
    }
    return quartile;
  },
  /**
   * Formats number to user friendly readable format
   *
   * @param {string|number} dataValue
   * @param {int} decimalPlacesForExponentialValues
   * @param {int} decimalPlaces
   * @param {boolean} forceDecimal - flag to determine if to force decimal places even if the value is zero
   *
   * @returns {string} formattedValue - formatted value
   */
  descriptiveFormatting: (
    dataValue,
    decimalPlacesForExponentialValues,
    decimalPlaces,
    forceDecimal = false,
  ) => {
    if (dataValue !== undefined) {
      dataValue = parseFloat(dataValue);
    }
    decimalPlacesForExponentialValues =
      decimalPlacesForExponentialValues === undefined
        ? 4
        : decimalPlacesForExponentialValues;
    decimalPlaces = decimalPlaces === undefined ? "" : decimalPlaces;
    let formattedValue;

    if (
      dataValue === "" ||
      dataValue === null ||
      Math.abs(dataValue) >= NoValue ||
      Math.abs(dataValue) >= NoValue2 ||
      dataValue === "-" ||
      isNaN(dataValue)
    ) {
      formattedValue = "-";
    } else if (dataValue === 0 && forceDecimal === false) {
      formattedValue = "0";
    } else if (dataValue === 0 && forceDecimal === true) {
      formattedValue =
        decimalPlaces === ""
          ? numberFormat(dataValue, 4)
          : numberFormat(dataValue, decimalPlaces);
      // } else if (abs(dataValue) < 0.001) {
      //   formattedValue = sprintf("%10." + decimalPlacesForExponentialValues + "e", dataValue);
    } else if (abs(dataValue) < 0.01) {
      formattedValue =
        decimalPlaces === ""
          ? numberFormat(dataValue, 4)
          : numberFormat(dataValue, decimalPlaces);
    } else if (abs(dataValue) < 1) {
      formattedValue =
        decimalPlaces === ""
          ? numberFormat(dataValue, 4)
          : numberFormat(dataValue, decimalPlaces);
    } else if (dataValue > 1000000) {
      if (isInteger(dataValue)) {
        formattedValue = dataValue;
      } else {
        formattedValue =
          decimalPlaces === ""
            ? numberFormat(dataValue, 4)
            : numberFormat(dataValue, decimalPlaces);
      }
      // } else if (abs(dataValue) > 1000000) {
      //   formattedValue = sprintf("%10." + decimalPlacesForExponentialValues + "e", dataValue);
    } else if (isInteger(dataValue)) {
      if (forceDecimal === true) {
        formattedValue =
          decimalPlaces === ""
            ? numberFormat(dataValue, 4)
            : numberFormat(dataValue, decimalPlaces);
      } else {
        formattedValue = dataValue;
      }
    } else {
      formattedValue =
        decimalPlaces === ""
          ? numberFormat(dataValue, 4)
          : numberFormat(dataValue, decimalPlaces);
    }

    // if (floatval(dataValue) != 0 && floatval(formattedValue) == 0 && formattedValue.toString().indexOf('e') == -1) {
    //   formattedValue = sprintf("%10." + decimalPlacesForExponentialValues + "e", dataValue);
    // }

    return formattedValue;
  },

  /**
   * Update loading status by key
   *
   * @param {object} loadingStatus
   * @param {function} setLoadingStatus
   * @param {string} loadingStatusKey
   * @param {boolean} isLoading
   */
  updateLoadingStatus: (
    loadingStatus,
    setLoadingStatus,
    loadingStatusKey,
    isLoading,
  ) => {
    let loadingStatusCopy = cloneObject(loadingStatus);
    loadingStatusCopy[loadingStatusKey] = isLoading;
    setLoadingStatus(loadingStatusCopy);
  },
  /**
   * Load recipe page
   *
   * @param {string} category
   * @param {object} params
   * @param {QueryClient} queryClient
   * @param {boolean} shouldSaveHistory
   * @param {boolean} newTab
   */
  renderRecipePage: (
    category,
    params,
    queryClient,
    shouldSaveHistory,
    newTab,
  ) => {
    const pageKey = category.toLowerCase();
    const queryString = Helper.createQueryString(params);
    Helper.renderPage(
      `#${pageKey}?${queryString}`,
      pageKey,
      `#${pageKey}?${queryString}`,
      queryClient,
      shouldSaveHistory,
      newTab,
    );
  },
  /**
   * Store user settings in browser local storage for access
   *
   * @param {object} userSettings
   */
  storeUserSettings: (userSettings) => {
    window.localStorage.setItem("userSettings", JSON.stringify(userSettings));
  },
  /**
   * Set user settings
   *
   * @param {string} key
   * @param {*} value
   * @param {string} type
   * @param {object} component
   * @param {object} filters
   * @returns {AbortController}
   */
  setUserSettings: (key, value, type = "site", component, filters = {}) => {
    value = { value: value };
    key = generateUserSettingsKey(key, type, component, filters);
    return Api.setUserSettings(
      (res) => {
        if (res.success) {
          const userSettings = JSON.parse(
            window.localStorage.getItem("userSettings"),
          );
          if (userSettings[`${key}|${type}`] === undefined) {
            userSettings[`${key}|${type}`] = res.data;
          } else {
            userSettings[`${key}|${type}`].value = res.data.value;
          }
          window.localStorage.setItem(
            "userSettings",
            JSON.stringify(userSettings),
          );
        } else {
          notification.warning({
            message: "User Settings",
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: "User Settings",
          description: err,
        });
      },
      {
        key: key,
        value: value,
        type: type,
      },
    );
  },
  /**
   * Get user settings based on key and type
   *
   * @param {string} key
   * @param {string} type
   * @param {object} component
   * @param {object} filters
   * @param {string} valueKey
   * @returns {*} settings
   */
  getUserSettings: (
    key,
    type = "site",
    component,
    filters = {},
    valueKey = "value",
  ) => {
    key = generateUserSettingsKey(key, type, component, filters);
    const userSettings = JSON.parse(
      window.localStorage.getItem("userSettings"),
    );
    let settings;
    if (userSettings && userSettings[`${key}|${type}`]) {
      settings = JSON.parse(userSettings[`${key}|${type}`]?.value)?.[valueKey];
    }
    return settings;
  },
  /**
   * Reload OEE chart and grid components
   *
   * @param {object} reloadGridFilters
   * @param {function} setReloadGrid
   * @param {function} setReloadGrids
   * @param {function} setReloadGridFilters
   * @param {object} reloadChartFilters
   * @param {function} setReloadChart
   * @param {function} setReloadChartFilters
   * @param {string} OEETrendGridKey
   * @param {string} OEEBreakdownGridKey
   * @param {string} chartKey
   * @param {object} updatedOEEFilters
   */
  reloadOEEGridAndChartComponents: (
    reloadGridFilters,
    setReloadGrid,
    setReloadGrids,
    setReloadGridFilters,
    reloadChartFilters,
    setReloadChart,
    setReloadChartFilters,
    OEETrendGridKey,
    OEEBreakdownGridKey,
    chartKey,
    updatedOEEFilters,
  ) => {
    // reload OEE grids
    let reloadGridFiltersCopy = Helper.cloneObject(reloadGridFilters);
    reloadGridFiltersCopy[OEETrendGridKey] = updatedOEEFilters;
    reloadGridFiltersCopy[OEEBreakdownGridKey] = updatedOEEFilters;
    setReloadGridFilters(reloadGridFiltersCopy);
    setReloadGrids([OEETrendGridKey, OEEBreakdownGridKey]);
    setReloadGrid(Date.now());

    // reload OEE line chart
    let reloadChartFiltersCopy = Helper.cloneObject(reloadChartFilters);
    reloadChartFiltersCopy[chartKey] = updatedOEEFilters;
    setReloadChartFilters(reloadChartFiltersCopy);
    setReloadChart({
      [ComponentNameMapper.oee_historical_trend]: Date.now(),
    });
  },
  /**
   * Download datalog(s)
   *
   * @param {string} dsk
   * @param {string} type
   * @param {string} fileName
   * @param {object} loadingStatus
   * @param {function} setLoadingStatus
   * @param {string} loadingStatusKey
   */
  downloadDatalog: (
    dsk,
    type = "raw",
    fileName,
    loadingStatus,
    setLoadingStatus,
    loadingStatusKey,
  ) => {
    Api.downloadDatalog(
      (res) => {
        Helper.downloadBlobAsFile(res, fileName);
        if (typeof setLoadingStatus === "function") {
          Helper.updateLoadingStatus(
            loadingStatus,
            setLoadingStatus,
            loadingStatusKey,
            false,
          );
        }
      },
      (err) => {
        notification.error({
          message: "Download Datalog",
          description: err,
        });
        if (typeof setLoadingStatus === "function") {
          Helper.updateLoadingStatus(
            loadingStatus,
            setLoadingStatus,
            loadingStatusKey,
            false,
          );
        }
      },
      { dsk, type },
    );
  },
  /**
   * Get search filter values
   *
   * @param {object} formValues
   * @param {object} filters
   * @param {string} pageKey
   * @returns {object} filterValues
   */
  getFilterValues: (formValues, filters, pageKey) => {
    const rawFilters = Object.keys(formValues)
      .filter((key) => {
        return (
          (!Array.isArray(formValues[key]) && formValues[key] !== undefined) ||
          (Array.isArray(formValues[key]) && formValues[key].length > 0)
        );
      })
      .reduce((obj, key) => {
        obj[key] = formValues[key];
        return obj;
      }, {});

    const filterValues = Object.keys(rawFilters).reduce((obj, key) => {
      if (
        rawFilters[key] !== null &&
        typeof rawFilters[key] === "object" &&
        "min" in rawFilters[key] &&
        "max" in rawFilters[key]
      ) {
        obj[key] =
          rawFilters[key].min || rawFilters[key].max
            ? `${rawFilters[key].min ?? ""},${rawFilters[key].max ?? ""}`
            : undefined;
      } else if (Array.isArray(rawFilters[key])) {
        obj[key] = rawFilters[key]
          .map((value) => {
            let formatted = value;
            if (value && dayjs(value, "YYYY-MM-DD", true).isValid()) {
              formatted = dayjs(value).format("YYYY-MM-DD");
            } else if (typeof value === "object") {
              formatted = value.value;
            }
            return formatted;
          })
          .join(",");
      } else if (rawFilters[key] !== null) {
        obj[key] = rawFilters[key];
      }

      return obj;
    }, {});

    // set current mfg process value
    filterValues.manufacturing_process = filters[pageKey].manufacturing_process;
    // set current show user data type value
    filterValues.show = filters[pageKey].show;

    return filterValues;
  },
  /**
   * Check if recipeInput field is empty
   *
   * @param {object} recipeInput
   * @returns {boolean}
   */
  isEmptyRecipeInput: (recipeInput) => {
    return !recipeInput || Object.keys(recipeInput).length === 0
      ? true
      : Object.values(recipeInput).every(
          (item) => !item || (Array.isArray(item) && item.length === 0),
        );
  },
  /**
   * Format file size
   *
   * @param {number} size
   * @returns {string}
   */
  formatFileSize: (size) => {
    let formattedSize = "";
    if (!isNaN(size)) {
      if (size < 1024) formattedSize = `${size} B`;
      else if (size < 1024 * 1024)
        formattedSize = `${(size / 1024).toFixed(2)} KB`;
      else if (size < 1024 * 1024 * 1024)
        formattedSize = `${(size / (1024 * 1024)).toFixed(2)} MB`;
      else formattedSize = `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
    }
    return formattedSize;
  },
  /**
   * Get test data
   *
   * @param {object} apiParams
   * @param {function} successCbk
   * @param {function} warningCbk
   * @param {function} errorCbk
   * @param {object} cacheData
   * @param {object} prerenderData
   * @returns {AbortController} abortCtl
   */
  getTestData: (
    apiParams,
    successCbk,
    warningCbk,
    errorCbk,
    cacheData,
    prerenderData = {},
  ) => {
    const abortCtl = Api.getTestData(
      (res) => {
        if (res.success) {
          const testDataKey = Object.keys(res.data)[0];
          if (testDataKey && res.data[testDataKey].test_info) {
            successCbk({
              ...prerenderData,
              test_key: Helper.generateTestKey(res.data[testDataKey].test_info),
              test_number: res.data[testDataKey].test_info.test_number,
              test_type: res.data[testDataKey].test_info.test_type,
              test_name: res.data[testDataKey].test_info.test_name,
              test_unit: res.data[testDataKey].test_info.test_unit,
              stats_type: res.data[testDataKey].stats_type,
              site: res.data[testDataKey].site ?? "255",
              tnum: res.data[testDataKey].test_info.test_number,
              tnum_dsk: res.data[testDataKey].test_info.data_struc_key,
              program_key: res.data[testDataKey].test_info.program_key,
              program: res.data[testDataKey].test_info.program,
              has_per_pin_map_data:
                res.data[testDataKey].test_info.has_per_pin_map_data,
              actual_test_number:
                res.data[testDataKey].test_info.actual_test_number,
            });
          } else {
            warningCbk("Test info not found.", 5);
          }
        } else {
          warningCbk(res.message, 5);
        }
      },
      (err) => {
        errorCbk(err, 5);
      },
      apiParams,
      cacheData,
    );

    return abortCtl;
  },
  /**
   * Create test key from test info
   *
   * @param {object} testInfo
   * @returns {string}
   */
  generateTestKey: (testInfo) => {
    return `${testInfo.program_key}|${testInfo.test_number}|${testInfo.test_type}|${testInfo.data_struc_key}`;
  },
  /**
   * Generate row options menu based on input type
   *
   * @param {object} rowOptions
   * @param {string} pageKey
   * @param {object} filters
   * @param {function} setFilters
   * @param {object} prerenderData
   * @param {function} setPrerenderData
   * @param {object} rowOptionActions
   * @param {function} setShouldGenerateRow
   * @returns {array} options
   */
  createRowOptions: (
    rowOptions,
    pageKey,
    filters,
    setFilters,
    prerenderData,
    setPrerenderData,
    rowOptionActions,
    setShouldGenerateRow,
  ) => {
    let options = [];
    if (rowOptions) {
      options = rowOptions.map((rowOption) => {
        const option = RowOptionsFields[rowOption.key];
        const rowOptionItem = getRowOptionItem(
          option,
          rowOption,
          pageKey,
          filters,
          setFilters,
          prerenderData,
          setPrerenderData,
          rowOptionActions,
          setShouldGenerateRow,
        );
        return option.tooltip ? (
          <Tooltip key={`tooltip_${rowOption.key}`} title={option.tooltip}>
            <div>{rowOptionItem}</div>
          </Tooltip>
        ) : (
          rowOptionItem
        );
      });
    }

    return options;
  },
  /**
   * Check if variable has value other than null and undefined
   *
   * @param {*} variable
   * @returns {boolean}
   */
  hasValue: (variable) => {
    return variable !== null && variable !== undefined;
  },
  /**
   * Get datalogs by group
   *
   * @param {object} params
   * @param {function} successCbk
   * @param {object} messageApi
   * @returns {AbortController}
   */
  getGroupedDatalogs: (params, successCbk, messageApi) => {
    return Api.getGroupBreakdown(
      (res) => {
        if (res.success) {
          successCbk(res.data);
        } else {
          messageApi.warning("Failed in getting grouped datalogs data.");
        }
      },
      (err) => {
        messageApi.error(`Error in getting grouped datalogs data. ${err}`);
      },
      params,
    );
  },
  /**
   * Check if component is queued
   *
   * @param {string} queueGroup
   * @param {string} queueKey
   * @returns {boolean}
   */
  isComponentQueued: (queueGroup, queueKey) => {
    const currentQueueKeys = useQueueStore.getState().currentKeys;
    const getQueueByGroup = useQueueStore.getState().getQueueByGroup;
    const chartsQueue = getQueueByGroup(queueGroup);

    return (
      chartsQueue?.length > 0 &&
      chartsQueue.some((item) => item.key === queueKey) &&
      currentQueueKeys[queueGroup] !== queueKey
    );
  },
  /**
   * Check for queue process and iterate to next queued process if found
   *
   * @param {string} queueGroup
   * @param {string} queueKey
   */
  checkQueueProcess: (queueGroup, queueKey) => {
    const currentQueueKeys = useQueueStore.getState().currentKeys;
    const getQueueByGroup = useQueueStore.getState().getQueueByGroup;
    const triggerDone = useQueueStore.getState().triggerDone;
    if (
      getQueueByGroup(queueGroup)?.length > 0 &&
      currentQueueKeys[queueGroup] === queueKey
    ) {
      triggerDone(queueGroup);
    }
  },
  /**
   * Get queue group by component type
   *
   * @param {object} component
   * @returns {string} queueGroup
   */
  getQueueGroup: (component) => {
    let queueGroup = "";
    switch (component.type) {
      case "chart":
        queueGroup = "charts";
        break;
    }

    return queueGroup;
  },
  /**
   * Display of loading indicator
   *
   * @param {object} component
   * @returns {JSX.Element}
   */
  showLoading: (component) => {
    let loading = (
      <Spin
        className="m-auto!"
        size="large"
        indicator={<LoadingOutlined spin />}
      />
    );
    if (component && component.type === "chart") {
      loading = <ChartLoading />;
    }

    return loading;
  },
  /**
   * Remove one or more tabs and update active tab
   *
   * @param {array} targetKeys
   * @param {string} activeTabKey
   * @param {array} tabItems
   * @param {function} setTabItems
   * @param {function} setActiveTabKey
   */
  removeTabs: (
    targetKeys,
    activeTabKey,
    tabItems,
    setTabItems,
    setActiveTabKey,
  ) => {
    let newTabItems = tabItems;
    let newActiveKey = activeTabKey;
    let lastIndex = -1;
    targetKeys.forEach((targetKey) => {
      newTabItems.forEach((item, i) => {
        if (item.key === targetKey) {
          lastIndex = i - 1;
        }
      });
      newTabItems = newTabItems.filter((item) => item.key !== targetKey);
      if (newTabItems.length && newActiveKey === targetKey) {
        if (lastIndex >= 0) {
          newActiveKey = newTabItems[lastIndex].key;
        } else {
          newActiveKey = newTabItems[0].key;
        }
      }
    });
    setTabItems(newTabItems);
    setActiveTabKey(newActiveKey);
  },
  /**
   * Handle values that includes 'Overall'
   *
   * @param {array} values
   * @param {string|int} overall - 'all'|255
   */
  handleOverallValue: (values, overall) => {
    const val = (item) =>
      item && typeof item === "object" && "value" in item ? item.value : item;

    if (val(last(values)) === overall) {
      remove(values, (item) => val(item) !== overall);
    } else if (val(values[0]) === overall) {
      values.shift();
    }
  },
  /**
   * Get recipe data by given report key
   *
   * @param {int} reportKey
   * @param {object} messageApi
   * @returns {object} recipeData
   */
  getRecipeDataByReportKey: async (reportKey, messageApi) => {
    let recipeData = {};
    const response = await Api.fetchData(
      `/api/v1/npi/report/get/${reportKey}/1`,
      "get",
      {},
    );
    if (response.success) {
      recipeData = response.data ?? {};
    } else {
      messageApi.warning(response.message, 5);
    }

    return recipeData;
  },
  /**
   * Decompress base64 encoded data (base64_encode(gzcompress(json_encode($data)))) into json object
   *
   * @param {string} base64
   * @returns {object}
   */
  decompressWithCompressionStream: async (base64) => {
    const buffer = base64ToUint8Array(base64);
    const stream = new Response(buffer).body;
    const decompressedStream = stream.pipeThrough(
      new DecompressionStream("deflate"),
    );
    const decompressedArrayBuffer = await new Response(
      decompressedStream,
    ).arrayBuffer();
    const decompressedStr = new TextDecoder().decode(decompressedArrayBuffer);

    return JSON.parse(decompressedStr);
  },
  /**
   * True if `actual` is included in (or equal to) `expected`.
   *
   * @param {string|number|boolean|array} expected
   * @param {string|number|boolean|array} actual
   * @returns {boolean} isIncluded
   */
  containsValue: (expected, actual) => {
    const actualIsArr = Array.isArray(actual);
    const expectedIsArr = Array.isArray(expected);
    let isIncluded;

    if (expectedIsArr && actualIsArr) {
      isIncluded = actual.every((v) => expected.includes(v));
    } else if (expectedIsArr) {
      isIncluded = expected.includes(actual);
    } else if (actualIsArr) {
      isIncluded = actual.includes(expected);
    } else {
      isIncluded = expected === actual;
    }

    return isIncluded;
  },
  /**
   * Open tab of selected test
   *
   * @param {object} props
   */
  openTestTab: (props) => {
    const tabsKey = Helper.generateTabKey(
      "per_test_tabs_{analysis_set_key}",
      props.prerenderData ?? {},
    );
    const getPageTabs = useBoundStore.getState().getPageTabs;
    const tabs = getPageTabs(tabsKey);
    if (typeof tabs?.setActiveTabKey === "function") {
      const tabKey = Helper.generateTabKey(
        "{actual_test_number}: {test_name}",
        props.data,
      );
      tabs.setActiveTabKey(tabKey);
      if (tabs.tabsRef?.current) {
        tabs.tabsRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }
  },
};

export default Helper;
