import Helper from "./helper";

/**
 * Input validator
 */
const Validator = {
  /**
   * Check for key press validation
   *
   * @param {Event} e
   * @returns {boolean} isValid
   */
  onKeyPress: (e) => {
    let isValid = true;

    if (e.which !== 13 && e.which !== 8) {
      switch (e.target.id) {
        case "otp":
          isValid = Helper.validateMaxLength(e, e.target.value + e.key);
          break;
        case "add_new_user_username":
          // allow only alphanumeric characters
          isValid = Helper.validateKeyPressed(e, e.key, /^[a-zA-Z0-9]+$/);
          break;
      }
    }

    return isValid;
  },
  onPaste: (e) => {
    return Helper.validateMaxLength(e, e.clipboardData.getData("Text"));
  },
  /**
   * Validate maximum value with regards with minimum value
   *
   * @param {*} maxValue
   * @param {*} minValue
   * @returns {Promise}
   */
  maxValue: (maxValue, minValue) => {
    if (
      maxValue === undefined ||
      minValue === undefined ||
      maxValue === null ||
      minValue === null ||
      maxValue === "" ||
      minValue === "" ||
      parseFloat(minValue) <= parseFloat(maxValue)
    ) {
      return Promise.resolve();
    }
    return Promise.reject(
      new Error("Maximum value must not be less than the minimum value!"),
    );
  },
  /**
   * Validate to allow only positive valu
   *
   * @param {*} value
   * @returns {Promise}
   */
  positiveValue: (value) => {
    if (value === undefined || value === "" || parseFloat(value) >= 0) {
      return Promise.resolve();
    }
    return Promise.reject(new Error("Value must not be less than 0!"));
  },
};

export default Validator;
