/**
 * Example fetch utilizing data streaming from server
 * NOTE: This is only a PoC script. Code still needs to be
 * changed
 */
export const handleStreamData = (setCurData, tableParams) => {
  const url = `${process.env.NEXT_PUBLIC_APP_API_URL}/api/v1/home/<USER>
  const token = window.localStorage.getItem("accessToken");
  const abortCtl = new AbortController();

  /**
   * Read JSON
   *
   * @param {object} reader
   */
  const readJson = async (reader) => {
    let result = await reader.read();

    while (!result.done && !abortCtl.signal.aborted) {
      const chunk = result.value;
      const json = new TextDecoder("utf-8").decode(chunk);
      if (json.trim() !== "") {
        const parsedData = JSON.parse(json);
        if (setCurData) {
          setCurData(parsedData);
        }
      }
      result = await reader.read();
    }
  };

  /**
   * Get data from API
   */
  fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({
      stream: 1,
      ...tableParams,
    }),
    signal: abortCtl.signal,
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error("HTTP error " + response.status);
      }
      return response.body.getReader();
    })
    .then((reader) => readJson(reader))
    .catch((error) => {
      console.log("An error occurred while fetching the data");
      console.log(error);
    });

  return abortCtl;
};
