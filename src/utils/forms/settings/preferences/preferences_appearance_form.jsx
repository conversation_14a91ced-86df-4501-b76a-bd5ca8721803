"use client";
import { Tabs } from "antd";
import ThemeTab from "./appearance_form_tabs/theme_tab";
import ChartsTab from "./appearance_form_tabs/charts_tab";
import TablesTab from "./appearance_form_tabs/tables_tab";
import IconsTab from "./appearance_form_tabs/icons_tab";
import SignificantDigitSettingsTab from "./appearance_form_tabs/significant_digit_settings_tab";

/**
 * Form for managing appearance-related settings.
 *
 * @returns {JSX.Element}
 */
export default function AppearanceForm({ pageKey }) {
  const items = [
    {
      key: "theme",
      label: "Theme",
      children: <ThemeTab />,
    },
    {
      key: "charts",
      label: "Charts",
      children: <ChartsTab />,
    },
    {
      key: "tables",
      label: "Tables",
      children: <TablesTab />,
    },
    {
      key: "icons",
      label: "Icons",
      children: <IconsTab />,
    },
    {
      key: "significant_digit_settings",
      label: "Significant Digit Settings",
      children: <SignificantDigitSettingsTab pageKey={pageKey} />,
    },
  ];
  return <Tabs defaultActiveKey="theme" items={items} />;
}
