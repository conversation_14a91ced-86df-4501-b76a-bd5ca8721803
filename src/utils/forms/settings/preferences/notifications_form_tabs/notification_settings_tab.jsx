"use client";

import { useState, useEffect } from "react";
import { <PERSON>lex, <PERSON>, Col, Typo<PERSON>, Switch } from "antd";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import Helper from "../../../../helper";

const { Text } = Typography;

/**
 * Function to get the initial settings value from local storage
 *
 * @returns {Array}
 */
function getSettingsFromLocalStorage() {
  const localSystemGeneratedActivitySettings = Helper.getUserSettings(
    "system_generated_activity",
    "sitewide.preferences.notifications",
    {},
  );
  const localUserActivitySettings = Helper.getUserSettings(
    "user_activity",
    "sitewide.preferences.notifications",
    {},
  );
  const localDownloadSettings = Helper.getUserSettings(
    "download",
    "sitewide.preferences.notifications",
    {},
  );
  const localEngineeringUploadStatusSettings = Helper.getUserSettings(
    "engineering_upload_status",
    "sitewide.preferences.notifications",
    {},
  );
  const localProductionUploadStatusSettings = Helper.getUserSettings(
    "production_upload_status",
    "sitewide.preferences.notifications",
    {},
  );
  return [
    {
      key: "system_generated_activity",
      value: localSystemGeneratedActivitySettings,
    },
    { key: "user_activity", value: localUserActivitySettings },
    { key: "download", value: localDownloadSettings },
    {
      key: "engineering_upload_status",
      value: localEngineeringUploadStatusSettings,
    },
    {
      key: "production_upload_status",
      value: localProductionUploadStatusSettings,
    },
  ];
}

/**
 * Ensures the setting has a value, using default values if not already set.
 *
 * @param {string} key
 * @param {object} initialSetting
 * @returns {object}
 */
function initializeSetting(key, initialSetting) {
  return initialSetting.value
    ? initialSetting
    : {
        key: key,
        value: { in_app: true, email: true, push: true },
      };
}

export default function NotificationSettingsTab() {
  const [
    initialSystemGeneratedActivitySettings,
    initialUserActivitySettings,
    initialDownloadSettings,
    initialEngineeringUploadStatusSettings,
    initialProductionUploadStatusSettings,
  ] = getSettingsFromLocalStorage();

  const [systemGeneratedActivitySettings, setSystemGeneratedActivitySettings] =
    useState(
      initializeSetting(
        "system_generated_activity",
        initialSystemGeneratedActivitySettings,
      ),
    );
  const [userActivitySettings, setUserActivitySettings] = useState(
    initializeSetting("user_activity", initialUserActivitySettings),
  );
  const [downloadSettings, setDownloadSettings] = useState(
    initializeSetting("download", initialDownloadSettings),
  );
  const [engineeringUploadStatusSettings, setEngineeringUploadStatusSettings] =
    useState(
      initializeSetting(
        "engineering_upload_status",
        initialEngineeringUploadStatusSettings,
      ),
    );
  const [productionUploadStatusSettings, setProductionUploadStatusSettings] =
    useState(
      initializeSetting(
        "production_upload_status",
        initialProductionUploadStatusSettings,
      ),
    );

  const settings = [
    {
      key: "system_generated_activity",
      label: "System Generated Activity",
      value: systemGeneratedActivitySettings.value,
      setStateFn: setSystemGeneratedActivitySettings,
    },
    {
      key: "user_activity",
      label: "User Activity",
      value: userActivitySettings.value,
      setStateFn: setUserActivitySettings,
    },
    {
      key: "download",
      label: "Download",
      value: downloadSettings.value,
      setStateFn: setDownloadSettings,
    },
    {
      key: "engineering_upload_status",
      label: "Engineering Upload Status",
      value: engineeringUploadStatusSettings.value,
      setStateFn: setEngineeringUploadStatusSettings,
    },
    {
      key: "production_upload_status",
      label: "Production Upload Status",
      value: productionUploadStatusSettings.value,
      setStateFn: setProductionUploadStatusSettings,
    },
  ];

  useEffect(() => {
    Helper.setUserSettings(
      systemGeneratedActivitySettings.key,
      systemGeneratedActivitySettings.value,
      "sitewide.preferences.notifications",
      {},
    );
  }, [systemGeneratedActivitySettings]);

  useEffect(() => {
    Helper.setUserSettings(
      userActivitySettings.key,
      userActivitySettings.value,
      "sitewide.preferences.notifications",
      {},
    );
  }, [userActivitySettings]);

  useEffect(() => {
    Helper.setUserSettings(
      downloadSettings.key,
      downloadSettings.value,
      "sitewide.preferences.notifications",
      {},
    );
  }, [downloadSettings]);

  useEffect(() => {
    Helper.setUserSettings(
      engineeringUploadStatusSettings.key,
      engineeringUploadStatusSettings.value,
      "sitewide.preferences.notifications",
      {},
    );
  }, [engineeringUploadStatusSettings]);

  useEffect(() => {
    Helper.setUserSettings(
      productionUploadStatusSettings.key,
      productionUploadStatusSettings.value,
      "sitewide.preferences.notifications",
      {},
    );
  }, [productionUploadStatusSettings]);

  /**
   * Switch component onChange callback
   *
   * @param {string} type
   * @param {boolean} value
   * @param {Function} setStateFn
   * @returns {void}
   */
  function changeSettingByType(type, value, setStateFn) {
    setStateFn((prev) => {
      return {
        ...prev,
        value: {
          ...prev.value,
          [type]: value,
        },
      };
    });
  }

  /**
   * Renders a set of switches for a given setting
   *
   * @param {object} settingValues
   * @param {Function} setStateFn
   * @returns
   */
  function renderSettingSwitches(settingValues, setStateFn) {
    return ["in_app", "email", "push"].map((type) => {
      return (
        <Col key={type} span={5}>
          <Switch
            defaultValue={settingValues[type]}
            checkedChildren={<CheckOutlined />}
            unCheckedChildren={<CloseOutlined />}
            onChange={(value) => changeSettingByType(type, value, setStateFn)}
          />
        </Col>
      );
    });
  }

  return (
    <Flex vertical gap={25}>
      <Row>
        <Col offset={7} span={5}>
          <Text>In-app (bell)</Text>
        </Col>
        <Col span={5}>
          <Text>Email</Text>
        </Col>
        <Col span={5}>
          <Text>Push</Text>
        </Col>
      </Row>

      {settings.map(({ key, label, value, setStateFn }) => {
        return (
          <Row key={key}>
            <Col span={7}>
              <Text>{label}</Text>
            </Col>
            {renderSettingSwitches(value, setStateFn)}
          </Row>
        );
      })}
    </Flex>
  );
}
