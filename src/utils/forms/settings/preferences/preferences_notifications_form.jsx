"use client";

import { Tabs } from "antd";
import NotificationSettingsTab from "./notifications_form_tabs/notification_settings_tab";
import AlertDashboardTab from "./notifications_form_tabs/alert_dashboard_tab";
import NotificationCenterTab from "./notifications_form_tabs/notification_center_tab";

export default function NotificationsForm() {
  const items = [
    {
      key: "notification_settings",
      label: "Notification Settings",
      children: <NotificationSettingsTab />,
    },
    {
      key: "alert_dashboard",
      label: "Alert Dashboard",
      children: <AlertDashboardTab />,
    },
    {
      key: "notification_center",
      label: "Notification Center",
      children: <NotificationCenterTab />,
    },
  ];
  return <Tabs defaultActiveKey="notification_settings" items={items} />;
}
