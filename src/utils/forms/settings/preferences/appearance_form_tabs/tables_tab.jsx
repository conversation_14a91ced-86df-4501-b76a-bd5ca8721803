"use client";

import { useState, useEffect } from "react";
import { Row, Col, Typography, Switch } from "antd";
import Helper from "../../../../helper";
import { UserSettingsKeys } from "../../../../user_settings_keys";

const { Text } = Typography;

/**
 * Function to get the initial settings value from local storage
 *
 * @returns {Array}
 */
function getSettingsFromLocalStorage() {
  const localLastState = Helper.getUserSettings(
    UserSettingsKeys.grid_last_state,
    "sitewide.preferences.appearance",
    {},
  );
  return [
    {
      key: UserSettingsKeys.grid_last_state,
      value: localLastState,
    },
  ];
}

/**
 * Ensures the setting has a value, using default values if not already set.
 *
 * @param {string} key
 * @param {object} initialSetting
 * @returns {object}
 */
function initializeSetting(key, initialSetting) {
  return initialSetting.value
    ? initialSetting
    : { key: key, value: { grid_last_state: true } };
}

/**
 * Tables tab in user settings preferences appearance form
 *
 * @returns {JSX.Element}
 */
export default function TablesTab() {
  const [initialLastState] = getSettingsFromLocalStorage();

  const [lastState, setLastState] = useState(
    initializeSetting(UserSettingsKeys.grid_last_state, initialLastState),
  );

  useEffect(() => {
    Helper.setUserSettings(
      lastState.key,
      lastState.value,
      "sitewide.preferences.appearance",
      {},
    );
  }, [lastState]);

  /**
   * Switch component onChange callback
   *
   * @param {boolean} value
   * @param {Function} setStateFn
   * @returns {void}
   */
  function onChangeSetting(value, setStateFn) {
    setStateFn((prev) => ({ ...prev, value: { value } }));
  }

  return (
    <Row>
      <Col span={8}>
        <Text>Table Last State (continue where you left off)</Text>
      </Col>
      <Col span={5}>
        <Switch
          defaultValue={lastState.value.value}
          checkedChildren={"ON"}
          unCheckedChildren={"OFF"}
          onChange={(value) => onChangeSetting(value, setLastState)}
        />
      </Col>
    </Row>
  );
}
