import { useState, useRef } from "react";
import { <PERSON>pp, <PERSON><PERSON> } from "antd";
import YHGrid from "../../../../grid/yh_grid";
import { UserSettingsKeys } from "../../../../user_settings_keys";
import { ComponentNameMapper } from "../../../../grid/component_name_mapper";
import { useBoundStore } from "../../../../../store/store";
import { useEffectApiFetch } from "../../../../../hooks";
import Helper from "../../../../helper";
import Api from "../../../../api";

/**
 * Significant digit settings tab in user settings preferences appearance form
 *
 * @returns {JSX.Element}
 */
export default function SignificantDigitSettingsTab({ pageKey }) {
  const { message } = App.useApp();
  const filters = useBoundStore((state) => state.filters);
  const setFilters = useBoundStore((state) => state.setFilters);

  const significantDigitGridRef = useRef();
  const [
    significantDigitSettingsGridComponent,
    setSignificantDigitSettingsGridComponent,
  ] = useState();

  useEffectApiFetch(
    () => {
      return getSignificantDigitSettingsGridComponent();
    },
    () => {
      setSignificantDigitSettingsGridComponent();
    },
  );

  /**
   * Get and set the significant digit settings grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getSignificantDigitSettingsGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setSignificantDigitSettingsGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: ComponentNameMapper.significant_digit_settings_table,
      },
    );
    return abortCtl;
  };

  /**
   * Check if cell data is valid
   *
   * @param {string} rowName
   * @param {string} columnName
   * @param {number} cellData
   * @returns {boolean} isValid
   */
  const validateCellData = (rowName, columnName, cellData) => {
    let isValid = true;
    if (cellData < 0) {
      message.error(
        `Negative value in Row "${rowName}" and Column "${columnName}" is not allowed.`,
      );
      isValid = false;
    }
    return isValid;
  };

  /**
   * Save the current grid data
   *
   * @returns {void}
   */
  const saveSignificantDigitSettingsData = () => {
    let isValidValue = true;
    if (significantDigitGridRef.current !== undefined) {
      let significantDigitSettingsData = [];
      significantDigitGridRef.current.api.forEachNode((rowNode) => {
        const data = rowNode.data;
        if (isValidValue) {
          const { label, table, chart, chart_csv_excel, others } = data;
          isValidValue =
            validateCellData(label, "Table", table) &&
            validateCellData(label, "Chart", chart) &&
            validateCellData(label, "Chart CSV/Excel", chart_csv_excel) &&
            validateCellData(label, "Others", others);
          if (!isValidValue) {
            return;
          }
          significantDigitSettingsData.push(data);
        }
      });

      if (!isValidValue) {
        return;
      }
      Helper.setUserSettings(
        UserSettingsKeys.significant_digit_settings_grid,
        significantDigitSettingsData,
        "sitewide.preferences.appearance",
        {},
      );
      message.success("Successfully saved!");
    }
  };

  return (
    <>
      {significantDigitSettingsGridComponent && (
        <>
          <Button onClick={saveSignificantDigitSettingsData}>Save</Button>
          <YHGrid
            gridRef={significantDigitGridRef}
            gridId={UserSettingsKeys.significant_digit_settings_grid}
            gridOptions={{ rowModelType: "clientSide" }}
            component={significantDigitSettingsGridComponent}
            filters={filters}
            setFilters={setFilters}
            rowGroups={[]}
            pageKey={pageKey}
            wrapperClassName="flex grow flex-col h-full"
          />
        </>
      )}
    </>
  );
}
