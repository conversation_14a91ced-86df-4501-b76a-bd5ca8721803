"use client";

import { useState, useEffect } from "react";
import { Flex, Row, Col, Typography, Switch } from "antd";
import Helper from "../../../../helper";

const { Text } = Typography;

/**
 * Function to get the initial settings value from local storage
 *
 * @returns {Array}
 */
function getSettingsFromLocalStorage() {
  const localLastState = Helper.getUserSettings(
    "charts_last_state",
    "sitewide.preferences.appearance",
    {},
  );
  return [
    {
      key: "charts_last_state",
      value: localLastState,
    },
  ];
}

/**
 * Ensures the setting has a value, using default values if not already set.
 *
 * @param {string} key
 * @param {object} initialSetting
 * @returns {object}
 */
function initializeSetting(key, initialSetting) {
  return initialSetting.value
    ? initialSetting
    : { key: key, value: { charts_last_state: true } };
}

/**
 * Charts tab in user settings preferences appearance form
 *
 * @returns {JSX.Element}
 */
export default function ChartsTab() {
  const [initialLastState] = getSettingsFromLocalStorage();

  const [lastState, setLastState] = useState(
    initializeSetting("charts_last_state", initialLastState),
  );

  useEffect(() => {
    Helper.setUserSettings(
      lastState.key,
      lastState.value,
      "sitewide.preferences.appearance",
      {},
    );
  }, [lastState]);

  /**
   * Switch component onChange callback
   *
   * @param {boolean} value
   * @param {Function} setStateFn
   * @returns {void}
   */
  function onChangeSetting(value, setStateFn) {
    setStateFn((prev) => ({ ...prev, value: { value } }));
  }

  return (
    <Flex vertical gap={24}>
      <Row>
        <Col span={8}>
          <Text>Chart Last State (continue where you left off)</Text>
        </Col>
        <Col span={5}>
          <Switch
            defaultValue={lastState.value.value}
            checkedChildren={"ON"}
            unCheckedChildren={"OFF"}
            onChange={(value) => onChangeSetting(value, setLastState)}
          />
        </Col>
      </Row>
    </Flex>
  );
}
