"use client";

import {
  Col,
  Collapse,
  Flex,
  List,
  message,
  Radio,
  Row,
  Select,
  Space,
  Switch,
  Tooltip,
  Typography,
} from "antd";
import { InfoCircleTwoTone } from "@ant-design/icons";
import { useState, useEffect } from "react";
import { useEffectApiFetch } from "../../../../hooks";
import Api from "../../../api";
import Helper from "../../../helper";
import { UserSettingsKeys } from "../../../user_settings_keys";

const { Title, Text } = Typography;

const specificPages = [];
const loginStartPageOptions = [
  {
    value: "home_page",
    label: "Home Page",
  },
  {
    value: "continue_where_you_left_off",
    label: "Continue where you left off",
  },
  {
    value: "specific_page",
    label: "Specific Page",
  },
];
const lastStateSettingItems = [
  {
    key: UserSettingsKeys.current_column_settings_last_state,
    title: "Current Column Settings",
    description: "Applies to: added columns, width, order, pinned columns",
  },
  {
    key: UserSettingsKeys.added_sidebar_filters_last_state,
    title: "Added Sidebar Filters",
    description:
      "Save the filter fields you've added to the sidebar for later use",
  },
  {
    key: UserSettingsKeys.top_main_filters_last_state,
    title: "Top Main Filters",
    description:
      "Applies to: MFG process, Lot ID or Datalog, Production, My Eng, Other Eng",
  },
  {
    key: UserSettingsKeys.sidebar_last_state,
    title: "Sidebar",
    description:
      "Retain the collapsed or expanded state of both left and right sidebars",
  },
  {
    key: UserSettingsKeys.pinned_rows_last_state,
    title: "Pinned Rows",
    description: "Retain the rows you pinned for future use",
  },
];

/**
 * Get saved settings value from local storage.
 * If there is no saved settings are found or they
 * are invalid, it generates a set of default settings
 * and stores these settings to local storage.
 *
 * @returns {object}
 */
const getSettingsValues = () => {
  const savedSettings = Helper.getUserSettings(
    UserSettingsKeys.general_settings,
    "sitewide.preferences.general",
    {},
  );
  if (
    savedSettings !== undefined &&
    savedSettings !== null &&
    typeof savedSettings === "object" &&
    Object.keys(savedSettings).length > 0
  ) {
    return savedSettings;
  }
  const defaultSettings = {
    [UserSettingsKeys.language]: "en",
    [UserSettingsKeys.timezone]:
      Intl.DateTimeFormat().resolvedOptions().timeZone,
    [UserSettingsKeys.login_start_page]: "home_page",
    [UserSettingsKeys.open_pages_in_new_tab]: false,
    [UserSettingsKeys.remember_my_actions]: true,
    [UserSettingsKeys.current_column_settings_last_state]: true,
    [UserSettingsKeys.added_sidebar_filters_last_state]: true,
    [UserSettingsKeys.top_main_filters_last_state]: true,
    [UserSettingsKeys.sidebar_last_state]: true,
    [UserSettingsKeys.pinned_rows_last_state]: true,
  };
  Helper.setUserSettings(
    UserSettingsKeys.general_settings,
    defaultSettings,
    "sitewide.preferences.general",
    {},
  );
  return defaultSettings;
};

export default function GeneralForm() {
  const [messageApi, contextHolder] = message.useMessage();
  const settingsValues = getSettingsValues();
  const [language, setLanguage] = useState(
    settingsValues[UserSettingsKeys.language],
  );
  const [timezone, setTimezone] = useState(
    settingsValues[UserSettingsKeys.timezone],
  );
  const [loginStartPage, setLoginStartPage] = useState(
    settingsValues[UserSettingsKeys.login_start_page],
  );
  const [openPagesInNewTab, setOpenPagesInNewTab] = useState(
    settingsValues[UserSettingsKeys.open_pages_in_new_tab],
  );
  const [rememberMyActions, setRememberMyActions] = useState(
    settingsValues[UserSettingsKeys.remember_my_actions],
  );
  const [currentColumnSettingsLastState, setCurrentColumnSettingsLastState] =
    useState(
      settingsValues[UserSettingsKeys.current_column_settings_last_state],
    );
  const [addedSidebarFiltersLastState, setAddedSidebarFiltersLastState] =
    useState(settingsValues[UserSettingsKeys.added_sidebar_filters_last_state]);
  const [topMainFiltersLastState, setTopMainFiltersLastState] = useState(
    settingsValues[UserSettingsKeys.top_main_filters_last_state],
  );
  const [sidebarLastState, setSidebarLastState] = useState(
    settingsValues[UserSettingsKeys.sidebar_last_state],
  );
  const [pinnedRowsLastState, setPinnedRowsLastState] = useState(
    settingsValues[UserSettingsKeys.pinned_rows_last_state],
  );

  const [languagesChoices, setLanguagesChoices] = useState([]);
  const [timezonesChoices, setTimezonesChoices] = useState([]);
  const [languagesChoicesRendered, setLanguagesChoicesRendered] = useState([]);
  const [timezonesChoicesRendered, setTimezonesChoicesRendered] = useState([]);

  useEffectApiFetch(
    () => {
      return Api.getLanguages(
        (res) => {
          if (res.success) {
            setLanguagesChoices(res.data);
          } else {
            messageApi.open({ type: "warning", content: res.message });
          }
        },
        (err) => {
          messageApi.open({ type: "error", content: err });
        },
      );
    },
    () => {
      setLanguagesChoices([]);
    },
    [],
  );

  useEffectApiFetch(
    () => {
      return Api.getTimezones(
        (res) => {
          if (res.success) {
            setTimezonesChoices(res.data);
          } else {
            messageApi.open({ type: "warning", content: res.message });
          }
        },
        (err) => {
          messageApi.open({ type: "error", content: err });
        },
      );
    },
    () => {
      setTimezonesChoices([]);
    },
    [],
  );

  useEffect(() => {
    setLanguagesChoicesRendered(languagesChoices);
  }, [languagesChoices]);

  useEffect(() => {
    setTimezonesChoicesRendered(timezonesChoices);
  }, [timezonesChoices]);

  useEffect(() => {
    const data = {
      [UserSettingsKeys.language]: language,
      [UserSettingsKeys.timezone]: timezone,
      [UserSettingsKeys.login_start_page]: loginStartPage,
      [UserSettingsKeys.open_pages_in_new_tab]: openPagesInNewTab,
      [UserSettingsKeys.remember_my_actions]: rememberMyActions,
      [UserSettingsKeys.current_column_settings_last_state]:
        currentColumnSettingsLastState,
      [UserSettingsKeys.added_sidebar_filters_last_state]:
        addedSidebarFiltersLastState,
      [UserSettingsKeys.top_main_filters_last_state]: topMainFiltersLastState,
      [UserSettingsKeys.sidebar_last_state]: sidebarLastState,
      [UserSettingsKeys.pinned_rows_last_state]: pinnedRowsLastState,
    };
    Helper.setUserSettings(
      UserSettingsKeys.general_settings,
      data,
      "sitewide.preferences.general",
      {},
    );
  }, [
    language,
    timezone,
    loginStartPage,
    openPagesInNewTab,
    rememberMyActions,
    currentColumnSettingsLastState,
    addedSidebarFiltersLastState,
    topMainFiltersLastState,
    sidebarLastState,
    pinnedRowsLastState,
  ]);

  /**
   * The Select component onSearch callback
   *
   * @param {string} settings
   * @param {string} value
   */
  const onSearchSettings = (settings, value) => {
    switch (settings) {
      case UserSettingsKeys.language:
        setLanguagesChoicesRendered(
          languagesChoices.filter((set) =>
            set.label.toLowerCase().includes(value.toLowerCase()),
          ),
        );
        break;
      case UserSettingsKeys.timezone:
        setTimezonesChoicesRendered(
          timezonesChoices.filter((set) =>
            set.label.toLowerCase().includes(value.toLowerCase()),
          ),
        );
        break;
    }
  };

  /**
   * The onChange callback for updating specific settings
   * based on the provided key.
   *
   * @param {string} key
   * @param {string} value
   */
  const onChangeSettings = (key, value) => {
    switch (key) {
      case UserSettingsKeys.language:
        setLanguage(value);
        setLanguagesChoicesRendered(languagesChoices);
        break;
      case UserSettingsKeys.timezone:
        setTimezone(value);
        setTimezonesChoicesRendered(timezonesChoices);
        break;
      case UserSettingsKeys.login_start_page:
        setLoginStartPage(value);
        break;
      case UserSettingsKeys.open_pages_in_new_tab:
        setOpenPagesInNewTab(value);
        break;
      case UserSettingsKeys.remember_my_actions:
        setRememberMyActions(value);
        break;
      case UserSettingsKeys.current_column_settings_last_state:
        setCurrentColumnSettingsLastState(value);
        break;
      case UserSettingsKeys.added_sidebar_filters_last_state:
        setAddedSidebarFiltersLastState(value);
        break;
      case UserSettingsKeys.top_main_filters_last_state:
        setTopMainFiltersLastState(value);
        break;
      case UserSettingsKeys.sidebar_last_state:
        setSidebarLastState(value);
        break;
      case UserSettingsKeys.pinned_rows_last_state:
        setPinnedRowsLastState(value);
        break;
    }
  };

  return (
    <>
      {contextHolder}
      <Title level={4}>General Settings</Title>
      <Flex vertical gap={12}>
        <Row>
          <Col span={6}>
            <Text>Language</Text>
          </Col>
          <Col span={15}>
            <Select
              className="w-52"
              defaultValue={language}
              options={languagesChoicesRendered}
              showSearch
              optionFilterProp="label"
              onSearch={(value) => {
                onSearchSettings(UserSettingsKeys.language, value);
              }}
              onChange={(value) => {
                onChangeSettings(UserSettingsKeys.language, value);
              }}
            ></Select>
          </Col>
        </Row>

        <Row>
          <Col span={6}>
            <Text>Timezone</Text>
          </Col>
          <Col span={15}>
            <Select
              className="w-52"
              defaultValue={timezone}
              options={timezonesChoicesRendered}
              showSearch
              optionFilterProp="label"
              onSearch={(value) => {
                onSearchSettings(UserSettingsKeys.timezone, value);
              }}
              onChange={(value) => {
                onChangeSettings(UserSettingsKeys.timezone, value);
              }}
            ></Select>
          </Col>
        </Row>

        <Row>
          <Col span={6}>
            <Text className="mr-4">Choose Login Start Page</Text>
            <Tooltip
              placement="top"
              title="Choose where to go after logging in: home, last visited, or a specific page."
            >
              <InfoCircleTwoTone />
            </Tooltip>
          </Col>
          <Col span={15}>
            <Row>
              <Col span={16}>
                <Radio.Group
                  defaultValue={loginStartPage}
                  options={loginStartPageOptions}
                  onChange={({ target }) => {
                    onChangeSettings(
                      UserSettingsKeys.login_start_page,
                      target.value,
                    );
                  }}
                ></Radio.Group>
              </Col>
              <Col span={6}>
                <Select
                  className="w-44"
                  placeholder="Select"
                  options={specificPages}
                  disabled={loginStartPage !== "specific_page"}
                ></Select>
              </Col>
            </Row>
          </Col>
        </Row>

        <Row>
          <Col span={6}>
            <Text className="mr-4">Always Open Pages in New Tab</Text>
            <Tooltip
              placement="top"
              title="Set pages and subpages to open in a new tab by default."
            >
              <InfoCircleTwoTone />
            </Tooltip>
          </Col>
          <Col span={15}>
            <Switch
              defaultValue={openPagesInNewTab}
              checkedChildren={"Yes"}
              unCheckedChildren={"No"}
              onChange={(value) => {
                onChangeSettings(UserSettingsKeys.open_pages_in_new_tab, value);
              }}
            />
          </Col>
        </Row>

        <Row>
          <Col span={6}>
            <Text className="mr-4">Always Remember My Actions</Text>
            <Tooltip
              placement="top"
              title="Enable to save your preferences for actions and apply them automatically. Disable to reset and be prompted for choices again."
            >
              <InfoCircleTwoTone />
            </Tooltip>
          </Col>
          <Col span={15}>
            <Switch
              defaultValue={rememberMyActions}
              checkedChildren={"Yes"}
              unCheckedChildren={"No"}
              onChange={(value) => {
                onChangeSettings(UserSettingsKeys.remember_my_actions, value);
              }}
            />
          </Col>
        </Row>

        <Row>
          <Collapse
            className="w-full"
            accordion={true}
            items={[
              {
                key: "1",
                label: "Remember Last State Settings",
                children: (
                  <Space className="ml-4" direction="vertical">
                    <Text>
                      Manage settings for features that remember your last used
                      preferences or states. Toggle options on or off as needed.
                    </Text>
                    <div className="h-36 overflow-y-scroll">
                      <List
                        bordered={true}
                        size="small"
                        dataSource={lastStateSettingItems}
                        renderItem={(item) => {
                          return (
                            <List.Item key={item.key}>
                              <List.Item.Meta
                                title={item.title}
                                description={item.description}
                              />
                              <Switch
                                checkedChildren={"Yes"}
                                unCheckedChildren={"No"}
                                defaultValue={settingsValues[item.key]}
                                onChange={(value) => {
                                  onChangeSettings(item.key, value);
                                }}
                              />
                            </List.Item>
                          );
                        }}
                      />
                    </div>
                  </Space>
                ),
              },
            ]}
          />
        </Row>
      </Flex>
    </>
  );
}
