import { App, Form, Input } from "antd";
import Api from "../api";

/**
 * Form to add new team
 *
 * @param {Form} form
 * @param {function} setIsAddNewTeamModalOpen
 * @param {function} setIsAddNewTeamFormValuesValid
 * @param {function} reloadTeamsData
 * @returns {JSX.Element}
 */
export default function AddNewTeamForm({
  form,
  setIsAddNewTeamModalOpen,
  setIsAddNewTeamFormValuesValid,
  reloadTeamsData,
}) {
  const { message } = App.useApp();
  const onFormFinishFn = (values) => {
    Api.getData(
      "/api/v1/internal/user_role_permission_team/team",
      "post",
      () => {
        form.resetFields();
        setIsAddNewTeamModalOpen(false);
        setIsAddNewTeamFormValuesValid(false);
        reloadTeamsData();
        message.success("Successfully added new team.", 5);
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: values.name,
        display_name: values.name,
        description: values.description,
      },
    );
  };

  return (
    <Form
      clearOnDestroy={true}
      layout="vertical"
      form={form}
      onFinish={onFormFinishFn}
      onValuesChange={() => {
        form
          .validateFields()
          .then(() => setIsAddNewTeamFormValuesValid(true))
          .catch((err) =>
            setIsAddNewTeamFormValuesValid(err.errorFields.length === 0),
          );
      }}
      initialValues={{
        name: "",
        description: "",
      }}
    >
      <Form.Item
        name="name"
        label="Team Name"
        rules={[{ required: true, message: "Please input Team name" }]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        name="description"
        label="Team Description"
        rules={[{ required: true, message: "Please input Team description" }]}
      >
        <Input />
      </Form.Item>
    </Form>
  );
}
