// import { SaveOutlined, EditOutlined } from "@ant-design/icons";
import { Form, Input, notification, Typography } from "antd";
// import { useState } from "react";
import { useBoundStore } from "../../store/store";
// import PhotoUpload from "./fields/photo_upload";

const { Title } = Typography;
const { TextArea } = Input;

/**
 * Form to show/update user profile
 *
 * @returns {JSX.Element}
 */
const ProfileForm = () => {
  // const [loading] = useState(false);
  // const [allowSubmit, setAllowSubmit] = useState(false);
  const userData = useBoundStore((state) => state.userData);
  const [form] = Form.useForm();

  /**
   * Triggered when a field value changes
   * Check if form is valid and allow form submit if valid
   */
  const onFieldsChange = () => {
    // const isFormValid =
    //   !form.getFieldsError().some((item) => item.errors.length > 0) &&
    //   form.getFieldValue("name") &&
    //   form.getFieldValue("username");
    // setAllowSubmit(isFormValid);
  };

  /**
   * Change user password
   */
  const updateProfile = () => {
    notification.warning({
      message: "Update Profile",
      description: "Coming Soon!",
    });
  };

  return (
    <Form
      form={form}
      name="profile_form"
      layout="vertical"
      wrapperCol={{
        span: 6,
      }}
      requiredMark={true}
      onFinish={updateProfile}
      onFieldsChange={onFieldsChange}
      autoComplete="off"
      disabled
    >
      <Title level={4}>My Profile</Title>
      {/* <Form.Item>
        <PhotoUpload />
      </Form.Item> */}
      <Form.Item
        name="name"
        label="Name"
        className="mt-6"
        initialValue={userData.full_name}
        rules={[
          {
            required: true,
            message: "Please input your name!",
          },
        ]}
      >
        <Input placeholder="Enter name" />
      </Form.Item>
      <Form.Item
        name="username"
        label="Username"
        initialValue={userData.name}
        rules={[
          {
            required: true,
            message: "Please input your username!",
          },
        ]}
      >
        <Input placeholder="Enter username" />
      </Form.Item>
      <Form.Item
        name="email"
        label="Email"
        initialValue={userData.email}
        rules={[
          {
            required: true,
            message: "Please input your email!",
          },
        ]}
      >
        <Input placeholder="Enter username" />
      </Form.Item>
      <Form.Item name="about_me" label="About Me">
        <TextArea
          rows={4}
          maxLength={100}
          showCount
          autoSize={{
            minRows: 4,
            maxRows: 4,
          }}
        />
      </Form.Item>
      {/* <Form.Item className="mt-14 text-right">
        <Space>
          <Button icon={<EditOutlined />} loading={loading}>
            Edit
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SaveOutlined />}
            disabled={!allowSubmit}
            loading={loading}
          >
            Save
          </Button>
        </Space>
      </Form.Item> */}
    </Form>
  );
};

export default ProfileForm;
