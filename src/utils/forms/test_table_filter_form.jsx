import { Button, Checkbox, Col, Form, Input, Row, Select } from "antd";
import { QuestionCircleTwoTone } from "@ant-design/icons";
import Helper from "../helper";
import FiltersSection from "../components/filters_section";
import { OptionsList } from "./options_list";

const savedFilterSettings = [];
const analyseBinsOptions = [];
const showOnlyCheckboxOptions = [
  {
    value: "followed_tests_show_only",
    label: "Followed Tests",
    disabled: true,
  },
  {
    value: "calculated_tests_show_only",
    label: "Calculated Tests",
    disabled: false,
  },
  {
    value: "calculated_related_tests_show_only",
    label: "Calculated & Related Tests",
    disabled: false,
  },
  {
    value: "summarize_by_pin_show_only",
    label: "Summarize by Pin",
    disabled: false,
  },
  {
    value: "no_lower_limit_show_only",
    label: "No Lower Limit",
    disabled: false,
  },
  {
    value: "no_upper_limit_show_only",
    label: "No Upper Limit",
    disabled: false,
  },
  {
    value: "show_all_tests",
    label: "Show All Tests",
    disabled: false,
  },
];
const excludeCheckboxOptions = [
  {
    value: "no_lower_limit_exclude",
    label: "No Lower Limit",
    disabled: false,
  },
  {
    value: "no_upper_limit_exclude",
    label: "No Upper Limit",
    disabled: false,
  },
];

/*
 * Form for filtering test table data
 *
 * @param {FormInstance} form
 * @param {object} gridFilters
 * @param {function} setGridFilters
 * @param {function} reloadTestsData
 * @returns {JSX.Element}
 * */
export default function TestTableFilterForm({
  form,
  gridFilters,
  setGridFilters,
  reloadTestsData,
}) {
  /**
   * Apply filters to tests grid
   *
   * @param {object} values
   */
  const applyFilters = (values) => {
    const gridFiltersCopy = Helper.cloneObject(gridFilters);
    gridFiltersCopy.test_type = values.test_type?.join();
    gridFiltersCopy.tname_show_only = values.tname_show_only;
    gridFiltersCopy.tnum_show_only = values.tnum_show_only;
    showOnlyCheckboxOptions.forEach((item) => {
      if (!item.disabled) {
        gridFiltersCopy[item.value] = values.filter_options_show_only?.includes(
          item.value,
        );
      }
    });

    gridFiltersCopy.tname_exclude = values.tname_exclude;
    gridFiltersCopy.atnum_exclude = values.atnum_exclude;
    excludeCheckboxOptions.forEach((item) => {
      gridFiltersCopy[item.value] = values.filter_options_exclude?.includes(
        item.value,
      );
    });
    setGridFilters(gridFiltersCopy);
    reloadTestsData();
  };

  return (
    <div className="p-4">
      <Form layout="vertical" form={form} onFinish={applyFilters}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="load_saved_filter_settings"
              label="Load Saved Filter Settings"
            >
              <Select
                mode="multiple"
                placeholder="- Select -"
                disabled={true}
                options={savedFilterSettings}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="analyse_bins" label="Analyse Bins">
              <Select
                mode="multiple"
                placeholder="Select Bins"
                options={analyseBinsOptions}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="test_type" label="Test Type">
              <Checkbox.Group options={OptionsList.test_type} />
            </Form.Item>
          </Col>
        </Row>
        <FiltersSection
          sectionTitle="Show Only"
          textFieldsProps={[
            {
              formItemProps: {
                name: "tname_show_only",
                label: "Test Names",
                extra: "Use comma to separate multiple selections",
              },
              inputProps: { placeholder: "Test Names you want to show only" },
            },
            {
              formItemProps: {
                name: "tnum_show_only",
                label: "Test Numbers",
                extra: "Use comma to separate multiple selections",
              },
              inputProps: { placeholder: "Test Numbers you want to show only" },
            },
          ]}
          checkboxFieldsProps={{
            formItemProps: { name: "filter_options_show_only" },
            inputProps: {
              options: showOnlyCheckboxOptions,
            },
          }}
        />
        <FiltersSection
          sectionTitle="Exclude"
          textFieldsProps={[
            {
              formItemProps: {
                name: "tname_exclude",
                label: "Test Names",
                extra: "Use comma to separate multiple selections",
              },
              inputProps: { placeholder: "Test Names you want to exclude" },
            },
            {
              formItemProps: {
                name: "atnum_exclude",
                label: "Test Numbers",
                extra: "Use comma to separate multiple selections",
              },
              inputProps: { placeholder: "Test Numbers you want to exclude" },
            },
          ]}
          checkboxFieldsProps={{
            formItemProps: { name: "filter_options_exclude" },
            inputProps: {
              options: excludeCheckboxOptions,
            },
          }}
        />
        <Row>
          <Col span={24}>
            <Form.Item name="auto_retain_filter_settings_for_next_visit">
              <Checkbox defaultChecked={true}>
                Auto-Retain Filter Settings for Next Visit{"  "}
                <QuestionCircleTwoTone />
              </Checkbox>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={4}>
          <Col span={16}>
            <Form.Item
              name="current_filter"
              label="Save Current Filter Settings as:"
              tooltip={{
                placement: "right",
                icon: <QuestionCircleTwoTone />,
                title: "Save current filter settings",
              }}
            >
              <Input disabled={true} placeholder="Custom name" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Button className="mt-6">Save Current Filters</Button>
          </Col>
        </Row>
      </Form>
    </div>
  );
}
