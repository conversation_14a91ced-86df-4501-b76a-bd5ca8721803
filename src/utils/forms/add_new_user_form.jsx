import { UserOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { App, Checkbox, Form, Input, Modal, Typography } from "antd";
import { useEffect } from "react";
import Validator from "../validator";
import Api from "../api";
import Helper from "../helper";
import FilterSelect from "../grid/components/filter_select";
import Password<PERSON>ield from "./fields/password_field";
import styles from "./styles.module.css";

const { Title } = Typography;

/**
 * Form to add new user
 *
 * @param {Form} form
 * @param {function} setIsAddNewUserModalOpen
 * @param {function} setIsDisableAddUserButton
 * @param {object} loadingStatus
 * @param {function} setLoadingStatus
 * @param {string} loadingStatusKey
 * @param {function} reloadUsersData
 * @param {array} rowData
 * @returns {JSX.Element}
 */
const AddNewUserForm = ({
  form,
  setIsAddNewUserModalOpen,
  setIsDisableAddUserButton,
  loadingStatus,
  setLoadingStatus,
  loadingStatusKey,
  reloadUsersData,
  rowData,
}) => {
  const formValues = Form.useWatch([], form);
  const { message } = App.useApp();
  const { confirm } = Modal;

  useEffect(() => {
    form
      .validateFields({
        validateOnly: true,
      })
      .then(
        () => {
          setIsDisableAddUserButton(false);
        },
        () => {
          setIsDisableAddUserButton(true);
        },
      );
  }, [formValues]);

  /**
   * Check for existing email and ask for confirmation
   *
   * @param {object} values
   */
  const confirmAddNewUser = (values) => {
    const isExisting =
      Object.values(rowData).filter((data) => {
        return data.email === values.email;
      }).length > 0;

    if (isExisting) {
      confirm({
        title: "Email Address Already Exists!",
        icon: <ExclamationCircleOutlined />,
        content:
          "The email address you entered is already taken. Do you still want to proceed?",
        okText: "Yes",
        cancelText: "No",
        onOk() {
          addNewUser(values);
        },
      });
    } else {
      addNewUser(values);
    }
  };

  /**
   * Add new user
   *
   * @param {object} values
   */
  const addNewUser = (values) => {
    Helper.updateLoadingStatus(
      loadingStatus,
      setLoadingStatus,
      loadingStatusKey,
      true,
    );
    Api.addUserViaAdmin(
      (res) => {
        if (res.success) {
          message.success(res.message);
          setIsAddNewUserModalOpen(false);
          reloadUsersData();
        } else {
          message.warning(res.message, 10);
        }
        Helper.updateLoadingStatus(
          loadingStatus,
          setLoadingStatus,
          loadingStatusKey,
          false,
        );
      },
      (err) => {
        message.error(err, 10);
        Helper.updateLoadingStatus(
          loadingStatus,
          setLoadingStatus,
          loadingStatusKey,
          false,
        );
      },
      { password_confirmation: values.password, ...values },
    );
  };

  return (
    <>
      <Form
        form={form}
        className={styles.modalForm}
        name="add_new_user_form"
        layout="vertical"
        requiredMark={true}
        onFinish={confirmAddNewUser}
        autoComplete="off"
        preserve={false}
      >
        <Title level={4}>Add New User</Title>
        <Form.Item
          name="name"
          label="Username"
          className="mt-6"
          help="Username must be 3-75 characters, alphanumeric only, no spaces or symbols"
          rules={[
            {
              required: true,
              message: "Please input username!",
            },
            {
              min: 3,
              message: "Username must be at least 3 characters.",
            },
            {
              max: 75,
              message: "Username must not exceed 75 characters.",
            },
          ]}
          hasFeedback
        >
          <Input
            id="add_new_user_username"
            placeholder="Username"
            prefix={<UserOutlined />}
            onKeyDown={Validator.onKeyPress}
          />
        </Form.Item>
        <Form.Item
          name="full_name"
          label="Full Name"
          className="mt-6"
          rules={[
            {
              required: true,
              message: "Please input full name!",
            },
          ]}
          hasFeedback
        >
          <Input placeholder="First name and Last name" />
        </Form.Item>
        <Form.Item
          name="email"
          label="Email Address"
          rules={[
            {
              required: true,
              message: "Please input email address!",
            },
            { type: "email", message: "Please enter a valid email address!" },
          ]}
          hasFeedback
        >
          <Input type="email" placeholder="Email" />
        </Form.Item>
        <PasswordField
          name="password"
          label="Temporary Password"
          placeholder="Input password"
          requiredMessage="Please input temporary password!"
          hasGeneratePasswordBtn={true}
          form={form}
        />
        <Form.Item
          name="user_type"
          label="User Type"
          rules={[
            {
              required: true,
              message: "Please select a user type!",
            },
          ]}
          hasFeedback
        >
          <FilterSelect
            componentKey="add_new_user_user_type"
            placeholder="-Select-"
            className="grow"
            params={{
              api: {
                url: "api/v1/internal/options/list/user/types",
                cache_it: 0,
              },
            }}
          />
        </Form.Item>
        <Form.Item
          name="department"
          label="Department"
          rules={[
            {
              required: true,
              message: "Please input department name!",
            },
          ]}
          hasFeedback
        >
          <Input placeholder="Department assigned" />
        </Form.Item>
        <Form.Item
          className="hidden"
          name="with_zendesk_access"
          valuePropName="checked"
        >
          <Checkbox>With Zendesk Access</Checkbox>
        </Form.Item>
      </Form>
    </>
  );
};

export default AddNewUserForm;
