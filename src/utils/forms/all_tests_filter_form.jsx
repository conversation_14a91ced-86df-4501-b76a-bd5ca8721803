// import { QuestionCircleTwoTone } from "@ant-design/icons";
import {
  // Button,
  Checkbox,
  Col,
  Divider,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Space,
  // Tooltip,
  Typography,
} from "antd";
import React, { memo, useState } from "react";
import { useBoundStore } from "../../store/store";
import FilterSelect from "../grid/components/filter_select";

const { Text, Title } = Typography;

/**
 * Form used for filtering search table data
 *
 * @param {FormInstance} allTestsFilterForm
 * @param {function} onFinish
 * @param {string} pageKey
 * @param {function} setSiteListOptions
 * @returns {JSX.Element}
 */
const AllTestsFilterForm = ({
  allTestsFilterForm,
  onFinish,
  pageKey,
  setSiteListOptions,
}) => {
  const [robustDataNDisabled, setRobustDataNDisabled] = useState(true);
  const urlParams = useBoundStore((state) => state.urlParams);

  /**
   * Triggers when stats type field was changed
   *
   * @param {string} value
   */
  const handleStatsTypeChange = (value) => {
    const robustDataN = allTestsFilterForm.getFieldValue("iqr_n");
    allTestsFilterForm.setFieldValue(
      "iqr_n",
      value !== "iqr"
        ? undefined
        : robustDataN === undefined
          ? 1.5
          : robustDataN,
    );
    setRobustDataNDisabled(value !== "iqr");
  };

  return (
    <div className="p-4">
      <Form
        form={allTestsFilterForm}
        name="all_tests_filter_form"
        layout="vertical"
        initialValues={{
          test_type: ["m", "p", "f"],
          stats_type: "rp",
        }}
        onFinish={onFinish}
      >
        <Title level={5}>All Tests Filters</Title>
        {/* <Row gutter={14}>
          <Col span={6}>
            <Form.Item label="Load Saved Filter Settings">
              <Select
                placeholder="-Select-"
                popupMatchSelectWidth={false}
                options={[]}
                disabled
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label=" ">
              <Space>
                <Button disabled>Delete</Button>
                <Button type="primary" disabled>
                  Load
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row> */}
        <Row gutter={14}>
          <Col span={6}>
            <Form.Item name="site" label="Site Number">
              <FilterSelect
                placeholder="Select Sites"
                mode="multiple"
                setOptions={setSiteListOptions}
                params={{
                  api: {
                    url: "api/v1/internal/options/list/sites",
                    src_type: urlParams[pageKey].src_type,
                    src_value: urlParams[pageKey].src_value,
                    cache_it: 0,
                  },
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={14}>
          {/* <Col span={6}>
            <Form.Item label="Site Number">
              <FilterSelect
                placeholder="Select Sites"
                params={{
                  api: {
                    url: "api/v1/internal/options/list/sites",
                    dsk: urlParams[pageKey].dsk,
                    cache_it: 0,
                  },
                }}
              />
            </Form.Item>
          </Col> */}
          {/* <Col span={6}>
            <Form.Item label="Analyse Bins">
              <Select
                mode="multiple"
                placeholder="Select Bins"
                popupMatchSelectWidth={false}
                maxTagCount="responsive"
                options={[]}
                disabled
              />
            </Form.Item>
          </Col> */}
          <Col span={6}>
            <Form.Item name="stats_type" label="Results Based On">
              <Select
                placeholder=""
                popupMatchSelectWidth={false}
                onChange={handleStatsTypeChange}
                options={[
                  { label: "All (Last Results Per Die)", value: "rp" },
                  {
                    label: "Passing Die Only (Last Results Per Die)",
                    value: "passing_unit",
                  },
                  { label: "Robust Data (Last Results Per Die)", value: "iqr" },
                  { label: "All", value: "all" },
                ]}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="iqr_n" label="Robust Data N">
              <InputNumber className="w-full" disabled={robustDataNDisabled} />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Space size="large">
              <Form.Item name="show_all_tests" valuePropName="checked">
                <Checkbox>Show All Tests</Checkbox>
              </Form.Item>
              <Form.Item name="show_pseudo_tests" valuePropName="checked">
                <Checkbox>Show Pseudo Tests Only</Checkbox>
              </Form.Item>
            </Space>
          </Col>
        </Row>
        <Divider className="mt-0!">
          <Text strong>Show Only</Text>
        </Divider>
        <Row gutter={14}>
          <Col span={6}>
            <Form.Item
              name="tname_show_only"
              label="Test Names"
              extra="Use comma to separate multiple selections"
            >
              <Input placeholder="Test Names you want to show only" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="atnum_show_only"
              label="Test Numbers"
              extra="Use comma to separate multiple selections"
            >
              <Input placeholder="Test Numbers you want to show only" />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Space size="large">
              <Form.Item name="no_fails_show_only" valuePropName="checked">
                <Checkbox>No Fails</Checkbox>
              </Form.Item>
              <Form.Item name="invalid_cp_show_only" valuePropName="checked">
                <Checkbox>Invalid Cp</Checkbox>
              </Form.Item>
              <Form.Item name="invalid_cpk_show_only" valuePropName="checked">
                <Checkbox>Invalid Cpk</Checkbox>
              </Form.Item>
              <Form.Item
                name="no_lower_limit_show_only"
                valuePropName="checked"
              >
                <Checkbox>No Lower Limit</Checkbox>
              </Form.Item>
              <Form.Item
                name="no_upper_limit_show_only"
                valuePropName="checked"
              >
                <Checkbox>No Upper Limit</Checkbox>
              </Form.Item>
              {/* <Form.Item valuePropName="checked">
                <Checkbox disabled>Calculated & Related Tests</Checkbox>
              </Form.Item>
              <Form.Item valuePropName="checked">
                <Checkbox disabled>Excluded Tests Only</Checkbox>
              </Form.Item> */}
            </Space>
          </Col>
        </Row>
        <Divider className="mt-0!">
          <Text strong>Exclude</Text>
        </Divider>
        <Row gutter={14}>
          <Col span={6}>
            <Form.Item
              name="tname_exclude"
              label="Test Names"
              extra="Use comma to separate multiple selections"
            >
              <Input placeholder="Test Names you want to exclude" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="atnum_exclude"
              label="Test Numbers"
              extra="Use comma to separate multiple selections"
            >
              <Input placeholder="Test Numbers you want to exclude" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="part_id_exclude" label="Part ID">
              <Input placeholder="Part ID you want to exclude" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={14}>
          <Col span={6}>
            <Form.Item name="cp_less_than_exclude" label="Cp Less Than">
              <InputNumber placeholder="Cp less" className="w-full" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="cp_greater_than_exclude" label="and Greater Than">
              <InputNumber placeholder="Cp greater" className="w-full" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="cpk_less_than_exclude" label="Cpk Less Than">
              <InputNumber placeholder="Cpk less" className="w-full" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="cpk_greater_than_exclude" label="and Greater Than">
              <InputNumber placeholder="Cpk greater" className="w-full" />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Space size="large">
              <Form.Item name="no_fails_exclude" valuePropName="checked">
                <Checkbox>No Fails</Checkbox>
              </Form.Item>
              <Form.Item name="invalid_cp_exclude" valuePropName="checked">
                <Checkbox>Invalid Cp</Checkbox>
              </Form.Item>
              <Form.Item name="invalid_cpk_exclude" valuePropName="checked">
                <Checkbox>Invalid Cpk</Checkbox>
              </Form.Item>
              <Form.Item name="no_lower_limit_exclude" valuePropName="checked">
                <Checkbox>No Lower Limit</Checkbox>
              </Form.Item>
              <Form.Item name="no_upper_limit_exclude" valuePropName="checked">
                <Checkbox>No Upper Limit</Checkbox>
              </Form.Item>
            </Space>
          </Col>
        </Row>
        {/* <Divider className="mt!=0">
          <Text strong>Save Filter Settings</Text>
        </Divider>
        <Row>
          <Col span={24}>
            <Form.Item className="mb-2" valuePropName="checked">
              <Checkbox disabled>
                Auto-Retain Filter Settings for Next Visit
              </Checkbox>
              <Tooltip
                title="Enable this option to automatically retain your current
settings as default for your next visit."
              >
                <QuestionCircleTwoTone />
              </Tooltip>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={14}>
          <Col span={6}>
            <Form.Item
              name=""
              label="Save Current Filter Settings as:"
              tooltip={{
                title:
                  "Save your current configuration with a custom name for future use.",
                icon: <QuestionCircleTwoTone />,
              }}
            >
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label=" ">
              <Button disabled>Save Current Filters</Button>
            </Form.Item>
          </Col>
        </Row> */}
      </Form>
    </div>
  );
};

export default memo(AllTestsFilterForm);
