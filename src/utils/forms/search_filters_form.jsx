import "./override.css";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Tabs, Row, Col, ConfigProvider } from "antd";
import { useEffect, useState } from "react";
import { useBoundStore } from "../../store/store";
import Helper from "../helper";
import { UserSettingsKeys } from "../user_settings_keys";
import { ComponentNameMapper } from "../grid/component_name_mapper";
import { SearchFilterFieldsMapper } from "./mappers/search_filter_fields_mapper";
import SearchTableFiltersForm from "./search_table_filters_form";
// import SearchRefineFiltersForm from "./search_refine_filters_form";

const rememberSearchFilterFields = ["date_range"];

/**
 * Form to apply more filters on search results
 *
 * @param {string} pageKey
 * @param {Form} searchFilterForm
 * @param {function} setSearchFilterFields
 * @returns {JSX.Element}
 */
const SearchFiltersForm = ({
  pageKey,
  searchFilterForm,
  setSearchFilterFields,
}) => {
  const [appliedFilters, setAppliedFilters] = useState([]);
  const [isFormChanged, setIsFormChanged] = useState(false);
  const filters = useBoundStore((state) => state.filters);
  const setFilters = useBoundStore((state) => state.setFilters);
  const currentPageData = useBoundStore((state) => state.currentPageData);
  const gridComponentRefs = useBoundStore((state) => state.gridComponentRefs);
  const { message } = App.useApp();

  const searchFilterInitialValues = {};
  const filterTabItems = [
    {
      key: "table-filters-tab",
      label: "Table Filters",
      children: (
        <SearchTableFiltersForm
          pageKey={pageKey}
          filters={filters}
          setFilters={setFilters}
          searchFilterForm={searchFilterForm}
          setSearchFilterFields={setSearchFilterFields}
        />
      ),
      className: "flex-container",
    },
    // {
    //   key: "refine-by-tab",
    //   label: "Refine By",
    //   children: <SearchRefineFiltersForm />,
    //   className: "flex-container",
    // },
  ];

  useEffect(() => {
    if (currentPageData.key == pageKey) {
      if (filters[pageKey] === undefined) {
        filters[pageKey] = {};
      }
      updateAppliedFilters(filters);
    }
  }, [filters[pageKey]]);

  useEffect(() => {
    loadFiltersToForm(filters);
  }, [appliedFilters]);

  /**
   * Update applied filters display
   *
   * @param {object} filters
   */
  const updateAppliedFilters = (filters) => {
    let appliedFiltersArr = [];
    let values = [];
    Object.keys(filters[pageKey]).forEach((key) => {
      if (SearchFilterFieldsMapper[key]) {
        switch (SearchFilterFieldsMapper[key].type) {
          case "checkbox":
            appliedFiltersArr.push({
              key: key,
              fieldKey: key,
              value: filters[pageKey][key],
              valueText: SearchFilterFieldsMapper[key].label,
            });
            break;
          case "checkbox.group":
            filters[pageKey][key].split(",").forEach((value) => {
              appliedFiltersArr.push({
                key: value,
                fieldKey: key,
                value: value,
                valueText: SearchFilterFieldsMapper[value]
                  ? SearchFilterFieldsMapper[value].label
                  : value,
              });
            });
            break;
          case "select.multiple":
            values = filters[pageKey][key]
              .split(",")
              .map((value) => {
                return typeof value === "object" ? value.label : value;
              })
              .join(", ");
            appliedFiltersArr.push({
              key: key,
              fieldKey: key,
              label: SearchFilterFieldsMapper[key].label,
              value: filters[pageKey][key],
              valueText: values,
            });
            break;
          default:
            appliedFiltersArr.push({
              key: key,
              fieldKey: key,
              label: SearchFilterFieldsMapper[key].label,
              value: filters[pageKey][key],
              valueText: filters[pageKey][key],
            });
        }
      }
    });
    setAppliedFilters(appliedFiltersArr);
  };

  /**
   * Load filters to form fields
   *
   * @param {object} filters
   */
  const loadFiltersToForm = (filters) => {
    let filtersCopy = Helper.cloneObject(filters);
    let fieldValue;
    let shouldSetFilters = false;
    const savedSearchFilters = Helper.getUserSettings(
      UserSettingsKeys.search_filter_values,
    );
    Object.keys(SearchFilterFieldsMapper).forEach((fieldKey) => {
      switch (SearchFilterFieldsMapper[fieldKey].type) {
        case "select.multiple":
        case "checkbox.group":
          fieldValue =
            filtersCopy[pageKey][fieldKey] !== undefined
              ? filtersCopy[pageKey][fieldKey].split(",")
              : [];
          searchFilterForm.setFieldValue(
            fieldKey,
            JSON.parse(JSON.stringify(fieldValue)),
          );
          if (fieldValue.length === 0) {
            fieldValue = searchFilterInitialValues[fieldKey];
            if (Array.isArray(fieldValue) && fieldValue.length > 0) {
              filtersCopy[pageKey][fieldKey] = fieldValue.join();
            }
          }
          break;
        default:
          searchFilterForm.setFieldValue(
            fieldKey,
            filtersCopy[pageKey][fieldKey]
              ? filtersCopy[pageKey][fieldKey]
              : savedSearchFilters?.[fieldKey]
                ? savedSearchFilters[fieldKey]
                : SearchFilterFieldsMapper[fieldKey].defaultValue
                  ? SearchFilterFieldsMapper[fieldKey].defaultValue
                  : undefined,
          );
      }
      if (filtersCopy[pageKey][fieldKey] !== filters[pageKey][fieldKey]) {
        shouldSetFilters = true;
      }
    });

    if (shouldSetFilters) {
      setFilters(filtersCopy);
    }
  };

  /**
   * Apply filters to search data
   *
   * @param {object} values
   */
  const applyFilters = (values) => {
    const filterValues = Helper.getFilterValues(values, filters, pageKey);
    let filtersCopy = Helper.cloneObject(filters);
    filtersCopy[pageKey] = filterValues;
    setFilters(filtersCopy);
    gridComponentRefs[
      ComponentNameMapper.home_search_table
    ]?.current?.reloadGridData();

    Helper.setUserSettings(
      UserSettingsKeys.search_filter_values,
      Helper.filterObjectByKeys(filterValues, rememberSearchFilterFields),
    );
  };

  /**
   * Clear applied filters
   */
  const clearFilters = () => {
    searchFilterForm.resetFields();
    setIsFormChanged(false);
    message.success("Search filters successfully cleared");
  };

  /**
   * Track if any form field is modified to enable/disable the Apply Filter button
   */
  const handleValuesChange = () => {
    setIsFormChanged(searchFilterForm.isFieldsTouched());
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Collapse: {
            colorBorder: "#91D5FF",
            headerBg: "#E6F7FF",
            colorText: "rgba(0, 0, 0, 0.88)",
          },
        },
      }}
    >
      <div className="flex-container px-2">
        <Form
          form={searchFilterForm}
          className="flex-container"
          name="search_filter_form"
          layout="vertical"
          onFinish={applyFilters}
          onValuesChange={handleValuesChange}
        >
          <div className="flex-container">
            <Tabs
              className="searchFiltersTab flex-container"
              defaultActiveKey="table-filters-tab"
              items={filterTabItems}
            />
          </div>
          <Form.Item className="mt-3">
            <Row gutter={12}>
              <Col span={12}>
                <Button onClick={() => clearFilters()} block>
                  Clear All
                </Button>
              </Col>
              <Col span={12}>
                <Button
                  type="primary"
                  onClick={() => searchFilterForm.submit()}
                  disabled={!isFormChanged}
                  block
                >
                  Apply Filter
                </Button>
              </Col>
            </Row>
          </Form.Item>
        </Form>
      </div>
    </ConfigProvider>
  );
};

export default SearchFiltersForm;
