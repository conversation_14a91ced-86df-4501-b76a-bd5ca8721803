import { PlusOutlined, MinusCircleOutlined } from "@ant-design/icons";
import { Button, Form, Select, Space, Typography } from "antd";
import styles from "./styles.module.css";

const { Text } = Typography;

/**
 * Form to add new column
 *
 * @returns {JSX.Element}
 */
const AddNewColumnForm = () => {
  const [form] = Form.useForm();

  return (
    <Form
      form={form}
      className={styles.modalForm}
      layout="vertical"
      name="add_new_column_form"
      initialValues={{
        columns: [null],
      }}
    >
      <Form.List name="columns">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <Space key={key} className="my-1" align="center">
                <Form.Item
                  {...restField}
                  name={[name, "column"]}
                  label="Select Column"
                  rules={[
                    {
                      required: true,
                      message: "Please select a column",
                    },
                  ]}
                >
                  <Select
                    showSearch
                    placeholder="-Select Column-"
                    popupMatchSelectWidth={false}
                    options={[
                      {
                        value: "process_date",
                        label: "Process Date",
                      },
                      {
                        value: "handler_id",
                        label: "Handler ID",
                      },
                      {
                        value: "lot_id",
                        label: "Lot ID",
                      },
                    ]}
                  />
                </Form.Item>
                <Form.Item
                  {...restField}
                  name={[name, "position"]}
                  label="Select Position"
                >
                  <Select
                    placeholder="-Select Insert Position-"
                    popupMatchSelectWidth={false}
                    options={[
                      {
                        value: "end_of_table",
                        label: "End of Table",
                      },
                      {
                        value: "beginning_of_table",
                        label: "Beginning of Table",
                      },
                    ]}
                  />
                </Form.Item>
                <Text className="mx-2">or</Text>
                <Form.Item
                  {...restField}
                  name={[name, "insert_column_after"]}
                  label="Insert Column After"
                >
                  <Select
                    showSearch
                    placeholder="-Select Column-"
                    popupMatchSelectWidth={false}
                    options={[
                      {
                        value: "process_date",
                        label: "Process Date",
                      },
                      {
                        value: "handler_id",
                        label: "Handler ID",
                      },
                      {
                        value: "lot_id",
                        label: "Lot ID",
                      },
                    ]}
                  />
                </Form.Item>
                <MinusCircleOutlined
                  className="ml-2"
                  onClick={() => remove(name)}
                />
              </Space>
            ))}
            <Form.Item className="py-4">
              <Button onClick={() => add()} block icon={<PlusOutlined />}>
                Add Another Column
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>
    </Form>
  );
};

export default AddNewColumnForm;
