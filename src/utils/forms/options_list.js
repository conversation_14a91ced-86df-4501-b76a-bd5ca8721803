/**
 * List options for select fields
 */
export const OptionsList = {
  npi_stats_types: [
    {
      value: "all",
      label: "All",
    },
    {
      value: "pass",
      label: "Die Passing This Test",
    },
    {
      value: "passing_unit",
      label: "Passing Die",
    },
    {
      value: "iqr_dsk",
      label: "Robust Per File",
    },
    {
      value: "iqr_grp",
      label: "Robust Per Group",
    },
    {
      value: "iqr_agg",
      label: "Robust Aggregate",
    },
  ],
  npi_layout_options: [
    {
      value: "w-full",
      label: "1 Item per Row",
    },
    {
      value: "w-1/2",
      label: "2 Items per Row",
    },
    {
      value: "w-1/3",
      label: "3 Items per Row",
    },
    {
      value: "w-1/4",
      label: "4 Items per Row",
    },
    {
      value: "w-1/5",
      label: "5 Items per Row",
    },
  ],
  stats_type: [
    { label: "All (Last Results Per Die)", value: "rp" },
    {
      label: "Passing Die Only (Last Results Per Die)",
      value: "passing_unit",
    },
    { label: "Robust Data (Last Results Per Die)", value: "iqr" },
    { label: "All", value: "all" },
    { label: "Custom (All)", value: "custom_all", hidden: true },
    { label: "Custom (Last result per Die)", value: "custom", hidden: true },
  ],
  site: [{ label: "Overall", value: 255 }],
  sort_dir: [
    {
      value: "asc",
      label: "Ascending",
    },
    {
      value: "desc",
      label: "Descending",
    },
  ],
  chartspace_chart_types: {
    single: [
      {
        value: "bar_histogram",
        label: "Bar Histogram",
      },
      {
        value: "curve_histogram",
        label: "Curved Histogram",
      },
      {
        value: "qq_plot",
        label: "Normal Probability",
      },
      {
        value: "scatter",
        label: "Scatter",
      },
      {
        value: "wafer",
        label: "Wafer",
      },
    ],
    per_pin: [
      {
        value: "bar_histogram",
        label: "Bar Histogram",
      },
      {
        value: "curve_histogram",
        label: "Curved Histogram",
      },
      {
        value: "qq_plot",
        label: "Normal Probability",
      },
      {
        value: "scatter",
        label: "Scatter",
      },
      {
        value: "boxplot",
        label: "Boxwhisker",
      },
      {
        value: "wafer",
        label: "Wafer",
        disabled: true,
      },
    ],
    default: [
      {
        value: "bar_histogram",
        label: "Bar Histogram",
      },
      {
        value: "curve_histogram",
        label: "Curved Histogram",
      },
      {
        value: "qq_plot",
        label: "Normal Probability",
      },
      {
        value: "scatter",
        label: "Scatter",
      },
      {
        value: "boxplot",
        label: "Boxwhisker",
      },
      {
        value: "wafer",
        label: "Wafer",
      },
      {
        value: "multi_test_bar_histogram",
        label: "Multi Test Bar Histogram",
        disabled: true,
      },
      {
        value: "multi_test_curved_histogram",
        label: "Multi Test Curved Histogram",
        disabled: true,
      },
      {
        value: "multi_test_qq_plot",
        label: "Multi Test Normal Probability",
        disabled: true,
      },
      {
        value: "multi_test_scatter",
        label: "Multi Test Scatter",
        disabled: true,
      },
      {
        value: "multi_test_boxwhisker",
        label: "Multi Test Boxwhisker",
        disabled: true,
      },
    ],
  },
  test_type: [
    {
      value: "m",
      label: "MPR",
    },
    {
      value: "p",
      label: "PTR",
    },
    {
      value: "f",
      label: "FTR",
    },
  ],
};
