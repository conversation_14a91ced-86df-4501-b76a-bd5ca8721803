import { Col, Divider, Form, Input, Row, Typography } from "antd";
import React, { memo } from "react";
import Helper from "../helper";
import { useBoundStore } from "../../store/store";

const { Text, Title } = Typography;

/**
 * Form used for filtering tests table data
 *
 * @param {FormInstance} testsFilterForm
 * @param {object} gridFilters
 * @param {function} setGridFilters
 * @param {function} reloadTestsData
 * @returns {JSX.Element}
 */
const TestsFilterForm = ({
  testsFilterForm,
  gridFilters,
  setGridFilters,
  reloadTestsData,
}) => {
  const setNpiReportOptionsTestFilters = useBoundStore(
    (state) => state.setNpiReportOptionsTestFilters,
  );

  /**
   * Apply filters to tests grid
   *
   * @param {object} values
   */
  const applyFilters = (values) => {
    const gridFiltersCopy = Helper.cloneObject(gridFilters);
    Object.keys(values).forEach((key) => {
      gridFiltersCopy[key] = values[key];
    });
    setNpiReportOptionsTestFilters(values);
    setGridFilters(gridFiltersCopy);
    reloadTestsData();
  };

  return (
    <div className="p-4">
      <Form
        form={testsFilterForm}
        name="tests_filter_form"
        layout="vertical"
        onFinish={applyFilters}
      >
        <Title level={5}>Tests Filters</Title>
        <Divider className="mt-0!">
          <Text strong>Show Only</Text>
        </Divider>
        <Row gutter={14}>
          <Col span={12}>
            <Form.Item
              name="tname_show_only"
              label="Test Names"
              extra="Use comma to separate multiple selections"
            >
              <Input placeholder="Test Names you want to show only" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="atnum_show_only"
              label="Test Numbers"
              extra="Use comma to separate multiple selections"
            >
              <Input placeholder="Test Numbers you want to show only" />
            </Form.Item>
          </Col>
        </Row>
        <Divider className="mt-0!">
          <Text strong>Exclude</Text>
        </Divider>
        <Row gutter={14}>
          <Col span={12}>
            <Form.Item
              name="tname_exclude"
              label="Test Names"
              extra="Use comma to separate multiple selections"
            >
              <Input placeholder="Test Names you want to exclude" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="atnum_exclude"
              label="Test Numbers"
              extra="Use comma to separate multiple selections"
            >
              <Input placeholder="Test Numbers you want to exclude" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default memo(TestsFilterForm);
