"use client";

import { Flex, Form, InputNumber, Slider, Typography } from "antd";
import { useEffect, useState } from "react";
const { Text } = Typography;

/**
 * A step component that consists of a slider and an input number that synchonizes in value
 *
 * @param {string} inputName
 * @param {string} label
 * @param {int} min
 * @param {int} max
 * @param {int|float} step
 * @param {object} form
 * @param {function} handleFieldsChange
 * @param {boolean} isVertical
 * @param {boolean} isDisabled
 * @returns {JSX.Element}
 */
const IntegerStep = ({
  inputName = "",
  label = "",
  min = 0,
  max = 360,
  step = 1,
  form,
  handleFieldsChange,
  isVertical = true,
  isDisabled,
  ...props
}) => {
  const [inputValue, setInputValue] = useState(0);

  useEffect(() => {
    setInputValue(form.getFieldValue(inputName));
  }, []);

  const onChange = (newValue) => {
    setInputValue(newValue);
    const inputValue = {};
    inputValue[inputName] = newValue;
    form.setFieldsValue(inputValue);
    handleFieldsChange();
  };

  return (
    <Flex vertical={isVertical} gap={8} {...props}>
      <Text>{label}</Text>
      <Flex gap={8} className={isVertical ? "w-full" : ""}>
        <Slider
          min={min}
          max={max}
          step={step}
          onChange={onChange}
          value={typeof inputValue === "number" ? inputValue : 0}
          disabled={isDisabled}
          className="w-4/5"
        />
        <Form.Item name={inputName} noStyle>
          <InputNumber
            min={min}
            max={max}
            step={step}
            value={inputValue}
            onChange={onChange}
            disabled={isDisabled}
          />
        </Form.Item>
      </Flex>
    </Flex>
  );
};

export default IntegerStep;
