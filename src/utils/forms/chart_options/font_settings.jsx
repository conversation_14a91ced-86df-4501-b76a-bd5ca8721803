import { Form, Flex, InputNumber, Select } from "antd";
import { memo } from "react";

const fontOptions = [
  {
    value: "Arial",
    label: <span className="font-sans">Arial</span>,
    category: "sans-serif",
  },
  {
    value: "Courier New",
    label: <span className="font-mono">Courier New</span>,
    category: "monospace",
  },
  {
    value: "Times New Roman",
    label: <span className="font-serif">Times New Roman</span>,
    category: "serif",
  },
  {
    value: "Georgia",
    label: <span className="font-serif">Georgia</span>,
    category: "serif",
  },
  {
    value: "Helvetica",
    label: <span className="font-sans">Helvetica</span>,
    category: "sans-serif",
  },
];

/**
 * Font settings component of chart options
 *
 * @param {string} target
 * @param {string} targetLabel
 * @param {int} minFontSize
 * @param {int} maxFontSize
 * @param {int} defaultFontSize
 * @param {string} className
 * @returns {JSX.Element}
 */
const FontSettings = ({
  target,
  targetLabel = "Font",
  minFontSize = 8,
  maxFontSize = 72,
  defaultFontSize = 14,
  className = "",
}) => {
  const fontFieldName = `${target}_font`;
  const sizeFieldName = `${target}_font_size`;

  return (
    <Flex justify="space-between" gap={8} className={className}>
      <Form.Item
        name={fontFieldName}
        label={targetLabel}
        className="w-3/4"
        tooltip="Select the font family for the text"
      >
        <Select
          showSearch
          placeholder="Select font"
          options={fontOptions}
          optionFilterProp="value"
          className="w-full"
        />
      </Form.Item>

      <Form.Item
        name={sizeFieldName}
        label="Font Size"
        tooltip="Set the font size in pixels"
      >
        <InputNumber
          min={minFontSize}
          max={maxFontSize}
          defaultValue={defaultFontSize}
          formatter={(value) => `${value}px`}
          parser={(value) => value.replace("px", "")}
          className="w-24"
        />
      </Form.Item>
    </Flex>
  );
};

export default memo(FontSettings);
