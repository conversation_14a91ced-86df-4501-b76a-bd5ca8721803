"use client";

import { Form, Flex, Select, Typography } from "antd";
const { Text } = Typography;

const lineStyleOptions = [
  {
    value: "Solid",
    label: "Solid",
  },
  {
    value: "ShortDash",
    label: "Short Dash",
  },
  {
    value: "ShortDot",
    label: "Short Dot",
  },
  {
    value: "ShortDashDot",
    label: "Short Dash Dot",
  },
  {
    value: "Dot",
    label: "Dot",
  },
  {
    value: "LongDash",
    label: "Long Dash",
  },
  {
    value: "DashDot",
    label: "Dash Dot",
  },
  {
    value: "LongDashDot",
    label: "Long Dash Dot",
  },
];

/**
 * Line style select component
 *
 * @param {string} inputName
 * @returns {JSX.Element}
 */
const LineStyleSelect = ({ inputName, ...props }) => {
  return (
    <Flex align="center" className="w-2/3" {...props}>
      <Text>Line Style:</Text>
      <Form.Item name={inputName} noStyle>
        <Select
          className="ml-1 w-2/3"
          showSearch
          placeholder="Select line style"
          options={lineStyleOptions}
        ></Select>
      </Form.Item>
    </Flex>
  );
};

export default LineStyleSelect;
