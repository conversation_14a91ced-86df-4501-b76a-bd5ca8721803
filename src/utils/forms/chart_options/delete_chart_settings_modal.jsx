"use client";

import { App, <PERSON><PERSON>, Flex, <PERSON>, Modal, Select } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { useState } from "react";
import Api from "../../api";

/**
 * Delete chart settings modal component
 *
 * @param {boolean} open
 * @param {function} setModalOpen
 * @param {array} savedSettingsOptions
 * @param {function} getSavedChartSettingsList
 * @returns {JSX.Element}
 */
const DeleteChartSettingsModal = ({
  open,
  setModalOpen,
  savedSettingsOptions,
  getSavedChartSettingsList,
}) => {
  const [form] = Form.useForm();
  const [{ confirm }, contextHolder] = Modal.useModal();
  const { message } = App.useApp();
  const [isDeleteBtnDisabled, setIsDeleteBtnDisabled] = useState(true);

  /**
   * Delete the selected saved chart settings
   */
  const deleteChartSettings = () => {
    Api.deleteChartSettings(
      (res) => {
        if (res.success) {
          message.success("Chart Settings Deleted!", 5);
          getSavedChartSettingsList();
          setModalOpen(false);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        id: form.getFieldValue("settings_key"),
      },
    );
  };

  /**
   * Confirmation to delete saved settings
   */
  const confirmDelete = () => {
    const confirmModal = confirm({
      title: "Confirm Deletion",
      width: 480,
      icon: <InfoCircleOutlined style={{ color: "rgb(252 211 77)" }} />,
      content:
        "Are you sure you want to delete the selected chart settings permanently? This action cannot be undone.",
      footer: (
        <Flex justify="right" gap="small" className="mt-3">
          <Button
            key="back"
            onClick={() => {
              setModalOpen(true);
              confirmModal.destroy();
            }}
          >
            Cancel
          </Button>
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              deleteChartSettings();
              confirmModal.destroy();
            }}
          >
            Delete
          </Button>
        </Flex>
      ),
    });
  };

  /**
   * When user cancels the modal
   */
  const handleCancel = () => {
    setModalOpen(false);
  };

  /**
   * Handler when user selects a settings name
   */
  const handleSelectChange = () => {
    const settingsKey = form.getFieldValue("settings_key");
    setIsDeleteBtnDisabled(settingsKey === "");
  };

  return (
    <>
      {contextHolder}
      <Modal
        title="Delete Chart Settings"
        open={open}
        onOk={confirmDelete}
        onCancel={handleCancel}
        destroyOnHidden
        footer={[
          <Button key="back" onClick={handleCancel}>
            Close
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={confirmDelete}
            disabled={isDeleteBtnDisabled}
          >
            Delete
          </Button>,
        ]}
      >
        <Form form={form}>
          <Form.Item name="settings_key" required>
            <Select
              showSearch
              placeholder="Select Settings Name"
              options={savedSettingsOptions}
              onChange={handleSelectChange}
            ></Select>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default DeleteChartSettingsModal;
