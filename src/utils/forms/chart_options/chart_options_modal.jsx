"use client";

import {
  Divider,
  Flex,
  Form,
  Input,
  InputNumber,
  Modal,
  Select,
  Switch,
} from "antd";
import { useEffect, useState } from "react";
import { useBoundStore } from "../../../store/store";
import LegendColorPicker from "./legend_color_picker";

const { TextArea } = Input;
const plotLineNames = ["mean", "test_limits", "robust_limits"];
const dataRangeOptions = [
  {
    value: "minMax",
    label: "Data Min & Max",
  },
  {
    value: "dataAndLimits",
    label: "Data & Limits",
  },
  {
    value: "robustLimits",
    label: "Robust Limits",
  },
  {
    value: "testLimits",
    label: "Test Limits",
  },
];

/**
 * Chart options modal component
 *
 * @param {boolean} open Set to true to open the modal, false otherwise
 * @returns {JSX.Element}
 */
const ChartOptionsModal = ({ open }) => {
  const [form] = Form.useForm();
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const currentChart = useBoundStore((state) => state.currentChart);
  const [isBellCurveDisabled, setIsBellCurveDisabled] = useState(true);
  const [isYAxis2TitleDisabled, setIsYAxis2TitleDisabled] = useState(true);
  const [isPlotLinesDisabled, setIsPlotLinesDisabled] = useState(true);
  const [isDataRangeSelectDisabled, setIsDataRangeSelectDisabled] =
    useState(false);
  const [legendColors, setLegendColors] = useState([]);
  const [isLegendOptionsDisabled, setIsLegendOptionsDisabled] = useState(false);
  const [isXAxisTitleDisabled, setIsXAxisTitleDisabled] = useState(false);
  const [isYAxisTitleDisabled, setIsYAxisTitleDisabled] = useState(false);

  useEffect(() => {
    if (open) {
      const options = currentChart.userOptions;
      const xAxis = options.xAxis[0];
      const yAxis = options.yAxis[0];
      const fieldsValue = {
        chart_title: options.title.text ?? "",
        chart_subtitle: options?.subtitle?.text ?? "",
        x_axis_title: xAxis.title?.text ?? "",
        x_axis_gridlines: xAxis.gridLineWidth > 0,
        x_axis_label_angle: xAxis.labels.rotation,
        y_axis_title: yAxis.title.text ?? "",
        y_axis_gridlines: yAxis.gridLineWidth > 0,
        y_axis_log_scale: yAxis.type === "logarithmic",
      };

      setIsYAxis2TitleDisabled(true);
      if (options.yAxis.length === 2) {
        if (options.yAxis[1].visible === false) {
          setIsYAxis2TitleDisabled(true);
        } else {
          setIsYAxis2TitleDisabled(false);
          fieldsValue.y_axis2_title = options.yAxis[1].title.text ?? "";
        }
      }

      if (options.series[2] && options.series[2].type === "bellcurve") {
        setIsBellCurveDisabled(false);
        fieldsValue.bell_curve = options.series[2].visible;
      } else {
        setIsBellCurveDisabled(true);
      }

      const minRange = yAxis.min ?? xAxis.min;
      const maxRange = yAxis.max ?? xAxis.max;
      if (options.testData) {
        setIsDataRangeSelectDisabled(false);
        if (typeof minRange === "number" && typeof maxRange === "number") {
          fieldsValue.data_range = null;
          fieldsValue.min_range = parseFloat(minRange);
          fieldsValue.max_range = parseFloat(maxRange);
        } else {
          fieldsValue.data_range = "minMax";
          fieldsValue.min_range = parseFloat(options.testData.min_result);
          fieldsValue.max_range = parseFloat(options.testData.max_result);
        }
      } else {
        setIsDataRangeSelectDisabled(true);
      }

      form.setFieldsValue(fieldsValue);

      if (Array.isArray(xAxis.plotLines)) {
        setPlotLineValues(xAxis.plotLines);
        setIsPlotLinesDisabled(false);
      } else if (Array.isArray(yAxis.plotLines)) {
        setPlotLineValues(yAxis.plotLines);
        setIsPlotLinesDisabled(false);
      } else {
        setIsPlotLinesDisabled(true);
      }

      setIsLegendOptionsDisabled(options.disableOptions?.legend);
      setIsXAxisTitleDisabled(options.disableOptions?.xAxisTitle);
      setIsYAxisTitleDisabled(options.disableOptions?.yAxisTitle);
    }
  }, [open]);

  /**
   * Set the plot line values in the form
   *
   * @param {object} plotLines
   */
  const setPlotLineValues = (plotLines) => {
    const fieldsValue = {};
    plotLines.forEach((plotObj) => {
      if (plotLineNames.includes(plotObj.name)) {
        fieldsValue[plotObj.name] = plotObj.width > 0;
      }
    });
    form.setFieldsValue(fieldsValue);
  };

  /**
   * Get the modified plot line definitions
   *
   * @param {object} plotLines
   * @param {object} values
   * @returns {object} plotLines
   */
  const getPlotLines = (plotLines, values) => {
    plotLines.forEach((plotObj) => {
      if (plotLineNames.includes(plotObj.name)) {
        const name = plotLineNames[plotLineNames.indexOf(plotObj.name)];
        plotObj.width = values[name] ? 1 : 0;
        plotObj.label.style.color = values[name] ? "red" : "transparent";
      }
    });
    return plotLines;
  };

  /**
   * Set the min and max range values
   *
   * @param {string} value The selected option value
   */
  const setMinMaxValues = (value) => {
    const testData = currentChart.userOptions.testData;
    const range = {
      minMax: () => {
        return {
          min_range: testData.min_result,
          max_range: testData.max_result,
        };
      },
      dataAndLimits: () => {
        return {
          min_range: Math.min(testData.min_result, testData.lo_limit),
          max_range: Math.max(testData.max_result, testData.hi_limit),
        };
      },
      robustLimits: () => {
        return {
          min_range: testData.robust_lo_limit,
          max_range: testData.robust_hi_limit,
        };
      },
      testLimits: () => {
        return {
          min_range: testData.lo_limit,
          max_range: testData.hi_limit,
        };
      },
    };

    form.setFieldsValue((range[value] || range.minMax)());
  };

  /**
   * Handles the event where a field changes in the form
   *
   * @param {array} changedFields
   */
  const handleFieldsChange = (changedFields) => {
    if (changedFields[0].name.includes("data_range")) {
      setMinMaxValues(changedFields[0].value);
    }
  };

  /**
   * Apply the selected chart options
   */
  const applyChartOptions = () => {
    const options = currentChart.userOptions;
    const xAxis = options.xAxis[0];
    const yAxis = options.yAxis[0];
    const values = form.getFieldsValue(true);

    const newOptions = {
      title: {
        text: values.chart_title,
      },
      subtitle: {
        text: values.chart_subtitle,
      },
      // Let's make the x-axis definition an array like the y-axis
      // since there might be charts that require multiple x-axis
      xAxis: [
        {
          title: {
            text: values.x_axis_title ?? "",
          },
          labels: {
            rotation: values.x_axis_label_angle,
          },
          gridLineWidth: values.x_axis_gridlines ? 1 : 0,
        },
      ],
      yAxis: [
        {
          title: {
            text: values.y_axis_title,
          },
          type: values.y_axis_log_scale ? "logarithmic" : "linear",
          gridLineWidth: values.y_axis_gridlines ? 1 : 0,
        },
        {
          title: {
            text: values.y_axis2_title,
          },
          gridLineWidth: values.y_axis_gridlines ? 1 : 0,
        },
      ],
    };

    // The data range
    if (typeof yAxis.min !== "undefined" && typeof yAxis.max !== "undefined") {
      newOptions.yAxis[0].min = values.min_range;
      newOptions.yAxis[0].max = values.max_range;
    } else if (
      typeof xAxis.min !== "undefined" &&
      typeof xAxis.max !== "undefined"
    ) {
      newOptions.xAxis[0].min = values.min_range;
      newOptions.xAxis[0].max = values.max_range;
      if (
        typeof options.name !== "undefined" &&
        options.name === "curve_histogram"
      ) {
        newOptions.xAxis[2] = {
          min: values.min_range,
          max: values.max_range,
        };
      }
    }

    // Toggling of bell curve and changing the color set in the series
    newOptions.series = [];
    options.series.forEach((series, idx) => {
      const seriesProps = {};
      if (series.type === "bellcurve") {
        seriesProps.visible = values.bell_curve;
        seriesProps.showInLegend = values.bell_curve;
      }
      if (legendColors[idx]) {
        seriesProps.color = legendColors[idx];
      }
      newOptions.series.push(seriesProps);
    });

    // Limit marks
    if (Array.isArray(xAxis.plotLines)) {
      newOptions.xAxis[0].plotLines = getPlotLines(
        [...options.xAxis[0].plotLines],
        values,
      );
    } else if (Array.isArray(yAxis.plotLines)) {
      newOptions.yAxis[0].plotLines = getPlotLines(
        [...options.yAxis[0].plotLines],
        values,
      );
    }
    // Sets the colors from the options
    newOptions.colors = legendColors;

    currentChart.update(newOptions);
    setIsChartOptionsOpen(false);

    if (options.chartKey) {
      sessionStorage.setItem(options.chartKey, JSON.stringify(newOptions));
    }
  };

  /**
   * Closes the modal
   */
  const cancelChartOptions = () => {
    setIsChartOptionsOpen(false);
  };

  /**
   * Executes after the modal closes
   */
  const handleAfterCloseModal = () => {
    form.resetFields();
  };

  // console.log("UserOptions:", currentChart.userOptions);
  return (
    <Modal
      width={"40vw"}
      title="Chart Options"
      open={open}
      onOk={applyChartOptions}
      onCancel={cancelChartOptions}
      afterClose={handleAfterCloseModal}
      destroyOnClose
    >
      <Form
        form={form}
        initialValues={{ x_axis_label_angle: 0 }}
        onFieldsChange={handleFieldsChange}
        className="mt-3"
      >
        <Form.Item name="chart_title" label="Chart Title">
          <Input></Input>
        </Form.Item>
        <Form.Item name="chart_subtitle" label="Chart Subtitle">
          <TextArea rows={2}></TextArea>
        </Form.Item>
        <Divider orientation="left">Primary X-Axis</Divider>
        <Flex gap="large">
          <Form.Item name="x_axis_title" label="Axis Title">
            <Input disabled={isXAxisTitleDisabled}></Input>
          </Form.Item>
          <Form.Item name="x_axis_gridlines" label="Gridlines">
            <Switch></Switch>
          </Form.Item>
          <Form.Item name="x_axis_label_angle" label="Label Angle">
            <InputNumber />
          </Form.Item>
        </Flex>
        <Divider orientation="left">Primary Y-Axis</Divider>
        <Flex gap="large">
          <Form.Item name="y_axis_title" label="Axis Title">
            <Input disabled={isYAxisTitleDisabled}></Input>
          </Form.Item>
          <Form.Item name="y_axis_gridlines" label="Gridlines">
            <Switch></Switch>
          </Form.Item>
          <Form.Item name="y_axis_log_scale" label="Log Scale">
            <Switch></Switch>
          </Form.Item>
          <Form.Item name="bell_curve" label="Bell Curve">
            <Switch disabled={isBellCurveDisabled}></Switch>
          </Form.Item>
        </Flex>
        <Divider orientation="left">Secondary Y-Axis</Divider>
        <Flex gap="large">
          <Form.Item name="y_axis2_title" label="Axis Title">
            <Input disabled={isYAxis2TitleDisabled}></Input>
          </Form.Item>
        </Flex>
        <Divider orientation="left">Plot Lines</Divider>
        <Flex wrap="wrap" gap={"large"}>
          <Form.Item name="test_limits" label="Test Limits">
            <Switch disabled={isPlotLinesDisabled}></Switch>
          </Form.Item>
          <Form.Item name="robust_limits" label="Robust Limits">
            <Switch disabled={isPlotLinesDisabled}></Switch>
          </Form.Item>
          <Form.Item name="mean" label="Mean">
            <Switch disabled={isPlotLinesDisabled}></Switch>
          </Form.Item>
        </Flex>
        <Divider orientation="left">Data</Divider>
        <Flex wrap="wrap" gap={"large"}>
          <Form.Item label="Data Range" name="data_range" className="w-1/3">
            <Select
              showSearch
              placeholder="Select range"
              options={dataRangeOptions}
              disabled={isDataRangeSelectDisabled}
            />
          </Form.Item>
          <Form.Item name="min_range" label="Min">
            <InputNumber disabled={isDataRangeSelectDisabled}></InputNumber>
          </Form.Item>
          <Form.Item name="max_range" label="Max">
            <InputNumber disabled={isDataRangeSelectDisabled}></InputNumber>
          </Form.Item>
        </Flex>
        <Divider orientation="left">Color</Divider>
        <LegendColorPicker
          form={form}
          chartOptionModalOpen={open}
          legendColors={legendColors}
          setLegendColors={setLegendColors}
          disabled={isLegendOptionsDisabled}
        ></LegendColorPicker>
      </Form>
    </Modal>
  );
};

export default ChartOptionsModal;
