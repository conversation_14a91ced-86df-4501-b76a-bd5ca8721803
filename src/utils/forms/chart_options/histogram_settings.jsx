"use client";

import { Form, Radio } from "antd";
import { useState } from "react";
import IntegerStep from "./integer_step";

/**
 * Histogram settings component of chart options
 *
 * @param {object} form
 * @param {function} handleFieldsChange
 * @param {boolean} hasBinSpacing
 * @returns {JSX.Element}
 */
const HistogramSettings = ({
  form,
  handleFieldsChange,
  hasBinSpacing = true,
}) => {
  const [isBinCountDisabled, setIsBinCountDisabled] = useState(false);
  const [isBinWidthDisabled, setIsBinWidthDisabled] = useState(true);
  const [isBinSpacingDisabled, setIsBinSpacingDisabled] = useState(true);

  /**
   * Handle the bin count, width and spacing inputs when use toggles bin control selection
   */
  const handleBinControlChange = () => {
    const binControl = form.getFieldValue("bin_control");
    setIsBinCountDisabled(binControl !== "bin_count");
    setIsBinWidthDisabled(binControl !== "bin_width");
    setIsBinSpacingDisabled(binControl !== "bin_spacing");
  };

  return (
    <Form.Item name="bin_control" className="mb-2 mt-2 w-full">
      <Radio.Group
        className="flex flex-col gap-y-2"
        onChange={handleBinControlChange}
      >
        <Radio value="bin_count">
          <IntegerStep
            inputName="bin_count"
            label="Bin Count"
            form={form}
            handleFieldsChange={handleFieldsChange}
            isVertical={false}
            className="w-full"
            isDisabled={isBinCountDisabled}
            min={1}
            max={60}
            align="center"
          ></IntegerStep>
        </Radio>
        <Radio value="bin_width">
          <IntegerStep
            inputName="bin_width"
            label="Bin Width"
            form={form}
            handleFieldsChange={handleFieldsChange}
            isVertical={false}
            className="w-full"
            isDisabled={isBinWidthDisabled}
            align="center"
          ></IntegerStep>
        </Radio>
        {hasBinSpacing && (
          <Radio value="bin_spacing" className="!hidden">
            <IntegerStep
              inputName="bin_spacing"
              label="Spacing Between Bars"
              form={form}
              handleFieldsChange={handleFieldsChange}
              isVertical={false}
              className="w-full"
              isDisabled={isBinSpacingDisabled}
              min={0}
              max={1}
              step={0.1}
              align="center"
            ></IntegerStep>
          </Radio>
        )}
      </Radio.Group>
    </Form.Item>
  );
};

export default HistogramSettings;
