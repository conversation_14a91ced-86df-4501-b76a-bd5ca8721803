"use client";

import { Checkbox, Col, Form, Row, Typography } from "antd";
const { Text } = Typography;

const stats = [
  {
    value: "mean",
    label: "Mean",
  },
  {
    value: "cp",
    label: "Cp",
  },
  {
    value: "median",
    label: "Median",
  },
  {
    value: "cpu",
    label: "Cpu",
  },
  {
    value: "stdev",
    label: "Standard Deviation",
  },
  {
    value: "cpl",
    label: "Cpl",
  },
  {
    value: "min_result",
    label: "Min Result",
  },
  {
    value: "cpk",
    label: "Cpk",
  },
  {
    value: "max_result",
    label: "Max Result",
  },
  {
    value: "outlier_count",
    label: "Outlier Count",
  },
  {
    value: "execs",
    label: "Execs Overall",
  },
  {
    value: "lolim",
    label: "Low Limit",
  },
  {
    value: "fails_overall",
    label: "Fails Overall",
  },
  {
    value: "hilim",
    label: "High Limit",
  },
  {
    value: "pfails_unit",
    label: "PFails Unit",
  },
  {
    value: "iqr_lolim",
    label: "Robust Low Limit",
  },
  {
    value: "iqr_hilim",
    label: "Robust High Limit",
  },
  {
    value: "pfails_execs",
    label: "PFails Execs",
  },
  {
    value: "kurtosis",
    label: "Kurtosis",
  },
  {
    value: "skewness",
    label: "Skewness",
  },
];

/**
 * Stats below the chart component
 *
 * @param {object} form
 * @returns {JSX.Element}
 */
const StatsBelowChart = ({ form }) => {
  /**
   * When user clicks the select all checkbox
   *
   * @param {object} evt
   */
  const handleChange = (evt) => {
    form.setFieldsValue({
      stats: evt.target.checked ? stats.map((stat) => stat.value) : [],
    });
  };

  return (
    <>
      <Checkbox className="mb-2" onChange={handleChange}>
        <Text strong>Select All</Text>
      </Checkbox>
      <Form.Item name="stats" noStyle>
        <Checkbox.Group className="w-full">
          <Row gutter={[0, 8]}>
            {stats.map((stat) => {
              return (
                <Col span={12} key={stat.value}>
                  <Checkbox value={stat.value}>{stat.label}</Checkbox>
                </Col>
              );
            })}
          </Row>
        </Checkbox.Group>
      </Form.Item>
    </>
  );
};

export default StatsBelowChart;
