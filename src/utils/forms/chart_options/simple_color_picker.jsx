"use client";

import { ColorPicker, Form, Flex, Input, Typography } from "antd";
import { blue, green, presetPalettes, red } from "@ant-design/colors";
import { useEffect, useState } from "react";
const { Text } = Typography;

/**
 * Generate preset colors
 *
 * @param {object} presets
 * @returns {array}
 */
const genPresetColors = (presets = presetPalettes) =>
  Object.entries(presets).map(([label, colors]) => ({
    label,
    colors,
  }));

const presets = genPresetColors({
  blue,
  red,
  green,
});

/**
 * Simple color picker component
 *
 * @param {string} inputName
 * @param {string} label
 * @param {object} form
 * @param {function} handleFieldsChange
 * @param {boolean} isDisabled
 * @returns {JSX.Element}
 */
const SimpleColorPicker = ({
  inputName,
  label,
  form,
  handleFieldsChange,
  isDisabled = false,
  ...props
}) => {
  const [colorValue, setColorValue] = useState("#ffffff");

  useEffect(() => {
    setColorValue(form.getFieldValue(inputName) ?? null);
  }, []);

  /**
   * When the color selection changes
   *
   * @param {object} color
   */
  const handleColorSelectionChange = (color) => {
    const value = {};
    value[inputName] = color.toHexString();
    form.setFieldsValue(value);
    setColorValue(color.toHexString());
    handleFieldsChange();
  };

  return (
    <Flex align="center" {...props}>
      <Text>{label ?? "Line Color:"}</Text>
      <Form.Item hidden name={inputName} noStyle>
        <Input></Input>
      </Form.Item>
      <ColorPicker
        className="ml-1"
        onChange={handleColorSelectionChange}
        value={colorValue}
        presets={presets}
        trigger="hover"
        disabled={isDisabled}
      ></ColorPicker>
    </Flex>
  );
};

export default SimpleColorPicker;
