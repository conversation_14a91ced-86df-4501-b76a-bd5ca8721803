"use client";

import { Divider, Form, Flex, Input, Switch, Typography } from "antd";
import { useEffect } from "react";
import IntegerStep from "./integer_step";
import FontSettings from "./font_settings";
const { Text } = Typography;

/**
 * Axis settings component of chart options
 *
 * @param {string} axis
 * @param {object} form
 * @param {boolean} hasConvertToLog
 * @param {boolean} hasLogScale
 * @param {boolean} logScaleIsDisabled
 * @param {function} handleFieldsChange
 * @param {string} title
 * @returns {JSX.Element}
 */
const AxisSettings = ({
  axis = "x",
  form,
  hasConvertToLog = false,
  hasLogScale = false,
  logScaleIsDisabled = false,
  handleFieldsChange,
  title = "X-Axis",
}) => {
  useEffect(() => {
    // Uncheck the switch if log scale is disabled
    if (logScaleIsDisabled) {
      const value = {};
      value[`${axis}_axis_log_scale`] = !logScaleIsDisabled;
      form.setFieldsValue(value);
    }
  }, [logScaleIsDisabled]);

  return (
    <>
      <Divider orientation="left">{title}</Divider>
      <Flex justify="space-between" gap={8}>
        <Flex className="w-1/2" align="center">
          <Text>Show Label:</Text>
          <Form.Item name={`show_${axis}_axis_label`} label=" ">
            <Switch className="ml-1"></Switch>
          </Form.Item>
        </Flex>
        <Form.Item
          name={`${axis}_axis_title`}
          label={`${title} Label:`}
          className="w-3/5"
        >
          <Input></Input>
        </Form.Item>
      </Flex>
      <FontSettings target={`${axis}_axis`}></FontSettings>
      <IntegerStep
        label="Label Angle"
        inputName={`${axis}_axis_label_angle`}
        form={form}
        handleFieldsChange={handleFieldsChange}
        className="w-full"
      ></IntegerStep>
      <Flex justify={"space-between"} gap={8} className="mt-2 mb-2">
        <Flex className="w-1/2">
          <Text>Gridlines:</Text>
          <Form.Item name={`${axis}_axis_gridlines`} noStyle>
            <Switch className="ml-1"></Switch>
          </Form.Item>
        </Flex>
        {hasLogScale && (
          <Flex>
            <Text>Log Scale:</Text>
            <Form.Item name={`${axis}_axis_log_scale`} noStyle>
              <Switch disabled={logScaleIsDisabled} className="ml-1"></Switch>
            </Form.Item>
          </Flex>
        )}
        {hasConvertToLog && (
          <Flex>
            <Text>Convert to Log:</Text>
            <Form.Item name={`${axis}_axis_to_log`} noStyle>
              <Switch className="ml-1"></Switch>
            </Form.Item>
          </Flex>
        )}
      </Flex>
    </>
  );
};

export default AxisSettings;
