"use client";

import { Flex, Form, InputNumber, Typography } from "antd";
import LineStyleSelect from "./line_style_select";
const { Text } = Typography;

/**
 * Line settings component of chart options
 *
 * @returns {JSX.Element}
 */
const LineSettings = () => {
  return (
    <Flex justify="space-between" wrap gap={8}>
      <Flex align="center">
        <Text>Line Thickness:</Text>
        <Form.Item name="line_width" noStyle>
          <InputNumber className="ml-1"></InputNumber>
        </Form.Item>
      </Flex>
      <LineStyleSelect inputName="line_style"></LineStyleSelect>
    </Flex>
  );
};

export default LineSettings;
