"use client";

import { Flex, Form, InputN<PERSON>ber, <PERSON>lider, Switch, Typography } from "antd";
const { Text } = Typography;

/**
 * Normal probability settings component of chart options
 *
 * @returns {JSX.Element}
 */
const NormalProbabilitySettings = () => {
  return (
    <Flex wrap gap={8} justify="space-between">
      <Flex className="w-1/3" align="center">
        <Text>Show Line:</Text>
        <Form.Item name="show_normal_probability_line" noStyle>
          <Switch className="ml-1"></Switch>
        </Form.Item>
      </Flex>
      <Flex className="" align="center">
        <Text>Line Thickness:</Text>
        <Form.Item name="normal_probability_line_width" noStyle>
          <InputNumber className="ml-1"></InputNumber>
        </Form.Item>
      </Flex>
      <Form.Item
        name="normal_probability_line_transparency"
        label="Transparency"
        className="w-1/2"
      >
        <Slider min={0} max={1} step={0.1} />
      </Form.Item>
    </Flex>
  );
};

export default NormalProbabilitySettings;
