"use client";

import { Divider, Form, Flex, InputNumber, Select, Typography } from "antd";
const { Text } = Typography;

const rangeOptions = [
  {
    value: "dataAndLimits",
    label: "Data and Limits",
  },
  {
    value: "minMax",
    label: "Data Min & Max",
  },
  {
    value: "testLimits",
    label: "Test Limits",
  },
  {
    value: "pct20Limits",
    label: "-/+ 20% of Test Limits",
  },
  {
    value: "robustLimits",
    label: "Robust Limits",
  },
];

/**
 * Axis range component of chart options
 *
 * @param {string} axis
 * @param {boolean} isTestResults
 * @param {string} title
 * @param {fuction} setMinMaxValues
 * @returns {JSX.Element}
 */
const AxisRange = ({
  axis = "x",
  isTestResults = false,
  title = "X-Axis Range",
  setMinMaxValues,
}) => {
  return (
    <>
      <Divider orientation="left">{title}</Divider>
      {isTestResults ? (
        <Flex vertical>
          <Form.Item name={`${axis}_axis_range`} label="">
            <Select
              showSearch
              placeholder="Select range"
              options={rangeOptions}
              onChange={setMinMaxValues}
            ></Select>
          </Form.Item>
          <Flex justify="space-between" gap={8}>
            <Flex align="center">
              <Text className="mr-1">Minimum:</Text>
              <Form.Item name={`${axis}_min_range`} noStyle>
                <InputNumber className="w-1/3 grow"></InputNumber>
              </Form.Item>
            </Flex>
            <Flex align="center">
              <Text className="mr-1">Maximum:</Text>
              <Form.Item name={`${axis}_max_range`} noStyle>
                <InputNumber className="w-1/3 grow"></InputNumber>
              </Form.Item>
            </Flex>
          </Flex>
        </Flex>
      ) : (
        <Flex justify="space-between" gap={8} className="mb-2">
          <Flex align="center">
            <Text className="mr-1">Minimum:</Text>
            <Form.Item name={`${axis}_min_range`} noStyle>
              <InputNumber className="w-1/3 grow"></InputNumber>
            </Form.Item>
          </Flex>
          <Flex align="center">
            <Text className="mr-1">Maximum:</Text>
            <Form.Item name={`${axis}_max_range`} noStyle>
              <InputNumber className="w-1/3 grow"></InputNumber>
            </Form.Item>
          </Flex>
        </Flex>
      )}
    </>
  );
};

export default AxisRange;
