"use client";

import {
  <PERSON>pp,
  But<PERSON>,
  Checkbox,
  <PERSON>lapse,
  Divider,
  Flex,
  Form,
  Input,
  Modal,
  Radio,
  Select,
  theme,
  Tooltip,
  Typography,
} from "antd";
import {
  CloseOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import { useCallback, useEffect, useState } from "react";
import { debounce, isEqual, merge } from "lodash";
import { useBoundStore } from "../../../store/store";
import ChartHelper from "../../charts/chart_helper";
import Api from "../../api";
import LegendColorPicker from "./legend_color_picker";
import AxisSettings from "./axis_settings";
import AxisRange from "./axis_range";
import BarSettings from "./bar_settings";
import LineSettings from "./line_settings";
import HistogramSettings from "./histogram_settings";
import NormalProbabilitySettings from "./normal_probability_settings";
import BoxPlotSettings from "./boxplot_settings";
import ScatterSettings from "./scatter_settings";
import StatsBelowChart from "./stats_below_chart";
import FontSettings from "./font_settings";
import PlotLineSettings from "./plot_line_settings";
import SaveChartSettingsModal from "./save_chart_settings_modal";
import DeleteChartSettingsModal from "./delete_chart_settings_modal";

const { Title } = Typography;
const { TextArea } = Input;
const sigmaLines = ["1s", "2s", "3s", "6s"];
const plotLineNames = [
  "mean",
  "test_limits",
  "robust_limits",
  "1s",
  "2s",
  "3s",
  "6s",
];
const excludeFormItems = ["legend"];

/**
 * Chart options drawer component
 *
 * @param {boolean} open Set to true to open the drawer, false otherwise
 * @returns {JSX.Element}
 */
const ChartOptionsDrawer = ({ open }) => {
  const [form] = Form.useForm();
  const [loadSettingsForm] = Form.useForm();
  const { token } = theme.useToken();
  const [{ confirm }, contextHolder] = Modal.useModal();
  const { message } = App.useApp();
  const panelStyle = {
    background: token.yhHeaderColorBg,
  };
  const currentChart = useBoundStore((state) => state.currentChart);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const [chartName, setChartName] = useState(
    currentChart.userOptions?.yhDefinedConfig?.name ?? "",
  );
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const [activeCollapseKey, setActiveCollapseKey] = useState([
    "axisSettings",
    "histogramSettings",
    "barSettings",
    "lineSettings",
    "boxPlotSettings",
    "scatterSettings",
    "plotLines",
    // "stats",
    "other",
  ]);
  const [appliedValues, setAppliedValues] = useState({});
  const [legendColors, setLegendColors] = useState({});
  const [logScaleIsDisabled, setLogScaleIsDisabled] = useState(false);
  const [logScaleAxis, setLogScaleAxis] = useState("");
  const [convertToLogAxis, setConverToLogAxis] = useState("");
  const [hasBellCurve, setHasBellCurve] = useState(false);
  const [showLegendPicker, setShowLegendPicker] = useState(true);
  const [savedSettingsOptions, setSavedSettingsOptions] = useState([]);
  const [saveSettingsModalOpen, setSaveSettingsModalOpen] = useState(false);
  const [deleteSettingsModalOpen, setDeleteSettingsModalOpen] = useState(false);
  const [chartOptionSections, setChartOptionSections] = useState([]);
  const [newChartOptions, setNewChartOptions] = useState({});
  // const [currentChartId, setCurrentChartId] = useState(
  // currentChart?.container?.id,
  // );

  useEffect(() => {
    if (open) {
      // Get the list of saved settings
      getSavedChartSettingsList();

      // Set the applicable sections of the current chart
      const userOptions = currentChart.userOptions;
      const chartOptionSections = [];
      if (userOptions?.yhDefinedConfig?.noAxisSettings !== true) {
        chartOptionSections.push(getAxisSettings());
      }
      if (isHistogram(userOptions)) {
        chartOptionSections.push(getHistogramSettings());
      }
      if (
        userOptions?.yhDefinedConfig?.noBarSettings !== true &&
        (userOptions?.chart?.type === "column" ||
          userOptions?.chart?.type === "bar")
      ) {
        chartOptionSections.push(getBarSettings());
      }
      if (hasLineSeries(userOptions)) {
        chartOptionSections.push(getLineSettings());
      }
      if (userOptions?.chart?.type === "boxplot") {
        chartOptionSections.push(getBoxPlotSettings());
      }
      if (userOptions?.yhDefinedConfig?.hasStatsInfo) {
        chartOptionSections.push(getPlotLineSettings());
        chartOptionSections.push(getStatsBelowChartSettings());
      }
      if (userOptions?.yhDefinedConfig?.class === "normalProbability") {
        chartOptionSections.push(getNormalProbabilitySettings());
      }
      if (hasScatterSeries(userOptions)) {
        chartOptionSections.push(getScatterSettings());
      }
      chartOptionSections.push(getOtherSettings());
      setChartOptionSections(chartOptionSections);
      setLogScaleAxis(userOptions?.yhDefinedConfig?.logScaleAxis ?? "");
      setLogScaleOptionState(currentChart);
      setConverToLogAxis(userOptions?.yhDefinedConfig?.convertToLogAxis ?? "");
      setShowLegendPicker(
        typeof userOptions?.yhDefinedConfig?.default
          ?.show_legend_color_picker !== "undefined"
          ? userOptions.yhDefinedConfig.default.show_legend_color_picker
          : true,
      );

      // Get the chart settings and populate the form fields
      const values = getValuesFromChartSettings();
      setAppliedValues(values);
      form.setFieldsValue(values);
    }
  }, [open, chartName]);

  useEffect(() => {
    if (open) {
      handleFieldsChange();
    }
  }, [legendColors]);

  // useEffect(() => {
  //   if (
  //     currentChart?.container?.id !== undefined &&
  //     currentChartId !== undefined &&
  //     currentChartId !== currentChart?.container?.id
  //   ) {
  //     checkCloseConfirm();
  //   }
  //   setCurrentChartId(currentChart?.container?.id);
  // }, [currentChart?.container?.id]);

  /**
   * Checks if chart has line series
   *
   * @param {object} userOptions
   * @returns {boolean}
   */
  const hasLineSeries = (userOptions) => {
    return userOptions?.series.some((seriesObj) => seriesObj?.type === "line");
  };

  /**
   * Checks if chart has scatter series
   *
   * @param {object} userOptions
   * @returns {boolean}
   */
  const hasScatterSeries = (userOptions) => {
    return userOptions?.series.some(
      (seriesObj) =>
        seriesObj.type === "scatter" && seriesObj.visible !== false,
    );
  };

  /**
   * Sets the state of the log scale option, i.e. disabled or not
   *
   * @param {object} currentChart
   */
  const setLogScaleOptionState = (currentChart) => {
    const logScaleAxis =
      currentChart.userOptions?.yhDefinedConfig?.logScaleAxis;
    if (logScaleAxis) {
      const minMax = form.getFieldsValue([
        `${logScaleAxis}_min_range`,
        `${logScaleAxis}_max_range`,
      ]);
      setLogScaleIsDisabled(
        minMax[`${logScaleAxis}_min_range`] === 0 ||
          minMax[`${logScaleAxis}_max_range`] === 0,
      );
    } else {
      setLogScaleIsDisabled(true);
    }
  };

  /**
   * Checks if the current chart is a histogram
   *
   * @param {object} userOptions
   * @returns {boolean}
   */
  const isHistogram = (userOptions) => {
    return userOptions.name?.includes("histogram");
  };

  /**
   * Checks if the current chart is a curve histogram
   *
   * @param {object} userOptions
   * @returns {boolean}
   */
  const isCurveHistogram = (userOptions) => {
    return (
      userOptions.name === "curve_histogram" ||
      (userOptions.series[0].type === "histogram" &&
        userOptions?.series[3]?.type === "spline")
    );
  };

  /**
   * Checks if the y2 axis range is independent of the y1 axis range.
   * Currently, only the normal probability chart has a dependent y2 axis range.
   *
   * @param {object} userOptions
   * @returns {boolean}
   */
  const y2AxisRangeIsIndependent = (userOptions) => {
    const chartClassesWithDependentY2AxisRange = ["normalProbability"];
    return !chartClassesWithDependentY2AxisRange.includes(
      userOptions?.yhDefinedConfig?.class,
    );
  };

  /**
   * Get the axis that plots the results
   *
   * @param {object} userOptions
   * @returns {object} axis
   */
  const getResultsAxis = (userOptions) => {
    let axis = userOptions.yAxis[0];
    if (isCurveHistogram(userOptions)) {
      axis = userOptions.xAxis[2];
    } else if (userOptions?.yhDefinedConfig?.resultsAxis === "x") {
      axis = userOptions.xAxis[0];
    } else if (userOptions?.yhDefinedConfig?.resultsAxis === "y") {
      axis = userOptions.yAxis[0];
    } else if (userOptions?.yhDefinedConfig?.resultsAxis === "y2") {
      axis = userOptions.yAxis[1];
    }

    return axis;
  };

  /**
   * The default values of chart
   *
   * @returns {object} settings
   */
  const getDefaultValues = () => {
    const userOptions = currentChart.userOptions;
    const defaultSettings = currentChart.userOptions.yhDefinedConfig?.default;
    const settings = {
      chart_title: defaultSettings.title ?? "",
      chart_title_font: "Arial",
      chart_title_font_size: 14,
      chart_subtitle: defaultSettings.subtitle ?? "",
      chart_subtitle_font: "Arial",
      chart_subtitle_font_size: 12,
      show_tooltip: true,
    };
    if (userOptions?.yhDefinedConfig?.noAxisSettings !== true) {
      settings.show_x_axis_label = true;
      settings.show_y_axis_label = true;
      settings.x_axis_title = defaultSettings?.x?.title ?? "";
      settings.x_axis_label_angle = 300;
      settings.x_axis_gridlines = 0;
      settings.x_axis_font = "Arial";
      settings.x_axis_font_size = 12;
      settings.x_min_range = defaultSettings?.x?.min ?? null;
      settings.x_max_range = defaultSettings?.x?.max ?? null;
      settings.y_axis_title = defaultSettings?.y?.title ?? "";
      settings.y_axis_label_angle = 0;
      settings.y_axis_font = "Arial";
      settings.y_axis_font_size = 12;
      settings.y_min_range = defaultSettings?.y?.min ?? null;
      settings.y_max_range = defaultSettings?.y?.max ?? null;
      settings.y_axis_gridlines = 1;
    }
    if (userOptions?.yhDefinedConfig?.resultsAxis) {
      settings.sigma_set = [];
      plotLineNames.forEach((lineName) => {
        if (sigmaLines.includes(lineName)) {
          if (!settings.sigma_set.includes(lineName)) {
            settings.sigma_set.push(lineName);
          }
          settings[lineName] = false;
          settings[`${lineName}_style`] = "ShortDash";
          settings[`${lineName}_width`] = 0;
          settings[`${lineName}_color`] = "#ff4d4f";
          settings[`${lineName}_value`] = true;
        } else {
          settings[lineName] = true;
          settings[`${lineName}_style`] = "ShortDash";
          settings[`${lineName}_width`] = 1;
          settings[`${lineName}_color`] = "#ff4d4f";
          settings[`${lineName}_value`] = true;
        }
      });
      settings[`${userOptions.yhDefinedConfig.resultsAxis}_axis_range`] =
        userOptions?.yhDefinedConfig?.resultsAxisRange ?? "dataAndLimits";
    }
    // If has secondary y-axis
    if (userOptions.yAxis[1] && userOptions.yAxis[1].visible !== false) {
      settings.y2_axis_title = defaultSettings?.y2?.title ?? "";
      settings.y2_axis_gridlines = 0;
      settings.y2_min_range = defaultSettings?.y2?.min ?? null;
      settings.y2_max_range = defaultSettings?.y2?.max ?? null;
      settings.y2_axis_font = "Arial";
      settings.y2_axis_font_size = 12;
      settings.y2_axis_label_angle = 0;
      settings.show_y2_axis_label = true;
    }
    // If has log scale option
    const logScaleAxis = userOptions?.yhDefinedConfig?.logScaleAxis;
    if (logScaleAxis === "y") {
      settings.y_axis_log_scale = true;
    } else if (logScaleAxis === "x") {
      settings.x_axis_log_scale = true;
    } else if (logScaleAxis === "y2") {
      settings.x_axis_log_scale = true;
    }
    // If has convert to log option
    if (userOptions?.yhDefinedConfig?.convertToLogAxis === "x") {
      settings.x_axis_to_log = true;
    } else if (userOptions?.yhDefinedConfig?.convertToLogAxis === "y") {
      settings.y_axis_to_log = true;
    }
    // Bell curve settings
    const bellCurveSeries = userOptions?.series?.find(
      (seriesProp) => seriesProp.type === "bellcurve",
    );
    if (bellCurveSeries) {
      settings.bell_curve = false;
      settings.bell_curve_color = "#95de64";
    }
    // Histogram settings
    if (isHistogram(userOptions)) {
      settings.bin_count = 50;
      settings.bin_control = "bin_count";
    }
    // Bar/column settings
    if (
      userOptions?.yhDefinedConfig?.noBarSettings !== true &&
      (userOptions?.chart?.type === "column" ||
        userOptions?.chart?.type === "bar")
    ) {
      settings.bar_orientation = "column";
      settings.bar_spacing = 0.1;
      settings.bar_control = "bar_spacing";
    }
    if (userOptions?.chart?.type === "boxplot") {
      const boxplotSeries = currentChart.series.find(
        (seriesObj) => seriesObj.initialType === "boxplot",
      );
      settings.boxplot_median_color = boxplotSeries?.color;
      settings.boxplot_box_color = "#ffffff";
      settings.whisker_width = 1;
      settings.boxplot_box_transparency = 0;
    }
    // If has line series
    const lineSeriesIndex = userOptions?.series.findIndex(
      (seriesObj) => seriesObj.type === "line",
    );
    if (lineSeriesIndex !== -1) {
      settings.line_width = 1;
      settings.line_style = "Solid";
    }
    // If has scatter series
    const scatterSeriesIndex = userOptions?.series.findIndex(
      (seriesObj) =>
        seriesObj.type === "scatter" && seriesObj.visible !== false,
    );
    if (scatterSeriesIndex !== -1) {
      settings.scatter_symbol = "circle";
      settings.scatter_size = 3;
      settings.scatter_transparency = 0;
    }
    settings.legend_pos = "bottom";

    return settings;
  };

  /**
   * Get the form values equivalent from chart settings
   *
   * @returns {object} settings
   */
  const getValuesFromChartSettings = () => {
    const userOptions = currentChart.userOptions;
    setChartName(userOptions?.yhDefinedConfig?.name ?? "");
    const xExtremes = currentChart.xAxis[0].getExtremes();
    const yExtremes = currentChart.yAxis[0].getExtremes();
    const xAxis = isCurveHistogram(userOptions)
      ? userOptions.xAxis[2]
      : userOptions.xAxis[0];
    const settings = {
      chart_title: userOptions.title.text ?? "",
      chart_title_font: userOptions.title?.style?.fontFamily ?? "Arial",
      chart_title_font_size: userOptions.title?.style?.fontSize
        ? userOptions.title.style.fontSize.replace("px", "")
        : 14,
      chart_subtitle: userOptions.subtitle?.text ?? "",
      chart_subtitle_font: userOptions?.subtitle?.style?.fontFamily ?? "Arial",
      chart_subtitle_font_size: userOptions?.subtitle?.style?.fontSize
        ? userOptions.subtitle.style.fontSize.replace("px", "")
        : 12,
      show_tooltip:
        typeof userOptions.tooltip?.enabled !== "undefined"
          ? userOptions.tooltip.enabled
          : true,
    };

    if (userOptions?.yhDefinedConfig?.noAxisSettings !== true) {
      settings.show_x_axis_label = true;
      settings.show_y_axis_label = true;
      settings.x_axis_title = userOptions.xAxis[0].title?.text ?? "";
      settings.x_axis_label_angle = xAxis?.labels?.rotation ?? 0;
      settings.x_axis_gridlines = userOptions.xAxis[0]?.gridLineWidth ? 1 : 0;
      settings.x_axis_font =
        userOptions.xAxis[0].title?.style?.fontFamily ?? "Arial";
      settings.x_axis_font_size = userOptions.xAxis[0].title?.style?.fontSize
        ? userOptions.xAxis[0].title.style.fontSize.replace("px", "")
        : 12;
      settings.x_min_range = xExtremes.min;
      settings.x_max_range = xExtremes.max;
      settings.y_axis_title = userOptions.yAxis[0].title?.text ?? "";
      settings.y_axis_label_angle = userOptions.yAxis[0]?.labels?.rotation ?? 0;
      settings.y_axis_font =
        userOptions.yAxis[0].title?.style?.fontFamily ?? "Arial";
      settings.y_axis_font_size = userOptions.yAxis[0].title?.style?.fontSize
        ? userOptions.yAxis[0].title.style.fontSize.replace("px", "")
        : 12;
      settings.y_min_range = yExtremes.min;
      settings.y_max_range = yExtremes.max;
      settings.y_axis_gridlines = userOptions.yAxis[0].gridLineWidth ? 1 : 0;
    }

    if (userOptions?.yhDefinedConfig?.resultsAxis) {
      settings.sigma_set = [];
      const plotLines = getResultsAxis(userOptions)?.plotLines ?? [];
      plotLines.forEach((plotObj) => {
        let lineName = plotObj.name;
        const showLine =
          plotObj.color !== "transparent" &&
          plotObj.label.style.color !== "transparent";
        if (sigmaLines.includes(plotObj.name)) {
          lineName = "sigma";
          if (!settings.sigma_set.includes(plotObj.name) && showLine) {
            settings.sigma_set.push(plotObj.name);
          }
        }
        settings[lineName] = showLine;
        settings[`${lineName}_style`] = plotObj.dashStyle;
        settings[`${lineName}_width`] = plotObj.width;
        settings[`${lineName}_color`] = plotObj.color;
        settings[`${lineName}_value`] = true;
      });
      settings[`${userOptions.yhDefinedConfig.resultsAxis}_axis_range`] =
        userOptions?.yhDefinedConfig?.resultsAxisRange ?? "dataAndLimits";
    }

    // If has secondary y-axis
    if (userOptions.yAxis[1] && userOptions.yAxis[1].visible !== false) {
      const y2Extremes = currentChart.yAxis[1].getExtremes();
      settings.y2_axis_title = userOptions.yAxis[1].title?.text ?? "";
      settings.y2_axis_gridlines = userOptions.yAxis[1].gridLineWidth ? 1 : 0;
      settings.y2_min_range = y2Extremes.min;
      settings.y2_max_range = y2Extremes.max;
      settings.y2_axis_font =
        userOptions.yAxis[1]?.title?.style?.fontFamily ?? "Arial";
      settings.y2_axis_font_size = userOptions.yAxis[1]?.title?.style?.fontSize
        ? userOptions.yAxis[1].title.style.fontSize.replace("px", "")
        : 12;
      settings.y2_axis_label_angle =
        userOptions.yAxis[1]?.labels?.rotation ?? 0;
      settings.show_y2_axis_label = true;
    }

    // If has log scale option
    const logScaleAxis = userOptions?.yhDefinedConfig?.logScaleAxis;
    if (logScaleAxis === "y") {
      settings.y_axis_log_scale =
        userOptions.yAxis[0].type === "logarithmic" ? true : false;
    } else if (logScaleAxis === "x") {
      settings.x_axis_log_scale =
        userOptions.xAxis[0].type === "logarithmic" ? true : false;
    } else if (logScaleAxis === "y2") {
      settings.x_axis_log_scale =
        userOptions.yAxis[1].type === "logarithmic" ? true : false;
    }

    // If has convert to log option
    if (userOptions?.yhDefinedConfig?.convertToLogAxis === "x") {
      settings.x_axis_to_log = true;
    } else if (userOptions?.yhDefinedConfig?.convertToLogAxis === "y") {
      settings.y_axis_to_log = true;
    }

    // Bell curve settings
    const bellCurveSeries = userOptions?.series?.find(
      (seriesProp) => seriesProp.type === "bellcurve",
    );
    if (bellCurveSeries) {
      settings.bell_curve = bellCurveSeries.visible;
      settings.bell_curve_color = "#95de64";
      setHasBellCurve(true);
    } else {
      setHasBellCurve(false);
    }

    // Histogram settings
    if (userOptions.series[0]?.binsNumber) {
      settings.bin_count = userOptions.series[0].binsNumber;
      settings.bin_control = "bin_count";
      settings.bin_width = userOptions.series[0].pointWidth;
    }

    // Bar/column settings
    if (
      userOptions?.yhDefinedConfig?.noBarSettings !== true &&
      (userOptions?.chart?.type === "column" ||
        userOptions?.chart?.type === "bar")
    ) {
      settings.bar_orientation = userOptions.chart.type;
      settings.bar_width = userOptions.plotOptions?.series?.pointWidth;
      settings.bar_spacing =
        userOptions.plotOptions?.series?.pointPadding ?? 0.1;
      settings.bar_control = "bar_spacing";
    }

    if (userOptions?.chart?.type === "boxplot") {
      const boxplotSeries = currentChart.series.find(
        (seriesObj) => seriesObj.initialType === "boxplot",
      );
      settings.boxplot_median_color = boxplotSeries?.color;
      settings.boxplot_box_color = "#ffffff";
      settings.whisker_width = 1;
      settings.boxplot_box_transparency = 0;
    }

    // If has line series
    const lineSeriesIndex = userOptions?.series.findIndex(
      (seriesObj) => seriesObj.type === "line",
    );
    if (lineSeriesIndex !== -1) {
      settings.line_width = userOptions.series[lineSeriesIndex].lineWidth ?? 1;
      settings.line_style =
        userOptions.series[lineSeriesIndex].dashStyle ?? "Solid";
      // Color should be set thru legend section
      // settings.line_color = currentChart.series[lineSeriesIndex].color;
    }

    // If has scatter series
    const scatterSeriesIndex = userOptions?.series.findIndex(
      (seriesObj) =>
        seriesObj.type === "scatter" && seriesObj.visible !== false,
    );
    if (scatterSeriesIndex !== -1) {
      settings.scatter_symbol =
        userOptions.series[scatterSeriesIndex]?.marker?.symbol ?? "circle";
      settings.scatter_size =
        userOptions.series[scatterSeriesIndex]?.marker?.size ?? 3;
      settings.scatter_transparency = 0;
    }

    // Normal probability line settings
    if (userOptions?.yhDefinedConfig?.class === "normalProbability") {
      const normalProbLine = userOptions.series?.find(
        (s) => s.name === "Normal Probability Line",
      );
      if (normalProbLine) {
        settings.show_normal_probability_line =
          normalProbLine.visible !== false;
        settings.normal_probability_line_width = normalProbLine.lineWidth || 4;
        settings.normal_probability_line_transparency = normalProbLine.opacity
          ? 1 - normalProbLine.opacity
          : 0;
      }
    }

    settings.legend_pos = userOptions?.legend?.align ?? "bottom";

    return settings;
  };

  /**
   * Get common axis definitions
   *
   * @param {object} chartOptions
   * @param {string} axis
   * @returns {object} def
   */
  const getAxisDef = (chartOptions, axis = "x") => {
    const def = {
      min: chartOptions[`${axis}_min_range`],
      max: chartOptions[`${axis}_max_range`],
      title: {
        text: chartOptions[`show_${axis}_axis_label`]
          ? chartOptions[`${axis}_axis_title`]
          : undefined,
        style: {
          fontFamily: chartOptions[`${axis}_axis_font`],
          fontSize: chartOptions[`${axis}_axis_font_size`] + "px",
        },
      },
      labels: {
        rotation: chartOptions[`${axis}_axis_label_angle`],
      },
      gridLineWidth: chartOptions[`${axis}_axis_gridlines`] ? 1 : 0,
    };
    if (axis === "y") {
      def.type =
        chartOptions[`${axis}_axis_log_scale`] &&
        chartOptions[`${axis}_min_range`] !== 0 &&
        chartOptions[`${axis}_max_range`] !== 0
          ? "logarithmic"
          : "linear";
    }

    return def;
  };

  /**
   * Apply the selected chart options
   *
   * @param {object} currentChart
   * @param {object} form
   * @param {object} legendColors
   * @param {boolean} save
   */
  const applyChartOptions = (
    currentChart,
    form,
    legendColors,
    save = false,
  ) => {
    const chartKey = currentChart.userOptions?.yhDefinedConfig?.chartKey;
    const chartOptions = form.getFieldsValue();
    let newOptions = {
      title: {
        text: chartOptions.chart_title,
        style: {
          fontFamily: chartOptions.chart_title_font,
          fontSize: `${chartOptions.chart_title_font_size}px`,
        },
      },
      subtitle: {
        text: chartOptions.chart_subtitle,
        style: {
          fontFamily: chartOptions.chart_subtitle_font,
          fontSize: `${chartOptions.chart_subtitle_font_size}px`,
        },
      },
      tooltip: {
        enabled: chartOptions.show_tooltip,
      },
      xAxis: [getAxisDef(chartOptions, "x")],
      yAxis: [getAxisDef(chartOptions, "y")],
    };

    if (currentChart.userOptions?.yAxis[1]) {
      newOptions.yAxis[1] = getAxisDef(chartOptions, "y2");
    }

    if (isCurveHistogram(currentChart.userOptions)) {
      if (currentChart.userOptions?.xAxis[2]) {
        newOptions.xAxis[2] = {
          min: chartOptions.x_min_range,
          max: chartOptions.x_max_range,
          labels: {
            rotation: chartOptions.x_axis_label_angle,
          },
          title: {
            text: chartOptions.x_axis_title,
            style: {
              fontFamily: chartOptions.x_axis_font,
              fontSize: `${chartOptions.x_axis_font_size}px`,
            },
          },
        };
        ChartHelper.updateAxisTitleWithActualValue(
          currentChart.userOptions.testData,
          newOptions.xAxis[2],
        );
      }
    }

    setLogScaleOptionState(currentChart);

    newOptions = merge(
      newOptions,
      setBarOptions(chartOptions),
      setLegendPos(currentChart, chartOptions.legend_pos),
      setPlotLines(currentChart, chartOptions),
      setScatterOptions(currentChart, chartOptions),
      setBarOrientation(currentChart, chartOptions),
      setHistogramOptions(currentChart, chartOptions, chartKey),
      assignLegendColors(currentChart, chartOptions, legendColors),
      setLineSeriesOptions(currentChart, chartOptions),
      setBoxPlotOptions(currentChart, chartOptions),
      setNormalProbabilityLineOptions(currentChart, chartOptions),
    );

    // console.log("current chart:", currentChart);
    // console.log("new user chart options:", newOptions);
    // console.log("form values:", chartOptions);

    currentChart.update(newOptions);

    if (save && getChartType()) {
      if (chartKey) {
        retainedChartOptions[chartKey] =
          ChartHelper.filterRetainableChartOptions(newOptions);
      }

      const cleanedOptions = ChartHelper.cleanupChartOptions(newOptions);
      setNewChartOptions(cleanedOptions);
      saveDefaultSettings(cleanedOptions);
    }
  };

  /**
   * Debounce the application of options
   */
  const delayApplyChartOptions = useCallback(
    debounce((currentChart, form, legendColors) => {
      applyChartOptions(currentChart, form, legendColors);
    }, 500),
    [],
  );

  /**
   * Set the scatter properties
   *
   * @param {object} currentChart
   * @param {object} chartOptions
   * @returns {object} options
   */
  const setScatterOptions = (currentChart, chartOptions) => {
    const options = { series: [] };
    const legendColorsArr = Object.values(legendColors);
    currentChart.userOptions?.series?.forEach((seriesObj, idx) => {
      if (seriesObj.type === "scatter" && seriesObj.visible !== false) {
        const color = legendColorsArr[idx] ?? currentChart.series[idx].color;
        options.series[idx] = {
          marker: {
            symbol: chartOptions.scatter_symbol,
            radius: chartOptions.scatter_size,
            fillColor: ChartHelper.hexToRGBA(
              color,
              1 - chartOptions.scatter_transparency,
            ),
          },
        };
      } else {
        options.series[idx] = {};
      }
    });

    return options;
  };

  /**
   * Set the bar/column orientation
   *
   * @param {object} currentChart
   * @param {object} chartOptions
   * @returns {object} options
   */
  const setBarOrientation = (currentChart, chartOptions) => {
    const options = {};
    if (chartOptions.bar_orientation) {
      options.chart = {
        type: chartOptions.bar_orientation,
      };
      if (
        !currentChart.userOptions?.plotOptions[chartOptions.bar_orientation]
      ) {
        options.plotOptions = {};
        options.plotOptions[chartOptions.bar_orientation] = {
          stacking: "normal",
        };
      }
      if (
        ["bar", "column"].includes(currentChart.userOptions?.series[0]?.type)
      ) {
        options.series = [
          {
            type: chartOptions.bar_orientation,
          },
        ];
      }
    }

    return options;
  };

  /**
   * Sets the line series option properties
   *
   * @param {object} chartOptions
   * @returns {object} options
   */
  const setLineSeriesOptions = (currentChart, chartOptions) => {
    const options = {
      series: [],
    };
    currentChart.userOptions?.series.forEach((seriesObj, index) => {
      if (seriesObj.type === "line") {
        options.series[index] = {
          lineWidth: chartOptions.line_width,
          dashStyle: chartOptions.line_style,
          // lineColor: chartOptions.line_color,
          // color: chartOptions.line_color,
        };
      }
    });

    return options;
  };

  /**
   * Set the normal probability line options
   *
   * @param {object} currentChart the current chart
   * @param {object} chartOptions the chart options
   * @returns {object} the options to be applied to the chart
   */
  const setNormalProbabilityLineOptions = (currentChart, chartOptions) => {
    const options = { series: [] };

    if (currentChart?.userOptions?.series) {
      currentChart.userOptions.series.forEach((series, index) => {
        if (series && series.name === "Normal Probability Line") {
          options.series[index] = {
            visible: chartOptions?.show_normal_probability_line ?? false,
            lineWidth: chartOptions?.normal_probability_line_width ?? 1,
            opacity:
              1 - (chartOptions?.normal_probability_line_transparency ?? 0),
          };
        }
      });
    }

    return options;
  };

  /**
   * Sets the boxplot option properties
   *
   * @param {object} currentChart
   * @param {object} chartOptions
   * @returns {object} options
   */
  const setBoxPlotOptions = (currentChart, chartOptions) => {
    let options = {};
    if (currentChart.userOptions?.chart?.type === "boxplot") {
      options = {
        plotOptions: {
          boxplot: {
            fillColor: ChartHelper.hexToRGBA(
              chartOptions.boxplot_box_color,
              1 - chartOptions.boxplot_box_transparency,
            ),
            medianColor: chartOptions.boxplot_median_color,
            whiskerWidth: chartOptions.whisker_width,
          },
        },
      };
    }

    return options;
  };

  /**
   * Set the histogram options
   *
   * @param {object} currentChart
   * @param {object} chartOptions
   * @param {string} chartKey
   * @returns {object} options
   */
  const setHistogramOptions = (currentChart, chartOptions, chartKey) => {
    const binOption = {
      bin_count: () => {
        if (isHistogram(currentChart.userOptions)) {
          return { bin_count: chartOptions.bin_count ?? 50 };
        }
      },
      bin_width: () => {
        if (chartOptions.bin_width) {
          return {
            bin_width: chartOptions.bin_width,
          };
        }
      },
      bin_spacing: () => {
        if (chartOptions.bin_spacing) {
          return {
            bin_spacing: chartOptions.bin_spacing,
          };
        }
      },
    };

    const binOptions = (
      binOption[form.getFieldValue("bin_control")] || binOption.bin_count
    )();

    let options = {};
    if (binOptions) {
      delete chartOptions.bin_count;
      delete chartOptions.bin_width;
      delete chartOptions.bin_spacing;
      const histogramOptions =
        chartComponentRefs[chartKey]?.current?.getHistogramOptions(
          merge({}, chartOptions, binOptions),
        ) ?? {};
      options = {
        series: histogramOptions.series ?? [],
        xAxis: [
          {
            tickPositions: histogramOptions.x_categories,
          },
        ],
      };
    }

    return options;
  };

  /**
   * Set the bar options
   *
   * @param {object} chartOptions
   * @returns {object}
   */
  const setBarOptions = (chartOptions) => {
    const barOption = {
      bar_width: () => {
        if (chartOptions.bar_width) {
          return {
            plotOptions: {
              series: {
                pointWidth: chartOptions.bar_width,
                pointPadding: undefined,
              },
            },
          };
        }
      },
      bar_spacing: () => {
        if (chartOptions.bar_spacing) {
          return {
            plotOptions: {
              series: {
                pointPadding: chartOptions.bar_spacing,
                pointWidth: undefined,
              },
            },
          };
        }
      },
    };

    return (
      barOption[form.getFieldValue("bar_control")] || barOption.bar_width
    )();
  };

  /**
   * Get the modified plot line definitions
   *
   * @param {object} plotLines
   * @param {object} values
   * @returns {object} plotLines
   */
  const getPlotLines = (plotLines, values) => {
    plotLines.forEach((plotObj) => {
      if (plotLineNames.includes(plotObj.name)) {
        // Check for sigma lines since they're on a checkbox group
        const lineName = sigmaLines.includes(plotObj.name)
          ? "sigma"
          : plotObj.name;
        const showLine =
          lineName === "sigma"
            ? values.sigma_set?.includes(plotObj.name)
            : values[lineName];
        if (showLine) {
          plotObj.dashStyle = values[`${lineName}_style`];
          plotObj.width = values[`${lineName}_width`];
          plotObj.color = values[`${lineName}_color`];
          plotObj.label.style.color = values[`${lineName}_color`];
          plotObj.label.text = values[`${lineName}_value`]
            ? `${plotObj.label.text.split(":")[0]}: ${plotObj.formattedValue}`
            : plotObj.label.text.split(":")[0];
        } else {
          plotObj.dashStyle = "ShortDash";
          plotObj.width = 0;
          plotObj.color = "transparent";
          plotObj.label.style.color = "transparent";
        }
      }
    });

    return plotLines;
  };

  /**
   * Set the plot lines, e.g. test limits
   *
   * @param {object} currentChart
   * @param {object} chartOptions
   * @return {object} options
   */
  const setPlotLines = (currentChart, chartOptions) => {
    const options = {};
    if (Array.isArray(currentChart.userOptions?.xAxis[0]?.plotLines)) {
      options.xAxis = [
        {
          plotLines: getPlotLines(
            [...currentChart.userOptions.xAxis[0].plotLines],
            chartOptions,
          ),
        },
      ];
    } else if (Array.isArray(currentChart.userOptions?.xAxis[2]?.plotLines)) {
      options.xAxis = [
        {},
        {
          plotLines: getPlotLines(
            [...currentChart.userOptions.xAxis[2].plotLines],
            chartOptions,
          ),
        },
      ];
    } else if (Array.isArray(currentChart.userOptions?.yAxis[0]?.plotLines)) {
      options.yAxis = [
        {
          plotLines: getPlotLines(
            [...currentChart.userOptions.yAxis[0].plotLines],
            chartOptions,
          ),
        },
      ];
    }

    return options;
  };

  /**
   * Assign legend colors
   *
   * @param {object} currentChart
   * @param {object} chartOptions
   * @param {object} legendColors
   * @returns {object} options
   */
  const assignLegendColors = (currentChart, chartOptions, legendColors) => {
    const options = {
      series: [],
    };
    currentChart.series?.forEach((series) => {
      const props = {
        color: legendColors[series.name] ?? series.color,
      };
      if (series.type === "bellcurve") {
        props.visible = chartOptions.bell_curve;
        props.showInLegend = chartOptions.bell_curve;
        props.color = chartOptions.bell_curve_color;
      }
      options.series.push(props);
    });

    return options;
  };

  /**
   * Set legend position properties
   *
   * @param {object} currentChart
   * @param {string} legendPos
   * @returns {object}
   */
  const setLegendPos = (currentChart, legendPos) => {
    const setPos = {
      bottom: () => {
        return {
          chart: {
            marginBottom: currentChart.marginBottom,
            marginRight: currentChart.marginRight,
          },
          legend: {
            enabled: true,
            align: "center",
            verticalAlign: "bottom",
            layout: "horizontal",
            floating: false,
          },
        };
      },
      top: () => {
        return {
          chart: {
            marginTop: 100,
            marginRight: 70,
          },
          legend: {
            enabled: true,
            align: "center",
            verticalAlign: "top",
            layout: "horizontal",
            floating: true,
            x: 0,
            y: 0,
          },
        };
      },
      right: () => {
        return {
          chart: {
            marginRight: currentChart.marginRight,
            marginBottom: currentChart.marginBottom,
          },
          legend: {
            enabled: true,
            align: "right",
            verticalAlign: "middle",
            layout: "vertical",
            margin: 24,
          },
        };
      },
      hide: () => {
        return {
          chart: {
            marginBottom: currentChart.marginBottom - 10,
            marginRight: currentChart.marginRight,
          },
          legend: {
            enabled: false,
          },
        };
      },
    };

    return (setPos[legendPos] || setPos.bottom)();
  };

  /**
   * Set the min and max range values
   *
   * @param {string} value The selected option value
   */
  const setMinMaxValues = (value) => {
    const testData = currentChart.userOptions.testData;
    const resultsAxis =
      currentChart.userOptions?.yhDefinedConfig?.resultsAxis ?? "y";
    const minMax = {};
    const lo_limit = Array.isArray(testData.lo_limit)
      ? testData.lo_limit[0]
      : testData.lo_limit;
    const hi_limit = Array.isArray(testData.hi_limit)
      ? testData.hi_limit[0]
      : testData.hi_limit;
    const range = {
      minMax: () => {
        minMax[`${resultsAxis}_min_range`] = testData.min_result;
        minMax[`${resultsAxis}_max_range`] = testData.max_result;
        return minMax;
      },
      dataAndLimits: () => {
        minMax[`${resultsAxis}_min_range`] = Math.min(
          testData.min_result,
          lo_limit,
        );
        minMax[`${resultsAxis}_max_range`] = Math.max(
          testData.max_result,
          hi_limit,
        );
        return minMax;
      },
      robustLimits: () => {
        ((minMax[`${resultsAxis}_min_range`] = testData.robust_lo_limit),
          (minMax[`${resultsAxis}_max_range`] = testData.robust_hi_limit));
        return minMax;
      },
      testLimits: () => {
        minMax[`${resultsAxis}_min_range`] = lo_limit;
        minMax[`${resultsAxis}_max_range`] = hi_limit;
        return minMax;
      },
      pct20Limits: () => {
        minMax[`${resultsAxis}_min_range`] = lo_limit - lo_limit * 0.2;
        minMax[`${resultsAxis}_max_range`] = hi_limit + hi_limit * 0.2;
        return minMax;
      },
    };
    form.setFieldsValue((range[value] || range.minMax)());
  };

  /**
   * Handles the event where a field changes in the form
   */
  const handleFieldsChange = () => {
    delayApplyChartOptions(currentChart, form, legendColors);
  };

  /**
   * Confirmation modal if user tries to close without applying options
   *
   * @param {object} info
   */
  const confirmClose = (info) => {
    const confirmModal = confirm({
      title: info.title,
      width: 480,
      icon: <InfoCircleOutlined style={{ color: "rgb(252 211 77)" }} />,
      content: info.content,
      footer: (
        <Flex justify="right" gap="small" className="mt-3">
          <Button
            key="back"
            onClick={() => {
              confirmModal.destroy();
            }}
          >
            Cancel
          </Button>
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              if (info.submitText !== "Reset") {
                setIsChartOptionsOpen(false);
                resetChartOptions(appliedValues);
              } else {
                resetChartOptions(getDefaultValues());
              }
              confirmModal.destroy();
            }}
          >
            {info.submitText}
          </Button>
        </Flex>
      ),
    });
  };

  /**
   * Resets the chart settings to default
   *
   * @param {object} values The form values
   */
  const resetChartOptions = (values) => {
    form.setFieldsValue(values);
    applyChartOptions(currentChart, form, legendColors);
  };

  /**
   * Get the fields value without the unneeded items
   *
   * @returns {object} values
   */
  const getFieldsValue = () => {
    const values = form.getFieldsValue(false);
    excludeFormItems.forEach((key) => {
      delete values[key];
    });

    return values;
  };

  /**
   * Confirm the reset of options
   */
  const confirmReset = () => {
    if (!isEqual(appliedValues, getDefaultValues())) {
      confirmClose({
        title: "Reset to Default Settings",
        content:
          "Are you sure you want to reset to default settings? Any unsaved changes will be lost. You can cancel to keep your current settings.",
        submitText: "Reset",
      });
    }
  };

  /**
   * Closes the drawer
   */
  const closeChartOptions = () => {
    setIsChartOptionsOpen(false);
    // setCurrentChartId(undefined);
  };

  // For debugging
  // function findDifference(obj1, obj2) {
  //   const diffKeys = Object.keys(obj1).reduce((acc, key) => {
  //     if (!(key in obj2) || obj1[key] !== obj2[key]) {
  //       acc.push(key);
  //     }
  //     return acc;
  //   }, []);

  //   Object.keys(obj2).forEach((key) => {
  //     if (!(key in obj1) || obj1[key] !== obj2[key]) {
  //       if (!diffKeys.includes(key)) {
  //         diffKeys.push(key);
  //       }
  //     }
  //   });

  //   console.log("diff:", diffKeys);
  // }

  /**
   * Check if there are changes not applied
   */
  const checkCloseConfirm = () => {
    // findDifference(appliedValues, getFieldsValue());
    // console.log("applied values:", appliedValues);
    // console.log("form values:", getFieldsValue());
    if (!isEqual(appliedValues, getFieldsValue())) {
      confirmClose({
        title: "Confirm Exit",
        content:
          "Are you sure you want to leave chart settings without applying your changes? All changes will be lost.",
        submitText: "Leave without applying",
      });
    } else {
      closeChartOptions();
    }
  };

  /**
   * When user clicks the collapse header
   *
   * @param {string} key
   */
  const handleCollapseChange = (key) => {
    setActiveCollapseKey(key);
  };

  /**
   * Checks whether the chart has secondary visible y-axis
   *
   * @returns {boolean}
   */
  const hasSecondaryYAxis = () => {
    return (
      currentChart.userOptions?.yAxis[1] &&
      currentChart.userOptions?.yAxis[1]?.opposite &&
      currentChart.userOptions?.yAxis[1]?.visible !== false
    );
  };

  /**
   * When user manually clicks the apply button
   */
  const applyChartOptionsManually = () => {
    setAppliedValues(getFieldsValue());
    applyChartOptions(currentChart, form, legendColors, true);
  };

  /**
   * Get the chart type of current chart
   *
   * @returns {string}
   */
  const getChartType = () => {
    return currentChart.userOptions?.yhDefinedConfig?.chartType ?? "";
  };

  /**
   * Get the list of saved settings
   */
  const getSavedChartSettingsList = () => {
    Api.getSavedChartSettingsList(
      (res) => {
        if (res.success) {
          setSavedSettingsOptions(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      { type: getChartType() },
    );
  };

  /**
   * Load options from saved settings
   *
   * @param {object} options
   */
  const loadChartOptions = (options) => {
    currentChart.update(options);
    const values = getValuesFromChartSettings();
    setAppliedValues(values);
    form.setFieldsValue(values);
  };

  /**
   * Fetch and load a saved chart settings
   */
  const loadChartSettings = () => {
    Api.getSavedChartSettings(
      (res) => {
        if (res.success) {
          loadChartOptions(JSON.parse(res.data.value));
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      { id: loadSettingsForm.getFieldValue("settings_key") },
    );
  };

  /**
   * Save default chart settings
   *
   * @param {string} value The stringified chart options
   */
  const saveDefaultSettings = (value) => {
    Api.saveChartSettings(
      (res) => {
        if (res.success) {
          message.success("Chart settings applied successfully.", 5);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        key: getChartType(),
        value: value,
        type: `chart_option_defaults`,
      },
    );
  };

  /**
   * Opens the save chart settings modal
   */
  const openSaveChartSettingsModal = () => {
    setSaveSettingsModalOpen(true);
  };

  /**
   * Opens the delete chart settings modal
   */
  const openDeleteChartSettingsModal = () => {
    setDeleteSettingsModalOpen(true);
  };

  /**
   * Get the plot line settings section
   *
   * @returns {object}
   */
  const getPlotLineSettings = () => {
    return {
      key: "plotLines",
      label: "Plot Line Settings",
      style: panelStyle,
      children: (
        <>
          <PlotLineSettings
            target="mean"
            targetLabel="Show Mean"
            dividerTitle="Mean Line"
            handleFieldsChange={handleFieldsChange}
            form={form}
            isChartOptionsOpen={open}
          ></PlotLineSettings>
          <PlotLineSettings
            target="test_limits"
            targetLabel="Show Test Limits"
            dividerTitle="Test Limit Lines"
            handleFieldsChange={handleFieldsChange}
            form={form}
            isChartOptionsOpen={open}
          ></PlotLineSettings>
          <PlotLineSettings
            target="robust_limits"
            targetLabel="Show Robust Limits"
            dividerTitle="Robust Limit Lines"
            handleFieldsChange={handleFieldsChange}
            form={form}
            isChartOptionsOpen={open}
          ></PlotLineSettings>
          <PlotLineSettings
            target="sigma"
            targetLabel="Show Sigma Lines"
            dividerTitle="Sigma Lines"
            handleFieldsChange={handleFieldsChange}
            form={form}
            isChartOptionsOpen={open}
          ></PlotLineSettings>
          {hasBellCurve && (
            <PlotLineSettings
              target="bell_curve"
              targetLabel="Show Bell Curve"
              dividerTitle="Bell Curve"
              handleFieldsChange={handleFieldsChange}
              form={form}
              isChartOptionsOpen={open}
            ></PlotLineSettings>
          )}
        </>
      ),
    };
  };

  /**
   * Get the other settings section
   *
   * @returns {object}
   */
  const getOtherSettings = () => {
    return {
      key: "other",
      label: "Other Settings",
      style: panelStyle,
      children: (
        <>
          <Divider orientation="left">Display</Divider>
          <FontSettings
            target="chart_title"
            targetLabel="Main Title Font"
          ></FontSettings>
          <FontSettings
            target="chart_subtitle"
            targetLabel="Subtitle Font"
          ></FontSettings>
          {/* <FontSettings
                    target="information"
                    targetLabel="Information Font"
                  ></FontSettings>
                  <FontSettings
                    target="stats"
                    targetLabel="Stats Font"
                  ></FontSettings> */}
          <Form.Item name="show_tooltip" valuePropName="checked" noStyle>
            <Checkbox className="mb-2">Show Tooltip</Checkbox>
          </Form.Item>
          <Form.Item name="legend_pos" label="Legend Position">
            <Radio.Group className="flex justify-between">
              <Radio value="bottom">Bottom</Radio>
              <Radio value="top">Top</Radio>
              <Radio value="right">Right</Radio>
              <Radio value="hide">Hide</Radio>
            </Radio.Group>
          </Form.Item>
          {showLegendPicker && (
            <LegendColorPicker
              form={form}
              chartOptionOpen={open}
              disabled={false}
              legendColors={legendColors}
              setLegendColors={setLegendColors}
            ></LegendColorPicker>
          )}
        </>
      ),
    };
  };

  /**
   * Get the stats below the chart settings section
   *
   * @returns {object}
   */
  const getStatsBelowChartSettings = () => {
    return {
      key: "stats",
      label: "Stats Below the Chart",
      style: panelStyle,
      children: <StatsBelowChart form={form}></StatsBelowChart>,
    };
  };

  /**
   * Get the normal probability settings section
   *
   * @returns {object}
   */
  const getNormalProbabilitySettings = () => {
    return {
      key: "normalProbabilitySettings",
      label: "Normal Probability Settings",
      style: panelStyle,
      children: <NormalProbabilitySettings></NormalProbabilitySettings>,
    };
  };

  /**
   * Get the box plot settings section
   *
   * @returns {object}
   */
  const getBoxPlotSettings = () => {
    return {
      key: "boxPlotSettings",
      label: "Box Plot Settings",
      style: panelStyle,
      children: (
        <BoxPlotSettings
          form={form}
          handleFieldsChange={handleFieldsChange}
        ></BoxPlotSettings>
      ),
    };
  };

  /**
   * Get the scatter settings section
   *
   * @returns {object}
   */
  const getScatterSettings = () => {
    return {
      key: "scatterSettings",
      label: "Scatter Settings",
      style: panelStyle,
      children: (
        <ScatterSettings
          form={form}
          handleFieldsChange={handleFieldsChange}
        ></ScatterSettings>
      ),
    };
  };

  /**
   * Get the bar settings section
   *
   * @returns {object}
   */
  const getBarSettings = () => {
    return {
      key: "barSettings",
      label: "Bar Settings",
      style: panelStyle,
      children: <BarSettings form={form}></BarSettings>,
    };
  };

  /**
   * Get the line settings section
   *
   * @returns {object}
   */
  const getLineSettings = () => {
    return {
      key: "lineSettings",
      label: "Line Settings",
      style: panelStyle,
      children: <LineSettings></LineSettings>,
    };
  };

  /**
   * Get the histogram settings section
   *
   * @returns {object}
   */
  const getHistogramSettings = () => {
    return {
      key: "histogramSettings",
      label: "Histogram Settings",
      style: panelStyle,
      children: (
        <HistogramSettings
          form={form}
          handleFieldsChange={handleFieldsChange}
          hasBinSpacing={currentChart.userOptions?.name === "bar_histogram"}
        ></HistogramSettings>
      ),
    };
  };

  const getAxisSettings = () => {
    return {
      key: "axisSettings",
      label: "Axis Settings",
      style: panelStyle,
      children: (
        <>
          <AxisSettings
            axis="x"
            form={form}
            hasConvertToLog={convertToLogAxis === "x"}
            hasLogScale={logScaleAxis === "x"}
            handleFieldsChange={handleFieldsChange}
            title="X-Axis"
          ></AxisSettings>
          <AxisRange
            axis="x"
            isTestResults={
              currentChart.userOptions?.yhDefinedConfig?.resultsAxis === "x"
            }
            title="X-Axis Range"
            setMinMaxValues={setMinMaxValues}
          ></AxisRange>
          <AxisSettings
            axis="y"
            form={form}
            hasLogScale={logScaleAxis === "y"}
            logScaleIsDisabled={logScaleIsDisabled}
            handleFieldsChange={handleFieldsChange}
            title={hasSecondaryYAxis() ? "Primary Y-Axis" : "Y-Axis"}
          ></AxisSettings>
          <AxisRange
            axis="y"
            isTestResults={
              currentChart.userOptions?.yhDefinedConfig?.resultsAxis === "y"
            }
            title={
              hasSecondaryYAxis() ? "Primary Y-Axis Range" : "Y-Axis Range"
            }
            setMinMaxValues={setMinMaxValues}
          ></AxisRange>
          {hasSecondaryYAxis() && (
            <AxisSettings
              axis="y2"
              form={form}
              hasLogScale={false}
              logScaleIsDisabled={true}
              handleFieldsChange={handleFieldsChange}
              title="Secondary Y-Axis"
            ></AxisSettings>
          )}
          {hasSecondaryYAxis() &&
            y2AxisRangeIsIndependent(currentChart.userOptions) && (
              <AxisRange
                axis="y2"
                isTestResults={
                  currentChart.userOptions?.yhDefinedConfig?.resultsAxis ===
                  "y2"
                }
                title="Secondary Y-Axis Range"
                setMinMaxValues={setMinMaxValues}
              ></AxisRange>
            )}
        </>
      ),
    };
  };

  return (
    <div className="p-2 h-full flex flex-col">
      {contextHolder}
      <SaveChartSettingsModal
        open={saveSettingsModalOpen}
        setModalOpen={setSaveSettingsModalOpen}
        savedSettingsOptions={savedSettingsOptions}
        getSavedChartSettingsList={getSavedChartSettingsList}
        chartOptionValues={{
          value: newChartOptions,
          type: getChartType(),
        }}
      ></SaveChartSettingsModal>
      <DeleteChartSettingsModal
        open={deleteSettingsModalOpen}
        setModalOpen={setDeleteSettingsModalOpen}
        savedSettingsOptions={savedSettingsOptions}
        getSavedChartSettingsList={getSavedChartSettingsList}
      ></DeleteChartSettingsModal>
      <Flex justify="space-between">
        <Title level={5}>Chart Options: {chartName}</Title>
        <CloseOutlined onClick={checkCloseConfirm}></CloseOutlined>
      </Flex>
      <Form className="flex justify-around" form={loadSettingsForm}>
        <Form.Item name="settings_key" label="" className="w-3/4">
          <Select
            showSearch
            placeholder={
              savedSettingsOptions.length
                ? "Load saved settings"
                : "No saved settings"
            }
            options={savedSettingsOptions}
            onChange={loadChartSettings}
          ></Select>
        </Form.Item>
        <Tooltip title="Save the applied settings">
          <Button
            icon={<SaveOutlined />}
            onClick={openSaveChartSettingsModal}
          ></Button>
        </Tooltip>
        <Tooltip title="Delete a saved settings">
          <Button
            icon={<DeleteOutlined />}
            onClick={openDeleteChartSettingsModal}
          ></Button>
        </Tooltip>
      </Form>
      <Form
        form={form}
        preserve={false}
        initialValues={appliedValues}
        onFieldsChange={handleFieldsChange}
        layout="vertical"
        className="overflow-y-auto"
      >
        <Form.Item name="chart_title" label="Chart Main Title">
          <TextArea rows={2}></TextArea>
        </Form.Item>
        <Form.Item name="chart_subtitle" label="Subtitle">
          <TextArea rows={2}></TextArea>
        </Form.Item>
        <Collapse
          activeKey={activeCollapseKey}
          onChange={handleCollapseChange}
          items={chartOptionSections}
        ></Collapse>
      </Form>
      <Flex justify="flex-end" gap={8} className="pt-2! pb-1!">
        <Button onClick={checkCloseConfirm}>Close</Button>
        <Button onClick={confirmReset}>Reset to Default</Button>
        <Button onClick={applyChartOptionsManually} type="primary">
          Apply Settings
        </Button>
      </Flex>
    </div>
  );
};

export default ChartOptionsDrawer;
