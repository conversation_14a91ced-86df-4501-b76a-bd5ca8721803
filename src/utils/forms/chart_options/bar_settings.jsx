"use client";

import { Form, InputNumber, Radio } from "antd";
import { useState } from "react";

/**
 * Bar settings component of chart options
 *
 * @param {object} form
 * @param {function} handleFieldsChange
 * @param {boolean} hasLine
 * @returns {JSX.Element}
 */
const BarSettings = ({ form }) => {
  const [isBarWidthDisabled, setIsBarWidthDisabled] = useState(true);
  const [isBarSpacingDisabled, setIsBarSpacingDisabled] = useState(false);

  /**
   * Handle the bar width and spacing inputs when use toggles bar control selection
   */
  const handleBarControlChange = () => {
    const barControl = form.getFieldValue("bar_control");
    setIsBarWidthDisabled(barControl === "bar_spacing");
    setIsBarSpacingDisabled(barControl === "bar_width");
  };

  return (
    <>
      <Form.Item name="bar_control" className="mb-2 mt-2">
        <Radio.Group
          className="flex flex-col gap-y-2"
          onChange={handleBarControlChange}
        >
          <Radio value="bar_width">
            Bar Width
            <Form.Item name="bar_width" noStyle>
              <InputNumber
                className="ml-1"
                disabled={isBarWidthDisabled}
              ></InputNumber>
            </Form.Item>
          </Radio>
          <Radio value="bar_spacing" className="!hidden">
            Spacing Between Bars
            <Form.Item name="bar_spacing" noStyle>
              <InputNumber
                className="ml-1"
                step={0.1}
                disabled={isBarSpacingDisabled}
              ></InputNumber>
            </Form.Item>
          </Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item name="bar_orientation" label="Orientation">
        <Radio.Group>
          <Radio value="column">Vertical</Radio>
          <Radio value="bar">Horizontal</Radio>
        </Radio.Group>
      </Form.Item>
    </>
  );
};

export default BarSettings;
