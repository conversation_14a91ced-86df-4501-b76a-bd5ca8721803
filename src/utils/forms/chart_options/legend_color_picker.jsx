"use client";

import { ColorPicker, Flex, Form, Select, Space } from "antd";
import { blue, green, presetPalettes, red } from "@ant-design/colors";
import { XFilled } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { useBoundStore } from "../../../store/store";

/**
 * Generate preset colors
 *
 * @param {object} presets
 * @returns {array}
 */
const genPresetColors = (presets = presetPalettes) =>
  Object.entries(presets).map(([label, colors]) => ({
    label,
    colors,
  }));

const presets = genPresetColors({
  blue,
  red,
  green,
});

/**
 * Legend color selection component
 *
 * @param {object} form
 * @param {boolean} chartOptionOpen
 * @param {object} legendColors
 * @param {function} setLegendColors
 * @param {boolean} disabled
 * @returns {JSX.Element}
 */
const LegendColorPicker = ({
  form,
  chartOptionOpen,
  legendColors,
  setLegendColors,
  disabled,
}) => {
  const currentChart = useBoundStore((state) => state.currentChart);
  const [legendOptions, setLegendOptions] = useState([]);
  const [colorValue, setColorValue] = useState("#ffffff");
  const [isColorPickerDisabled, setIsColorPickerDisabled] = useState(true);

  useEffect(() => {
    if (chartOptionOpen) {
      const legendOptions = [];
      const legendColors = {};
      currentChart.series.forEach((seriesData) => {
        legendColors[seriesData.name] = seriesData.color;
        if (seriesData?.visible && seriesData?.type !== "bellcurve") {
          legendOptions.push({
            value: seriesData.name,
            label: (
              <Space size="small">
                <XFilled style={{ color: legendColors[seriesData.name] }} />
                {seriesData.name}
              </Space>
            ),
          });
        }
      });
      setLegendOptions(legendOptions);
      setLegendColors(legendColors);
    }
  }, [chartOptionOpen]);

  /**
   * When user selects a legend
   *
   * @param {string} value
   */
  const handleLegendSelection = (value) => {
    setIsColorPickerDisabled(false);
    setColorValue(legendColors[value]);
  };

  /**
   * When user selects a color in the picker
   *
   * @param {object} color
   */
  const handleColorSelection = (color) => {
    const hexColor = color.toHexString();
    setColorValue(hexColor);
    const legendName = form.getFieldValue("legend");
    setLegendColors((prevState) => {
      const newState = { ...prevState };
      newState[legendName] = hexColor;
      return newState;
    });
    // Update the color of the selected option
    setLegendOptions((prevState) => {
      const newState = [...prevState];
      const selectedIndex = newState.findIndex(
        (option) => option.value === legendName,
      );
      newState[selectedIndex].label = (
        <Space size="small">
          <XFilled style={{ color: hexColor }} />
          {legendName}
        </Space>
      );
      return newState;
    });
  };

  return (
    <Flex gap={"small"} align="center">
      <span className="w-3/5">
        <Form.Item name="legend" noStyle>
          <Select
            showSearch
            placeholder="Select legend"
            options={legendOptions}
            onSelect={handleLegendSelection}
            disabled={disabled}
            className="w-full"
          ></Select>
        </Form.Item>
      </span>
      <ColorPicker
        onChangeComplete={handleColorSelection}
        value={colorValue}
        presets={presets}
        disabled={isColorPickerDisabled}
        trigger="hover"
      ></ColorPicker>
    </Flex>
  );
};

export default LegendColorPicker;
