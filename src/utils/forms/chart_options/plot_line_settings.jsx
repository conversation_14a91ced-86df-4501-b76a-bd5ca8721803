"use client";

import {
  Checkbox,
  Divider,
  Form,
  Flex,
  InputNumber,
  Select,
  Switch,
  Typography,
} from "antd";
import { useEffect, useState } from "react";
import SimpleColorPicker from "./simple_color_picker";
const { Text } = Typography;

const lineStyleOptions = [
  {
    value: "Solid",
    label: "Solid",
  },
  {
    value: "ShortDash",
    label: "Short Dash",
  },
  {
    value: "ShortDot",
    label: "Short Dot",
  },
  {
    value: "ShortDashDot",
    label: "Short Dash Dot",
  },
  {
    value: "Dot",
    label: "Dot",
  },
  {
    value: "LongDash",
    label: "Long Dash",
  },
  {
    value: "DashDot",
    label: "Dash Dot",
  },
  {
    value: "LongDashDot",
    label: "Long Dash Dot",
  },
];

/**
 * Plot line settings component
 *
 * @param {string} target
 * @param {string} targetLabel
 * @param {string} dividerTitle
 * @param {object} form
 * @param {function} handleFieldsChange
 @ @param {boolean} isChartOptionsOpen
 * @returns {JSX.Element}
 */
const PlotLineSettings = ({
  target,
  targetLabel = "",
  dividerTitle = "",
  form,
  handleFieldsChange,
  isChartOptionsOpen,
}) => {
  const [dependentInputsDisabled, setDependentInputsDisabled] = useState(true);

  useEffect(() => {
    if (isChartOptionsOpen) {
      handleShowPlotLineChange(form.getFieldValue(target));
    }
  }, [isChartOptionsOpen]);

  /**
   * When the show plot line changes from on to off or vice versa
   *
   * @param {boolean} value The switch value
   */
  const handleShowPlotLineChange = (value) => {
    setDependentInputsDisabled(!value);
    if (target === "sigma") {
      form.setFieldsValue({ sigma_set: value ? ["3s"] : [] });
    }
  };

  return (
    <Flex vertical className="mb-2">
      <Divider orientation="left">{dividerTitle}</Divider>
      <Flex justify="space-between" gap={8} wrap="wrap">
        <Flex className="w-1/2">
          <Text>{`${targetLabel}:`}</Text>
          <Form.Item name={`${target}`} noStyle>
            <Switch
              className="ml-1"
              onChange={handleShowPlotLineChange}
            ></Switch>
          </Form.Item>
        </Flex>
        {target !== "bell_curve" && (
          <Flex>
            <Flex>
              <Text>{`Show Value:`}</Text>
              <Form.Item name={`${target}_value`} noStyle>
                <Switch
                  className="ml-1"
                  disabled={dependentInputsDisabled}
                ></Switch>
              </Form.Item>
            </Flex>
          </Flex>
        )}
        {target === "sigma" && (
          <Form.Item name={`${target}_set`} noStyle>
            <Checkbox.Group className="flex gap-2">
              <Checkbox value="1s">1s</Checkbox>
              <Checkbox value="2s">2s</Checkbox>
              <Checkbox value="3s">3s</Checkbox>
              <Checkbox value="6s">6s</Checkbox>
            </Checkbox.Group>
          </Form.Item>
        )}
        {target !== "bell_curve" && (
          <Flex align="center">
            <Text>Line Thickness:</Text>
            <Form.Item name={`${target}_width`} noStyle>
              <InputNumber
                className="ml-1"
                disabled={dependentInputsDisabled}
              ></InputNumber>
            </Form.Item>
          </Flex>
        )}
        <SimpleColorPicker
          inputName={`${target}_color`}
          form={form}
          handleFieldsChange={handleFieldsChange}
          isDisabled={dependentInputsDisabled}
        ></SimpleColorPicker>
        {target !== "bell_curve" && (
          <Flex align="center" className="w-2/3">
            <Text>Line Style:</Text>
            <Form.Item name={`${target}_style`} noStyle>
              <Select
                className="ml-1 w-2/3"
                showSearch
                placeholder="Select line style"
                options={lineStyleOptions}
                disabled={dependentInputsDisabled}
              ></Select>
            </Form.Item>
          </Flex>
        )}
      </Flex>
    </Flex>
  );
};

export default PlotLineSettings;
