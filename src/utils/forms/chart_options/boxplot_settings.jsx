"use client";

import { Divider, Form, Flex, InputN<PERSON>ber, Slider, Typography } from "antd";
import SimpleColorPicker from "./simple_color_picker";
const { Text } = Typography;

/**
 * Boxplot settings component of chart options
 *
 * @param {object} form
 * @param {function} handleFieldsChange
 * @returns {JSX.Element}
 */
const BoxPlotSettings = ({ form, handleFieldsChange }) => {
  return (
    <>
      <Divider orientation="left">Box</Divider>
      <Form.Item name="boxplot_box_transparency" label="Fill Transparency">
        <Slider min={0} max={1} step={0.1} />
      </Form.Item>
      <Flex justify={"space-between"} gap={8}>
        <SimpleColorPicker
          inputName="boxplot_median_color"
          label="Median Color:"
          form={form}
          handleFieldsChange={handleFieldsChange}
        ></SimpleColorPicker>
        <SimpleColorPicker
          inputName="boxplot_box_color"
          label="Box Color:"
          form={form}
          handleFieldsChange={handleFieldsChange}
        ></SimpleColorPicker>
      </Flex>
      <Divider orientation="left">Whisker</Divider>
      <Flex align="center">
        <Text>Line Thickness:</Text>
        <Form.Item name="whisker_width" noStyle>
          <InputNumber className="ml-1"></InputNumber>
        </Form.Item>
      </Flex>
    </>
  );
};

export default BoxPlotSettings;
