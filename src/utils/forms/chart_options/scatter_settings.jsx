"use client";

import { Flex, Form, Select } from "antd";
import IntegerStep from "./integer_step";

const shapeOptions = [
  {
    value: "circle",
    label: "Circle",
  },
  {
    value: "square",
    label: "Square",
  },
  {
    value: "triangle",
    label: "Triangle",
  },
  {
    value: "diamond",
    label: "Diamond",
  },
];
/**
 * Scatter settings component of chart options
 *
 * @param {object} form
 * @param {object} handleFieldsChange
 * @returns {JSX.Element}
 */
const ScatterSettings = ({ form, handleFieldsChange }) => {
  return (
    <Flex wrap gap={8} justify="space-between">
      <Flex align="center" className="w-1/3">
        <Form.Item name="scatter_symbol" label="Shape" className="w-full">
          <Select
            showSearch
            placeholder="Select shape"
            options={shapeOptions}
            className="w-full"
          ></Select>
        </Form.Item>
      </Flex>
      <IntegerStep
        label="Transparency"
        inputName="scatter_transparency"
        form={form}
        handleFieldsChange={handleFieldsChange}
        className="w-1/2"
        min={0}
        max={1}
        step={0.1}
      ></IntegerStep>
      <IntegerStep
        label="Size"
        inputName="scatter_size"
        form={form}
        handleFieldsChange={handleFieldsChange}
        className="w-full"
        min={1}
        max={10}
      ></IntegerStep>
    </Flex>
  );
};

export default ScatterSettings;
