"use client";

import { App, But<PERSON>, Flex, Form, Input, Modal } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { useState } from "react";
import Api from "../../api";

/**
 * Save chart settings modal component
 *
 * @param {boolean} open
 * @param {function} setModalOpen
 * @param {array} savedSettingsOptions
 * @param {function} getSavedChartSettingsList
 * @param {object} chartOptionValues
 * @returns {JSX.Element}
 */
const SaveChartSettingsModal = ({
  open,
  setModalOpen,
  savedSettingsOptions,
  getSavedChartSettingsList,
  chartOptionValues,
}) => {
  const [form] = Form.useForm();
  const [{ confirm }, contextHolder] = Modal.useModal();
  const { message } = App.useApp();
  const [isSaveBtnDisabled, setIsSaveBtnDisabled] = useState(true);

  /**
   * Save the chart settings
   */
  const saveChartSettings = () => {
    Api.saveChartSettings(
      (res) => {
        if (res.success) {
          message.success("Chart Settings Saved!", 5);
          getSavedChartSettingsList();
          setModalOpen(false);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        key: form.getFieldValue("settings_name"),
        ...chartOptionValues,
      },
    );
  };

  /**
   * Confirmation if name already exists
   */
  const confirmOverwrite = () => {
    const confirmModal = confirm({
      title: "Chart Settings Name Already Exists",
      width: 480,
      icon: <InfoCircleOutlined style={{ color: "rgb(252 211 77)" }} />,
      content:
        "The settings name you entered already exists. What do you want to do?",
      footer: (
        <Flex justify="right" gap="small" className="mt-3">
          <Button
            key="back"
            onClick={() => {
              setModalOpen(true);
              confirmModal.destroy();
            }}
          >
            Rename and Save as New
          </Button>
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              saveChartSettings();
              confirmModal.destroy();
            }}
          >
            Overwrite Existing
          </Button>
        </Flex>
      ),
    });
  };

  /**
   * Checks whether the name already exists
   *
   * @returns {boolean}
   */
  const checkNameExists = () => {
    return savedSettingsOptions.some(
      (option) => option.label === form.getFieldValue("settings_name"),
    );
  };

  /**
   * When user cancels the modal
   */
  const handleCancel = () => {
    setModalOpen(false);
  };

  /**
   * Handle the saving of the chart settings
   */
  const handleSave = () => {
    if (checkNameExists()) {
      setModalOpen(false);
      confirmOverwrite();
    } else {
      saveChartSettings();
    }
  };

  /**
   * When user types in the chart settings name input
   */
  const handleNameInputChange = () => {
    const name = form.getFieldValue("settings_name");
    setIsSaveBtnDisabled(name === "");
  };

  return (
    <>
      {contextHolder}
      <Modal
        title="Save Chart Settings"
        open={open}
        onOk={handleSave}
        onCancel={handleCancel}
        destroyOnHidden
        footer={[
          <Button key="back" onClick={handleCancel}>
            Close
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleSave}
            disabled={isSaveBtnDisabled}
          >
            Save
          </Button>,
        ]}
      >
        <Form form={form}>
          <Form.Item name="settings_name" required>
            <Input
              placeholder="Chart Settings Name"
              onChange={handleNameInputChange}
            ></Input>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default SaveChartSettingsModal;
