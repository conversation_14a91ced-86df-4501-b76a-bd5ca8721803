import { App, Form, Select } from "antd";
import { useState } from "react";
import { useEffectApiFetch } from "../../hooks";
import Api from "../api";
import styles from "./styles.module.css";

/**
 * Form to load saved filter settings
 *
 * @param {FormInstance} form
 * @returns {JSX.Element}
 */
const LoadFilterSettingsForm = ({
  form,
  setIsDisableDeleteFilterButton,
  setIsDisableLoadFilterButton,
  shouldUpdateSavedFilterOptions,
}) => {
  const [savedFilterOptions, setSavedFilterOptions] = useState([]);
  const { message } = App.useApp();

  useEffectApiFetch(
    () => {
      return getSavedFilterOptions();
    },
    () => {
      setSavedFilterOptions([]);
    },
    [shouldUpdateSavedFilterOptions],
  );

  /**
   * Get saved filters
   *
   * @returns {AbortController} abortCtl
   */
  const getSavedFilterOptions = () => {
    const abortCtl = Api.getSavedFilterOptions(
      (res) => {
        if (res.success) {
          setSavedFilterOptions(res.data);
        } else {
          message.warning(res.message);
        }
      },
      (err) => {
        message.error(err);
      },
    );

    return abortCtl;
  };

  /**
   * Handle change event of saved filter selection
   *
   * @param {int} filterKey
   */
  const onChange = (filterKey) => {
    setIsDisableDeleteFilterButton(!filterKey);
    setIsDisableLoadFilterButton(!filterKey);
  };

  return (
    <>
      <Form
        form={form}
        name="load_filter_settings_form"
        className={styles.modalForm}
      >
        <Form.Item
          name="filter_key"
          rules={[
            {
              required: true,
              message: "Please select saved filter settings!",
            },
          ]}
        >
          <Select
            showSearch
            allowClear
            placeholder="Please select saved filter"
            optionFilterProp="children"
            popupMatchSelectWidth={false}
            onChange={onChange}
            filterOption={(input, option) =>
              (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
            }
            options={savedFilterOptions}
          />
        </Form.Item>
      </Form>
    </>
  );
};

export default LoadFilterSettingsForm;
