import { Button, Form, Input, Statistic, Typography } from "antd";
import <PERSON><PERSON><PERSON>ield from "./fields/password_field";
import styles from "./styles.module.css";

const { Text, Title } = Typography;
const { Countdown } = Statistic;

/**
 * Form to reset user password
 *
 * @param {FormInstance} form
 * @param {function} onFinish
 * @param {function} onFinishFailed
 * @param {string} validateMessage
 * @param {string} validateStatus
 * @param {boolean} allowSubmit
 * @param {function} setAllowSubmit
 * @param {boolean} isRedirectToLoginPage
 * @param {boolean} isRedirecting
 * @param {function} redirectToLoginPage
 * @param {boolean} loading
 * @param {string} formTitle
 * @returns {JSX.Element}
 */
const ResetPasswordForm = ({
  form,
  onFinish,
  onFinishFailed,
  validateMessage,
  validateStatus,
  allowSubmit,
  setAllowSubmit,
  isRedirectToLoginPage = false,
  isRedirecting,
  redirectToLoginPage,
  loading,
  formTitle = "",
}) => {
  /**
   * Triggered when a field value changes
   * Check if form is valid and allow form submit if valid
   */
  const onFieldsChange = () => {
    const isFormValid =
      !form.getFieldsError().some((item) => item.errors.length > 0) &&
      form.getFieldValue("password") &&
      form.getFieldValue("password_confirmation");
    setAllowSubmit(isFormValid);
  };

  return (
    <Form
      form={form}
      name="reset_password"
      className={styles.resetPasswordForm}
      layout="vertical"
      requiredMark={true}
      onFinish={onFinish}
      onFinishFailed={onFinishFailed}
      onFieldsChange={onFieldsChange}
      autoComplete="off"
    >
      <Title level={4}>{formTitle}</Title>
      <PasswordField
        name="password"
        label="New Password"
        placeholder="Enter new password"
        requiredMessage="Please input your password!"
        className="mt-6"
        form={form}
      />
      <Form.Item
        name="password_confirmation"
        label="Confirm New Password"
        dependencies={["password"]}
        help={validateMessage}
        validateStatus={validateStatus}
        hasFeedback
        rules={[
          {
            required: true,
            message: "Please confirm your password!",
          },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue("password") === value) {
                return Promise.resolve();
              }
              return Promise.reject(
                new Error("The two passwords that you entered do not match!"),
              );
            },
          }),
        ]}
      >
        <Input.Password placeholder="Confirm new password" />
      </Form.Item>
      <Text type="secondary">
        Use at least 12 characters. Don&apos;t use a password from another site
        or something too obvious like your pet&apos;s name.
      </Text>
      {validateStatus === "success" && isRedirectToLoginPage && (
        <div>
          {isRedirecting ? (
            <Text>Redirecting to login page...</Text>
          ) : (
            <Countdown
              value={Date.now() + 1000 * 5}
              onFinish={redirectToLoginPage}
              prefix="You will be redirected to login page in"
              suffix="seconds..."
              format="s"
              valueStyle={{
                fontSize: 12,
              }}
            />
          )}
        </div>
      )}
      <Form.Item className="my-6">
        <Button
          type="primary"
          htmlType="submit"
          block
          disabled={!allowSubmit}
          loading={loading}
        >
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
};

export default ResetPasswordForm;
