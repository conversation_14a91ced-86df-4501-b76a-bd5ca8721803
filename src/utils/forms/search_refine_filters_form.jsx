import { InfoCircleFilled } from "@ant-design/icons";
import {
  Alert,
  Checkbox,
  DatePicker,
  Divider,
  Form,
  Space,
  Typography,
} from "antd";
import React, { memo, useEffect, useRef, useState } from "react";

const { Text } = Typography;
const { RangePicker } = DatePicker;

/**
 * Form used for filtering search table data
 *
 * @returns {JSX.Element}
 */
const SearchRefineFiltersForm = () => {
  const [filterFieldsHeight, setFilterFieldsHeight] = useState();
  const filterFieldsWrapperRef = useRef();

  useEffect(() => {
    calculateFilterFieldsHeight();
  }, []);

  /**
   * Calculate filter fields height
   */
  const calculateFilterFieldsHeight = () => {
    setFilterFieldsHeight(filterFieldsWrapperRef.offsetHeight);
  };

  return (
    <div className="flex-container">
      <div ref={filterFieldsWrapperRef} className="fill-height overflow-y-auto">
        <div
          style={{
            height: `${filterFieldsHeight}px`,
          }}
        >
          <Space direction="vertical">
            <Alert
              className="mb-4"
              message={
                <>
                  <InfoCircleFilled
                    style={{
                      color: "#1890ff",
                    }}
                  />{" "}
                  <Text strong>Note:</Text>{" "}
                  <Text>
                    You must select at least one (1) Datalog from the table.
                  </Text>
                </>
              }
              type="info"
              closable
            />
            <Form.Item name="refine_by">
              <Checkbox.Group disabled>
                <Space direction="vertical">
                  <Checkbox value="manufacturing_process">
                    Manufacturing Process
                  </Checkbox>
                  <Checkbox value="program">Program</Checkbox>
                  <Checkbox value="tester">Tester</Checkbox>
                  <Checkbox value="family">Family</Checkbox>
                  <Checkbox value="temperature">Temperature</Checkbox>
                  <Checkbox value="test_step">Test Step</Checkbox>
                  <Checkbox value="run_type">Run Type</Checkbox>
                  <Checkbox value="loadboard_id">Loadboard ID</Checkbox>
                  <Checkbox value="handler_id">Handler ID</Checkbox>
                </Space>
              </Checkbox.Group>
            </Form.Item>
            <Divider orientation="left">
              <Text strong>Filter by Date</Text>
            </Divider>
            <Form.Item name="processed_date" label="Processed Date">
              <RangePicker className="w-full" disabled allowEmpty />
            </Form.Item>
            <Divider>or</Divider>
            <Form.Item name="testing_start_date" label="Testing Start Date">
              <RangePicker className="w-full" disabled allowEmpty />
            </Form.Item>
            <Form.Item name="testing_end_date" label="Testing End Date">
              <RangePicker className="w-full" disabled allowEmpty />
            </Form.Item>
          </Space>
        </div>
      </div>
    </div>
  );
};

export default memo(SearchRefineFiltersForm);
