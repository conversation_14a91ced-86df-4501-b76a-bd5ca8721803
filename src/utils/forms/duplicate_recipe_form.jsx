import { App, Form, Input } from "antd";
import Api from "../api";
import Helper from "../helper";
import styles from "./styles.module.css";

/**
 * Form to duplicate recipe
 *
 * @param {Form} form
 * @param {object} recipeData
 * @param {function} setIsDuplicateRecipeModalOpen
 * @param {object} loadingStatus
 * @param {function} setLoadingStatus
 * @param {string} loadingStatusKey
 * @param {function} reloadRecipesData
 * @returns {JSX.Element}
 */
const DuplicateRecipeForm = ({
  form,
  recipeData,
  setIsDuplicateRecipeModalOpen,
  loadingStatus,
  setLoadingStatus,
  loadingStatusKey,
  reloadRecipesData,
}) => {
  const { message } = App.useApp();

  /**
   * Duplicate recipe
   *
   * @param {object} values
   */
  const duplicateRecipe = (values) => {
    Helper.updateLoadingStatus(
      loadingStatus,
      setLoadingStatus,
      loadingStatusKey,
      true,
    );
    Api.duplicateRecipe(
      (res) => {
        if (res.success) {
          message.success(res.message);
          setIsDuplicateRecipeModalOpen(false);
          reloadRecipesData();
        } else {
          message.warning(res.message, 10);
        }
        Helper.updateLoadingStatus(
          loadingStatus,
          setLoadingStatus,
          loadingStatusKey,
          false,
        );
      },
      (err) => {
        message.error(err, 10);
        Helper.updateLoadingStatus(
          loadingStatus,
          setLoadingStatus,
          loadingStatusKey,
          false,
        );
      },
      {
        recipe_key: recipeData.recipe_key,
        name: values.recipe_name,
      },
    );
  };

  return (
    <Form
      form={form}
      className={styles.modalForm}
      layout="vertical"
      name="duplicate_recipe_form"
      onFinish={duplicateRecipe}
    >
      <Form.Item
        name="recipe_name"
        label="Rename Recipe Name"
        rules={[
          {
            required: true,
            message: "Please input a recipe name!",
          },
        ]}
      >
        <Input />
      </Form.Item>
    </Form>
  );
};

export default DuplicateRecipeForm;
