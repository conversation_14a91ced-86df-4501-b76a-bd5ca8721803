import { Checkbox, Col, Form, Row, Select, Typography } from "antd";
import { useState } from "react";
import WaferNotchSVG from "../charts/components/wafer_notch_svg";
import styles from "./styles.module.css";

const { Text } = Typography;

/**
 * Form to set wafer map orientation
 *
 * @param {FormInstance} form
 * @param {object} component
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @returns {JSX.Element}
 */
const OrientationSettingsForm = ({
  form,
  component,
  chartKey,
  chartCustomData,
}) => {
  const defaultRotation = chartCustomData[chartKey].rotation ?? "90";
  const defaultFlip = chartCustomData[chartKey].flip ?? "none";
  const [rotation, setRotation] = useState(defaultRotation);
  const [flip, setFlip] = useState(defaultFlip);
  const notchPosition = chartCustomData[chartKey].wafer_flat ?? null;

  return (
    <Row>
      <Col span={12}>
        <Form
          form={form}
          id={`orientation_settings_${component.id}`}
          className={styles.modalForm}
          layout="vertical"
          initialValues={{
            rotation: defaultRotation,
            flip: defaultFlip,
            display_arrows: chartCustomData[chartKey].displayArrows ?? false,
          }}
        >
          <Form.Item name="rotation" label="Map Rotation">
            <Select
              getPopupContainer={() =>
                document.getElementById(`orientation_settings_${component.id}`)
              }
              onChange={setRotation}
              options={[
                {
                  value: "0",
                  label: <>0&#176;</>,
                },
                {
                  value: "90",
                  label: <>90&#176;</>,
                },
                {
                  value: "180",
                  label: <>180&#176;</>,
                },
                {
                  value: "270",
                  label: <>270&#176;</>,
                },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="flip"
            label="Flip Map"
            help={
              <>
                Blue Arrow is Y Direction
                <br />
                Orange Arrow is X Direction
              </>
            }
          >
            <Select
              getPopupContainer={() =>
                document.getElementById(`orientation_settings_${component.id}`)
              }
              onChange={setFlip}
              options={[
                {
                  value: "none",
                  label: "None",
                },
                {
                  value: "x",
                  label: "Flip X",
                },
                {
                  value: "y",
                  label: "Flip Y",
                },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="display_arrows"
            className="mt-8"
            valuePropName="checked"
          >
            <Checkbox>Display Arrows on Maps</Checkbox>
          </Form.Item>
        </Form>
      </Col>
      <Col span={12} className="text-center">
        <Text>Orientation Preview</Text>
        <div className="p-4 flex justify-center">
          <WaferNotchSVG
            notchPosition={notchPosition}
            rotation={rotation}
            flip={flip}
          />
        </div>
      </Col>
    </Row>
  );
};

export default OrientationSettingsForm;
