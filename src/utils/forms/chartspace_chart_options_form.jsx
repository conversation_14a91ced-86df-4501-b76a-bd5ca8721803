import { QuestionCircleTwoTone, PlusCircleOutlined } from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  Button,
  Checkbox,
  Col,
  Divider,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Space,
  Tooltip,
  Typography,
} from "antd";
import React, { memo, useEffect, useState } from "react";

const { Text, Title } = Typography;

/**
 * Form used for editing chartspace chart options
 *
 * @param {FormInstance} chartspaceChartOptionsForm
 * @param {function} onFinish
 * @param {object} initialValues
 * @returns {JSX.Element}
 */
const ChartspaceChartOptionsForm = ({
  chartspaceChartOptionsForm,
  onFinish,
  initialValues,
}) => {
  const [chartType, setChartType] = useState(initialValues.chart);

  useEffect(() => {
    chartspaceChartOptionsForm.setFieldsValue(initialValues);
  }, []);

  return (
    <div className="p-4">
      <Form
        form={chartspaceChartOptionsForm}
        name="chartspace_chart_options_form"
        onFinish={onFinish}
      >
        <Title level={5}>Edit Chart</Title>
        <Row gutter={14}>
          <Col span={8}>
            <Form.Item
              label="Load Saved Chart Settings"
              name="saved_chart_settings"
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
            >
              <Select
                placeholder="-Select-"
                popupMatchSelectWidth={false}
                options={[]}
                disabled
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Select Chart Type"
              name="chart"
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
              rules={[
                {
                  required: true,
                  message: "Please select a chart type!",
                },
              ]}
            >
              <Select
                placeholder="-Select-"
                popupMatchSelectWidth={false}
                onChange={setChartType}
                options={[
                  {
                    value: "bar_histogram",
                    label: "Bar Histogram",
                  },
                  {
                    value: "curve_histogram",
                    label: "Curved Histogram",
                  },
                  {
                    value: "qq_plot",
                    label: "Normal Probability",
                  },
                  {
                    value: "scatter",
                    label: "Scatter",
                  },
                  {
                    value: "boxplot",
                    label: "Boxwhisker",
                  },
                  {
                    value: "wafer",
                    label: "Wafer",
                  },
                  // commented for now since this is still for development
                  // {
                  //   value: "multi_test_bar_histogram",
                  //   label: "Multi Test Bar Histogram",
                  //   disabled: true,
                  // },
                  // {
                  //   value: "multi_test_curved_histogram",
                  //   label: "Multi Test Curved Histogram",
                  //   disabled: true,
                  // },
                  // {
                  //   value: "multi_test_qq_plot",
                  //   label: "Multi Test Normal Probability",
                  //   disabled: true,
                  // },
                  // {
                  //   value: "multi_test_scatter",
                  //   label: "Multi Test Scatter",
                  //   disabled: true,
                  // },
                  // {
                  //   value: "multi_test_boxwhisker",
                  //   label: "Multi Test Boxwhisker",
                  //   disabled: true,
                  // },
                ]}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Select Display Layout"
              name="display_chart_size"
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
              rules={[
                {
                  required: true,
                  message: "Please select a display layout!",
                },
              ]}
            >
              <Select
                placeholder="-Select-"
                popupMatchSelectWidth={false}
                options={[
                  {
                    value: 1,
                    label: "1 Chart per Row",
                  },
                  {
                    value: 2,
                    label: "2 Charts per Row",
                  },
                  {
                    value: 3,
                    label: "3 Charts per Row",
                  },
                  {
                    value: 4,
                    label: "4 Charts per Row",
                  },
                  {
                    value: 5,
                    label: "5 Charts per Row",
                  },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>
        <Divider className="mt-0!">
          <Text strong>Edit Chart Settings</Text>
        </Divider>
        <Row>
          <Col span={24}>
            <Space align="end" size="large">
              <Form.Item
                className="w-44!"
                name="stats_type"
                label="Data Source"
                labelCol={{ span: 24 }}
                wrapperCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: "Please select a data source!",
                  },
                ]}
              >
                <Select
                  placeholder="-Select-"
                  popupMatchSelectWidth={false}
                  options={[
                    {
                      value: "all",
                      label: "All",
                    },
                    {
                      value: "rp",
                      label: "All (Last Results Per Die)",
                    },
                    {
                      value: "pass",
                      label: "Die Passing this Test (Last Results Per Die)",
                    },
                    {
                      value: "passing_unit",
                      label: "Passing Die (Last Results Per Die)",
                    },
                    {
                      value: "iqr",
                      label: "Robust Data (Last Results Per Die)",
                    },
                  ]}
                />
              </Form.Item>
              <Form.Item name="iqr_n" label="Robust Data N">
                <InputNumber className="w-16" disabled />
              </Form.Item>
              <Form.Item
                className="w-44!"
                name="cs_chart_range"
                label="Data Range"
                labelCol={{ span: 24 }}
                wrapperCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: "Please select a data range!",
                  },
                ]}
              >
                <Select
                  placeholder="-Select-"
                  popupMatchSelectWidth={false}
                  disabled
                  options={[
                    {
                      value: "test_limits_20pct",
                      label: "-/+20% Test Limits",
                    },
                    {
                      value: "data_and_limits",
                      label: "Data and Limits",
                    },
                    {
                      value: "test_limits",
                      label: "Test Limits",
                    },
                    {
                      value: "robust_limit",
                      label: "Robust Limits",
                    },
                    {
                      value: "min_max",
                      label: "Data Min & Max",
                    },
                    {
                      value: "overall_min_max",
                      label: "Min & Max of Selection",
                    },
                    {
                      value: "custom_range",
                      label: "Custom Range",
                    },
                  ]}
                />
              </Form.Item>
              <Form.Item name="min_cs_chart_range" label="Min">
                <InputNumber className="w-16" disabled />
              </Form.Item>
              <Form.Item name="max_cs_chart_range" label="Max">
                <InputNumber className="w-16" disabled />
              </Form.Item>
            </Space>
          </Col>
        </Row>
        {["bar_histogram", "curve_histogram"].indexOf(chartType) !== -1 && (
          <Row>
            <Col span={24}>
              <Space size="large">
                <Form.Item name="" valuePropName="checked">
                  <Checkbox disabled>Show y-axis as pct (%)</Checkbox>
                </Form.Item>
                <Form.Item name="" valuePropName="checked">
                  <Checkbox disabled>Include Overall Data (curve)</Checkbox>
                </Form.Item>
              </Space>
            </Col>
          </Row>
        )}
        {chartType === "boxplot" && (
          <>
            <Row>
              <Col span={24}>
                <Space size="large">
                  <Form.Item
                    className="m-0"
                    name="log_scale"
                    valuePropName="checked"
                  >
                    <Checkbox disabled>Use Log Scale</Checkbox>
                  </Form.Item>
                  <Form.Item
                    className="m-0"
                    name="show_outliers_in_boxwhisker"
                    valuePropName="checked"
                  >
                    <Checkbox disabled>Show Outliers</Checkbox>
                  </Form.Item>
                </Space>
              </Col>
            </Row>
            <Row gutter={14}>
              <Col span={8}>
                <Form.Item
                  label="Plot by"
                  name="box_plot_by"
                  labelCol={{ span: 24 }}
                  wrapperCol={{ span: 24 }}
                >
                  <Select
                    placeholder="-Select-"
                    popupMatchSelectWidth={false}
                    options={[
                      {
                        value: "group",
                        label: "Group",
                      },
                      {
                        value: "dlog",
                        label: "Datalog",
                      },
                    ]}
                    disabled
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="Sort by"
                  name="box_sort_by"
                  labelCol={{ span: 24 }}
                  wrapperCol={{ span: 24 }}
                >
                  <Select
                    placeholder="-Select-"
                    popupMatchSelectWidth={false}
                    options={[]}
                    disabled
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="Sort Order"
                  name="box_sort_dir"
                  labelCol={{ span: 24 }}
                  wrapperCol={{ span: 24 }}
                >
                  <Select
                    placeholder="-Select-"
                    popupMatchSelectWidth={false}
                    options={[
                      {
                        value: "asc",
                        label: "Ascending",
                      },
                      {
                        value: "desc",
                        label: "Descending",
                      },
                    ]}
                    disabled
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        )}
        {chartType === "wafer" && (
          <Row>
            <Col span={24}>
              <Space size="large">
                <Form.Item name="hide_reprobe" valuePropName="checked">
                  <Checkbox disabled>Hide Reprobe (+)</Checkbox>
                </Form.Item>
              </Space>
            </Col>
          </Row>
        )}
        <Row>
          <Col span={24}>
            <Space align="end" size="large">
              <Form.Item name="" label="X-axis angle">
                <InputNumber className="w-20" disabled />
              </Form.Item>
              <Form.Item name="" label="Axes Font Size">
                <InputNumber className="w-20" disabled />
              </Form.Item>
              <Form.Item name="" label="Plot Width">
                <InputNumber className="w-20" disabled />
              </Form.Item>
              <Form.Item name="" label="Plot Height">
                <InputNumber className="w-20" disabled />
              </Form.Item>
            </Space>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Space size="large">
              <Form.Item name="" valuePropName="checked">
                <Checkbox disabled>Show Data per Site</Checkbox>
              </Form.Item>
              <Form.Item name="" valuePropName="checked">
                <Checkbox disabled>Hide Default Status Info</Checkbox>
              </Form.Item>
              <Form.Item name="show_limits" valuePropName="checked">
                <Checkbox>Show Test Limits</Checkbox>
              </Form.Item>
            </Space>
          </Col>
        </Row>
        <Divider className="mt-0!">
          <Text strong>Stats Below The Chart</Text>
        </Divider>
        <Row>
          <Col span={24}>
            <Alert
              className="mb-2"
              message={
                <>
                  <Text strong>Note:</Text> You can only select up to a maximum
                  of 10
                </>
              }
              type="info"
              closable
            />
            <Checkbox.Group
              style={{
                width: "100%",
              }}
              disabled
            >
              <Row>
                <Col flex={"20%"}>
                  <Space direction="vertical">
                    <Checkbox value="">Mean</Checkbox>
                    <Checkbox value="">Standard Deviation</Checkbox>
                    <Checkbox value="">Max Result</Checkbox>
                    <Checkbox value="">Cpu</Checkbox>
                    <Checkbox value="">Cpk</Checkbox>
                  </Space>
                </Col>
                <Col flex={"20%"}>
                  <Space direction="vertical">
                    <Checkbox value="">Median</Checkbox>
                    <Checkbox value="">Min Result</Checkbox>
                    <Checkbox value="">Cp</Checkbox>
                    <Checkbox value="">Cpl</Checkbox>
                    <Checkbox value="">Execs Overall</Checkbox>
                  </Space>
                </Col>
                <Col flex={"20%"}>
                  <Space direction="vertical">
                    <Checkbox value="">Fails Overall</Checkbox>
                    <Checkbox value="">Pfails Exces</Checkbox>
                    <Checkbox value="">High Limit</Checkbox>
                    <Checkbox value="">IQR High Limit</Checkbox>
                    <Checkbox value="">Kurtosis</Checkbox>
                  </Space>
                </Col>
                <Col flex={"20%"}>
                  <Space direction="vertical">
                    <Checkbox value="">Pfails Unit</Checkbox>
                    <Checkbox value="">Low Limit</Checkbox>
                    <Checkbox value="">IQR Low Limit</Checkbox>
                    <Checkbox value="">Outlier Count</Checkbox>
                    <Checkbox value="">Skewness</Checkbox>
                  </Space>
                </Col>
                <Col flex={"20%"}>
                  <Space direction="vertical">
                    <Checkbox value="">P-Value (QQ-Plot)</Checkbox>
                  </Space>
                </Col>
              </Row>
            </Checkbox.Group>
          </Col>
        </Row>
        <Divider className="mt-0!">
          <Text strong>More Chart Options</Text>
        </Divider>
        <Row gutter={14}>
          <Col span={6}>
            <Form.Item
              label="Primary Sort"
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
            >
              <Select
                placeholder="-Select-"
                popupMatchSelectWidth={false}
                options={[]}
                disabled
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label=" "
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
            >
              <Space>
                <Button icon={<PlusCircleOutlined />} disabled>
                  Add More Sorting
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
        {/* commented out for now since it is not decided if this should be present or not */}
        {/* <Row gutter={14}>
          <Col span={6}>
            <Form.Item
              name=""
              label="Arrangement"
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
              tooltip={{
                title: "Chart arrangement",
                icon: <InfoCircleOutlined />,
              }}
            >
              <Select
                placeholder="-Select-"
                popupMatchSelectWidth={false}
                options={[]}
                disabled
              />
            </Form.Item>
          </Col>
        </Row> */}
        <Row>
          <Col span={24}>
            <Form.Item className="mb-2" valuePropName="checked">
              <Checkbox disabled>
                Auto-Retain Filter Settings for Next Visit
              </Checkbox>
              <Tooltip
                title="Enable this option to automatically retain your current
settings as default for your next visit."
              >
                <QuestionCircleTwoTone />
              </Tooltip>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={14}>
          <Col span={6}>
            <Form.Item
              name="current_settings"
              label="Save Current Settings as:"
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
              tooltip={{
                title:
                  "Save your current configuration with a custom name for future use.",
                icon: <QuestionCircleTwoTone />,
              }}
            >
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label=" "
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
            >
              <Button disabled>Save Current Settings</Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default memo(ChartspaceChartOptionsForm);
