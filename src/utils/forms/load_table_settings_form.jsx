import { App, Form, Select } from "antd";
import { useState } from "react";
import { useEffectApiFetch } from "../../hooks";
import Api from "../api";
import styles from "./styles.module.css";

/**
 * Form to load saved columns
 *
 * @returns {JSX.Element}
 */
const LoadTableSettingsForm = ({
  loadTableSettingsForm,
  gridId,
  shouldUpdateSavedTableOptions,
  setIsDisableDeleteTableSettingsButton,
  setIsDisableLoadTableSettingsButton,
}) => {
  const [savedTableOptions, setSavedTableOptions] = useState([]);
  const { message } = App.useApp();

  useEffectApiFetch(
    () => {
      return getSavedTableOptions();
    },
    () => {
      setSavedTableOptions([]);
    },
    [shouldUpdateSavedTableOptions],
  );

  /**
   * Get saved table settings options
   *
   * @returns {AbortController} abortCtl
   */
  const getSavedTableOptions = () => {
    const abortCtl = Api.getSavedTableOptions(
      (res) => {
        if (res.success) {
          setSavedTableOptions(res.data);
        } else {
          message.warning(res.message);
        }
      },
      (err) => {
        message.error(err);
      },
      {
        table_id: gridId,
      },
    );

    return abortCtl;
  };

  /**
   * Handle change event of saved table settings selection
   *
   * @param {int} tableSettingsKey
   */
  const onChange = (tableSettingsKey) => {
    setIsDisableDeleteTableSettingsButton(!tableSettingsKey);
    setIsDisableLoadTableSettingsButton(!tableSettingsKey);
  };

  return (
    <>
      <Form
        form={loadTableSettingsForm}
        name="load_table_settings_form"
        className={styles.modalForm}
      >
        <Form.Item
          name="table_settings_key"
          rules={[
            {
              required: true,
              message: "Please select table name!",
            },
          ]}
        >
          <Select
            showSearch
            allowClear
            placeholder="Please Select Table"
            optionFilterProp="children"
            popupMatchSelectWidth={false}
            onChange={onChange}
            filterOption={(input, option) =>
              (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
            }
            options={savedTableOptions}
            notFoundContent="No saved settings yet"
          />
        </Form.Item>
      </Form>
    </>
  );
};

export default LoadTableSettingsForm;
