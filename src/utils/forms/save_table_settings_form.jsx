import { ExclamationCircleOutlined } from "@ant-design/icons";
import { Form, Input, message, Modal } from "antd";
import Api from "../api";
import styles from "./styles.module.css";

const { confirm } = Modal;

/**
 * Form to save table settings
 *
 * @oaram {FormInstance} saveTableSettingsForm
 * @param {string} gridId
 * @param {boolean} shouldUpdateSavedTableOptions
 * @param {function} setIsSaveTableSettingsModalOpen
 * @param {function} setIsDisableSaveTableSettingsButton
 * @param {function} setShouldUpdateSavedTableOptions
 * @param {function} getColumnState
 * @returns {JSX.Element}
 */
const SaveTableSettingsForm = ({
  saveTableSettingsForm,
  gridId,
  shouldUpdateSavedTableOptions,
  setIsSaveTableSettingsModalOpen,
  setIsDisableSaveTableSettingsButton,
  setShouldUpdateSavedTableOptions,
  getColumnState,
}) => {
  const [messageApi, contextHolder] = message.useMessage();

  /**
   * Handle change event of name field
   *
   * @param {Event} e
   */
  const onChange = (e) => {
    setIsDisableSaveTableSettingsButton(!e.target.value);
  };

  /**
   * Save table settings
   *
   * @param {object} values
   */
  const saveTableSettings = (values) => {
    Api.saveTableSettings(
      (res) => {
        if (res.success) {
          messageApi.success(res.message);
          saveTableSettingsForm.resetFields();
          setIsDisableSaveTableSettingsButton(true);
          setIsSaveTableSettingsModalOpen(false);
          setShouldUpdateSavedTableOptions(!shouldUpdateSavedTableOptions);
        } else {
          messageApi.warning(res.message);
        }
        saveTableSettingsForm.setFieldValue("is_overwrite", false);
      },
      (err, errorCode) => {
        if (errorCode === 409) {
          setIsSaveTableSettingsModalOpen(false);
          showOverwriteConfirm();
        } else {
          messageApi.error(err);
        }
        saveTableSettingsForm.setFieldValue("is_overwrite", false);
      },
      {
        table_id: gridId,
        name: values.name,
        data: getColumnState(),
        overwrite: values.is_overwrite,
      },
    );
  };

  /**
   * Show confirmation to overwrite or rename and save as new
   */
  const showOverwriteConfirm = () => {
    confirm({
      title: "Name Already Exists!",
      icon: <ExclamationCircleOutlined />,
      content:
        "The table name you entered already exists. What do you want to do?",
      okText: "Overwrite Existing",
      cancelText: "Rename and Save as New",
      onOk() {
        saveTableSettingsForm.setFieldValue("is_overwrite", true);
        saveTableSettingsForm.submit();
      },
      onCancel() {
        setIsSaveTableSettingsModalOpen(true);
      },
    });
  };

  return (
    <>
      {contextHolder}
      <Form
        form={saveTableSettingsForm}
        name="save_table_settings_form"
        className={styles.modalForm}
        onFinish={saveTableSettings}
      >
        <Form.Item
          name="name"
          rules={[
            {
              required: true,
              message: "Please input table name!",
            },
          ]}
        >
          <Input placeholder="Table Name" onChange={onChange} />
        </Form.Item>
        <Form.Item name="is_overwrite" initialValue={false} noStyle>
          <Input type="hidden" />
        </Form.Item>
      </Form>
    </>
  );
};

export default SaveTableSettingsForm;
