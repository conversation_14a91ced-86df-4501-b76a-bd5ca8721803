import { InfoCircleOutlined } from "@ant-design/icons";
import { Col, Divider, Form, Row, Select, Space, Typography } from "antd";
import { useRef } from "react";
import { AddParametricColumnGridBlueprint } from "../grid/blueprints/add_parametric_column_grid_blueprint";
import YHGrid from "../grid/yh_grid";
import styles from "./styles.module.css";

const { Text, Title } = Typography;

/**
 * Form to add parametric column
 *
 * @returns {JSX.Element}
 */
const AddParametricColumnForm = () => {
  const [form] = Form.useForm();
  const parametricColumnGridRef = useRef();

  return (
    <Form
      form={form}
      className={styles.modalForm}
      layout="vertical"
      name="add_parametric_column_form"
    >
      <Row className="my-1" gutter={8}>
        <Col span={8}>
          <Form.Item name="program" label="Program">
            <Select
              showSearch
              placeholder="Select a Program"
              popupMatchSelectWidth={false}
              options={[
                {
                  value: "program_1",
                  label: "Program 1",
                },
              ]}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name="tests"
            label="Tests"
            tooltip={{
              title: "You can select more than 1 test",
              icon: <InfoCircleOutlined />,
            }}
          >
            <Select
              mode="multiple"
              allowClear
              placeholder="Select Tests"
              popupMatchSelectWidth={false}
              options={[
                {
                  value: "test_1",
                  label: "Test 1",
                },
                {
                  value: "test_2",
                  label: "Test 2",
                },
              ]}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name="statistic_type"
            label="Statistic Type"
            initialValue={["all"]}
          >
            <Select
              showSearch
              popupMatchSelectWidth={false}
              options={[
                {
                  value: "all",
                  label: "All Data",
                },
              ]}
            />
          </Form.Item>
        </Col>
      </Row>
      <YHGrid
        gridRef={parametricColumnGridRef}
        component={AddParametricColumnGridBlueprint}
        filters={{}}
      />
      <Space className="text-center w-full" direction="vertical">
        <Divider>
          <Title level={5}>Column Placement</Title>
        </Divider>
        <Space>
          <Form.Item name="position" label="Select Position">
            <Select
              placeholder="-Select Insert Position-"
              popupMatchSelectWidth={false}
              options={[
                {
                  value: "end_of_table",
                  label: "End of Table",
                },
                {
                  value: "beginning_of_table",
                  label: "Beginning of Table",
                },
              ]}
            />
          </Form.Item>
          <Text className="mx-2">or</Text>
          <Form.Item name="insert_column_after" label="Insert Column After">
            <Select
              showSearch
              placeholder="-Select Column-"
              popupMatchSelectWidth={false}
              options={[
                {
                  value: "process_date",
                  label: "Process Date",
                },
                {
                  value: "handler_id",
                  label: "Handler ID",
                },
                {
                  value: "lot_id",
                  label: "Lot ID",
                },
              ]}
            />
          </Form.Item>
        </Space>
      </Space>
    </Form>
  );
};

export default AddParametricColumnForm;
