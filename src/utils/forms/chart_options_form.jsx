import { Form, Tabs } from "antd";
import React, { useEffect, useState } from "react";
import { useBoundStore } from "../../store/store";
import FormHelper from "../form_helper";
import LegendColorSelection from "./fields/legend_color_selection";
import AxisRangeSelection from "./fields/axis_range_selection";

/**
 * Form to update settings for chart
 *
 * @param {FormInstance} form
 * @param {object} fields
 * @param {object} settings
 * @param {object} component
 * @param {array} excludeFields
 * @returns {JSX.Element}
 */
const ChartOptionsForm = ({
  form,
  fields,
  settings,
  component,
  excludeFields,
}) => {
  const [chartOptionsTabItems, setChartOptionsTabItems] = useState([]);
  const currentChart = useBoundStore((state) => state.currentChart);

  useEffect(() => {
    const tabItems = getChartOptionsTabItems();
    setChartOptionsTabItems(tabItems);
  }, []);

  /**
   * Get chart options form tab items
   *
   * @returns {array} tabItems
   */
  const getChartOptionsTabItems = () => {
    const generalTabFormItems = [];
    const groupTabItems = Object.keys(fields).reduce((result, key) => {
      if (
        (!fields[key].includeIn ||
          (fields[key].includeIn &&
            fields[key].includeIn.indexOf(component.component) !== -1)) &&
        (!excludeFields || (excludeFields && excludeFields.indexOf(key) === -1))
      ) {
        if (fields[key].options) {
          const tabItem = {
            // TODO: Find a way to avoid fixed pixel sizes
            className: "h-[calc(100vh-400px)] overflow-auto",
            label: fields[key].label,
            key: key,
            children: createOptionGroupFormItems(fields[key], key),
          };
          result.push(tabItem);
        } else {
          const fieldValue = settings[key] ? settings[key] : "";
          generalTabFormItems.push(
            FormHelper.getFormItem(fields[key], key, fieldValue, "", 4, 20),
          );
        }
      }
      return result;
    }, []);

    const generalTabItem = {
      // TODO: Find a way to avoid fixed pixel sizes
      className: "h-[calc(100vh-400px)] overflow-auto",
      label: "General",
      key: "general",
      children: generalTabFormItems,
    };

    const tabItems = [generalTabItem, ...groupTabItems];

    return tabItems;
  };

  /**
   * Generate chart option group form items
   *
   * @param {object} optionGroup
   * @param {string} groupKey
   * @returns {JSX.Element}
   */
  const createOptionGroupFormItems = (optionGroup, groupKey) => {
    return Object.keys(optionGroup.options).map((optionKey) => {
      const field = optionGroup.options[optionKey];
      const fieldValue =
        settings[groupKey] && settings[groupKey][optionKey]
          ? settings[groupKey][optionKey]
          : "";
      const formItem =
        field.type === "custom"
          ? getCustomFormItem(field, optionKey, [groupKey], 4, 20)
          : FormHelper.getFormItem(
              field,
              optionKey,
              fieldValue,
              [groupKey],
              4,
              20,
            );
      return formItem;
    });
  };

  /**
   * Get custom field form item
   *
   * @param {object} field
   * @param {string} fieldKey
   * @param {string} fieldGroup
   * @param {int} labelColSpan
   * @param {int} wrapperColSpan
   * @returns {JSX.Element} formItem
   */
  const getCustomFormItem = (
    field,
    fieldKey,
    fieldGroup = "",
    labelColSpan = 6,
    wrapperColSpan = 18,
  ) => {
    let formItem = <></>;
    switch (fieldKey) {
      case "color":
        formItem = (
          <LegendColorSelection
            key={`${fieldGroup}_${fieldKey}`}
            form={form}
            chart={currentChart}
            field={field}
            labelColSpan={labelColSpan}
            wrapperColSpan={wrapperColSpan}
          />
        );
        break;
      case "axisRange":
        formItem = (
          <AxisRangeSelection
            // key={`${fieldGroup}_${fieldKey}`}
            form={form}
            // chart={currentChart}
            field={field}
            labelColSpan={labelColSpan}
            wrapperColSpan={wrapperColSpan}
          />
        );
        break;
    }

    return formItem;
  };

  return (
    <div>
      <Form form={form} size="small">
        <Tabs defaultActiveKey="1" items={chartOptionsTabItems} />
      </Form>
    </div>
  );
};

export default ChartOptionsForm;
