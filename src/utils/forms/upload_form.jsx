import {
  InboxOutlined,
  ExclamationCircleOutlined,
  FileOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import {
  App,
  Button,
  Col,
  Form,
  Row,
  Select,
  Typography,
  Upload,
  Alert,
  Modal,
  Tooltip,
  Checkbox,
  Space,
  Spin,
} from "antd";
import React, { useState, useRef, memo } from "react";
import { Archive } from "libarchive.js";
import { remove, pull } from "lodash";
import { useEffectApiFetch } from "../../../src/hooks";
import Helper from "../../../src/utils/helper";
import Api from "../../../src/utils/api";
import "./override.css";
import YHGrid from "../../../src/utils/grid/yh_grid";
import { ComponentNameMapper } from "../../../src/utils/grid/component_name_mapper";

const { Text, Title } = Typography;
const { <PERSON>agger } = Upload;

Archive.init({
  workerUrl: "/libarchive.js/dist/worker-bundle.js",
});

/**
 * Form used for uploading file/datalog
 *
 * @param {string} tabKey
 * @param {string} pageKey
 * @param {FormInstance} uploadForm
 * @param {boolean} isUploadFormOpen
 * @param {function} setIsUploadFormOpen
 * @param {boolean} uploadFormDisabled
 * @param {function} setUploadFormDisabled
 * @param {boolean} uploadingIsComplete
 * @param {function} setUploadingIsComplete
 * @param {object} userPdbStorageInfo
 * @returns {JSX.Element}
 */
const UploadForm = ({
  tabKey,
  pageKey,
  uploadForm,
  isUploadFormOpen,
  setIsUploadFormOpen,
  uploadFormDisabled,
  setUploadFormDisabled,
  uploadingIsComplete,
  setUploadingIsComplete,
  userPdbStorageInfo = {},
}) => {
  const [manufacturingProcessOptions, setManufacturingProcessOptions] =
    useState([]);
  const [subconOptions, setSubconOptions] = useState([]);
  // const [characterisationSettingOptions, setCharacterisationSettingOptions] =
  //   useState([]);
  const [archiveList, setArchiveList] = useState([]);
  const [fileTypeList, setFileTypeList] = useState([]);
  const [isExtracting, setIsExtracting] = useState(false);
  const [isDuplicateChecking, setIsDuplicateChecking] = useState(false);
  const [duplicateOptions, setDuplicateOptions] = useState([]);
  const [isDuplicateModalOpen, setIsDuplicateModalOpen] = useState(false);
  const [files, setFiles] = useState([]);
  const [invalidFiles, setInvalidFiles] = useState([]);
  const [oversizedFiles, setOversizedFiles] = useState([]);
  const [datalogTypeExist, setDatalogTypeExist] = useState(true);
  const [
    duplicateProductionUploadGridComponent,
    setDuplicateProductionUploadGridComponent,
  ] = useState();
  const { message } = App.useApp();
  const uploadRef = useRef();
  const duplicateProductionUploadGridRef = useRef();
  const maxFileSizeGB = 4; // TO DO: to be provided from back-end
  const [{ warning, error, confirm }, contextHolder] = Modal.useModal();
  let overwrittenFiles = [];
  let invalidFilesShown = false;

  useEffectApiFetch(
    () => {
      return Helper.getManufacturingProcessOptions(
        setManufacturingProcessOptions,
      );
    },
    () => {
      setManufacturingProcessOptions([]);
    },
  );

  useEffectApiFetch(
    () => {
      return getSubconOptions();
    },
    () => {
      setSubconOptions();
    },
    [manufacturingProcessOptions],
  );

  useEffectApiFetch(
    () => {
      return getDuplicateProductionUploadGridComponent();
    },
    () => {
      setDuplicateProductionUploadGridComponent();
    },
  );

  // useEffectApiFetch(
  //   () => {
  //     return getCharacterisationSettingOptions();
  //   },
  //   () => {
  //     setCharacterisationSettingOptions();
  //   },
  // );

  useEffectApiFetch(
    () => {
      return getArchiveList();
    },
    () => {
      setArchiveList([]);
    },
  );

  useEffectApiFetch(
    () => {
      return getFileTypesList();
    },
    () => {
      setFileTypeList([]);
    },
  );

  /**
   * Get and set duplicate production upload grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getDuplicateProductionUploadGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setDuplicateProductionUploadGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: ComponentNameMapper.duplicate_manual_production_upload,
      },
    );

    return abortCtl;
  };

  /**
   * Get and set supported archive extension list
   *
   * @returns {AbortController} abortCtl
   */
  const getArchiveList = () => {
    const abortCtl = Api.getSupportedArchiveExtensions(
      (res) => {
        if (res.success) {
          setArchiveList(res.data.map((item) => item.text));
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
    );

    return abortCtl;
  };

  /**
   * Get and set supported file type list
   *
   * @returns {AbortController} abortCtl
   */
  const getFileTypesList = () => {
    const abortCtl = Api.getSupportedFileTypes(
      (res) => {
        if (res.success) {
          setFileTypeList(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
    );

    return abortCtl;
  };

  /**
   * Get and set subcon options
   *
   * @returns {AbortController} abortCtl
   */
  const getSubconOptions = () => {
    const abortCtl = Api.getSelectOptions(
      (res) => {
        if (res.success) {
          setSubconOptions(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        field: "subcon",
        mfg_process: manufacturingProcessOptions
          .map((mfgProcess) => {
            return mfgProcess.value;
          })
          .join(),
        cache_it: 0,
      },
    );

    return abortCtl;
  };

  // /**
  //  * Get and set characterisation setting options
  //  *
  //  * @returns {AbortController} abortCtl
  //  */
  // const getCharacterisationSettingOptions = () => {
  //   const abortCtl = Api.getSelectOptions(
  //     (res) => {
  //       if (res.success) {
  //         setCharacterisationSettingOptions(res.data);
  //       } else {
  //         message.warning(res.message, 5);
  //       }
  //     },
  //     (err) => {
  //       message.error(err, 5);
  //     },
  //     {
  //       field: "prep_char_settings",
  //     },
  //   );

  //   return abortCtl;
  // };

  /**
   * Clears upload form
   */
  const clearFilters = () => {
    uploadForm.resetFields();
    setFiles([]);
    setInvalidFiles([]);
    setOversizedFiles([]);
    setIsExtracting(false);
    setUploadFormDisabled(false);
    setUploadingIsComplete(false);
    setDatalogTypeExist(true);
  };

  /**
   * Upload file request handler
   */
  const handleUploadRequest = () => {
    if (files.length > 0) {
      files.forEach((value, index) => {
        let filesCopy = Helper.cloneArray(files);
        filesCopy[index].status = "uploading";
        setFiles(filesCopy);

        const formData = new FormData();
        tabKey === "production_upload" &&
          formData.append("subcon", uploadForm.getFieldValue(`subcon`));
        formData.append("mfg_process", uploadForm.getFieldValue(`mfg_process`));
        formData.append(
          "is_public",
          tabKey === "production_upload"
            ? 1
            : uploadForm.getFieldValue(`is_public`),
        );
        formData.append("files[]", value.originFileObj ?? value);

        // upload files
        Api.uploadFile(
          (res) => {
            // addtionnal checking of datalog processing error
            if (res.success && !res.data[0].error) {
              let filesCopy = Helper.cloneArray(files);
              filesCopy[index].status = "done";
              filesCopy[index].response = res;
              setFiles(filesCopy);
            } else {
              let filesCopy = Helper.cloneArray(files);
              filesCopy[index].status = "error";
              filesCopy[index].response = res;
              filesCopy[index].error = {
                statusText: `${res.data[0].error}`,
              };
              setFiles(filesCopy);
            }
            filesCopy.every((file) => "response" in file) &&
              handleUploadComplete();
          },
          (err) => {
            let filesCopy = Helper.cloneArray(files);
            filesCopy[index].status = "error";
            filesCopy[index].response = err;
            filesCopy[index].error = {
              statusText: err,
            };
            setFiles(filesCopy);
            filesCopy.every((file) => "response" in file) &&
              handleUploadComplete();
          },
          formData,
          "form-data",
          tabKey === "production_upload"
            ? "manual_production_upload"
            : "engineering_upload",
        );
      });

      setIsUploadFormOpen(false);
      setUploadFormDisabled(true);
    }
  };

  /**
   * Validate if attached file is supported
   *
   * @param {object} file
   * @param {array} fileList
   */
  const validateFile = (file, fileList) => {
    if (isSupportedArchive(file)) {
      setIsExtracting(true);
      extractFiles(file, fileList);
    } else if (hasSupportedFileType(file.name)) {
      attachFile(file);
    } else {
      invalidFiles.push(file);
    }
  };

  /**
   * Check if the attached file is a supported archive file
   *
   * @param {object} file
   * @returns {boolean}
   */
  const isSupportedArchive = (file) => {
    return (
      file.name &&
      archiveList.some((extension) =>
        file.name.toLowerCase().endsWith("." + extension),
      )
    );
  };

  /**
   * Check if file has a supported file type
   *
   * @param {string} filename
   * @returns {boolean}
   */
  const hasSupportedFileType = (filename) => {
    return fileTypeList.some((type) => {
      return new RegExp(type.regex.slice(1, -2), "i").test(filename);
    });
  };

  /**
   * Check if attached files has a datalog type
   *
   * @param {array} fileList
   */
  const checkDatalogTypeExists = (fileList) => {
    const datalogTypeExist = fileList.some((file) =>
      fileTypeList.some(
        (type) =>
          new RegExp(type.regex.slice(1, -2), "i").test(file.name) &&
          type.datalog_type === "datalog",
      ),
    );
    setDatalogTypeExist(datalogTypeExist);
  };
  /**
   * Extract archive files
   *
   * @param {object} archiveFile
   * @param {array} fileList
   */
  const extractFiles = async (archiveFile, fileList) => {
    if (!archiveFile) return;

    const archive = await Archive.open(archiveFile);
    let extractedFiles = await archive.extractFiles();
    if (Object.keys(extractedFiles).length > 0) {
      // traverse extractedFiles and store file values
      traverseNestedArchive(extractedFiles, fileList);
    } else {
      error({
        centered: true,
        title: "Error on extracting files",
        maskStyle: { backgroundColor: "rgba(100, 100, 100, 0.1)" },
      });
    }
    fileList.some((file) => {
      if (file.name === archiveFile.name) {
        file.extracted = true;
        return true;
      }
    });

    // checking if all archive files are extracted
    let allArchiveFilesExtracted = fileList.every(
      (file) => !isSupportedArchive(file) || file.extracted === true,
    );
    setIsExtracting(!allArchiveFilesExtracted);
    if (allArchiveFilesExtracted && !invalidFilesShown) {
      let filesCopy = Helper.cloneArray(files);
      setFiles(filesCopy);
      showInvalidAndOversizedFiles();
      invalidFilesShown = true;
    }
  };

  /**
   * Traverse extractedFiles, remove non file values and store file values
   *
   * @param {object} extractedFiles
   * @param {array} fileList
   */
  const traverseNestedArchive = (extractedFiles, fileList) => {
    Object.values(extractedFiles).forEach((value) => {
      if (
        typeof value === "object" &&
        value !== null &&
        !(value instanceof File)
      ) {
        traverseNestedArchive(value, fileList);
      } else {
        validateFile(value, fileList);
      }
    });
  };

  /**
   * Checking of attached file
   *
   * @param {object} file
   */
  const attachFile = (file) => {
    if (files.some((item) => item.name === file.name)) {
      confirm({
        title: "File Already Attached!",
        icon: <ExclamationCircleOutlined />,
        centered: true,
        maskStyle: { backgroundColor: "rgba(100, 100, 100, 0.1)" },
        content: (
          <>
            <Text strong italic>
              {file.name}{" "}
            </Text>
            <Text>
              is already attached. Would you like to replace the existing file?
            </Text>
          </>
        ),
        okText: "Overwrite Existing",
        cancelText: "Cancel Attachment",
        onOk() {
          let filesCopy = Helper.cloneArray(files);
          remove(filesCopy, (item) => {
            return item.name === file.name;
          });
          filesCopy.push(file);
          setFiles(filesCopy);
        },
      });
    } else {
      const maxFileSizeBytes = maxFileSizeGB * 1024 * 1024 * 1024;
      if (file.size > maxFileSizeBytes) {
        oversizedFiles.push(file);
      } else {
        files.push(file);
      }
    }
  };

  /**
   * Validation before uploading files
   */
  const validateUpload = () => {
    if (tabKey === "engineering_upload") {
      const totalUploadSize = files.reduce(
        (accumulator, file) => accumulator + file.size,
        0,
      );
      if (
        totalUploadSize >
        userPdbStorageInfo?.capacity - userPdbStorageInfo?.used
      ) {
        error({
          centered: true,
          title: "Upload Limit Exceeded",
          content: (
            <Text>
              You cannot upload more files due to low storage. Your maximum
              capacity is {Helper.formatFileSize(userPdbStorageInfo.capacity)}.
              Please free up space or remove existing files before uploading
              again. If you need to add more storage, please contact{" "}
              <a
                href="https://yieldhub.zendesk.com"
                target="_blank"
                rel="noopener noreferrer"
              >
                support
              </a>
              .
            </Text>
          ),
        });
        return;
      }
    }

    uploadForm.getFieldValue(`overwrite`)
      ? handleUploadRequest()
      : checkDuplicate();
  };

  /**
   * Checking of duplicate files on database
   */
  const checkDuplicate = () => {
    setIsDuplicateChecking(true);
    if (files.length > 0) {
      const uploadType = tabKey === "production_upload" ? "manual" : "personal";
      const filenames = files.map((file) =>
        file.name.endsWith(".gz") ? file.name : `${file.name}.gz`,
      );

      let filesInfo = [];
      files.forEach((value) => {
        filesInfo.push({
          name: value.name,
          size: value.size,
          modified_date: value.lastModified,
        });
      });

      let payload = {
        files: filesInfo,
        upload_type: uploadType,
        file_name: filenames.join(), // for engineering upload param
      };

      Api.checkDuplicateFile(
        (res) => {
          setIsDuplicateChecking(false);
          if (res.success) {
            const hasDuplicate =
              res.data.table_data?.length ||
              (Array.isArray(res.data) &&
                res.data.some((item) => item.has_duplicate));
            if (hasDuplicate) {
              setIsDuplicateModalOpen(true);
              if (res.data.table_data?.length) {
                checkForDuplicates(res.data.table_data);
                duplicateProductionUploadGridComponent.props = {
                  ...duplicateProductionUploadGridComponent.props,
                  params: { data: res.data.table_data },
                };
              } else {
                checkForDuplicates(res.data);
                const filteredFiles = files.filter(
                  (file) => file.has_duplicate,
                );
                const checkboxOptions = filteredFiles.map((file) => ({
                  label: file.name,
                  value: file.name,
                }));
                setDuplicateOptions(checkboxOptions);
              }
            } else {
              handleUploadRequest();
            }
          } else {
            message.warning(res.message, 5);
          }
        },
        (err) => {
          setIsDuplicateChecking(false);
          message.error(err, 5);
        },
        payload,
      );
    }
  };

  /**
   * Checking of duplicate files on api response and reflecting it on files
   *
   * @param {array} data
   */
  const checkForDuplicates = (data) => {
    files.forEach((file) => {
      let matchingData = data.find(
        (d) =>
          d.file_name ===
          (file.name.endsWith(".gz") ? file.name : `${file.name}.gz`),
      );
      if (matchingData) {
        file.has_duplicate = matchingData.has_duplicate || true;
      }
    });
  };

  /**
   * Handles closing of duplicate modal
   */
  const handleCloseOverwriteModal = () => {
    overwrittenFiles = [];
    setIsDuplicateModalOpen(false);
  };

  /**
   * Handling of overwriting files
   */
  const overwriteFiles = () => {
    remove(files, (file) => {
      return file.has_duplicate && !overwrittenFiles.includes(file.name);
    });

    handleCloseOverwriteModal();
    handleUploadRequest();
  };

  /**
   * Handling of uploading files without duplicate
   */
  const uploadWithoutDuplicate = () => {
    remove(files, (file) => {
      return file.has_duplicate;
    });

    handleCloseOverwriteModal();
    handleUploadRequest();
  };

  /**
   * Handler when all files are uploaded
   */
  const handleUploadComplete = () => {
    let filesUploadedCount = files.filter(
      (file) => file.status === "done",
    ).length;
    setUploadingIsComplete(true);
    if (filesUploadedCount === files.length) {
      warning({
        centered: true,
        title: "Upload Complete!",
        icon: <CheckCircleOutlined style={{ color: "green" }} />,
        content:
          "All files have been successfully uploaded and are now being processed.",
        onOk() {
          clearFilters();
        },
        onCancel() {
          clearFilters();
        },
      });
    } else if (filesUploadedCount !== files.length && filesUploadedCount > 0) {
      error({
        centered: true,
        title: "Upload Error",
        content: (
          <div>
            {`${files.length - filesUploadedCount} out of ${files.length} files failed to upload.`}
            <br />
            Please try again or contact support if the issue persists.
          </div>
        ),
        okText: "View Files",
        onOk() {
          setIsUploadFormOpen(true);
        },
        onCancel() {
          setIsUploadFormOpen(true);
        },
      });
    } else {
      error({
        centered: true,
        title: "Upload Error",
        okText: files.length > 1 ? "View Files" : "View File",
        content: (
          <div>
            {files.length > 1
              ? "An error occured while uploading your files."
              : "An error occured while uploading your file."}
            <br />
            Please try again or contact support if the issue persists.
          </div>
        ),
        onOk() {
          setIsUploadFormOpen(true);
        },
        onCancel() {
          setIsUploadFormOpen(true);
        },
      });
    }
  };

  /**
   * Handles the cancellation of upload
   */
  const handleCancelUpload = () => {
    if (!isExtracting && !isDuplicateChecking) {
      if (files.length > 0) {
        if (uploadFormDisabled) {
          setIsUploadFormOpen(false);
          clearFilters();
        } else {
          confirm({
            title: "Cancel Without Uploading",
            icon: <ExclamationCircleOutlined />,
            centered: true,
            content:
              "Are you sure you want to cancel without uploading? All attachments will be lost.",
            okText: "Back",
            cancelText: "Cancel and Leave",
            onCancel() {
              setIsUploadFormOpen(false);
              clearFilters();
            },
          });
        }
      } else {
        setIsUploadFormOpen(false);
        clearFilters();
      }
    }
  };

  /**
   * Upload custom item renderer
   *
   * @param {object} originNode
   * @param {object} file
   * @returns {JSX.Element}
   */
  const customItemRender = (originNode, file) => {
    return file.status === "error" ? (
      <div
        style={{
          color: "red",
        }}
      >
        <span className="text-red block mb-[-6px]">
          {file.error.statusText}
        </span>
        {originNode}
        <span
          className="text-red italic block ml-1 mt-[-2px]"
          style={{
            fontSize: "0.65rem",
          }}
        >
          {Helper.formatFileSize(file.size)}
        </span>
      </div>
    ) : (
      <>
        {originNode}
        <Text
          italic
          className="block ml-1 mt-[-2px]"
          style={{
            fontSize: "0.65rem",
          }}
        >
          {Helper.formatFileSize(file.size)}
        </Text>
      </>
    );
  };

  /**
   * Display invalid files and oversized files if there are any
   */
  const showInvalidAndOversizedFiles = () => {
    if (invalidFiles.length > 0) {
      error({
        centered: true,
        width: "50%",
        title: "Invalid File Detected!",
        content: (
          <>
            <Text>
              The following type of file(s) are not supported and cannot be
              uploaded:
            </Text>
            <Space className="mt-5 mb-5 gap-y-1 w-full" direction="vertical">
              {invalidFiles.map((file, index) => (
                <Text key={index} strong italic>
                  {file.name}
                </Text>
              ))}
            </Space>
            <Text>Please cancel attachment(s) to proceed.</Text>
          </>
        ),
        okText: "Cancel Attachment",
        onOk() {
          setInvalidFiles([]);
        },
        onCancel() {
          setInvalidFiles([]);
        },
      });
    }

    if (oversizedFiles.length > 0) {
      error({
        centered: true,
        width: "50%",
        title: "File Size Limit Exceeded",
        content: (
          <>
            <Text>
              The following files(s) have exceeded the maximum file size allowed
              per upload. You can upload multiple files as long as the total
              size does not exceed 4GB.
            </Text>
            <Space className="mt-5 mb-5 gap-y-1 w-full" direction="vertical">
              {oversizedFiles.map((file, index) => (
                <Text key={index} strong italic>
                  {file.name}
                </Text>
              ))}
            </Space>
          </>
        ),
        onOk() {
          setOversizedFiles([]);
        },
        onCancel() {
          setOversizedFiles([]);
        },
      });
    }
  };

  /**
   * Update overwritten files value
   *
   * @param {string} fileName
   */
  const updateOverwrittenFiles = (fileName) => {
    if (overwrittenFiles.includes(fileName)) {
      pull(overwrittenFiles, fileName);
    } else {
      overwrittenFiles.push(fileName);
    }
  };

  /**
   * Action mapper for cell input elements
   */
  const cellActions = {
    updateOverwrittenFiles,
  };

  return (
    <Space size="middle" className="w-full">
      <Modal
        width="60%"
        open={isUploadFormOpen}
        destroyOnClose={true}
        onCancel={handleCancelUpload}
        centered
        forceRender
        footer={null}
        closable={false}
      >
        <div className="p-4">
          {contextHolder}
          <Form
            form={uploadForm}
            layout="vertical"
            onFinish={validateUpload}
            disabled={isExtracting || uploadFormDisabled || isDuplicateChecking}
          >
            <Form.Item name={`close_button`} noStyle>
              <Button
                className="ant-modal-close"
                style={{
                  cursor:
                    isExtracting || isDuplicateChecking
                      ? "not-allowed"
                      : "pointer",
                }}
                onClick={handleCancelUpload}
                icon={<CloseOutlined />}
                disabled={
                  (uploadFormDisabled && !uploadingIsComplete) ||
                  isExtracting ||
                  isDuplicateChecking
                }
              />
            </Form.Item>
            <Title level={5}>
              {tabKey === "engineering_upload" ? "Engineering " : "Production "}
              Upload
            </Title>
            <Row gutter={100}>
              <Col span={12}>
                <Form.Item
                  name={`mfg_process`}
                  label="Manufacturing Process"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    placeholder="Select Process"
                    options={manufacturingProcessOptions}
                  />
                </Form.Item>
              </Col>
              {tabKey === "engineering_upload" ? (
                <Col span={12}>
                  {/*  Will only be available once characterisation page is available https://www.wrike.com/open.htm?id=1344713053
                  <Form.Item
                    name={`prep_char_settings`}
                    label={
                      <>
                        <Text>
                          Read Characterisation Conditions Just Like:{" "}
                          <Text type="secondary">(optional)</Text>
                        </Text>
                      </>
                    }
                  >
                    <Select
                      placeholder="Select Condition"
                      options={characterisationSettingOptions}
                    />
                  </Form.Item> */}
                </Col>
              ) : (
                <Col span={12}>
                  <Form.Item
                    name={`subcon`}
                    label="Subcon"
                    rules={[
                      {
                        required: datalogTypeExist,
                      },
                    ]}
                  >
                    <Select
                      placeholder="Select Subcon"
                      options={subconOptions}
                    />
                  </Form.Item>
                </Col>
              )}
            </Row>
            <Row>
              <Col span={24}>
                <Form.Item name={`file`}>
                  <Dragger
                    ref={uploadRef}
                    multiple={true}
                    fileList={files}
                    listType={"picture"}
                    className="w-full overflow-hidden custom-dragger"
                    itemRender={customItemRender}
                    beforeUpload={() => {
                      return false;
                    }}
                    onChange={({ file, fileList }) => {
                      const allArchiveFilesExtracted = fileList.every(
                        (file) =>
                          !isSupportedArchive(file) || file.extracted === true,
                      );

                      // validation before file attachment
                      if (file.status !== "removed") {
                        invalidFilesShown = false;
                        validateFile(file, fileList);
                      } else {
                        setFiles(fileList);
                      }

                      // checking during the last file attached
                      if (
                        fileList.length > 0 &&
                        fileList[fileList.length - 1].uid === file.uid &&
                        allArchiveFilesExtracted
                      ) {
                        showInvalidAndOversizedFiles();
                        invalidFilesShown = true;
                        checkDatalogTypeExists(fileList);
                      }
                    }}
                    iconRender={(file) => {
                      let icon = <FileOutlined />;
                      if (file.status === "done") {
                        icon = (
                          <CheckCircleOutlined style={{ color: "green" }} />
                        );
                      } else if (file.status === "uploading") {
                        icon = (
                          <Spin
                            indicator={<LoadingOutlined spin />}
                            size="large"
                          />
                        );
                      } else if (file.status === "error") {
                        icon = <CloseCircleOutlined style={{ color: "red" }} />;
                      }
                      return icon;
                    }}
                  >
                    <p className="ant-upload-drag-icon">
                      <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">
                      Click or drag file to this area to upload
                    </p>
                    {tabKey === "engineering_upload" && (
                      <p
                        className="ant-upload-hint"
                        style={
                          userPdbStorageInfo?.capacity -
                            userPdbStorageInfo?.used <
                          0.1 * userPdbStorageInfo.capacity
                            ? { color: "#ef4444" }
                            : {}
                        }
                      >
                        Total Storage Capacity:{" "}
                        {Helper.formatFileSize(
                          userPdbStorageInfo?.capacity -
                            userPdbStorageInfo?.used,
                        )}{" "}
                        of {Helper.formatFileSize(userPdbStorageInfo?.capacity)}{" "}
                        remaining.
                      </p>
                    )}
                    <p className="ant-upload-hint">
                      Support for a single or bulk upload.
                      <br />
                      Supported archive: {archiveList.join(", ")}
                      {tabKey === "production_upload" && (
                        <>
                          <br />
                          Maximum of 4GB per file.
                          <br />
                          Files more than 500mb will take longer to process.
                        </>
                      )}
                    </p>
                  </Dragger>
                </Form.Item>
                {isExtracting && (
                  <div className="mb-4">
                    <Alert
                      message="Please wait while extracting attached archive files..."
                      type="warning"
                      showIcon
                    />
                  </div>
                )}
              </Col>
            </Row>
            {tabKey === "engineering_upload" && (
              <div>
                <Form.Item
                  name={`overwrite`}
                  valuePropName="checked"
                  initialValue={false}
                  noStyle
                >
                  <Checkbox>Overwrite files with same file name</Checkbox>
                </Form.Item>
                <Form.Item name={`is_public`} initialValue={0} noStyle>
                  <Checkbox
                    onChange={(e) =>
                      uploadForm.setFieldsValue({
                        [`is_public`]: e.target.checked ? 1 : 0,
                      })
                    }
                  >
                    Upload files as public
                  </Checkbox>
                </Form.Item>
              </div>
            )}
            <Row
              gutter={[8, 0]}
              style={{
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              <Col>
                <Button key="cancel" onClick={handleCancelUpload}>
                  Cancel
                </Button>
              </Col>
              <Col>
                <Button
                  key="clear_all"
                  disabled={
                    (uploadFormDisabled && !uploadingIsComplete) ||
                    isExtracting ||
                    isDuplicateChecking
                  }
                  onClick={clearFilters}
                >
                  Clear All
                </Button>
              </Col>
              <Col>
                <Form.Item shouldUpdate className="m-0">
                  {() => (
                    <Button
                      key="upload"
                      type="primary"
                      htmlType="submit"
                      loading={isDuplicateChecking}
                      disabled={
                        isExtracting ||
                        isDuplicateChecking ||
                        !uploadForm.getFieldValue(`mfg_process`) ||
                        files.length === 0 ||
                        uploadFormDisabled ||
                        (tabKey === "production_upload" &&
                          datalogTypeExist &&
                          !uploadForm.getFieldValue(`subcon`))
                      }
                    >
                      Upload Files
                    </Button>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Modal
              width="50%"
              centered
              destroyOnClose
              open={isDuplicateModalOpen}
              onOk={handleUploadRequest}
              onCancel={handleCloseOverwriteModal}
              footer={[
                <Button key="back" onClick={handleCloseOverwriteModal}>
                  Back
                </Button>,
                <Tooltip key="skip" title="Skip and upload remaining files">
                  <Button onClick={uploadWithoutDuplicate}>Skip</Button>
                </Tooltip>,
                <Button key="overwrite" type="primary" onClick={overwriteFiles}>
                  Overwrite Selected Files
                </Button>,
              ]}
            >
              <Space direction="vertical" className="p-2 min-w-full">
                <Title level={5}>We Found Duplicates!</Title>
                <span>
                  We found files with the same file name that already exist in
                  database. Select files you want to overwrite. Files not
                  selected will not be uploaded.
                </span>

                {tabKey === "production_upload" ? (
                  <>
                    <Row className="flex grow min-h-[30vh]">
                      <Col span={24}>
                        <YHGrid
                          gridRef={duplicateProductionUploadGridRef}
                          gridId={"duplicate_manual_production_upload"}
                          component={duplicateProductionUploadGridComponent}
                          filters={{}}
                          cellActions={cellActions}
                          rowGroups={[]}
                          pageKey={pageKey}
                          wrapperClassName="flex grow flex-col w-full h-full"
                        />
                      </Col>
                    </Row>
                    <Row>
                      <Col span={24}>
                        <div className="mt-4">
                          <Text strong>Note:</Text>
                        </div>
                        <div>
                          <Text strong>Smaller file size: </Text>
                          <Text>Cannot overwrite smaller files.</Text>
                        </div>
                        <div>
                          <Text strong>Unmodified: </Text>
                          <Text>Cannot overwrite identical files.</Text>
                        </div>
                        <div>
                          <Text strong>Larger file size: </Text>
                          <Text>Can overwrite larger files.</Text>
                        </div>
                        <div>
                          <Text strong>Modified: </Text>
                          <Text>
                            Can overwrite modified files with same file size.
                          </Text>
                        </div>
                      </Col>
                    </Row>
                  </>
                ) : (
                  <Checkbox.Group className="my-2">
                    <Space direction="vertical">
                      {duplicateOptions.map((option) => (
                        <Checkbox
                          checked={true}
                          key={option.value}
                          value={option.value}
                          onChange={(e) => {
                            updateOverwrittenFiles(e.target.value);
                          }}
                        >
                          <span className="break-all mx-1">{option.label}</span>
                        </Checkbox>
                      ))}
                    </Space>
                  </Checkbox.Group>
                )}
              </Space>
            </Modal>
          </Form>
        </div>
      </Modal>
    </Space>
  );
};

export default memo(UploadForm);
