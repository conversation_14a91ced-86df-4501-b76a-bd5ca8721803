import {
  // Checkbox,
  Form,
  Input,
} from "antd";
import Api from "../api";
import { useBoundStore } from "../../store/store";
import styles from "./styles.module.css";
import SearchInput from "./fields/search_input";

const { Search } = Input;

/**
 * Form to filter engineering_upload grid
 *
 * @param {Form} form
 * @param {string} gridId
 * @param {object} gridFilters
 * @param {function} setGridFilters
 * @param {function} setIsFilterTableModalOpen
 * @returns {JSX.Element}
 */
const EngineeringTableFiltersForm = ({
  form,
  gridId,
  gridFilters,
  setGridFilters,
  setIsFilterTableModalOpen,
}) => {
  const gridComponentRefs = useBoundStore((state) => state.gridComponentRefs);

  const applyFilter = (values) => {
    values.mfg_process = values.mfg_process?.join();
    const newGridFiltersValue = { ...gridFilters, ...values };
    setGridFilters(newGridFiltersValue);
    gridComponentRefs[gridId]?.current?.reloadGridData();
    setIsFilterTableModalOpen(false);
  };

  return (
    <Form
      form={form}
      className={styles.modalForm}
      layout="vertical"
      name="filter_table_form"
      onFinish={applyFilter}
    >
      <Form.Item
        name="file_name"
        label="Wildcard Search"
        help="Use % for wildcard search"
      >
        <Search placeholder="Input Filename" />
      </Form.Item>
      <Form.Item
        name="mfg_process"
        label="Manufacturing Process"
        help="You may select more than 1 process"
      >
        <SearchInput
          placeholder=""
          apiFunction={Api.getManufacturingProcessOptions}
          apiParams={{}}
          mode={"multiple"}
        />
      </Form.Item>
      {/* <Form.Item
        name="show_char_datalogs"
        valuePropName="checked"
        initialValue={true}
      >
        <Checkbox>Show Character Datalogs</Checkbox>
      </Form.Item> */}
    </Form>
  );
};

export default EngineeringTableFiltersForm;
