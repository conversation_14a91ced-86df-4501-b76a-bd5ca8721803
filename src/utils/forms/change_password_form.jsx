import { App, But<PERSON>, Form, Input, Typography } from "antd";
import { useState } from "react";
import { useBoundStore } from "../../store/store";
import Api from "../api";
import Helper from "../helper";
import Pass<PERSON><PERSON>ield from "./fields/password_field";

const { Text, Title } = Typography;

/**
 * Form to change user password
 *
 * @returns {JSX.Element}
 */
const ChangePasswordForm = () => {
  const [loading, setLoading] = useState(false);
  const [validateStatus, setValidateStatus] = useState();
  const [validateMessage, setValidateMessage] = useState();
  const [allowSubmit, setAllowSubmit] = useState(false);
  const setUserData = useBoundStore((state) => state.setUserData);
  const [form] = Form.useForm();
  const { message } = App.useApp();

  /**
   * Triggered when a field value changes
   * Check if form is valid and allow form submit if valid
   */
  const onFieldsChange = () => {
    const isFormValid =
      !form.getFieldsError().some((item) => item.errors.length > 0) &&
      form.getFieldValue("password") &&
      form.getFieldValue("password_confirmation");
    setAllowSubmit(isFormValid);
  };

  /**
   * Change user password
   *
   * @param {object} values
   */
  const changePassword = (values) => {
    setLoading(true);
    Api.changePassword(
      (res) => {
        if (res.success) {
          Helper.updateUserDataStorage(
            "initial_password_change",
            true,
            setUserData,
          );
          form.resetFields();
          setValidateStatus();
          setValidateMessage();
          message.success(res.message);
        } else {
          setValidateStatus("error");
          setValidateMessage(res.message);
        }
        setLoading(false);
      },
      (err) => {
        setValidateStatus("error");
        setValidateMessage(err);
        setLoading(false);
      },
      {
        old_password: values.current_password,
        new_password: values.password,
        new_password_confirmation: values.password_confirmation,
      },
    );
  };

  return (
    <>
      <Title level={4}>Change Password</Title>
      <Text>
        Changing your password will sign you out of all your devices. You will
        need to enter your new password on all your devices.
      </Text>
      <Form
        form={form}
        name="change_password_form"
        className="w-1/4"
        layout="vertical"
        requiredMark={true}
        onFinish={changePassword}
        onFieldsChange={onFieldsChange}
        autoComplete="off"
      >
        <Form.Item
          name="current_password"
          label="Current Password"
          className="mt-6"
          rules={[
            {
              required: true,
              message: "Please input your password!",
            },
            {
              min: 8,
              message: "Password must be at least 8 characters.",
            },
          ]}
          hasFeedback
        >
          <Input.Password placeholder="Enter current password" />
        </Form.Item>
        <PasswordField
          name="password"
          label="New Password"
          placeholder="Enter new password"
          requiredMessage="Please input new password!"
          form={form}
        />
        <Form.Item
          name="password_confirmation"
          label="Confirm New Password"
          dependencies={["password"]}
          help={validateMessage}
          validateStatus={validateStatus}
          hasFeedback
          rules={[
            {
              required: true,
              message: "Please confirm your password!",
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("password") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error("The two passwords that you entered do not match!"),
                );
              },
            }),
          ]}
        >
          <Input.Password placeholder="Confirm new password" />
        </Form.Item>
        <Form.Item className="my-6">
          <Button
            type="primary"
            htmlType="submit"
            disabled={!allowSubmit}
            loading={loading}
          >
            Change Password
          </Button>
        </Form.Item>
      </Form>
      <Text>
        Strength:
        <br />
        Use at least 12 characters. Don&apos;t use a password from another site
        or something too obvious like your pet&apos;s name.
      </Text>
    </>
  );
};

export default ChangePasswordForm;
