import { useEffect, useState } from "react";
import { Form, InputNumber, Select, Space, Typography } from "antd";
import ChartHelper from "../charts/chart_helper";
import styles from "./styles.module.css";

const { Text } = Typography;

/**
 * Form to set color scaling
 *
 * @param {FormInstance} form
 * @param {object} component
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @returns {JSX.Element}
 */
const ColorScaleForm = ({ form, component, chartKey, chartCustomData }) => {
  const [colorScaleOptions, setColorScaleOptions] = useState([]);
  const [bandCount, setBandCount] = useState(
    chartCustomData[chartKey].bandCount,
  );
  const [minScale, setMinScale] = useState(chartCustomData[chartKey].minValue);
  const [maxScale, setMaxScale] = useState(chartCustomData[chartKey].maxValue);
  // used for updating color scale options when resetting form
  const bandCountValue = Form.useWatch("band_count", form);
  const minScaleValue = Form.useWatch("min_scale", form);
  const maxScaleValue = Form.useWatch("max_scale", form);

  useEffect(() => {
    createColorScaleOptions();
  }, [bandCount, minScale, maxScale]);

  useEffect(() => {
    setBandCount(
      bandCountValue ? bandCountValue : chartCustomData[chartKey].bandCount,
    );
    setMinScale(
      minScaleValue ? minScaleValue : chartCustomData[chartKey].minValue,
    );
    setMaxScale(
      maxScaleValue ? maxScaleValue : chartCustomData[chartKey].maxValue,
    );
  }, [bandCountValue, minScaleValue, maxScaleValue]);

  /**
   * Generate color scale options
   */
  const createColorScaleOptions = () => {
    const colorScale = ChartHelper.generateParametricColorScale(
      minScale,
      maxScale,
      chartCustomData[chartKey].values,
      bandCount,
    );
    let scaleOptions = [];
    colorScale.forEach((scale) => {
      scaleOptions.push({
        value: scale.color,
        label: (
          <Space>
            <div
              className="color-swatch"
              style={{
                backgroundColor: scale.color,
              }}
            />
            {scale.name}
          </Space>
        ),
      });
    });
    setColorScaleOptions(scaleOptions);
    form.resetFields(["selected_color_scale"]);
  };

  return (
    <Form
      form={form}
      id={`color_scale_${component.id}`}
      className={styles.modalForm}
      layout="vertical"
    >
      <Form.Item label="Map Type" name="map_type" initialValue="linear">
        <Select
          getPopupContainer={() =>
            document.getElementById(`color_scale_${component.id}`)
          }
          options={[
            {
              value: "linear",
              label: "Linear",
            },
            {
              value: "sigma",
              label: "Sigma",
              disabled: true,
            },
            {
              value: "log",
              label: "Log",
              disabled: true,
            },
          ]}
        />
      </Form.Item>
      <Form.Item label="Band Count" name="band_count" initialValue={20}>
        <Select
          getPopupContainer={() =>
            document.getElementById(`color_scale_${component.id}`)
          }
          options={[
            {
              value: 20,
              label: "20",
            },
            {
              value: 10,
              label: "10",
            },
            {
              value: 5,
              label: "5",
            },
            {
              value: 2,
              label: "2",
            },
          ]}
        />
      </Form.Item>
      <Form.Item label="Show Band" name="selected_color_scale">
        <Select
          mode="multiple"
          allowClear
          getPopupContainer={() =>
            document.getElementById(`color_scale_${component.id}`)
          }
          options={colorScaleOptions}
        />
      </Form.Item>

      <Space>
        <Form.Item label="Minimum Scale" name="min_scale">
          <InputNumber placeholder="Enter Number" className="w-full" />
        </Form.Item>
        <Form.Item label="Maximum Scale" name="max_scale">
          <InputNumber placeholder="Enter Number" className="w-full" />
        </Form.Item>
      </Space>

      <Space className="my-4">
        <Text strong>Note: </Text>
        <Text>All changes made will only apply to your selected Map</Text>
      </Space>
    </Form>
  );
};

export default ColorScaleForm;
