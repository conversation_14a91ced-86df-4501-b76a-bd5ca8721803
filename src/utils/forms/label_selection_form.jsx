import { CloseCircleFilled } from "@ant-design/icons";
import { Button, Checkbox, Form, Input, List, Space, Typography } from "antd";
import { useEffect, useState } from "react";
import ColorPicker from "../components/color_picker";
import styles from "./styles.module.css";

const { Search } = Input;
const { Text } = Typography;
const presetColors = [
  "#0054A6",
  "#AEBBD8",
  "#004180",
  "#2092D8",
  "#B5D6F0",
  "#19699C",
  "#00A1AF",
  "#B4DCE1",
  "#00717B",
  "#3FA535",
  "#BDDDB5",
  "#2E7726",
  "#8FBB1C",
  "#D5E5AF",
  "#6B8B14",
  "#BB941C",
  "#E6D7AE",
  "#917316",
  "#FFC700",
  "#FFEAAB",
  "#C79C01",
  "#EB660D",
  "#F9C6A7",
  "#C6560B",
  "#CE2903",
  "#F1AF9F",
  "#A02002",
  "#A03058",
  "#DDAEC2",
  "#73233F",
  "#A91D9E",
  "#E1A2DB",
  "#7E177C",
  "#4E138C",
  "#C1A1D4",
  "#390E67",
  "#929288",
  "#D6D6D2",
  "#6B6B63",
];

const noColor = "#aabbcc";

/**
 * Get available labels
 *
 * @returns {array} availableLabels
 */
const getAvailableLabels = () => {
  let availableLabels = [];
  for (var i = 1; i < 100; i++) {
    availableLabels.push({
      key: `label_${i}`,
      name: `Label ${i}`,
      color: presetColors[Math.floor(Math.random() * presetColors.length)],
    });
  }

  return availableLabels;
};

const availableLabels = getAvailableLabels();

/**
 * Form to select labels
 *
 * @returns {JSX.Element}
 */
const LabelSelectionForm = ({ hasPublicLabelFilter = true }) => {
  const [labels, setLabels] = useState(availableLabels);
  const [color, setColor] = useState(noColor);
  const [searched, setSearched] = useState();
  const [form] = Form.useForm();

  useEffect(() => {
    filterLabels();
  }, [searched, color]);

  /**
   * Filter labels by name and color
   */
  const filterLabels = () => {
    const filteredLabels = availableLabels
      .filter((label) => {
        return label.color === color || color === noColor;
      })
      .filter((label) => {
        return label.name.indexOf(searched) !== -1 || !searched;
      });

    setLabels(filteredLabels);
  };

  /**
   * Search by label color
   *
   * @param {string} labelColor
   */
  const onClickColor = (labelColor) => {
    setColor(labelColor);
  };

  return (
    <Form form={form} className={styles.modalForm} name="label_selection_form">
      <Form.Item>
        <Space size="small">
          <Form.Item
            name="label"
            label="Select a Label"
            tooltip="Search label"
            initialValue={searched}
            colon={false}
          >
            <Search
              placeholder="Search Label"
              allowClear
              onSearch={setSearched}
            />
          </Form.Item>
          <Space size="0">
            <Form.Item>
              <ColorPicker
                color={color}
                presetColors={presetColors}
                onClickColor={onClickColor}
                swatchTooltip="Search by Label Color"
                colorPickerTitle="Select Label Color"
              />
            </Form.Item>
            {color !== noColor && (
              <Form.Item>
                <Button
                  type="link"
                  icon={<CloseCircleFilled />}
                  onClick={() => setColor(noColor)}
                ></Button>
              </Form.Item>
            )}
          </Space>
        </Space>
      </Form.Item>
      <List
        className="max-h-96 overflow-auto"
        dataSource={labels}
        renderItem={(item) => (
          <List.Item>
            <Form.Item
              key={item.key}
              name={item.key}
              initialValue={false}
              valuePropName="checked"
              noStyle
            >
              <Checkbox checked={false}>
                <Text style={{ color: `${item.color}` }}>{item.name}</Text>
              </Checkbox>
            </Form.Item>
          </List.Item>
        )}
        bordered
        split={false}
      />
      {hasPublicLabelFilter && (
        <Form.Item
          name="public_label_filter"
          initialValue={true}
          valuePropName="checked"
        >
          <Checkbox>Publicly Labelled Data Only</Checkbox>
        </Form.Item>
      )}
    </Form>
  );
};

export default LabelSelectionForm;
