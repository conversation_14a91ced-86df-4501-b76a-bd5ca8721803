import { Form, Input } from "antd";
import styles from "./styles.module.css";

/**
 * Form to save current filter settings
 *
 * @param {FormInstance} form
 * @param {function} onFinish
 * @returns {JSX.Element}
 */
const SaveFilterSettingsForm = ({
  form,
  onFinish,
  setIsDisableSaveFilterButton,
}) => {
  /**
   * Handle change event of name field
   *
   * @param {Event} e
   */
  const onChange = (e) => {
    setIsDisableSaveFilterButton(!e.target.value);
  };

  return (
    <>
      <Form
        form={form}
        name="save_filter_settings_form"
        className={styles.modalForm}
        onFinish={onFinish}
      >
        <Form.Item
          name="name"
          rules={[
            {
              required: true,
              message: "Please input filter settings name!",
            },
          ]}
        >
          <Input placeholder="Name" onChange={onChange} />
        </Form.Item>
        <Form.Item name="is_overwrite" initialValue={false} noStyle>
          <Input type="hidden" />
        </Form.Item>
      </Form>
    </>
  );
};

export default SaveFilterSettingsForm;
