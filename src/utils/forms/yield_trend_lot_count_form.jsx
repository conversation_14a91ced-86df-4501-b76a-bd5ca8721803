"use client";

import { Form, InputNumber } from "antd";
import { debounce } from "lodash";
import { useBoundStore } from "../../store/store";
import Helper from "../helper";
import { ComponentNameMapper } from "../grid/component_name_mapper";

/**
 * Yield trend lot count form component
 *
 * @returns {JSX.Element}
 */
export default function YieldTrendLotCountForm() {
  const setReloadGrids = useBoundStore((state) => state.setReloadGrids);
  const setReloadGrid = useBoundStore((state) => state.setReloadGrid);
  const setReloadGridFilters = useBoundStore(
    (state) => state.setReloadGridFilters,
  );
  const reloadGridFilters = useBoundStore((state) => state.reloadGridFilters);

  /**
   * Set number of lots for yield trend chart
   *
   * @param {int} lotCount
   */
  const setYieldTrendLotCount = (lotCount) => {
    // reload yield trend table
    const reloadGridKey = ComponentNameMapper.lot_yield_trend_table;
    if (reloadGridFilters[reloadGridKey] === undefined) {
      reloadGridFilters[reloadGridKey] = {};
    }
    let reloadGridFiltersCopy = Helper.cloneObject(reloadGridFilters);
    reloadGridFiltersCopy[reloadGridKey].lot_count = lotCount;
    setReloadGridFilters(reloadGridFiltersCopy);
    setReloadGrids(["lot_yield_trend_table"]);
    setReloadGrid(Date.now());
  };

  return (
    <Form
      key="form_yield_trend_lot_count"
      initialValues={{
        lot_count: 10,
      }}
    >
      <Form.Item className="mb-0" name="lot_count" label="Number of Lots">
        <InputNumber
          key={`input_number_lot_count`}
          min={1}
          max={100}
          onChange={debounce(setYieldTrendLotCount, 1000)}
        />
      </Form.Item>
    </Form>
  );
}
