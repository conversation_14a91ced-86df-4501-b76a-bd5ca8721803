"use client";

import { Flex, Form, Radio, Space, Typography } from "antd";
import { useState } from "react";
import TestListSelect from "../components/test_list_select";
import { useBoundStore } from "../../store/store";
import FilterSelect from "../grid/components/filter_select";

/**
 * Test selection form when trying to generate Selected Test Report
 *
 * @param {string} pageKey
 * @param {Form} form
 * @param {function} onFinish
 * @param {object} input
 * @param {function} setIsProceedSelectedTestBtnDisabled
 * @returns {JSX.Element}
 */
const TestSelectionForm = ({
  pageKey,
  form,
  onFinish,
  input,
  setIsProceedSelectedTestBtnDisabled,
}) => {
  const [isGroupingFieldsDisabled, setIsGroupingFieldsDisabled] =
    useState(true);
  const [isAggregateOptionsDisabled, setIsAggregateOptionsDisabled] =
    useState(true);
  const mfgProcess = useBoundStore((state) => state.mfgProcess);

  /**
   * Triggers when test selection change
   *
   * @param {object} valueObj
   */
  const onChangeTest = (valueObj) => {
    setIsGroupingFieldsDisabled(valueObj === undefined);
    setProceedSelectedTestBtnState();
  };

  /**
   * Triggers when grouping changes
   *
   * @param {Event} e
   */
  const onChangeGrouping = (e) => {
    const grouping = e.target.value;
    form.setFieldValue("grouping", grouping);
    setIsAggregateOptionsDisabled(grouping !== "group_by");
    setProceedSelectedTestBtnState();
  };

  /**
   * Enabled/Disable Proceed button to generate Selected Test Report
   */
  const setProceedSelectedTestBtnState = () => {
    setIsProceedSelectedTestBtnDisabled(
      form.getFieldValue("tnum") === undefined ||
        (form.getFieldValue("grouping") === "group_by" &&
          form.getFieldValue("group_by") === undefined),
    );
  };

  return (
    <div className="p-4">
      <Form
        form={form}
        layout="inline"
        name={`${pageKey}_test_selection_form`}
        initialValues={{
          tnum: undefined,
        }}
        onFinish={onFinish}
      >
        <Form.Item name="tnum" label="" className="w-full">
          <TestListSelect
            labelInValue
            apiParams={input}
            onChange={onChangeTest}
          ></TestListSelect>
        </Form.Item>
        {input.src_value.length > 1 && (
          <Form.Item
            className="w-full mt-4!"
            name="grouping"
            label=""
            initialValue="aggregate"
          >
            <Radio.Group
              block
              className="w-full"
              disabled={isGroupingFieldsDisabled}
              onChange={onChangeGrouping}
            >
              <Space direction="vertical" className="w-full">
                <Radio value="aggregate">Aggregate as one group</Radio>
                <Flex align="center" gap="middle">
                  <Radio value="group_by" className="flex-none!">
                    <Typography.Text
                      className="whitespace-nowrap"
                      disabled={isGroupingFieldsDisabled}
                    >
                      Group By
                    </Typography.Text>
                  </Radio>
                  <Form.Item name="group_by" label="" noStyle>
                    <FilterSelect
                      componentKey="select_aggregate_options"
                      placeholder="-Select-"
                      flex="1 1 auto"
                      className="w-full"
                      params={{
                        api: {
                          url: "api/v1/internal/options/list/aggregate_options",
                          mfg_process: mfgProcess,
                          cache_it: 0,
                        },
                      }}
                      disabled={
                        isGroupingFieldsDisabled || isAggregateOptionsDisabled
                      }
                      onChange={setProceedSelectedTestBtnState}
                    />
                  </Form.Item>
                </Flex>
                {/* Commented out since label is not yet implemented */}
                {/* <Flex align="center" gap="middle">
                  <Radio value="label">
                    <Typography.Text className="whitespace-nowrap" disabled={isGroupingFieldsDisabled}>
                      Group by Label
                    </Typography.Text>
                  </Radio>
                  <Select
                    mode="multiple"
                    options={[
                      {
                        value: "label_1",
                        label: "Label 1",
                      },
                    ]}
                    disabled={isGroupingFieldsDisabled}
                  />
                </Flex> */}
              </Space>
            </Radio.Group>
          </Form.Item>
        )}
      </Form>
    </div>
  );
};

export default TestSelectionForm;
