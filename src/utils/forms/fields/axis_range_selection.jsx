import { Form, InputNumber, Select, Space } from "antd";
import { useEffect } from "react";
import { useBoundStore } from "../../../store/store";

/**
 * Axis range selection component
 * When the range dropdown changes, the min and max inputs will be populated with their corresponding values
 *
 * @param {object} field
 * @param {int} labelColSpan
 * @param {int} wrapperColSpan
 * @returns {JSX.Element}
 */
export default function AxisRangeSelection({
  form,
  field,
  labelColSpan,
  wrapperColSpan,
}) {
  const testStatsInfoObj = useBoundStore((state) => state.testStatsInfo);
  const activeTestNumber = useBoundStore((state) => state.activeTestNumber);
  const testStatsInfo = testStatsInfoObj[activeTestNumber];

  useEffect(() => {
    if (typeof testStatsInfo !== "undefined") {
      form.setFieldsValue({
        minRange: testStatsInfo.min_result,
        maxRange: testStatsInfo.max_result,
      });
    }
  }, []);

  /**
   * Set the min and max range values
   *
   * @param {string} value The selected option value
   * @returns void
   */
  const setMinMaxValues = (value) => {
    const range = {
      minMax: () => {
        return {
          minRange: testStatsInfo.min_result,
          maxRange: testStatsInfo.max_result,
        };
      },
      dataAndLimits: () => {
        return {
          minRange: Math.min(testStatsInfo.min_result, testStatsInfo.lo_lim),
          maxRange: Math.max(testStatsInfo.max_result, testStatsInfo.hi_lim),
        };
      },
      robustLimits: () => {
        return {
          minRange: testStatsInfo.iqr_lo_lim,
          maxRange: testStatsInfo.iqr_hi_lim,
        };
      },
      testLimits: () => {
        return {
          minRange: testStatsInfo.lo_lim,
          maxRange: testStatsInfo.hi_lim,
        };
      },
    };

    form.setFieldsValue((range[value] || range.minMax)());
  };

  /**
   * Get the predefined axis range options
   *
   * @returns {Array}
   */
  const getAxisRangeOptions = () => {
    return [
      {
        value: "minMax",
        label: "Data Min & Max",
      },
      {
        value: "dataAndLimits",
        label: "Data & Limits",
      },
      {
        value: "robustLimits",
        label: "Robust Limits",
      },
      {
        value: "testLimits",
        label: "Test Limits",
      },
    ];
  };

  return (
    <Form.Item
      labelCol={{
        span: labelColSpan,
      }}
      wrapperCol={{
        span: wrapperColSpan,
      }}
    >
      <Space size="small">
        <Form.Item label={field.label}>
          <Select
            className="w-48!"
            showSearch
            allowClear
            placeholder="Select range"
            defaultValue={{ value: "minMax", label: "Data Min & Max" }}
            popupMatchSelectWidth={false}
            onChange={setMinMaxValues}
            options={getAxisRangeOptions()}
          />
        </Form.Item>
      </Space>
      <Form.Item label="Min Range" name="minRange">
        <InputNumber value={testStatsInfo?.min} />
      </Form.Item>
      <Form.Item label="Max Range" name="maxRange">
        <InputNumber value={testStatsInfo?.max} />
      </Form.Item>
    </Form.Item>
  );
}
