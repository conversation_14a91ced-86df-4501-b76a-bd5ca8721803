import { CloseOutlined, CheckOutlined, KeyOutlined } from "@ant-design/icons";
import { Button, Flex, Form, Input, Popover, Space, Typography } from "antd";
import { useCallback, useState } from "react";
import Helper from "../../helper";

const { Text } = Typography;

/**
 * Custom input for password field with validation
 *
 * @param {object} props
 * @returns {JSX.Element}
 */
const PasswordField = (props) => {
  const [validation, setValidation] = useState({
    charCount: false,
    lowercaseCount: false,
    uppercaseCount: false,
    numberCount: false,
    specialCharCount: false,
  });
  /**
   * Get password validation info
   *
   * @returns {JSX.Element}
   */
  const getPasswordValidationInfo = useCallback(() => {
    return (
      <Space direction="vertical">
        <Text type={validation.charCount ? "success" : "danger"}>
          <Space size="middle">
            {validation.charCount ? <CheckOutlined /> : <CloseOutlined />} At
            least 12 characters
          </Space>
        </Text>
        <Text type={validation.lowercaseCount ? "success" : "danger"}>
          <Space size="middle">
            {validation.lowercaseCount ? <CheckOutlined /> : <CloseOutlined />}
            At least 1 lower case
          </Space>
        </Text>
        <Text type={validation.uppercaseCount ? "success" : "danger"}>
          <Space size="middle">
            {validation.uppercaseCount ? <CheckOutlined /> : <CloseOutlined />}
            At least 1 upper case
          </Space>
        </Text>
        <Text type={validation.numberCount ? "success" : "danger"}>
          <Space size="middle">
            {validation.numberCount ? <CheckOutlined /> : <CloseOutlined />} At
            least 1 numerical number
          </Space>
        </Text>
        <Text type={validation.specialCharCount ? "success" : "danger"}>
          <Space size="middle">
            {validation.specialCharCount ? (
              <CheckOutlined />
            ) : (
              <CloseOutlined />
            )}
            At least 1 special character
          </Space>
        </Text>
      </Space>
    );
  }, [validation]);

  /**
   * Validate password field value
   *
   * @param {Event} e
   */
  const validatePassword = (e) => {
    const password = props.form.getFieldValue(props.name);
    let validationCopy = Helper.cloneObject(validation);
    validationCopy.charCount = Helper.validateMinLength(e, password);
    validationCopy.lowercaseCount = Helper.validateLowercaseCount(e, password);
    validationCopy.uppercaseCount = Helper.validateUppercaseCount(e, password);
    validationCopy.numberCount = Helper.validateNumberCount(e, password);
    validationCopy.specialCharCount = Helper.validateSpecialCharCount(
      e,
      password,
    );
    setValidation(validationCopy);
  };

  /**
   * Generate password and fill in password field
   *
   * @param {int} length
   */
  const generatePassword = (length) => {
    const lowercaseChars = "abcdefghijklmnopqrstuvwxyz";
    const uppercaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const specialChars = "!@#$%^&*()-_+=";
    const numericChars = "0123456789";

    let password = "";

    // Ensure at least one character from each character set
    password +=
      lowercaseChars[Math.floor(Math.random() * lowercaseChars.length)];
    password +=
      uppercaseChars[Math.floor(Math.random() * uppercaseChars.length)];
    password += specialChars[Math.floor(Math.random() * specialChars.length)];
    password += numericChars[Math.floor(Math.random() * numericChars.length)];

    // Generate remaining characters
    const remainingLength = length - password.length;
    const allChars =
      lowercaseChars + uppercaseChars + specialChars + numericChars;
    for (let i = 0; i < remainingLength; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the string to randomize the order of characters
    password = password
      .split("")
      .sort(() => Math.random() - 0.5)
      .join("");

    if (props.form) {
      props.form.setFieldsValue({ [props.name]: password });
      validatePassword({
        target: props.form.getFieldInstance(props.name).input,
      });
    }
  };

  return (
    <Flex gap="middle" align="flex-end">
      <Popover
        placement="bottomRight"
        content={getPasswordValidationInfo()}
        arrow={false}
        trigger="focus"
      >
        <Form.Item
          name={props.name}
          label={props.label}
          className={`${props.className ? props.className : ""} grow`}
          rules={[
            {
              required: true,
              message: props.requiredMessage
                ? props.requiredMessage
                : "Please input your password!",
            },
            {
              validator: () => {
                if (Object.values(validation).indexOf(false) !== -1) {
                  return Promise.reject();
                } else {
                  return Promise.resolve();
                }
              },
            },
          ]}
          hasFeedback
        >
          <Input.Password
            placeholder={props.placeholder}
            minLength={12}
            lowercasecount={1}
            uppercasecount={1}
            numbercount={1}
            specialcharcount={1}
            onChange={validatePassword}
          />
        </Form.Item>
      </Popover>
      {props.hasGeneratePasswordBtn && (
        <Button className="mb-4" onClick={() => generatePassword(12)}>
          <KeyOutlined />
        </Button>
      )}
    </Flex>
  );
};

export default PasswordField;
