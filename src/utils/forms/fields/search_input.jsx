import { App, Select } from "antd";
import { useEffect, useState } from "react";

let timeout;

/**
 * Perform livesearch of select options from API and load it to select component
 *
 * @param {string} searched - search query string value
 * @param {function} apiFunction - function call to API
 * @param {object} apiParams - function parameters for API call
 * @param {function} callback - callback function for success API call
 * @param {boolean} setLoading
 * @param {object} message
 */
const getOptions = (
  searched,
  apiFunction,
  apiParams,
  callback,
  setLoading,
  message,
) => {
  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
  }

  /**
   * Make API call to fetch option values and set it as select options
   */
  const fetchOptions = () => {
    setLoading(true);
    apiFunction(
      (res) => {
        if (res.success) {
          callback(res.data);
        } else {
          message.warning("Failed in getting options data.");
        }
        setLoading(false);
      },
      (err) => {
        message.error(`Error in getting options data. ${err}`);
        setLoading(false);
      },
      { ...apiParams, q: searched },
    );
  };
  timeout = setTimeout(fetchOptions, 300);
};

/**
 * Custom input for searching
 *
 * @param {string} searchValue
 * @param {boolean} shouldClearOptions
 * @param {function} setShouldClearOptions
 * @param {function} onChange
 * @param {function} onOptionsChange
 * @param {function} apiFunction
 * @param {object} apiParams
 * @oaram {*} props
 * @returns {JSX.Element}
 */
const SearchInput = ({
  searchValue,
  shouldClearOptions,
  setShouldClearOptions,
  onChange,
  onOptionsChange,
  apiFunction,
  apiParams,
  ...props
}) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const { message } = App.useApp();

  useEffect(() => {
    if (typeof onOptionsChange === "function") {
      onOptionsChange(data);
    }
  }, [data]);

  useEffect(() => {
    if (searchValue) {
      handleSearch(searchValue);
    }
  }, [searchValue]);

  useEffect(() => {
    if (shouldClearOptions === true) {
      setData([]);
      if (typeof setShouldClearOptions === "function") {
        setShouldClearOptions(false);
      }
    }
  }, [shouldClearOptions]);

  /**
   * Handle search event
   *
   * @param {string} searched
   */
  const handleSearch = (searched) => {
    getOptions(searched, apiFunction, apiParams, setData, setLoading, message);
  };

  /**
   * Handle focus event
   *
   * @param {FocusEvent} event
   */
  const handleFocus = (event) => {
    // trigger search when current option count is either 0 or 1
    // this is to trigger search when there is default value set
    if (data.length < 2) {
      handleSearch(event.target.value);
    }
  };

  /**
   * Handle change event
   * @param {string} searched
   */
  const handleChange = (selected) => {
    triggerChange(selected);
  };

  /**
   * Handle clear event
   */
  const handleClear = () => {
    // trigger search when current option count is 1
    // this is to trigger search when there is default value set
    if (data.length === 1) {
      handleSearch("");
    }
  };

  /**
   * Trigger change event to form
   *
   * @param {string} selected
   */
  const triggerChange = (selected) => {
    onChange?.(selected);
  };

  return (
    <>
      <Select
        showSearch
        allowClear
        mode={props.mode ?? "tags"}
        defaultActiveFirstOption={false}
        filterOption={false}
        onSearch={handleSearch}
        onFocus={handleFocus}
        onChange={handleChange}
        onClear={handleClear}
        notFoundContent={null}
        options={data}
        loading={loading}
        {...props}
      />
    </>
  );
};

export default SearchInput;
