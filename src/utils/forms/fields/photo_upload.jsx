import { PlusOutlined, LoadingOutlined } from "@ant-design/icons";
import { notification, Upload } from "antd";
import { useState } from "react";
import Image from "next/image";

/**
 * Get base64 encoded string value of file data
 *
 * @param {HTML.img} img
 * @param {function} callback
 */
const getBase64 = (img, callback) => {
  const reader = new FileReader();
  reader.addEventListener("load", () => callback(reader.result));
  reader.readAsDataURL(img);
};

/**
 * Hook function which will be executed before uploading
 *
 * @param {object} file
 * @returns {boolean}
 */
const beforeUpload = (file) => {
  const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
  if (!isJpgOrPng) {
    notification.error({
      message: "Photo Upload",
      description: "You can only upload JPG/PNG file!",
    });
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    notification.error({
      message: "Photo Upload",
      description: "Image must smaller than 2MB!",
    });
  }

  return isJpgOrPng && isLt2M;
};

/**
 * Photo upload component
 *
 * @returns {JSX.Element}
 */
export default function PhotoUpload() {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState();

  /**
   * A callback function that can be executed when uploading state is changing
   *
   * @param {object} info
   */
  const handleChange = (info) => {
    if (info.file.status === "uploading") {
      setLoading(true);
    } else if (info.file.status === "done") {
      // Get this url from response in real world.
      getBase64(info.file.originFileObj, (url) => {
        setLoading(false);
        setImageUrl(url);
      });
    }
  };

  /**
   * Upload button element
   */
  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div
        style={{
          marginTop: 8,
        }}
      >
        Upload
      </div>
    </div>
  );

  return (
    <div>
      <Upload
        name="avatar"
        listType="picture-circle"
        className="avatar-uploader"
        showUploadList={false}
        action=""
        beforeUpload={beforeUpload}
        onChange={handleChange}
      >
        {imageUrl ? (
          <Image
            src={imageUrl}
            alt="avatar"
            style={{
              width: "100%",
            }}
          />
        ) : (
          uploadButton
        )}
      </Upload>
    </div>
  );
}
