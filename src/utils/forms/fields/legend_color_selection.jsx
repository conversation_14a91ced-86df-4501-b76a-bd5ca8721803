import { Form, Input, Select, Space } from "antd";
import { useEffect, useState } from "react";
import ColorPicker from "../../components/color_picker";

/**
 * Legend color selection
 *
 * @param {FormInstance} form
 * @param {Highcharts} chart
 * @param {object} field
 * @param {int} labelColSpan
 * @param {int} wrapperColSpan
 * @returns {JSX.Element}
 */
export default function LegendColorSelection({
  form,
  chart,
  field,
  labelColSpan,
  wrapperColSpan,
}) {
  const [legendOptions, setLegendOptions] = useState([]);
  const [legendColorFields, setLegendColorFields] = useState([]);
  const [selectedLegend, setSelectedLegend] = useState();
  const [color, setColor] = useState("#aabbcc");

  useEffect(() => {
    getLegendOptions();
    getLegendColorFields();
  }, []);

  useEffect(() => {
    presetLegendColor(selectedLegend);
  }, [selectedLegend]);

  /**
   * Get legend options
   */
  const getLegendOptions = () => {
    let options = [];
    Object.keys(chart.series).forEach((key) => {
      options.push({
        value: key,
        label: chart.series[key].name,
      });
    });
    setLegendOptions(options);
  };

  /**
   * Get legend color fields that will hold color values
   */
  const getLegendColorFields = () => {
    let fields = [];
    Object.keys(chart.series).forEach((key) => {
      fields.push(
        <Form.Item
          key={`legend_color_${key}`}
          name={["legend", "color", key]}
          initialValue={chart.series[key].color}
          noStyle
        >
          <Input type="hidden" />
        </Form.Item>,
      );
    });
    setLegendColorFields(fields);
  };

  /**
   * Preset legend color for display as color swatch
   *
   * @param {string|int} legendKey
   */
  const presetLegendColor = (legendKey) => {
    if (legendKey) {
      const legendColorValue = form.getFieldValue([
        "legend",
        "color",
        legendKey,
      ]);
      const legendColor = legendColorValue
        ? legendColorValue
        : chart.series[legendKey].color;
      setColor(legendColor);
    }
  };

  /**
   * Set legend color
   *
   * @param {string} legendColor
   */
  const setLegendColor = (legendColor) => {
    form.setFieldValue(["legend", "color", selectedLegend], legendColor);
    setColor(legendColor);
  };

  return (
    <Form.Item
      labelCol={{
        span: labelColSpan,
      }}
      wrapperCol={{
        span: wrapperColSpan,
      }}
    >
      <Space size="small">
        <Form.Item label={field.label}>
          <Select
            className="w-48!"
            showSearch
            allowClear
            placeholder="Select a dataset"
            optionFilterProp="children"
            value={selectedLegend}
            popupMatchSelectWidth={false}
            onChange={setSelectedLegend}
            filterOption={(input, option) =>
              (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
            }
            options={legendOptions}
          />
        </Form.Item>
        <Form.Item>
          <ColorPicker color={color} onClickColor={setLegendColor} />
        </Form.Item>
      </Space>
      {legendColorFields}
    </Form.Item>
  );
}
