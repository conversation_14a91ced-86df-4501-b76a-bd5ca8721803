import { Checkbox, Form, Input } from "antd";
import { useState } from "react";
import ColorPicker from "../components/color_picker";
import styles from "./styles.module.css";

const presetColors = [
  "#0054A6",
  "#AEBBD8",
  "#004180",
  "#2092D8",
  "#B5D6F0",
  "#19699C",
  "#00A1AF",
  "#B4DCE1",
  "#00717B",
  "#3FA535",
  "#BDDDB5",
  "#2E7726",
  "#8FBB1C",
  "#D5E5AF",
  "#6B8B14",
  "#BB941C",
  "#E6D7AE",
  "#917316",
  "#FFC700",
  "#FFEAAB",
  "#C79C01",
  "#EB660D",
  "#F9C6A7",
  "#C6560B",
  "#CE2903",
  "#F1AF9F",
  "#A02002",
  "#A03058",
  "#DDAEC2",
  "#73233F",
  "#A91D9E",
  "#E1A2DB",
  "#7E177C",
  "#4E138C",
  "#C1A1D4",
  "#390E67",
  "#929288",
  "#D6D6D2",
  "#6B6B63",
];

/**
 * Form to create new label
 *
 * @returns {JSX.Element}
 */
const CreateNewLabelForm = () => {
  const [color, setColor] = useState("#aabbcc");
  const [form] = Form.useForm();

  /**
   * Set label color
   *
   * @param {string} labelColor
   */
  const onClickColor = (labelColor) => {
    setColor(labelColor);
  };

  return (
    <Form
      form={form}
      className={styles.modalForm}
      layout="vertical"
      name="create_new_label_form"
    >
      <Form.Item name="label_name" label="New Label">
        <Input placeholder="Label Name" />
      </Form.Item>
      <Form.Item name="label_color" label="Select Label Color">
        <ColorPicker
          color={color}
          presetColors={presetColors}
          onClickColor={onClickColor}
        />
      </Form.Item>
      <Form.Item name="is_public" initialValue={true} valuePropName="checked">
        <Checkbox>Allow others to see and use this label</Checkbox>
      </Form.Item>
    </Form>
  );
};

export default CreateNewLabelForm;
