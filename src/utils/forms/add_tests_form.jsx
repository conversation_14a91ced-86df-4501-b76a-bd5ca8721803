import { useEffect, useState } from "react";
import { Col, Form, Row, Select, message } from "antd";
import Api from "../api";
import TestListSelect from "../components/test_list_select";

const filterOptions = [
  {
    value: "followed_tests_show_only",
    label: "Followed Tests",
    disabled: true,
  },
  {
    value: "calculated_tests_show_only",
    label: "Calculated Tests",
    disabled: false,
  },
  {
    value: "calculated_related_tests_show_only",
    label: "Calculated & Related Tests",
    disabled: false,
  },
  {
    value: "summarize_by_pin_show_only",
    label: "Summarize by Pin",
    disabled: false,
  },
  {
    value: "select_tests_manually",
    label: "Select Tests Manually",
    disabled: false,
  },
];

/*
 * Form for new tests
 *
 * @param {React.Ref} gridRef
 * @param {FormInstance} form
 * @param {string} pageKey
 * @param {object} filters
 * @param {function} addUpdateTableRow
 * @param {object} tableUniqueCols
 * @param {function} setIsAddTestsModalOpen
 * @param {function} setIsAddTestsButtonLoading
 * @param {function} setIsModalButtonsDisabled
 * @returns {JSX.Element}
 * */
export default function AddTestsForm({
  gridRef,
  form,
  pageKey,
  filters,
  addUpdateTableRow,
  tableUniqueCols,
  setIsAddTestsModalOpen,
  setIsAddTestsButtonLoading,
  setIsModalButtonsDisabled,
}) {
  const [messageApi, messageContextHolder] = message.useMessage();
  const [testListApiParams, setTestListApiParams] = useState({});
  const [selectedFilter, setSelectedFilter] = useState(
    "calculated_tests_show_only",
  );

  useEffect(() => {
    const tnumsToExclude = [];
    gridRef.current?.api.forEachNode((node) => {
      tnumsToExclude.push(node.data.tnum);
    });
    const payload = {
      mfg_process: filters[pageKey].mfg_process,
      src_type: filters[pageKey].src_type,
      src_value: filters[pageKey].src_value,
      tnum_exclude: tnumsToExclude.join(),
    };
    if (selectedFilter !== "select_tests_manually") {
      payload[selectedFilter] = true;
    }
    setTestListApiParams(payload);
  }, [pageKey, filters, selectedFilter]);

  /*
   * Handle form onFinish event
   *
   * @param {object} values
   * */
  const handleOnFinish = (values) => {
    if (typeof addUpdateTableRow === "function") {
      const selectedTnums = values.select_tests;
      Api.getData(
        "/api/v1/test_level/calc_tests/table",
        "post",
        (res) => {
          if (res.success) {
            const data = res.data?.table_data;
            data.forEach((item) => {
              addUpdateTableRow(tableUniqueCols, item);
            });
            form.resetFields();
            setIsModalButtonsDisabled(false);
            setIsAddTestsButtonLoading(false);
            setIsAddTestsModalOpen(false);
          }
        },
        (err) => {
          messageApi.error(err, 5);
        },
        {
          mfg_process: filters[pageKey].mfg_process,
          src_type: filters[pageKey].src_type,
          src_value: filters[pageKey].src_value,
          tnum_show_only: selectedTnums
            .map((item) => {
              const splitItem = item.split("|");
              return splitItem[1];
            })
            .join(),
        },
      );
    }
  };

  return (
    <>
      {messageContextHolder}
      <div className="p-4">
        <Form
          form={form}
          initialValues={{ select_tests: [] }}
          onFinish={handleOnFinish}
        >
          <Row>
            <Col span={8}>
              <Select
                className="min-w-48"
                defaultValue={selectedFilter}
                options={filterOptions}
                onChange={(value) => {
                  setSelectedFilter(value);
                }}
              />
            </Col>
            <Col span={16}>
              <Form.Item name="select_tests">
                <TestListSelect
                  className="min-w-64 max-w-128"
                  id={pageKey}
                  mode={"multiple"}
                  apiParams={testListApiParams}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    </>
  );
}
