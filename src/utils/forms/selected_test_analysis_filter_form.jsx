import { ExclamationCircleOutlined } from "@ant-design/icons";
import {
  App,
  Button,
  Checkbox,
  Col,
  Divider,
  Flex,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Typography,
} from "antd";
import React, { memo, useState } from "react";
import { useBoundStore } from "../../store/store";
import FilterSelect from "../grid/components/filter_select";
import TestListSelect from "../components/test_list_select";
import Validator from "../validator";
import Api from "../api";
import Helper from "../../../src/utils/helper";
import { OptionsList } from "./options_list";

const { Title, Text } = Typography;

/**
 * Form used for creating new selected test analysis
 *
 * @param {FormInstance} selectedTestAnalysisFilterForm
 * @param {string} pageKey
 * @param {function} setIsSelectedTestAnalysisFilterOpen
 * @returns {JSX.Element}
 */
const SelectedTestAnalysisFilterForm = ({
  selectedTestAnalysisFilterForm,
  pageKey,
  setIsSelectedTestAnalysisFilterOpen,
}) => {
  const [robustDataNDisabled, setRobustDataNDisabled] = useState(true);
  const [customLimitsDisabled, setCustomLimitsDisabled] = useState(true);
  const [groupByDisabled, setGroupByDisabled] = useState(true);
  const pageMeta = useBoundStore((state) => state.pageMeta);
  const urlParams = useBoundStore((state) => state.urlParams);
  const [selectedTestNumber, setSelectedTestNumber] = useState(
    urlParams[pageKey].tnum,
  );
  const { message } = App.useApp();
  const [modal, contextHolder] = Modal.useModal();
  const groupingDisabled = pageMeta.analysis_type === "single";

  /**
   * Triggers when stats type field was changed
   *
   * @param {string} value
   */
  const handleStatsTypeChange = (value) => {
    const robustDataN = selectedTestAnalysisFilterForm.getFieldValue("iqr_n");
    selectedTestAnalysisFilterForm.setFieldValue(
      "iqr_n",
      value !== "iqr"
        ? undefined
        : robustDataN === undefined
          ? 1.5
          : robustDataN,
    );
    setRobustDataNDisabled(value !== "iqr");
  };

  /**
   * Triggers when submitting the form
   */
  const onFinish = () => {
    setIsSelectedTestAnalysisFilterOpen(false);
  };

  /**
   * Triggers when type of statistics type change
   *
   * @param {object} _.target
   */
  const onChangeStatsTypeType = ({ target }) => {
    setCustomLimitsDisabled(target.value === "statistics_type");
  };

  /**
   * Handle test list options change event
   *
   * @param {array} options
   */
  const onChangeTestListOptions = (options) => {
    if (options.length > 0 && selectedTestNumber) {
      selectedTestAnalysisFilterForm.setFieldValue("tnum", options[0]);
      setSelectedTestNumber();
    }
  };

  /**
   * Adjust limit values based on sigma
   */
  const adjustLimitsBasedOnSigma = () => {
    const testKey = selectedTestAnalysisFilterForm.getFieldValue("tnum").value;
    const tnum = testKey.split("|")[1];
    const dsk = testKey.split("|")[3];
    console.log(testKey, "testKey");
    Api.getLimitsBasedOnSigma(
      (res) => {
        if (res.success) {
          selectedTestAnalysisFilterForm.setFieldValue(
            ["custom_limit", "min"],
            res.data.low_limit,
          );
          selectedTestAnalysisFilterForm.setFieldValue(
            ["custom_limit", "max"],
            res.data.high_limit,
          );
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        dsk: dsk,
        tnum: tnum,
        sigma: selectedTestAnalysisFilterForm.getFieldValue("sigma_limit"),
      },
    );
  };

  /**
   * Display confirmation modal to reset filters
   */
  const showResetFiltersConfirmation = () => {
    modal.confirm({
      title: "Reset to Default Settings",
      icon: <ExclamationCircleOutlined />,
      content:
        "Are you sure you want to reset to default settings? Any unsaved changes will be lost. You can cancel to keep your current settings.",
      okText: "Reset",
      cancelText: "Cancel",
      onOk() {
        resetFilters();
      },
    });
  };

  /**
   * Reset filters form
   */
  const resetFilters = () => {
    selectedTestAnalysisFilterForm.resetFields();
    setRobustDataNDisabled(true);
    setCustomLimitsDisabled(true);
    setGroupByDisabled(true);
    setSelectedTestNumber(urlParams[pageKey].tnum);
    message.success("Filters successfully cleared.");
  };

  /**
   * Display confirmation modal to close filters
   */
  const showCanceltFiltersConfirmation = () => {
    modal.confirm({
      title: "Confirm Exit",
      icon: <ExclamationCircleOutlined />,
      content: "Are you sure you want to exit? All progress will be lost.",
      okText: "Leave without creating",
      cancelText: "Cancel",
      onOk() {
        closeFilters();
      },
    });
  };

  /**
   * Close filters form
   */
  const closeFilters = () => {
    setIsSelectedTestAnalysisFilterOpen(false);
    resetFilters();
  };

  return (
    <div className="p-4">
      {contextHolder}
      <Form
        form={selectedTestAnalysisFilterForm}
        name="selected_test_analysis_filter_form"
        layout="vertical"
        initialValues={{
          stats_type_type: "statistics_type",
          stats_type: "rp",
          site: 255,
        }}
        onFinish={onFinish}
      >
        <Title level={5} className="pb-3">
          New Analysis
        </Title>
        <Form.Item name="group_by_disabled" noStyle>
          <Input type="hidden" />
        </Form.Item>
        <Row gutter={14}>
          <Col span={24}>
            <Form.Item name="stats_type_type" className="w-full">
              <Radio.Group className="w-full" onChange={onChangeStatsTypeType}>
                <Flex flex={1} align="center" gap="middle">
                  <Flex gap="middle" align="center" flex={1}>
                    <Radio className="text-nowrap" value="statistics_type">
                      Statistics Type
                    </Radio>
                    <Form.Item name="stats_type" className="w-full" noStyle>
                      <Select
                        placeholder=""
                        popupMatchSelectWidth={false}
                        onChange={handleStatsTypeChange}
                        options={OptionsList.stats_type.filter((option) => {
                          return option.hidden !== true;
                        })}
                      />
                    </Form.Item>
                  </Flex>
                  <Text className="text-nowrap">or</Text>
                  <Radio className="text-nowrap" value="custom_all">
                    Custom (All)
                  </Radio>
                  <Radio value="custom">Custom (Last result per Die)</Radio>
                </Flex>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={14}>
          <Col span={8}>
            <Form.Item name="iqr_n" label=" ">
              <Space>
                <span className="text-nowrap">Robust Data N:</span>
                <InputNumber
                  className="w-full"
                  disabled={robustDataNDisabled}
                />
              </Space>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="site" label="Site Number">
              <FilterSelect
                allowClear={false}
                mode="multiple"
                onChange={(values) => {
                  Helper.handleOverallValue(values, 255);
                }}
                params={{
                  api: {
                    url: "api/v1/internal/options/list/sites",
                    src_type: urlParams[pageKey].src_type,
                    src_value: urlParams[pageKey].src_value,
                    cache_it: 0,
                  },
                }}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="tnum" label=" ">
              <TestListSelect
                labelInValue
                apiParams={urlParams[pageKey]}
                searchValue={selectedTestNumber}
                onChangeTestListOptions={onChangeTestListOptions}
              ></TestListSelect>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={14}>
          <Col span={12}>
            <Form.Item name="part_id_exclude" label="Exclude Part ID">
              <FilterSelect
                className="w-full"
                componentKey="select_exclude_part_id_options"
                placeholder="Select Part IDs"
                mode="multiple"
                maxTagCount="responsive"
                params={{
                  api: {
                    url: "api/v1/internal/options/list/part_ids",
                    mfg_process: urlParams[pageKey].mfg_process,
                    src_type: urlParams[pageKey].src_type,
                    src_value: urlParams[pageKey].src_value,
                    cache_it: 0,
                  },
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="analyse_bins" label="Show Only Analyse Bins">
              <FilterSelect
                className="w-full"
                componentKey="select_show_only_analyse_bins_options"
                placeholder="Select Bins"
                mode="multiple"
                maxTagCount="responsive"
                params={{
                  api: {
                    url: "api/v1/internal/options/list/bin_numbers",
                    mfg_process: urlParams[pageKey].mfg_process,
                    src_type: urlParams[pageKey].src_type,
                    src_value: urlParams[pageKey].src_value,
                    cache_it: 0,
                  },
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Divider className="mt-0!">
          <Text strong>Custom Limits</Text>
        </Divider>
        <Row gutter={14}>
          <Col span={8}>
            <Form.Item name="sigma_limit" label="Set Sigma Limit">
              <InputNumber className="w-full" disabled={customLimitsDisabled} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name={["custom_limit", "min"]} label="Set Limit">
              <InputNumber
                className="w-full"
                placeholder="Minimum"
                disabled={customLimitsDisabled}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name={["custom_limit", "max"]}
              label=" "
              dependencies={[["custom_limit", "min"]]}
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    return Validator.maxValue(
                      value,
                      getFieldValue(["custom_limit", "min"]),
                    );
                  },
                }),
              ]}
            >
              <InputNumber
                className="w-full"
                placeholder="Maximum"
                disabled={customLimitsDisabled}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={14}>
          <Col>
            <Button
              className="mb-4"
              disabled={customLimitsDisabled}
              onClick={adjustLimitsBasedOnSigma}
            >
              Adjust Limits Based on Sigma
            </Button>
          </Col>
          <Col>
            <Form.Item name="show_original_limits" valuePropName="checked">
              <Checkbox disabled={customLimitsDisabled}>
                Show also original limits
              </Checkbox>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={14}>
          <Col span={12}>
            <Form.Item
              name={["custom_range", "min"]}
              label="Set Data Range for Charts"
            >
              <InputNumber
                className="w-full"
                placeholder="Minimum"
                disabled={customLimitsDisabled}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={["custom_range", "max"]}
              label=" "
              dependencies={[["custom_range", "min"]]}
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    return Validator.maxValue(
                      value,
                      getFieldValue(["custom_range", "min"]),
                    );
                  },
                }),
              ]}
            >
              <InputNumber
                className="w-full"
                placeholder="Maximum"
                disabled={customLimitsDisabled}
              />
            </Form.Item>
          </Col>
        </Row>
        <Divider className="mt-0!">
          <Text strong>Chart Options</Text>
        </Divider>
        <Row gutter={14}>
          <Col span={16}>
            <Flex gap="middle">
              <Form.Item name="per_site" valuePropName="checked">
                <Checkbox className="text-nowrap">Show Data Per Site</Checkbox>
              </Form.Item>
              <Form.Item
                name="histogram_y_axis_as_percent"
                valuePropName="checked"
              >
                <Checkbox className="text-nowrap">
                  Show Y-Axis as % in Histogram
                </Checkbox>
              </Form.Item>
              <Flex gap="middle" align="center" flex={"1 1 auto"}>
                <Form.Item name="enable_group_by" valuePropName="checked">
                  <Checkbox
                    className="text-nowrap"
                    disabled={groupingDisabled}
                    onChange={({ target }) => {
                      setGroupByDisabled(!target.checked);
                      selectedTestAnalysisFilterForm.setFieldValue(
                        "group_by_disabled",
                        !target.checked,
                      );
                    }}
                  >
                    Group By
                  </Checkbox>
                </Form.Item>
                <Form.Item name="group_by" className="w-full">
                  <FilterSelect
                    className="w-full"
                    componentKey="select_group_by_options"
                    placeholder="-Select-"
                    defaultValue={urlParams[pageKey].group_by ?? ""}
                    disabled={groupByDisabled}
                    params={{
                      api: {
                        url: "api/v1/internal/options/list/aggregate_options",
                        mfg_process: urlParams[pageKey].mfg_process,
                        cache_it: 0,
                      },
                      allOption: {
                        label: "Grouped as one",
                        value: "",
                      },
                    }}
                  />
                </Form.Item>
              </Flex>
            </Flex>
          </Col>
          {/* commented out for now since Labels feature is not yet implemented */}
          {/* <Col span={8}>
            <Form.Item>
              <Select
                mode="multiple"
                placeholder="Select Labels"
                popupMatchSelectWidth={false}
                maxTagCount="responsive"
                options={[]}
                disabled={groupingDisabled}
              />
            </Form.Item>
          </Col> */}
        </Row>
        {/* <Row gutter={14}>
          <Col>
            <Form.Item
              name="saved_zoomed_dataset"
              label="Apply Saved Zoomed Dataset to Charts"
            >
              <Select popupMatchSelectWidth={false} options={[]} />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item name="histogram_show_y_axis_as_percent" label=" ">
              <Checkbox>Show Y-Axis as % in Histogram</Checkbox>
            </Form.Item>
          </Col>
        </Row> */}
        <Flex justify="flex-end" gap="middle">
          <Button
            key="cancel"
            onClick={() =>
              selectedTestAnalysisFilterForm.isFieldsTouched()
                ? showCanceltFiltersConfirmation()
                : closeFilters()
            }
          >
            Cancel
          </Button>
          <Button key="reset" onClick={() => showResetFiltersConfirmation()}>
            Reset
          </Button>
          <Button
            key="create"
            type="primary"
            onClick={() => selectedTestAnalysisFilterForm.submit()}
          >
            Create
          </Button>
        </Flex>
      </Form>
    </div>
  );
};

export default memo(SelectedTestAnalysisFilterForm);
