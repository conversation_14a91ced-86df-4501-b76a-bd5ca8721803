import {
  SaveOutlined,
  FolderOpenOutlined,
  Exclamation<PERSON>ircleOutlined,
  InfoCircleOutlined,
  UpOutlined,
  DownOutlined,
} from "@ant-design/icons";
import {
  App,
  Button,
  Checkbox,
  Col,
  Collapse,
  DatePicker,
  Divider,
  Flex,
  Form,
  Input,
  InputNumber,
  Modal,
  Row,
  Select,
  Space,
  Typography,
} from "antd";
import React, { memo, useEffect, useRef, useState } from "react";
import { useBoundStore } from "../../store/store";
import Api from "../api";
import Helper from "../helper";
import { useEffectApiFetch } from "../../hooks";
import { ComponentNameMapper } from "../grid/component_name_mapper";
import Validator from "../validator";
import GridHelper from "../grid/grid_helper";
import { UserSettingsKeys } from "../user_settings_keys";
import SaveFilterSettingsForm from "./save_filter_settings_form";
import { SearchFilterFieldsMapper } from "./mappers/search_filter_fields_mapper";
import LoadFilterSettingsForm from "./load_filter_settings_form";
import SearchInput from "./fields/search_input";

const { Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { confirm } = Modal;

const periodOptions = [
  {
    value: "-12 hours",
    label: "Last 12h",
  },
  {
    value: "-24 hours",
    label: "Last 24h",
  },
  {
    value: "-2 days",
    label: "Last 2 days",
  },
  {
    value: "-3 days",
    label: "Last 3 days",
  },
  {
    value: "-5 days",
    label: "Last 5 days",
  },
  {
    value: "-1 week",
    label: "Last Week",
  },
  {
    value: "-2 weeks",
    label: "Last 2 Weeks",
  },
  {
    value: "-1 month",
    label: "Last Month",
  },
  {
    value: "-2 months",
    label: "Last 2 Months",
  },
  {
    value: "-3 months",
    label: "Last 3 Months",
  },
  {
    value: "-6 months",
    label: "Last 6 Months",
  },
  {
    value: "-1 year",
    label: "Last Year",
  },
  {
    value: "-2 years",
    label: "Last 2 Years",
  },
  {
    value: "-3 years",
    label: "Last 3 Years",
  },
  {
    value: "-4 years",
    label: "Last 4 Years",
  },
  {
    value: "-5 years",
    label: "Last 5 Years",
  },
  {
    value: "-6 years",
    label: "Last 6 Years",
  },
  {
    value: "-7 years",
    label: "Last 7 Years",
  },
  {
    value: "-8 years",
    label: "Last 8 Years",
  },
  {
    value: "-10 years",
    label: "Last 10 Years",
  },
  {
    value: "all",
    label: "All",
  },
];

/**
 * Form used for filtering search table data
 *
 * @param {FormInstance} searchFilterForm
 * @param {function} setSearchFilterFields
 * @param {object} filters
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
const SearchTableFiltersForm = ({
  searchFilterForm,
  setSearchFilterFields,
  filters,
  pageKey,
}) => {
  const [filterFields, setFilterFields] = useState({});
  const [allFilterFields, setAllFilterFields] = useState({});
  const [selectedFilterFields, setSelectedFilterFields] = useState([]);
  const [isOpenFilterFieldsDropdown, setIsOpenFilterFieldsDropdown] =
    useState(false);
  const [filterFieldsHeight, setFilterFieldsHeight] = useState(0);
  const [searchGridComponent, setSearchGridComponent] = useState();
  const [
    isSaveCurrentFilterSettingsModalOpen,
    setIsSaveCurrentFilterSettingsModalOpen,
  ] = useState(false);
  const [isLoadFilterSettingsModalOpen, setIsLoadFilterSettingsModalOpen] =
    useState(false);
  const [isDisableSaveFilterButton, setIsDisableSaveFilterButton] =
    useState(true);
  const [isDisableDeleteFilterButton, setIsDisableDeleteFilterButton] =
    useState(true);
  const [isDisableLoadFilterButton, setIsDisableLoadFilterButton] =
    useState(true);
  const [shouldUpdateSavedFilterOptions, setShouldUpdateSavedFilterOptions] =
    useState(false);
  const mfgProcess = useBoundStore((state) => state.mfgProcess);
  const userData = useBoundStore((state) => state.userData);
  const currentPageData = useBoundStore((state) => state.currentPageData);
  const [saveFilterSettingsForm] = Form.useForm();
  const [loadFilterSettingsForm] = Form.useForm();
  const { message } = App.useApp();
  const filterFieldsWrapperRef = useRef();

  useEffect(() => {
    calculateFilterFieldsHeight();
  }, []);

  useEffect(() => {
    if (searchGridComponent && pageKey === currentPageData.key) {
      initFilterFields();
    }
  }, [searchGridComponent, filters[pageKey]]);

  useEffect(() => {
    if (
      selectedFilterFields &&
      selectedFilterFields.length > 0 &&
      Object.keys(allFilterFields).length > 0 &&
      pageKey === currentPageData.key
    ) {
      setFilterFields(
        Object.keys(allFilterFields)
          .filter((key) => {
            return selectedFilterFields.indexOf(key) !== -1;
          })
          .reduce((obj, key) => {
            obj[key] = allFilterFields[key];
            return obj;
          }, {}),
      );
      Helper.setUserSettings(
        UserSettingsKeys.search_filter_fields,
        selectedFilterFields,
      );
    }
  }, [selectedFilterFields]);

  useEffectApiFetch(
    () => {
      if (userData?.initial_password_change) {
        return getSearchGridComponent();
      }
    },
    () => {
      setSearchGridComponent();
    },
  );

  /**
   * Initialize filter fields
   */
  const initFilterFields = () => {
    const fields = generateFieldDefinitions(searchGridComponent);
    setAllFilterFields(fields);
    if (typeof setSearchFilterFields === "function") {
      setSearchFilterFields(fields);
    }
    if (selectedFilterFields && selectedFilterFields.length === 0) {
      const userFilterFields = Helper.getUserSettings(
        UserSettingsKeys.search_filter_fields,
      );
      setSelectedFilterFields(
        userFilterFields && userFilterFields.length > 0
          ? userFilterFields
          : Object.keys(getDefaultFilterFields(fields)),
      );
    } else {
      const updatedFilterFields = Object.keys(fields)
        .filter((key) => {
          return (
            selectedFilterFields && selectedFilterFields.indexOf(key) !== -1
          );
        })
        .reduce((obj, key) => {
          obj[key] = fields[key];
          return obj;
        }, {});
      setSelectedFilterFields(Object.keys(updatedFilterFields));
    }
  };

  /**
   * Get default filter fields
   *
   * @param {object} fields
   * @returns {object} filterFields
   */
  const getDefaultFilterFields = (fields) => {
    const filterFields = Object.keys(fields)
      .filter((key) => {
        return fields[key].isDefault === true;
      })
      .reduce((obj, key) => {
        obj[key] = fields[key];
        return obj;
      }, {});

    return filterFields;
  };

  /**
   * Calculate filter fields height
   */
  const calculateFilterFieldsHeight = () => {
    setFilterFieldsHeight(filterFieldsWrapperRef.offsetHeight);
  };

  /**
   * Get and set search grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getSearchGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setSearchGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: ComponentNameMapper.home_search_table,
      },
    );

    return abortCtl;
  };

  /**
   * Create filter field definitions from API parameters
   *
   * @param {object} searchGridComponent
   * @returns {object} fields
   */
  const generateFieldDefinitions = (searchGridComponent) => {
    const colDefs = searchGridComponent.props.settings.column_defs;
    const apiParams = searchGridComponent.props.params.body_params;
    const pageFilters = Helper.getPageFilters(filters, pageKey);
    const mfgProcesses = pageFilters.manufacturing_process
      ? pageFilters.manufacturing_process.split(",")
      : [];
    const mfgProcessColumnFields = GridHelper.getMfgProcessColumnFields(
      colDefs,
      mfgProcesses,
    );
    let fields = {};
    colDefs
      .filter((column) => {
        return (
          mfgProcessColumnFields.indexOf(column.field) !== -1 &&
          column.filterProps.hasDBField === true &&
          column.filterProps.hasFilterField !== false
        );
      })
      .forEach((column) => {
        fields[column.field] = {
          key: column.field,
          label: column.headerName,
          type: apiParams[column.field].type,
          filterType: apiParams[column.field].filter_type,
          isDefault: column.filterProps.hasDefaultFilterField === true,
        };
        if (column.filterProps.minFilterValue !== undefined) {
          fields[column.field].minFilterValue =
            column.filterProps.minFilterValue;
        }
      });

    return fields;
  };

  /**
   * Get search filter values
   *
   * @param {object} searchFilterFormValues
   * @returns {object} filterValues
   */
  const getFilterValues = (searchFilterFormValues) => {
    searchFilterFormValues.filter_fields = selectedFilterFields.join();
    const rawFilters = Object.keys(searchFilterFormValues)
      .filter((key) => {
        return (
          (!Array.isArray(searchFilterFormValues[key]) &&
            searchFilterFormValues[key] !== undefined) ||
          (Array.isArray(searchFilterFormValues[key]) &&
            searchFilterFormValues[key].length > 0)
        );
      })
      .reduce((obj, key) => {
        obj[key] = searchFilterFormValues[key];
        return obj;
      }, {});

    const filterValues = Object.keys(rawFilters).reduce((obj, key) => {
      if (Array.isArray(rawFilters[key])) {
        obj[key] = rawFilters[key]
          .map((value) => {
            return typeof value === "object" ? value.value : value;
          })
          .join();
      } else {
        obj[key] = rawFilters[key];
      }
      return obj;
    }, {});

    // set current mfg process value
    filterValues.manufacturing_process = filters[pageKey].manufacturing_process;

    return filterValues;
  };

  /**
   * Save filter settings
   *
   * @param {object} values
   */
  const saveFilterSettings = (values) => {
    Api.saveFilterSettings(
      (res) => {
        if (res.success) {
          message.success(res.message);
          saveFilterSettingsForm.resetFields();
          setIsDisableSaveFilterButton(true);
          setIsSaveCurrentFilterSettingsModalOpen(false);
          setShouldUpdateSavedFilterOptions(!shouldUpdateSavedFilterOptions);
        } else {
          message.warning(res.message);
        }
        saveFilterSettingsForm.setFieldValue("is_overwrite", false);
      },
      (err, errorCode) => {
        if (errorCode === 409) {
          setIsSaveCurrentFilterSettingsModalOpen(false);
          showOverwriteConfirm();
        } else {
          message.error(err);
        }
        saveFilterSettingsForm.setFieldValue("is_overwrite", false);
      },
      {
        name: values.name,
        filters: getFilterValues(searchFilterForm.getFieldsValue()),
        overwrite: values.is_overwrite,
      },
    );
  };

  /**
   * Show confirmation to overwrite or rename and save as new
   */
  const showOverwriteConfirm = () => {
    confirm({
      title: "Name Already Exists!",
      icon: <ExclamationCircleOutlined />,
      content: "The name you entered already exists. What do you want to do?",
      okText: "Overwrite Existing",
      cancelText: "Rename and Save as New",
      onOk() {
        saveFilterSettingsForm.setFieldValue("is_overwrite", true);
        saveFilterSettingsForm.submit();
      },
      onCancel() {
        setIsSaveCurrentFilterSettingsModalOpen(true);
      },
    });
  };

  /**
   * Show confirmation to delete selected filter
   */
  const showDeleteSelectedFilterConfirm = () => {
    confirm({
      title: "Confirm Deletion",
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <Paragraph>
            Are you sure you want to delete selected filter permanently?
          </Paragraph>
          <Paragraph>
            <Text strong>Warning!</Text>{" "}
            <Text>This action cannot be undone!</Text>
          </Paragraph>
        </>
      ),
      okText: "Delete",
      cancelText: "Cancel",
      onOk() {
        deleteSelectedFilter();
      },
    });
  };

  /**
   * Delete selected filter
   */
  const deleteSelectedFilter = () => {
    Api.deleteFilterSettings(
      (res) => {
        if (res.success) {
          message.success(res.message);
          loadFilterSettingsForm.resetFields();
          setIsDisableDeleteFilterButton(true);
          setIsDisableLoadFilterButton(true);
          setShouldUpdateSavedFilterOptions(!shouldUpdateSavedFilterOptions);
        } else {
          message.warning(res.message);
        }
      },
      (err) => {
        message.error(err);
      },
      {
        id: loadFilterSettingsForm.getFieldValue("filter_key"),
      },
    );
  };

  /**
   * Load selected filter
   */
  const loadSelectedFilter = () => {
    Api.getFilterSettings(
      (res) => {
        if (res.success) {
          setIsLoadFilterSettingsModalOpen(false);
          const savedFilters = JSON.parse(res.data.filters);
          Object.keys(savedFilters).forEach((key) => {
            if (
              allFilterFields[key] &&
              ["string", "array-string", "array"].indexOf(
                allFilterFields[key].type,
              ) !== -1 &&
              typeof savedFilters[key] === "string"
            ) {
              savedFilters[key] = savedFilters[key].split(",");
            }
          });
          if (savedFilters.filter_fields) {
            savedFilters.filter_fields = savedFilters.filter_fields.split(",");
          }
          searchFilterForm.resetFields();
          searchFilterForm.setFieldsValue(savedFilters);
          updateFilterFields();
        } else {
          message.warning(res.message);
        }
      },
      (err) => {
        message.error(err);
      },
      {
        id: loadFilterSettingsForm.getFieldValue("filter_key"),
      },
    );
  };

  /**
   * Get filter field element
   *
   * @param {object} field
   * @returns {JSX.Element} element
   */
  const getFieldElement = (field) => {
    let element = <div key={`input_${field.key}`}></div>;
    let rules = [];
    switch (field.filterType) {
      case "float_range":
        element = (
          <Form.Item
            key={`${field.type}_${field.key}_wrapper`}
            label={field.label}
            className="mb-0"
          >
            <Space key={`${field.type}_${field.key}`} align="start">
              <Form.Item
                key={`${field.type}_${field.key}_min`}
                name={[field.key, "min"]}
              >
                <InputNumber className="w-full" placeholder="Minimum" />
              </Form.Item>
              <Form.Item
                key={`${field.type}_${field.key}_max`}
                name={[field.key, "max"]}
                dependencies={[[field.key, "min"]]}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      return Validator.maxValue(
                        value,
                        getFieldValue([field.key, "min"]),
                      );
                    },
                  }),
                ]}
              >
                <InputNumber className="w-full" placeholder="Maximum" />
              </Form.Item>
            </Space>
          </Form.Item>
        );
        break;
      case "int_range":
        if (field.minFilterValue !== undefined) {
          rules.push(() => ({
            validator(_, value) {
              return Validator.positiveValue(value);
            },
          }));
        }
        element = (
          <Form.Item
            key={`${field.type}_${field.key}_wrapper`}
            label={field.label}
            className="mb-0"
          >
            <Space key={`${field.type}_${field.key}`} align="start">
              <Form.Item
                key={`${field.type}_${field.key}_min`}
                name={[field.key, "min"]}
                rules={rules}
              >
                <Input
                  type="number"
                  className="w-full"
                  placeholder="Minimum"
                  min={
                    field.minFilterValue !== undefined
                      ? field.minFilterValue
                      : null
                  }
                  onKeyDown={Validator.onKeyPress}
                  onPaste={Validator.onPaste}
                />
              </Form.Item>
              <Form.Item
                key={`${field.type}_${field.key}_max`}
                name={[field.key, "max"]}
                dependencies={[[field.key, "min"]]}
                rules={[
                  ...rules,
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      return Validator.maxValue(
                        value,
                        getFieldValue([field.key, "min"]),
                      );
                    },
                  }),
                ]}
              >
                <Input
                  type="number"
                  className="w-full"
                  placeholder="Maximum"
                  min={
                    field.minFilterValue !== undefined
                      ? field.minFilterValue
                      : null
                  }
                  onKeyDown={Validator.onKeyPress}
                  onPaste={Validator.onPaste}
                />
              </Form.Item>
            </Space>
          </Form.Item>
        );
        break;
      case "percent_range":
        element = (
          <Form.Item
            key={`${field.type}_${field.key}_wrapper`}
            label={field.label}
            className="mb-0"
          >
            <Space key={`${field.type}_${field.key}`} align="start">
              <Form.Item
                key={`${field.type}_${field.key}_min`}
                name={[field.key, "min"]}
              >
                <InputNumber className="w-full" placeholder="Minimum" min="0" />
              </Form.Item>
              <Form.Item
                key={`${field.type}_${field.key}_max`}
                name={[field.key, "max"]}
                dependencies={[[field.key, "min"]]}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      return Validator.maxValue(
                        value,
                        getFieldValue([field.key, "min"]),
                      );
                    },
                  }),
                ]}
              >
                <InputNumber
                  className="w-full"
                  placeholder="Maximum"
                  max="100"
                />
              </Form.Item>
            </Space>
          </Form.Item>
        );
        break;
      case "date_range":
        element = (
          <Form.Item
            key={`${field.type}_${field.key}`}
            name={field.key}
            label={field.label}
          >
            <RangePicker className="w-full" allowEmpty />
          </Form.Item>
        );
        break;
      case "string":
      case "array-string":
      case "array":
        element = (
          <Form.Item
            key={`${field.type}_${field.key}`}
            name={field.key}
            label={field.label}
          >
            <SearchInput
              placeholder={`Input ${field.label}`}
              apiFunction={Api.getSelectOptions}
              apiParams={{ field: field.key, mfg_process: mfgProcess }}
            />
          </Form.Item>
        );
        break;
      case "date":
        element = (
          <Form.Item
            key={`${field.type}_${field.key}`}
            name={field.key}
            label={field.label}
          >
            <DatePicker className="w-full" />
          </Form.Item>
        );
        break;
      case "float":
        element = (
          <Form.Item
            key={`${field.type}_${field.key}`}
            name={field.key}
            label={field.label}
          >
            <InputNumber className="w-full" />
          </Form.Item>
        );
        break;
      case "int":
        element = (
          <Form.Item
            key={`${field.type}_${field.key}`}
            name={field.key}
            label={field.label}
          >
            <Input
              placeholder={`Input ${field.label}`}
              type="number"
              onKeyDown={Validator.onKeyPress}
              onPaste={Validator.onPaste}
            />
          </Form.Item>
        );
        break;
      default:
        element = (
          <Form.Item
            key={`${field.type}_${field.key}`}
            name={field.key}
            label={field.label}
          >
            <Input placeholder={`Input ${field.label}`} />
          </Form.Item>
        );
    }

    return element;
  };

  /**
   * Triggers when opening/closing filter fields options
   *
   * @param {boolean} open
   */
  const onFilterFieldsDropdownVisibleChange = (open) => {
    setIsOpenFilterFieldsDropdown(open);
    searchFilterForm.setFieldValue("filter_fields", selectedFilterFields);
  };

  /**
   * Update filter fields to display
   */
  const updateFilterFields = () => {
    setSelectedFilterFields(searchFilterForm.getFieldValue("filter_fields"));
  };

  /**
   * Reset filter fields to default
   */
  const resetFilterFields = () => {
    const defaultFieldKeys = Object.keys(
      getDefaultFilterFields(allFilterFields),
    );
    searchFilterForm.setFieldValue("filter_fields", defaultFieldKeys);
    setSelectedFilterFields(defaultFieldKeys);
  };

  /**
   * Toggle hide cons field value
   *
   * @param {string} toggleField
   * @param {boolean} checked
   */
  const toggleHideConsFields = (toggleField, checked) => {
    if (checked) {
      searchFilterForm.setFieldValue(toggleField, false);
    }
  };

  return (
    <div className="flex-container">
      <Row gutter={24} className="mb-2">
        <Col span={12}>
          <Button
            icon={<FolderOpenOutlined />}
            onClick={() => setIsLoadFilterSettingsModalOpen(true)}
          >
            Load Filter Settings
          </Button>
        </Col>
        <Col span={12}>
          <Button
            icon={<SaveOutlined />}
            onClick={() => setIsSaveCurrentFilterSettingsModalOpen(true)}
          >
            Save Filters
          </Button>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={24}>
          <Form.Item className="mb-2" name="filter_fields">
            <Select
              className="w-full"
              placeholder="Select Filters to add/remove"
              mode="multiple"
              maxTagCount="responsive"
              showSearch
              open={isOpenFilterFieldsDropdown}
              dropdownRender={(menu) => (
                <>
                  {menu}
                  <Divider />
                  <Flex justify="space-between">
                    <Button type="text" onClick={() => resetFilterFields()}>
                      Reset
                    </Button>
                  </Flex>
                </>
              )}
              options={Object.values(
                Helper.sortObjectByKeyValue(allFilterFields, "label"),
              ).map((field) => ({
                label: field.label,
                value: field.key,
              }))}
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              filterSort={(optionA, optionB) => {
                return selectedFilterFields &&
                  selectedFilterFields.indexOf(optionA.value) !== -1 &&
                  selectedFilterFields.indexOf(optionB.value) === -1
                  ? -1
                  : 0;
              }}
              onDropdownVisibleChange={onFilterFieldsDropdownVisibleChange}
              onChange={updateFilterFields}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Divider className="m-0!">
            <Text strong>Filters</Text>
          </Divider>
        </Col>
      </Row>
      <Row className="mb-2">
        <Col
          span={24}
          style={{
            border: "1px solid #91D5FF",
          }}
        >
          <Collapse
            expandIconPosition="end"
            expandIcon={(props) => {
              return (
                <Text style={{ color: "#1890ff" }}>
                  {props.isActive ? (
                    <>
                      Hide <UpOutlined className="ml-1" />
                    </>
                  ) : (
                    <>
                      Show <DownOutlined className="ml-1" />
                    </>
                  )}
                </Text>
              );
            }}
            items={[
              {
                key: "1",
                label: (
                  <>
                    <InfoCircleOutlined
                      style={{ color: "#1890ff" }}
                      className="mr-1"
                    />
                    <Text strong>Search Tips</Text>
                  </>
                ),
                children: (
                  <div className="pl-8">
                    <ul>
                      <li>
                        <strong>AND Searches:</strong> All criteria across
                        multiple fields must be met.
                      </li>
                      <li>
                        <strong>OR Searches:</strong> Use &quot;OR&quot; within
                        a single field to match any of the entered values.
                      </li>
                      <li>
                        <strong>Combining AND/OR:</strong> Refine results by
                        combining multiple fields and values.
                      </li>
                      <li>
                        <strong>Case Insensitivity:</strong> Search terms are
                        case-insensitive.
                      </li>
                      <li>
                        <strong>Exclude Words:</strong> Use &apos;-&apos; to
                        exclude specific words.
                      </li>
                    </ul>
                  </div>
                ),
              },
            ]}
            bordered={false}
          />
        </Col>
      </Row>

      <div ref={filterFieldsWrapperRef} className="fill-height overflow-y-auto">
        <div
          style={{
            height: `${filterFieldsHeight}px`,
          }}
        >
          <Form.Item key="string_date_range" name="date_range" label="Period">
            <Select className="w-full" options={periodOptions} />
          </Form.Item>

          {Object.values(filterFields).map((field) => {
            return getFieldElement(field);
          })}

          <Space direction="vertical">
            <Space size="large">
              <Form.Item name="hide_cons_files" valuePropName="checked" noStyle>
                <Checkbox
                  onChange={(e) =>
                    toggleHideConsFields("hide_cons_subfiles", e.target.checked)
                  }
                >
                  {SearchFilterFieldsMapper.hide_cons_files.label}
                </Checkbox>
              </Form.Item>
              <Form.Item
                name="hide_cons_subfiles"
                valuePropName="checked"
                noStyle
              >
                <Checkbox
                  onChange={(e) =>
                    toggleHideConsFields("hide_cons_files", e.target.checked)
                  }
                >
                  {SearchFilterFieldsMapper.hide_cons_subfiles.label}
                </Checkbox>
              </Form.Item>
            </Space>
            {/* <Form.Item
              name="show_char_siblings"
              valuePropName="checked"
              noStyle
            >
              <Checkbox>
                {SearchFilterFieldsMapper.show_char_siblings.label}
              </Checkbox>
            </Form.Item>
            <Form.Item
              name="show_char_original"
              valuePropName="checked"
              noStyle
            >
              <Checkbox>
                {SearchFilterFieldsMapper.show_char_original.label}
              </Checkbox>
            </Form.Item>
            <Form.Item
              name="show_has_calc_tests"
              valuePropName="checked"
              noStyle
            >
              <Checkbox>
                {SearchFilterFieldsMapper.show_has_calc_tests.label}
              </Checkbox>
            </Form.Item> */}
            <Form.Item name="has_xy" valuePropName="checked" noStyle>
              <Checkbox>{SearchFilterFieldsMapper.has_xy.label}</Checkbox>
            </Form.Item>

            <Form.Item name="has_test_data" valuePropName="checked" noStyle>
              <Checkbox>
                {SearchFilterFieldsMapper.has_test_data.label}
              </Checkbox>
            </Form.Item>
            <Form.Item name="has_probe_data" valuePropName="checked" noStyle>
              <Checkbox>
                {SearchFilterFieldsMapper.has_probe_data.label}
              </Checkbox>
            </Form.Item>
            <Form.Item name="has_fab_data" valuePropName="checked" noStyle>
              <Checkbox>{SearchFilterFieldsMapper.has_fab_data.label}</Checkbox>
            </Form.Item>

            <Form.Item
              name="has_serial_numbers"
              valuePropName="checked"
              noStyle
            >
              <Checkbox>
                {SearchFilterFieldsMapper.has_serial_numbers.label}
              </Checkbox>
            </Form.Item>
            <Form.Item
              name="has_traceability_applied"
              valuePropName="checked"
              noStyle
            >
              <Checkbox>
                {SearchFilterFieldsMapper.has_traceability_applied.label}
              </Checkbox>
            </Form.Item>
            <Form.Item
              name="hide_traceability_split_data"
              valuePropName="checked"
              noStyle
            >
              <Checkbox>
                {SearchFilterFieldsMapper.hide_traceability_split_data.label}
              </Checkbox>
            </Form.Item>
            <Form.Item
              name="show_manual_uploaded_files"
              valuePropName="checked"
              noStyle
            >
              <Checkbox>
                {SearchFilterFieldsMapper.show_manual_uploaded_files.label}
              </Checkbox>
            </Form.Item>
          </Space>
        </div>
      </div>
      <Modal
        title="Save Filter"
        open={isSaveCurrentFilterSettingsModalOpen}
        okText="Save"
        cancelText="Close"
        onOk={() => saveFilterSettingsForm.submit()}
        onCancel={() => setIsSaveCurrentFilterSettingsModalOpen(false)}
        okButtonProps={{
          disabled: isDisableSaveFilterButton,
        }}
      >
        <SaveFilterSettingsForm
          form={saveFilterSettingsForm}
          onFinish={saveFilterSettings}
          setIsDisableSaveFilterButton={setIsDisableSaveFilterButton}
        />
      </Modal>
      <Modal
        title="Load Saved Filter"
        open={isLoadFilterSettingsModalOpen}
        onCancel={() => setIsLoadFilterSettingsModalOpen(false)}
        footer={[
          <Button
            key="close_btn"
            onClick={() => setIsLoadFilterSettingsModalOpen(false)}
          >
            Close
          </Button>,
          <Button
            key="delete_btn"
            danger
            onClick={showDeleteSelectedFilterConfirm}
            disabled={isDisableDeleteFilterButton}
          >
            Delete
          </Button>,
          <Button
            key="load_btn"
            type="primary"
            onClick={loadSelectedFilter}
            disabled={isDisableLoadFilterButton}
          >
            Load Selected Filter
          </Button>,
        ]}
      >
        <LoadFilterSettingsForm
          form={loadFilterSettingsForm}
          setIsDisableDeleteFilterButton={setIsDisableDeleteFilterButton}
          setIsDisableLoadFilterButton={setIsDisableLoadFilterButton}
          shouldUpdateSavedFilterOptions={shouldUpdateSavedFilterOptions}
        />
      </Modal>
    </div>
  );
};

export default memo(SearchTableFiltersForm);
