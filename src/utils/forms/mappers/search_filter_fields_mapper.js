/**
 * Form fields mapper for Search Filter
 */
export const SearchFilterFieldsMapper = {
  // mfg_process: {
  //   type: "checkbox.group",
  // },
  date_range: {
    label: "Period",
    type: "select",
    defaultValue: "all",
  },
  following: {
    label: "Following",
    type: "select.multiple",
  },
  favourites: {
    label: "Favourites",
  },
  invert_search: {
    label: "Invert Search",
  },
  lot_id: {
    label: "Lot ID",
    type: "select.multiple",
  },
  wafer_id: {
    label: "Wafer ID",
    type: "select.multiple",
  },
  file_name: {
    label: "File Name",
    type: "select.multiple",
  },
  program: {
    label: "Program",
    type: "select.multiple",
  },
  tester: {
    label: "Tester",
    type: "select.multiple",
  },
  run_type: {
    label: "Run Type",
    type: "select.multiple",
  },
  family: {
    label: "Family",
    type: "select.multiple",
  },
  temperature: {
    label: "Temperature",
    type: "select.multiple",
  },
  engineering_product: {
    label: "Engineering Product",
    type: "select.multiple",
  },
  data_log_yield: {
    label: "Data Log Yield",
  },
  data_good_units: {
    label: "Data Good Units",
  },
  data_log_units: {
    label: "Data Log Units",
  },
  mes_product: {
    label: "MES Products",
    type: "select.multiple",
  },
  subcon: {
    label: "Subcon",
    type: "select.multiple",
  },
  hide_cons_files: {
    label: "Hide Cons",
    type: "checkbox",
  },
  hide_cons_subfiles: {
    label: "Hide Cons Subfiles",
    type: "checkbox",
  },
  show_char_siblings: {
    label: "Show Per Condition Data",
    type: "checkbox",
  },
  show_char_original: {
    label: "Only Characterisation-Ready Data",
    type: "checkbox",
  },
  show_has_calc_tests: {
    label: "Only with Calculated Tests",
    type: "checkbox",
  },
  show_manual_uploaded_files: {
    label: "Only Manually Uploaded Data",
    type: "checkbox",
  },
  has_xy: {
    label: "Has X/Y Coordinates",
    type: "checkbox",
  },
  has_test_data: {
    label: "Has Final Test Data",
    type: "checkbox",
  },
  has_probe_data: {
    label: "Has Wafer Sort Data",
    type: "checkbox",
  },
  has_fab_data: {
    label: "Has WAT/E-Test Data",
    type: "checkbox",
  },
  show: {
    type: "checkbox.group",
  },
  production_data: {
    label: "Production Data",
    type: "checkbox",
  },
  personal_data: {
    label: "My Engineering Data",
    type: "checkbox",
  },
  other_personal_data: {
    label: "Other Engineering Data",
    type: "checkbox",
  },
  has_serial_numbers: {
    label: "Has Serial Numbers (OTP)",
    type: "checkbox",
  },
  has_traceability_applied: {
    label: "Has Traceability Recipe Applied Data",
    type: "checkbox",
  },
  hide_traceability_split_data: {
    label: "Hide Traceability Split Data",
    type: "checkbox",
  },
};
