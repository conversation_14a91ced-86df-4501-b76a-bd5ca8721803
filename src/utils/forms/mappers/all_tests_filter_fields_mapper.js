/**
 * Form fields mapper for Search Filter
 */
export const AllTestsFilterFieldsMapper = {
  site: {
    label: "Site Number",
    type: "select.multiple",
    optionsList: "siteListOptions",
    selectedAllLabel: "All Sites",
  },
  test_type: {
    label: "Test Type",
    type: "checkbox.group",
  },
  m: {
    label: "MPR",
    type: "checkbox",
  },
  p: {
    label: "PTR",
    type: "checkbox",
  },
  f: {
    label: "FTR",
    type: "checkbox",
  },
  stats_type: {
    label: "Results Based On",
    type: "select",
    required: true,
  },
  rp: {
    label: "All (Last Results Per Die)",
    type: "select",
  },
  passing_unit: {
    label: "Passing Die Only (Last Results Per Die)",
    type: "select",
  },
  iqr: {
    label: "Robust Data (Last Results Per Die)",
    type: "select",
  },
  all: {
    label: "All",
    type: "select",
  },
  iqr_n: {
    label: "Robust Data N",
    type: "input.number",
  },
  show_all_tests: {
    label: "Show All Tests",
    type: "checkbox",
  },
  show_pseudo_tests: {
    label: "Show Pseudo Tests Only",
    type: "checkbox",
  },
  tname_show_only: {
    label: "Test Names",
    type: "input",
  },
  atnum_show_only: {
    label: "Test Numbers",
    type: "input",
  },
  no_fails_show_only: {
    label: "No Fails",
    type: "checkbox",
  },
  invalid_cp_show_only: {
    label: "Invalid Cp",
    type: "checkbox",
  },
  invalid_cpk_show_only: {
    label: "Invalid Cpk",
    type: "checkbox",
  },
  no_lower_limit_show_only: {
    label: "No Lower Limit",
    type: "checkbox",
  },
  no_upper_limit_show_only: {
    label: "No Upper Limit",
    type: "checkbox",
  },
  tname_exclude: {
    label: "Exclude Test Names",
    type: "input",
  },
  atnum_exclude: {
    label: "Exclude Test Numbers",
    type: "input",
  },
  part_id_exclude: {
    label: "Exclude Part ID",
    type: "input",
  },
  cp_less_than_exclude: {
    label: "Exclude Cp Less Than",
    type: "input.number",
  },
  cp_greater_than_exclude: {
    label: "Exclude Cp Greater Than",
    type: "input.number",
  },
  cpk_less_than_exclude: {
    label: "Exclude Cpk Less Than",
    type: "input.number",
  },
  cpk_greater_than_exclude: {
    label: "Exclude Cpk Greater Than",
    type: "input.number",
  },
  no_fails_exclude: {
    label: "Exclude No Fails",
    type: "checkbox",
  },
  invalid_cp_exclude: {
    label: "Exclude Invalid Cp",
    type: "checkbox",
  },
  invalid_cpk_exclude: {
    label: "Exclude Invalid Cpk",
    type: "checkbox",
  },
  no_lower_limit_exclude: {
    label: "Exclude No Lower Limit",
    type: "checkbox",
  },
  no_upper_limit_exclude: {
    label: "Exclude No Upper Limit",
    type: "checkbox",
  },
};
