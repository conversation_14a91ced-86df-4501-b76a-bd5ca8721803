import {
  InboxOutlined,
  ExclamationCircleOutlined,
  FileOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  MinusCircleFilled,
} from "@ant-design/icons";
import {
  App,
  Col,
  Form,
  Row,
  Typography,
  Upload,
  Modal,
  Space,
  Spin,
  Input,
  Flex,
  Divider,
  Card,
  Button,
  Collapse,
} from "antd";
import React, { useState, useRef, memo, useEffect } from "react";
import dynamic from "next/dynamic";
import { merge, remove } from "lodash";
import { Archive } from "libarchive.js";
import { useFullScreenHandle } from "react-full-screen";
import Helper from "../helper";
import Api from "../api";
import "./override.css";
import { ComponentNameMapper } from "../grid/component_name_mapper";
const WaferMap = dynamic(() => import("../charts/wafer_map"), {
  ssr: false,
});
import ChartHelper from "../charts/chart_helper";
import ChartWrapper from "../charts/chart_wrapper";
import { useBoundStore } from "../../store/store";
import FilterSelect from "../grid/components/filter_select";
import { useEffectApiFetch } from "../../hooks";
import YHGrid from "../grid/yh_grid";

const { Text, Title } = Typography;
const { Dragger } = Upload;

Archive.init({
  workerUrl: "/libarchive.js/dist/worker-bundle.js",
});

/**
 * Modal containing form used for uploading pin mapping file
 *
 * @param {string} pageKey
 * @param {object} filters
 * @param {function} setFilters
 * @param {boolean} isUploadFormOpen
 * @param {function} setIsUploadFormOpen
 * @param {boolean} uploadFormDisabled
 * @param {function} setUploadFormDisabled
 * @param {boolean} uploadingIsComplete
 * @param {function} setUploadingIsComplete
 * @param {object} prerenderData
 * @param {function} setPrerenderData
 * @returns {JSX.Element}
 */
const PinMappingFileUploadModal = ({
  pageKey,
  filters,
  setFilters,
  isUploadFormOpen,
  setIsUploadFormOpen,
  uploadFormDisabled,
  setUploadFormDisabled,
  setUploadingIsComplete,
  prerenderData,
  setPrerenderData,
}) => {
  const [isSaveBtnDisabled, setIsSaveBtnDisabled] = useState(true);
  const [files, setFiles] = useState([]);
  const [invalidFiles, setInvalidFiles] = useState([]);
  const [oversizedFiles, setOversizedFiles] = useState([]);
  const [hasPerPinMapFile, setHasPerPinMapFile] = useState();
  const [showUploadComponent, setShowUploadComponent] = useState(false);
  const [showPreviewChart, setShowPreviewChart] = useState(false);
  const [showComparisonTable, setShowComparisonTable] = useState(false);
  const [comparisonGridComponent, setComparisonGridComponent] = useState();
  const [
    perPinMapChartComponentBlueprint,
    setPerPinMapChartComponentBlueprint,
  ] = useState();
  const [hasChartData, setHasChartData] = useState(true);
  const urlParams = useBoundStore((state) => state.urlParams);
  const renderComponentActions = useBoundStore(
    (state) => state.renderComponentActions,
  );
  const getPageElement = useBoundStore((state) => state.getPageElement);
  const updatePageElement = useBoundStore((state) => state.updatePageElement);
  const getPageTabs = useBoundStore((state) => state.getPageTabs);
  const { message } = App.useApp();
  const uploadRef = useRef(null);
  const perPinMapChartRef = useRef(null);
  const previewChartRef = useRef(null);
  const comparisonGridRef = useRef(null);
  const currentPerPinMapFile = useRef(null);
  const perPinMapChartfullScreenHandle = useFullScreenHandle();
  const previewChartfullScreenHandle = useFullScreenHandle();
  const perPinMapChartKey = `${pageKey}_${ComponentNameMapper.parametric_mpr_pin_file_data_map}`;
  const previewChartKey = `${pageKey}_${ComponentNameMapper.parametric_mpr_pin_file_data_map}_preview`;
  const perPinMapChartCustomData = {};
  const previewChartCustomData = {};
  const maxFileSizeGB = 1;
  const [{ error, confirm }, contextHolder] = Modal.useModal();
  const [uploadForm] = Form.useForm();
  const fileTypeList = [
    {
      value: "csv",
      extension: ".csv",
      regex: "/\\.csv(\\.gz)?$/i",
      datalog_type: "non_datalog",
    },
  ];

  ChartHelper.initChartCustomData(perPinMapChartCustomData, perPinMapChartKey);
  ChartHelper.initChartCustomData(previewChartCustomData, previewChartKey);

  useEffect(() => {
    if (isUploadFormOpen) {
      currentPerPinMapFile.current = prerenderData.test_info?.file;
      setHasPerPinMapFile(prerenderData.test_info?.file !== undefined);
      if (
        prerenderData.test_info?.ppm_unique_field &&
        prerenderData.test_info?.ppm_unique_value
      ) {
        uploadForm.setFieldValue(
          "ppm_unique_value",
          `${prerenderData.test_info.ppm_unique_field}|${prerenderData.test_info.ppm_unique_value}`,
        );
      }
    }
  }, [isUploadFormOpen]);

  useEffectApiFetch(
    () => {
      return getPerPinMapChartComponentBlueprint();
    },
    () => {
      setPerPinMapChartComponentBlueprint();
    },
  );

  useEffect(() => {
    setShowUploadComponent(!hasPerPinMapFile && files.length === 0);
  }, [hasPerPinMapFile, files]);

  useEffect(() => {
    if (files.length === 0) {
      setShowPreviewChart(false);
      setShowComparisonTable(false);
    }
    toggleSaveBtnState();
  }, [files]);

  useEffect(() => {
    if (showComparisonTable && comparisonGridComponent === undefined) {
      getComparisonGridComponent();
    }
  }, [showComparisonTable]);

  /**
   * Get and set comparison grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getComparisonGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setComparisonGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: ComponentNameMapper.mpr_per_pin_comparison_table,
      },
    );

    return abortCtl;
  };

  /**
   * Clears upload form
   */
  const clearFilters = () => {
    uploadForm.resetFields();
    setFiles([]);
    setInvalidFiles([]);
    setOversizedFiles([]);
    setUploadFormDisabled(false);
    setUploadingIsComplete(false);
  };

  /**
   * Upload file request handler
   *
   * @param {int} shouldSave
   */
  const handleUploadRequest = (shouldSave = 1) => {
    if (files.length > 0) {
      files.forEach((value, index) => {
        files[index].status = "uploading";

        const ppmUniqueValueArr = uploadForm
          .getFieldValue("ppm_unique_value")
          .split("|");

        const formData = new FormData();
        formData.append("file", value.originFileObj ?? value);
        formData.append("dsk", prerenderData.test_info?.dsk);
        formData.append("tnum", prerenderData.test_info?.test_number);
        formData.append("field", ppmUniqueValueArr[0]);
        formData.append("value", ppmUniqueValueArr[1]);

        Api.uploadPinMappingFile(
          (res) => {
            if (res.success) {
              files[index].status = "done";
              files[index].response = res;
            } else {
              files[index].status = "error";
              files[index].response = res;
              files[index].error = {
                statusText: `${res.message}`,
              };
            }
            files.every((file) => "response" in file) &&
              handleUploadComplete(shouldSave);
          },
          (err) => {
            files[index].status = "error";
            files[index].response = err;
            files[index].error = {
              statusText: err,
            };
            files.every((file) => "response" in file) &&
              handleUploadComplete(shouldSave);
          },
          {
            form_data: formData,
            save: shouldSave,
          },
          "form-data",
        );
      });

      if (shouldSave === 1) {
        setIsUploadFormOpen(false);
        setUploadFormDisabled(true);
      }
    }
  };

  /**
   * Validate if attached file is supported
   *
   * @param {object} file
   */
  const validateFile = (file) => {
    if (hasSupportedFileType(file.name)) {
      attachFile(file);
    } else {
      invalidFiles.push(file);
    }
  };

  /**
   * Check if file has a supported file type
   *
   * @param {string} filename
   * @returns {boolean}
   */
  const hasSupportedFileType = (filename) => {
    return fileTypeList.some((type) => {
      return new RegExp(type.regex.slice(1, -2), "i").test(filename);
    });
  };

  /**
   * Checking of attached file
   *
   * @param {object} file
   */
  const attachFile = (file) => {
    if (files.some((item) => item.name === file.name)) {
      confirm({
        title: "File Already Attached!",
        icon: <ExclamationCircleOutlined />,
        centered: true,
        maskStyle: { backgroundColor: "rgba(100, 100, 100, 0.1)" },
        content: (
          <>
            <Text strong italic>
              {file.name}{" "}
            </Text>
            <Text>
              is already attached. Would you like to replace the existing file?
            </Text>
          </>
        ),
        okText: "Overwrite Existing",
        cancelText: "Cancel Attachment",
        onOk() {
          let filesCopy = Helper.cloneArray(files);
          remove(filesCopy, (item) => {
            return item.name === file.name;
          });
          filesCopy.push(file);
          setFiles(filesCopy);
          files[0] = filesCopy[0];
          handleUploadRequest(0);
        },
      });
    } else {
      const maxFileSizeBytes = maxFileSizeGB * 1024 * 1024 * 1024;
      if (file.size > maxFileSizeBytes) {
        oversizedFiles.push(file);
      } else {
        setFiles([file]);
        files[0] = file;
        handleUploadRequest(0);
      }
    }
  };

  /**
   * Handler when all files are uploaded
   *
   * @param {int} shouldSave
   */
  const handleUploadComplete = (shouldSave) => {
    let filesUploadedCount = files.filter(
      (file) => file.status === "done",
    ).length;
    setUploadingIsComplete(true);
    if (filesUploadedCount === files.length) {
      if (files.length > 0 && files[0]?.response?.data) {
        setPrerenderData(files[0].response.data);
      }
      if (shouldSave === 1) {
        clearFilters();
        regenerateMPRMaps(true);
      } else {
        setShowPreviewChart(false);
        setShowComparisonTable(false);
      }
    } else if (filesUploadedCount !== files.length && filesUploadedCount > 0) {
      error({
        centered: true,
        title: "Upload Error",
        content: (
          <div>
            {`${files.length - filesUploadedCount} out of ${files.length} files failed to upload.`}
            <br />
            Please try again or contact support if the issue persists.
          </div>
        ),
        okText: "View Files",
        onOk() {
          setIsUploadFormOpen(true);
        },
        onCancel() {
          setIsUploadFormOpen(true);
        },
      });
    } else {
      error({
        centered: true,
        title: "Upload Error",
        okText: files.length > 1 ? "View Files" : "View File",
        content: (
          <div>
            {files.length > 1
              ? "An error occured while uploading your files."
              : "An error occured while uploading your file."}
            <br />
            Please try again or contact support if the issue persists.
          </div>
        ),
        onOk() {
          setIsUploadFormOpen(true);
        },
        onCancel() {
          setIsUploadFormOpen(true);
        },
      });
    }
  };

  /**
   * Trigger row remounting to regenerate content
   *
   * @param {boolean} hasPerPinMapData
   */
  const regenerateMPRMaps = (hasPerPinMapData) => {
    // rerender mpr maps row content
    renderComponentActions["mpr_maps_container"]((prev) => ({
      render: hasPerPinMapData,
      version: prev.version + 1,
      queued: false,
    }));

    // rerender mpr xy maps row content
    if (typeof renderComponentActions["mpr_xy_maps_container"] === "function") {
      renderComponentActions["mpr_xy_maps_container"]((prev) => ({
        render: hasPerPinMapData,
        version: prev.version + 1,
        queued: false,
      }));
    }

    // rerender mpr xy maps
    const mprXyMapRenderKeys = [
      `${ComponentNameMapper.parametric_mpr_xy_map_mean}_${prerenderData.test_info.test_number}`,
      `${ComponentNameMapper.parametric_mpr_xy_map_stdev}_${prerenderData.test_info.test_number}`,
      `${ComponentNameMapper.parametric_mpr_xy_map_min}_${prerenderData.test_info.test_number}`,
      `${ComponentNameMapper.parametric_mpr_xy_map_max}_${prerenderData.test_info.test_number}`,
    ];
    mprXyMapRenderKeys.forEach((renderKey) => {
      if (renderComponentActions[renderKey]) {
        renderComponentActions[renderKey]((prev) => ({
          render: hasPerPinMapData,
          version: prev.version + 1,
          queued: false,
        }));
      }
    });

    // update mpr xy chart options items state
    const chartOptionsItemKeys = [
      Helper.generateTabKey(
        "mean_mpr_xy_per_group_{test_number}",
        prerenderData.test_info,
      ),
      Helper.generateTabKey(
        "stdev_mpr_xy_per_group_{test_number}",
        prerenderData.test_info,
      ),
      Helper.generateTabKey(
        "min_mpr_xy_per_group_{test_number}",
        prerenderData.test_info,
      ),
      Helper.generateTabKey(
        "max_mpr_xy_per_group_{test_number}",
        prerenderData.test_info,
      ),
      Helper.generateTabKey(
        "mean_mpr_xy_per_datalog_{test_number}",
        prerenderData.test_info,
      ),
      Helper.generateTabKey(
        "stdev_mpr_xy_per_datalog_{test_number}",
        prerenderData.test_info,
      ),
      Helper.generateTabKey(
        "min_mpr_xy_per_datalog_{test_number}",
        prerenderData.test_info,
      ),
      Helper.generateTabKey(
        "max_mpr_xy_per_datalog_{test_number}",
        prerenderData.test_info,
      ),
    ];
    chartOptionsItemKeys.forEach((itemKey) => {
      const pageElement = getPageElement(itemKey) ?? {};
      pageElement.disabled = !hasPerPinMapData;
      updatePageElement(itemKey, pageElement);
    });

    // remove mpr xy tabs
    const mprXYTabs = {
      [Helper.generateTabKey(
        "mean_per_group_wafer_tabs_{test_number}",
        prerenderData.test_info,
      )]: [
        Helper.generateTabKey(
          "mean_mpr_xy_per_group_{test_number}",
          prerenderData.test_info,
        ),
        Helper.generateTabKey(
          "mean_mpr_xy_per_datalog_{test_number}",
          prerenderData.test_info,
        ),
      ],
      [Helper.generateTabKey(
        "stdev_per_group_wafer_tabs_{test_number}",
        prerenderData.test_info,
      )]: [
        Helper.generateTabKey(
          "stdev_mpr_xy_per_group_{test_number}",
          prerenderData.test_info,
        ),
        Helper.generateTabKey(
          "stdev_mpr_xy_per_datalog_{test_number}",
          prerenderData.test_info,
        ),
      ],
      [Helper.generateTabKey(
        "min_per_group_wafer_tabs_{test_number}",
        prerenderData.test_info,
      )]: [
        Helper.generateTabKey(
          "min_mpr_xy_per_group_{test_number}",
          prerenderData.test_info,
        ),
        Helper.generateTabKey(
          "min_mpr_xy_per_datalog_{test_number}",
          prerenderData.test_info,
        ),
      ],
      [Helper.generateTabKey(
        "max_per_group_wafer_tabs_{test_number}",
        prerenderData.test_info,
      )]: [
        Helper.generateTabKey(
          "max_mpr_xy_per_group_{test_number}",
          prerenderData.test_info,
        ),
        Helper.generateTabKey(
          "max_mpr_xy_per_datalog_{test_number}",
          prerenderData.test_info,
        ),
      ],
    };
    Object.keys(mprXYTabs).forEach((tabsKey) => {
      const tabs = getPageTabs(tabsKey);
      Helper.removeTabs(
        mprXYTabs[tabsKey],
        tabs.activeTabKey,
        tabs.items,
        tabs.setItems,
        tabs.setActiveTabKey,
      );
    });
  };

  /**
   * Handles the cancellation of upload
   */
  const handleCancelUpload = () => {
    if (files.length > 0) {
      if (uploadFormDisabled) {
        setIsUploadFormOpen(false);
        clearFilters();
      } else {
        confirm({
          title: "Cancel Without Uploading",
          icon: <ExclamationCircleOutlined />,
          centered: true,
          content:
            "Are you sure you want to cancel without uploading? All attachments will be lost.",
          okText: "Back",
          cancelText: "Cancel and Leave",
          onCancel() {
            setIsUploadFormOpen(false);
            clearFilters();
          },
        });
      }
    } else {
      setIsUploadFormOpen(false);
      clearFilters();
    }
  };

  /**
   * Upload custom item renderer
   *
   * @param {object} originNode
   * @param {object} file
   * @returns {JSX.Element}
   */
  const customItemRender = (originNode, file) => {
    return file.status === "error" ? (
      <div
        style={{
          color: "red",
        }}
      >
        <span className="text-red block mb-[-6px]">
          {file.error.statusText}
        </span>
        {originNode}
        <span
          className="text-red italic block ml-1 mt-[-2px]"
          style={{
            fontSize: "0.65rem",
          }}
        >
          {Helper.formatFileSize(file.size)}
        </span>
      </div>
    ) : (
      <div>
        <Flex gap="middle">
          {file.status === "current" && (
            <MinusCircleFilled
              style={{ color: "red", fontSize: "x-large" }}
              onClick={confirmPinMapFileDataDeletion}
            />
          )}
          <div className="flex-1">{originNode}</div>
        </Flex>
        {file.size !== undefined && (
          <Text
            italic
            className="block ml-1 mt-[-2px]"
            style={{
              fontSize: "0.65rem",
            }}
          >
            {Helper.formatFileSize(file.size)}
          </Text>
        )}
      </div>
    );
  };

  /**
   * Display invalid files and oversized files if there are any
   */
  const showInvalidAndOversizedFiles = () => {
    if (invalidFiles.length > 0) {
      error({
        centered: true,
        width: "50%",
        title: "Invalid File Detected!",
        content: (
          <>
            <Text>
              The following type of file(s) are not supported and cannot be
              uploaded:
            </Text>
            <Space className="mt-5 mb-5 gap-y-1 w-full" direction="vertical">
              {invalidFiles.map((file, index) => (
                <Text key={index} strong italic>
                  {file.name}
                </Text>
              ))}
            </Space>
            <Text>Please cancel attachment(s) to proceed.</Text>
          </>
        ),
        okText: "Cancel Attachment",
        onOk() {
          setInvalidFiles([]);
        },
        onCancel() {
          setInvalidFiles([]);
        },
      });
    }

    if (oversizedFiles.length > 0) {
      error({
        centered: true,
        width: "50%",
        title: "File Size Limit Exceeded",
        content: (
          <>
            <Text>
              The uploaded file has exceeded the maximum file size allowed of
              1GB.
            </Text>
            <Space className="mt-5 mb-5 gap-y-1 w-full" direction="vertical">
              {oversizedFiles.map((file, index) => (
                <Text key={index} strong italic>
                  {file.name}
                </Text>
              ))}
            </Space>
          </>
        ),
        onOk() {
          setOversizedFiles([]);
        },
        onCancel() {
          setOversizedFiles([]);
        },
      });
    }
  };

  /**
   * Confirm deletion of existing pin map file data
   *
   * @returns {boolean} confirmed
   */
  const confirmPinMapFileDataDeletion = async () => {
    const confirmed = await confirm({
      title: "Confirm Deletion",
      okText: "Delete",
      content: (
        <>
          Are you sure you want to delete this CSV file permanently?
          <br />
          <strong>Warning!</strong> This action cannot be undone!
        </>
      ),
    });
    if (confirmed) {
      deletePerPinMapData();
    }

    return confirmed;
  };

  /**
   * Delete per pin map data
   */
  const deletePerPinMapData = () => {
    Api.deletePerPinMapData(
      (res) => {
        if (res.success) {
          const ppmFields = [
            "mpr_per_pin_map_key",
            "pin_physical_map_name",
            "pin_physical_map_version",
            "ppm_unique_field",
            "ppm_unique_label",
            "ppm_unique_value",
            "file",
          ];
          ppmFields.forEach((field) => {
            delete prerenderData.test_info?.[field];
          });
          delete prerenderData.per_pin_map;
          setHasPerPinMapFile(false);
          regenerateMPRMaps(false);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        field: prerenderData.test_info.ppm_unique_field,
        value: prerenderData.test_info.ppm_unique_value,
      },
    );
  };

  /**
   * Get and set per pin map chart component blueprint
   *
   * @returns {AbortController} abortCtl
   */
  const getPerPinMapChartComponentBlueprint = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setPerPinMapChartComponentBlueprint(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: ComponentNameMapper.parametric_mpr_per_pin_map,
      },
    );

    return abortCtl;
  };

  /**
   * Display of uploaded file
   *
   * @param {string} filename
   * @returns {JSX.Element}
   */
  const displayUploadedFile = (filename, deleteFn) => {
    return (
      <Flex flex={1} gap="middle" className="my-2!">
        <MinusCircleFilled
          style={{ color: "red", fontSize: "x-large" }}
          onClick={deleteFn}
        />
        <Flex flex={1}>
          <Card className="flex-auto">
            <FileOutlined className="mr-2" />
            {filename}
          </Card>
        </Flex>
      </Flex>
    );
  };

  /**
   * Generate and display of per pin map chart preview
   *
   * @param {string} chartKey
   * @param {React.Ref} chartRef
   * @param {object} chartCustomData
   * @param {object} fullScreenHandle
   * @param {boolean} fromCache
   * @returns {JSX.Element}
   */
  const displayPerPinMapChartPreview = (
    chartKey,
    chartRef,
    chartCustomData,
    fullScreenHandle,
    fromCache = false,
  ) => {
    let chartFilters = {
      file: fromCache ? files[0]?.name : prerenderData.test_info?.file,
      dsk: prerenderData.test_info?.dsk?.toString(),
    };
    if (fromCache) {
      const ppmUniqueValueArr = uploadForm
        .getFieldValue("ppm_unique_value")
        .split("|");
      chartFilters = merge(chartFilters, {
        field: ppmUniqueValueArr[0],
        value: ppmUniqueValueArr[1],
        from_cache: true,
      });
    }
    const chart = (
      <WaferMap
        chartRef={chartRef}
        component={perPinMapChartComponentBlueprint}
        filters={filters}
        pageKey={pageKey}
        chartKey={chartKey}
        chartFilters={chartFilters}
        chartCustomData={chartCustomData}
        setHasChartData={setHasChartData}
        prerenderData={prerenderData}
      />
    );

    return (
      <ChartWrapper
        key={chartKey}
        chartComponent={chart}
        chartRef={chartRef}
        component={perPinMapChartComponentBlueprint}
        pageKey={pageKey}
        waferIdOptions={[]}
        chartCustomData={chartCustomData}
        chartKey={chartKey}
        displayChartControls={false}
        hasChartData={hasChartData}
        fullScreenHandle={fullScreenHandle}
      />
    );
  };

  /**
   * Enable/Disable save button
   */
  const toggleSaveBtnState = () => {
    setIsSaveBtnDisabled(
      !uploadForm.getFieldValue("ppm_unique_value") ||
        files.length === 0 ||
        uploadFormDisabled,
    );
  };

  return (
    <Modal
      width="50%"
      open={isUploadFormOpen}
      destroyOnClose={true}
      okText="Save to Program and Create Pin Map"
      okButtonProps={{
        disabled: isSaveBtnDisabled,
      }}
      onOk={() => uploadForm.submit()}
      onCancel={handleCancelUpload}
      centered
      closable
    >
      {isUploadFormOpen && (
        <div className="p-4">
          {contextHolder}
          <Form
            form={uploadForm}
            onFinish={() => handleUploadRequest()}
            disabled={uploadFormDisabled}
            onValuesChange={() => {
              toggleSaveBtnState();
            }}
          >
            <Title level={5}>Upload CSV Pin Map File</Title>
            <Row gutter={100} className="mb-2">
              <Col span={12}>
                <Form.Item
                  name="ppm_unique_value"
                  label="Program Used"
                  className="mb-2!"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <FilterSelect
                    className="w-full"
                    componentKey="ppm_unique_value_options"
                    params={{
                      api: {
                        url: "api/v1/internal/options/list/ppm",
                        src_type: urlParams[pageKey].src_type,
                        src_value: urlParams[pageKey].src_value,
                        cache_it: 0,
                      },
                    }}
                  />
                </Form.Item>
                {prerenderData.test_info?.ppm_unique_value && (
                  <Space className="ml-3">
                    <span>
                      Name: {prerenderData.test_info?.pin_physical_map_name}
                    </span>
                    <span>
                      Version:{" "}
                      {prerenderData.test_info?.pin_physical_map_version}
                    </span>
                  </Space>
                )}
              </Col>
            </Row>
            {showUploadComponent && (
              <Dragger
                ref={uploadRef}
                className="w-full overflow-hidden custom-dragger"
                fileList={files}
                listType={"picture"}
                maxCount={1}
                showUploadList={false}
                itemRender={customItemRender}
                beforeUpload={() => {
                  return false;
                }}
                onChange={({ file, fileList }) => {
                  // validation before file attachment
                  if (file.status !== "removed") {
                    validateFile(file);
                  } else {
                    setFiles(fileList);
                  }
                  // checking during the last file attached
                  if (
                    fileList.length > 0 &&
                    fileList[fileList.length - 1].uid === file.uid
                  ) {
                    showInvalidAndOversizedFiles();
                  }
                }}
                onRemove={(file) => {
                  let shouldRemove = true;
                  if (file.status === "current") {
                    shouldRemove = confirmPinMapFileDataDeletion();
                  }
                  return shouldRemove;
                }}
                iconRender={(file) => {
                  let icon = <FileOutlined />;
                  if (file.status === "done") {
                    icon = <CheckCircleOutlined style={{ color: "green" }} />;
                  } else if (file.status === "uploading") {
                    icon = (
                      <Spin indicator={<LoadingOutlined spin />} size="large" />
                    );
                  } else if (file.status === "error") {
                    icon = <CloseCircleOutlined style={{ color: "red" }} />;
                  }
                  return icon;
                }}
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">
                  Click or drag file to this area to upload
                </p>
                <p className="ant-upload-hint">
                  Support for a single upload only.
                  <br />
                  Must not exceed 1gb per file.
                </p>
              </Dragger>
            )}
            <Row>
              <Col span={24}>
                <Form.Item name={`file`} noStyle>
                  <Input type="hidden" />
                </Form.Item>
              </Col>
            </Row>
            {(hasPerPinMapFile || files.length > 0) && (
              <>
                <Flex gap={48}>
                  {hasPerPinMapFile && (
                    <Flex flex={1} vertical>
                      {files.length > 0 && (
                        <Title level={5} className="mt-2 ml-8">
                          Current
                        </Title>
                      )}
                      {displayUploadedFile(
                        currentPerPinMapFile.current,
                        confirmPinMapFileDataDeletion,
                      )}
                    </Flex>
                  )}

                  {files.length > 0 && (
                    <Flex flex={1} vertical>
                      {hasPerPinMapFile && (
                        <Title level={5} className="mt-2 ml-8">
                          Replacement
                        </Title>
                      )}
                      {displayUploadedFile(files[0].name, () => setFiles([]))}
                    </Flex>
                  )}
                </Flex>
                <Flex justify="flex-end" gap="middle">
                  <Button
                    disabled={files.length === 0}
                    onClick={() => {
                      setShowPreviewChart(true);
                      setShowComparisonTable(true);
                    }}
                  >
                    Preview
                  </Button>
                  <Upload
                    maxCount={1}
                    showUploadList={false}
                    beforeUpload={() => {
                      return false;
                    }}
                    onChange={({ file, fileList }) => {
                      // validation before file attachment
                      if (file.status !== "removed") {
                        validateFile(file);
                      } else {
                        setFiles(fileList);
                      }
                    }}
                  >
                    <Button>
                      {files.length === 0 ? "Upload" : "Re-upload"}
                    </Button>
                  </Upload>
                </Flex>
              </>
            )}
            {showComparisonTable && (
              <Collapse
                className="mt-2!"
                defaultActiveKey={["changes_made"]}
                items={[
                  {
                    key: "changes_made",
                    label: "Changes Made",
                    children: comparisonGridComponent && (
                      <YHGrid
                        gridRef={comparisonGridRef}
                        gridId={`${pageKey}_${comparisonGridComponent.name}`}
                        component={comparisonGridComponent}
                        pageKey={pageKey}
                        filters={filters}
                        setFilters={setFilters}
                        initialGridFilters={{
                          dsk: prerenderData.test_info?.dsk?.toString(),
                          field: prerenderData.test_info?.ppm_unique_field,
                          value: prerenderData.test_info?.ppm_unique_value,
                          edit_data: prerenderData.edit_data,
                        }}
                        rowGroups={[]}
                        wrapperClassName="flex grow flex-col h-full"
                      />
                    ),
                  },
                ]}
              ></Collapse>
            )}
            {(hasPerPinMapFile || showPreviewChart) && (
              <Row>
                <Col span={24} className="m-auto">
                  <Divider className="font-semibold!">Preview</Divider>
                  <Flex>
                    {hasPerPinMapFile && (
                      <div className="w-1/2 m-auto">
                        {files.length > 0 && showPreviewChart && (
                          <Title level={5} className="text-center">
                            Current
                          </Title>
                        )}
                        {displayPerPinMapChartPreview(
                          perPinMapChartKey,
                          perPinMapChartRef,
                          perPinMapChartCustomData,
                          perPinMapChartfullScreenHandle,
                        )}
                      </div>
                    )}
                    {showPreviewChart && (
                      <div className="w-1/2 m-auto">
                        {hasPerPinMapFile && (
                          <Title level={5} className="text-center">
                            Replacement
                          </Title>
                        )}
                        {displayPerPinMapChartPreview(
                          previewChartKey,
                          previewChartRef,
                          previewChartCustomData,
                          previewChartfullScreenHandle,
                          true,
                        )}
                      </div>
                    )}
                  </Flex>
                </Col>
              </Row>
            )}
          </Form>
        </div>
      )}
    </Modal>
  );
};

export default memo(PinMappingFileUploadModal);
