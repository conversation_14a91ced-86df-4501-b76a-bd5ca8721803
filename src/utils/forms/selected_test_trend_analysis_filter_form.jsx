import {
  ExclamationCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import {
  Button,
  Col,
  Divider,
  Flex,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Typography,
  DatePicker,
  message,
  Tooltip,
} from "antd";
import { memo, useEffect, useState } from "react";
import { useBoundStore } from "../../store/store";
import FilterSelect from "../grid/components/filter_select";
import TestListSelect from "../components/test_list_select";
import Helper from "../helper";
import Api from "../api";
import { useEffectApiFetch } from "../../hooks";
import { OptionsList } from "./options_list";

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

/**
 * Form used for creating new selected test trend analysis
 *
 * @param {FormInstance} selectedTestTrendAnalysisFilterForm
 * @param {string} pageKey
 * @param {function} setIsSelectedTestAnalysisFilterOpen
 * @returns {JSX.Element}
 */
const SelectedTestTrendAnalysisFilterForm = ({
  selectedTestTrendAnalysisFilterForm,
  pageKey,
  setIsSelectedTestAnalysisFilterOpen,
}) => {
  const [groupingOptions, setGroupingOptions] = useState([]);
  const urlParams = useBoundStore((state) => state.urlParams);
  const [modal, contextHolder] = Modal.useModal();
  const [messageApi] = message.useMessage();
  const [programFilterList, setProgramFilterList] = useState([]);
  const [initialValues, setInitialValues] = useState(null);
  const [selectedTestNumber, setSelectedTestNumber] = useState(
    urlParams[pageKey].tnum,
  );
  const [selectedMfgProcess, setSelectedMfgProcess] = useState(
    urlParams[pageKey].mfg_process,
  );
  const [selectedSubcon, setSelectedSubcon] = useState("");
  const [selectedProcId, setSelectedProcId] = useState("");
  const [selectedPartTyp, setSelectedPartTyp] = useState("");
  const [selectedProgram, setSelectedProgram] = useState("");
  const [selectedTest, setSelectedTest] = useState(urlParams[pageKey].tnum);
  const otherOptionsInitialValue = {
    mfg_process: urlParams[pageKey].mfg_process,
    stats_type_type: "statistics_type",
    stats_type: "rp",
    site: 255,
    sort_dir: "desc",
    sort_by: "start_date",
    datalogged_parts: "1",
    simulated_charts: ["trend_boxwhisker", "trend_line", "trend_cpcpk"],
  };

  useEffectApiFetch(
    () => {
      return getProgramFiltersList();
    },
    () => {
      setProgramFilterList([]);
    },
  );

  useEffectApiFetch(
    () => {
      return getProgramFiltersData();
    },
    () => {
      setInitialValues(otherOptionsInitialValue);
    },
  );

  useEffect(() => {
    const grouping = urlParams[pageKey].group_by || "file_name";
    const groupingOption = Helper.filterArrayAndFind(
      groupingOptions,
      "value",
      grouping,
    );
    selectedTestTrendAnalysisFilterForm.setFieldValue("group_by", {
      value: grouping,
      label: groupingOption?.label ?? "",
    });
  }, [groupingOptions]);

  /**
   * Get program filter list
   */
  const getProgramFiltersList = () => {
    const abortCtl = Api.getSelectOptions(
      (res) => {
        if (res.success) {
          setProgramFilterList(res.data);
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      {
        field: "program_filters",
        cache_it: 1,
      },
    );

    return abortCtl;
  };

  /**
   * Get program filter data
   */
  const getProgramFiltersData = () => {
    const abortCtl = Api.getDatalogInfo(
      (res) => {
        if (res.success) {
          const data = res.data;
          const uniqueSubcon = [...new Set(data.map((row) => row.subcon))];
          const uniqueProcId = [
            ...new Set(
              data.map((row) => row.proc_id).filter((value) => value !== ""),
            ),
          ];
          const uniquePartTyp = [
            ...new Set(
              data.map((row) => row.part_typ).filter((value) => value !== ""),
            ),
          ];
          const uniqueProgram = [...new Set(data.map((row) => row.program))];
          const newInitialValues = {
            ...otherOptionsInitialValue,
            subcon: uniqueSubcon,
            proc_id: uniqueProcId,
            part_typ: uniquePartTyp,
            program: uniqueProgram,
          };
          setInitialValues(newInitialValues);
          selectedTestTrendAnalysisFilterForm.setFieldsValue(newInitialValues);
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      {
        dsk: urlParams[pageKey].src_value,
      },
    );

    return abortCtl;
  };

  /**
   * Triggers when submitting the form
   */
  const onFinish = () => {
    setIsSelectedTestAnalysisFilterOpen(false);
  };

  /**
   * Handle test list options change event
   *
   * @param {array} options
   */
  const onChangeTestListOptions = (options) => {
    if (options.length > 0 && selectedTestNumber) {
      selectedTestTrendAnalysisFilterForm.setFieldValue("tnum", [options[0]]);
      setSelectedTestNumber();
    }
  };

  /**
   * Display confirmation modal to reset filters
   */
  const showResetFiltersConfirmation = () => {
    modal.confirm({
      title: "Reset to Default Settings",
      icon: <ExclamationCircleOutlined />,
      content:
        "Are you sure you want to reset to default settings? Any unsaved changes will be lost. You can cancel to keep your current settings.",
      okText: "Reset",
      cancelText: "Cancel",
      onOk() {
        resetFilters();
        message.success("Filters successfully cleared.");
      },
    });
  };

  /**
   * Reset filters form
   */
  const resetFilters = () => {
    selectedTestTrendAnalysisFilterForm.resetFields();
    setSelectedTestNumber(urlParams[pageKey].tnum);
  };

  /**
   * Reset program filters fields
   */
  const resetProgramFilters = () => {
    selectedTestTrendAnalysisFilterForm.setFieldsValue({
      subcon: undefined,
      proc_id: undefined,
      part_typ: undefined,
      program: undefined,
      tnum: undefined,
    });
    setSelectedSubcon("");
    setSelectedProcId("");
    setSelectedPartTyp("");
    setSelectedProgram("");
  };

  /**
   * Display confirmation modal to close filters
   */
  const showCanceltFiltersConfirmation = () => {
    modal.confirm({
      title: "Confirm Exit",
      icon: <ExclamationCircleOutlined />,
      content: "Are you sure you want to exit? All progress will be lost.",
      okText: "Leave without creating",
      cancelText: "Cancel",
      onOk() {
        closeFilters();
      },
    });
  };

  /**
   * Close filters form
   */
  const closeFilters = () => {
    setIsSelectedTestAnalysisFilterOpen(false);
    resetFilters();
  };

  return (
    <div className="p-4">
      {contextHolder}
      <Form
        form={selectedTestTrendAnalysisFilterForm}
        name="selected_test_trend_analysis_filter_form"
        layout="vertical"
        initialValues={initialValues}
        onFinish={onFinish}
      >
        <Title level={5} className="pb-3">
          New Trend Analysis
        </Title>
        <Divider className="mt-0!">
          <Text strong>Filters</Text>
        </Divider>
        <Row gutter={14}>
          <Col span={12}>
            <Form.Item name={`mfg_process`} label="Manufacturing Process">
              <FilterSelect
                className="w-full"
                onChange={(option) => {
                  resetProgramFilters();
                  setSelectedMfgProcess(option.value);
                  selectedTestTrendAnalysisFilterForm.setFieldValue(
                    "mfg_process",
                    option,
                  );
                }}
                params={{
                  api: {
                    field: "mfg_process",
                    method: "GET",
                    cache_it: 1,
                  },
                }}
              />
            </Form.Item>
          </Col>
          {programFilterList.includes("subcon") && (
            <Col span={12}>
              <Form.Item name={`subcon`} label="Subcon">
                <FilterSelect
                  className="w-full"
                  placeholder="-Select Subcon-"
                  mode="multiple"
                  onChange={(option) => {
                    resetProgramFilters();
                    setSelectedSubcon(option.join(","));
                    selectedTestTrendAnalysisFilterForm.setFieldValue(
                      "subcon",
                      option,
                    );
                  }}
                  params={{
                    api: {
                      field: `subcon`,
                      mfg_process: selectedMfgProcess,
                      cache_it: 0,
                    },
                  }}
                />
              </Form.Item>
            </Col>
          )}
        </Row>
        <Row gutter={14}>
          {programFilterList.includes("proc_id") && (
            <Col span={12}>
              <Form.Item name={`proc_id`} label="Process ID">
                <FilterSelect
                  className="w-full"
                  placeholder="-Select Process ID-"
                  mode="multiple"
                  onChange={(option) => {
                    setSelectedProcId(option.join(","));
                  }}
                  params={{
                    api: {
                      ...urlParams[pageKey],
                      mfg_process: selectedMfgProcess,
                      subcon: selectedSubcon,
                      program: selectedProgram,
                      proc_id: selectedProcId,
                      part_typ: selectedPartTyp,
                      url: `api/v1/test_level/trend/list/filters/proc_id`,
                      cache_it: 0,
                    },
                  }}
                  deps={[
                    selectedMfgProcess,
                    selectedSubcon,
                    selectedProcId,
                    selectedPartTyp,
                    selectedProgram,
                  ]}
                />
              </Form.Item>
            </Col>
          )}
          {programFilterList.includes("part_typ") && (
            <Col span={12}>
              <Form.Item name={`part_typ`} label="Engineering Product">
                <FilterSelect
                  className="w-full"
                  placeholder="-Select Engineering Product-"
                  mode="multiple"
                  onChange={(option) => {
                    setSelectedPartTyp(option.join(","));
                  }}
                  params={{
                    api: {
                      ...urlParams[pageKey],
                      mfg_process: selectedMfgProcess,
                      subcon: selectedSubcon,
                      program: selectedProgram,
                      proc_id: selectedProcId,
                      part_typ: selectedPartTyp,
                      url: `api/v1/test_level/trend/list/filters/part_typ`,
                      cache_it: 0,
                    },
                  }}
                  deps={[
                    selectedMfgProcess,
                    selectedSubcon,
                    selectedProcId,
                    selectedPartTyp,
                    selectedProgram,
                  ]}
                />
              </Form.Item>
            </Col>
          )}
        </Row>
        <Row gutter={14}>
          <Col span={12}>
            <Form.Item name={`program`} label="Programs">
              <FilterSelect
                className="w-full"
                placeholder="-Select Program-"
                mode="multiple"
                onChange={(option) => {
                  setSelectedProgram(option.join(","));
                }}
                params={{
                  api: {
                    ...urlParams[pageKey],
                    mfg_process: selectedMfgProcess,
                    subcon: selectedSubcon,
                    program: selectedProgram,
                    proc_id: selectedProcId,
                    part_typ: selectedPartTyp,
                    url: `api/v1/test_level/trend/list/filters/program`,
                    cache_it: 0,
                  },
                }}
                deps={[
                  selectedMfgProcess,
                  selectedSubcon,
                  selectedProcId,
                  selectedPartTyp,
                  selectedProgram,
                ]}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={`tnum`}
              label={
                <span>
                  Select Tests
                  <Tooltip title="Each selected test will open a new tab">
                    <InfoCircleOutlined className="ml-1" />
                  </Tooltip>
                </span>
              }
            >
              <TestListSelect
                labelInValue
                mode="multiple"
                apiParams={{
                  src_type: urlParams[pageKey].src_type,
                  src_value: urlParams[pageKey].src_value,
                  mfg_process: selectedMfgProcess,
                  subcon: selectedSubcon,
                  program: selectedProgram,
                  proc_id: selectedProcId,
                  part_typ: selectedPartTyp,
                }}
                searchValue={selectedTestNumber}
                onChangeTestListOptions={onChangeTestListOptions}
                onChange={(option) => {
                  setSelectedTest(option.map((opt) => opt.value).join(","));
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Divider className="mt-0!">
          <Text strong>Other Options</Text>
        </Divider>
        <Row gutter={14}>
          <Col span={12}>
            <Form.Item name={`stats_type`} label="Statistics Type">
              <Select
                placeholder="-Select Stats Type-"
                popupMatchSelectWidth={false}
                options={OptionsList.stats_type}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name={`datalogged_parts`} label="Minimum Datalog Units">
              <Input />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={14}>
          <Col span={12}>
            <Form.Item name={`start_date`} label="Date Range">
              <RangePicker format="YYYY-MM-DD" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name={`sort_by`} label="Sort By">
              <Flex className="w-full">
                <Select
                  placeholder="-Select Sort By-"
                  popupMatchSelectWidth={false}
                  options={[
                    {
                      value: "xlabel",
                      label: "X Label",
                    },
                    {
                      value: "start_date",
                      label: "Start Date",
                    },
                  ]}
                />
              </Flex>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={14}>
          <Col span={12}>
            <Form.Item name={`sort_dir`} label="Order">
              <Select
                placeholder="-Select Order-"
                options={OptionsList.sort_dir}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name={`group_by`} label="Group By">
              <FilterSelect
                labelInValue
                componentKey="select_aggregate_options"
                className="w-full"
                placeholder="-Select-"
                disabled={urlParams[pageKey].src_value?.split(",").length < 2}
                setOptions={setGroupingOptions}
                params={{
                  api: {
                    url: "api/v1/internal/options/list/aggregate_options",
                    mfg_process: selectedMfgProcess,
                    cache_it: 0,
                  },
                  allOption: {
                    label: "Grouped as one",
                    value: "",
                  },
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={14}>
          <Col span={12}>
            <Form.Item name={`simulated_charts`} label="Chart Types to Include">
              <Select
                mode="multiple"
                options={[
                  {
                    value: "trend_boxwhisker",
                    label: "Boxwhisker",
                  },
                  {
                    value: "trend_line",
                    label: "Trend Line",
                  },
                  {
                    value: "trend_cpcpk",
                    label: "Cp and Cpk Trend",
                  },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>
        <Flex justify="flex-end" gap="middle">
          <Button
            key="cancel"
            onClick={() =>
              selectedTestTrendAnalysisFilterForm.isFieldsTouched()
                ? showCanceltFiltersConfirmation()
                : closeFilters()
            }
          >
            Cancel
          </Button>
          <Button key="reset" onClick={() => showResetFiltersConfirmation()}>
            Reset
          </Button>
          <Button
            key="create"
            type="primary"
            disabled={!selectedTest}
            onClick={() => selectedTestTrendAnalysisFilterForm.submit()}
          >
            Create
          </Button>
        </Flex>
      </Form>
    </div>
  );
};

export default memo(SelectedTestTrendAnalysisFilterForm);
