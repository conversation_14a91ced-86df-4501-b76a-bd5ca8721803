import { InfoCircleOutlined } from "@ant-design/icons";
import {
  Al<PERSON>,
  Checkbox,
  Col,
  Collapse,
  Divider,
  Form,
  Row,
  Select,
  Space,
  Typography,
} from "antd";
import { useRef } from "react";
import { AddBinColumnGridBlueprint } from "../grid/blueprints/add_bin_column_grid_blueprint";
import YHGrid from "../grid/yh_grid";
import styles from "./styles.module.css";

const { Text, Title } = Typography;
const { Panel } = Collapse;

/**
 * Form to add bin column
 *
 * @returns {JSX.Element}
 */
const AddBinColumnForm = () => {
  const [form] = Form.useForm();
  const hardwareBinGridRef = useRef();
  const softwareBinGridRef = useRef();

  return (
    <Form
      form={form}
      className={styles.modalForm}
      layout="vertical"
      name="add_bin_column_form"
    >
      <Form.Item name="program" label="Program">
        <Select
          className="w-2/5!"
          showSearch
          placeholder="Input Program"
          popupMatchSelectWidth={false}
          options={[
            {
              value: "program_1",
              label: "Program 1",
            },
          ]}
        />
      </Form.Item>
      <Collapse defaultActiveKey={["software"]} accordion>
        <Panel header="Hardware" key="hardware">
          <YHGrid
            gridRef={hardwareBinGridRef}
            component={AddBinColumnGridBlueprint}
            filters={{ binType: "hardware" }}
          />
        </Panel>
        <Panel header="Software" key="software">
          <YHGrid
            gridRef={softwareBinGridRef}
            component={AddBinColumnGridBlueprint}
            filters={{ binType: "software" }}
          />
        </Panel>
      </Collapse>

      <Divider>
        <Title level={5}>Options</Title>
      </Divider>
      <Alert
        className="mb-2"
        message={
          <>
            <Text>Note:</Text>{" "}
            <Text>You must choose at least one per selection.</Text>
          </>
        }
        type="info"
        showIcon
      ></Alert>
      <Row>
        <Col span={12}>
          <Form.Item
            name="percentage_or_count"
            label={<Text strong>Percentage or Count</Text>}
            tooltip={{
              title: "TODO Info here",
              icon: <InfoCircleOutlined />,
            }}
          >
            <Space>
              <Form.Item>
                <Checkbox name="show_bin_percentage">
                  Show Bin Percentage
                </Checkbox>
              </Form.Item>
              <Form.Item>
                <Checkbox name="show_bin_count">Show Bin Count</Checkbox>
              </Form.Item>
            </Space>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="datalog_or_summary"
            label={<Text strong>Datalog or Summary</Text>}
            tooltip={{
              title: "TODO Info here",
              icon: <InfoCircleOutlined />,
            }}
          >
            <Space>
              <Form.Item>
                <Checkbox name="datalogged_data">Datalogged Data</Checkbox>
              </Form.Item>
              <Form.Item>
                <Checkbox name="summary_data">Summary Data</Checkbox>
              </Form.Item>
            </Space>
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={12}>
          <Form.Item
            name="display_or_search"
            label={<Text strong>Display or Search</Text>}
            tooltip={{
              title: "TODO Info here",
              icon: <InfoCircleOutlined />,
            }}
          >
            <Space>
              <Form.Item>
                <Checkbox name="show_all_values">Show All Values</Checkbox>
              </Form.Item>
              <Form.Item>
                <Checkbox name="show_only_min_max_percentage_inputs">
                  Show only Min-Max % Inputs
                </Checkbox>
              </Form.Item>
            </Space>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="site_selection"
            label={<Text strong>Site Selection</Text>}
            tooltip={{
              title: "TODO Info here",
              icon: <InfoCircleOutlined />,
            }}
          >
            <Select
              className="w-2/3!"
              mode="multiple"
              allowClear
              placeholder="Select Sites"
              popupMatchSelectWidth={false}
              options={[
                {
                  value: "site_1",
                  label: "Site 1",
                },
                {
                  value: "site_2",
                  label: "Site 2",
                },
              ]}
            />
          </Form.Item>
        </Col>
      </Row>
      <Space className="text-center w-full" direction="vertical">
        <Divider>
          <Title level={5}>Column Placement</Title>
        </Divider>
        <Space>
          <Form.Item name="position" label="Select Position">
            <Select
              placeholder="-Select Insert Position-"
              popupMatchSelectWidth={false}
              options={[
                {
                  value: "end_of_table",
                  label: "End of Table",
                },
                {
                  value: "beginning_of_table",
                  label: "Beginning of Table",
                },
              ]}
            />
          </Form.Item>
          <Text className="mx-2">or</Text>
          <Form.Item name="insert_column_after" label="Insert Column After">
            <Select
              showSearch
              placeholder="-Select Column-"
              popupMatchSelectWidth={false}
              options={[
                {
                  value: "process_date",
                  label: "Process Date",
                },
                {
                  value: "handler_id",
                  label: "Handler ID",
                },
                {
                  value: "lot_id",
                  label: "Lot ID",
                },
              ]}
            />
          </Form.Item>
        </Space>
      </Space>
    </Form>
  );
};

export default AddBinColumnForm;
