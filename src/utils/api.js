import Helper from "./helper";

const url = process.env.NEXT_PUBLIC_APP_API_URL || "";

/**
 * Get data from cache if available and from api request if not
 *
 * @param {string} apiUrl
 * @param {object} requestParams
 * @param {function} success
 * @param {function} error
 * @param {object} cacheData
 */
const getData = (apiUrl, requestParams, success, error, cacheData = {}) => {
  const cacheKey = Helper.generateCacheKey(apiUrl, requestParams);
  if (cacheData[cacheKey] !== undefined) {
    const checkDataInterval = setInterval(() => {
      if (cacheData[cacheKey].data !== undefined) {
        clearInterval(checkDataInterval);
        success(cacheData[cacheKey].data);
      }
    }, 500);
  } else {
    cacheData[cacheKey] = {};
    fetch(apiUrl, requestParams)
      .then(Helper.responseCallback)
      .then((res) => {
        cacheData[cacheKey].data = Helper.cloneObject(res);
        success(res);
      })
      .catch(Helper.errorCallback(error));
  }
};

const Api = {
  /**
   * Initialize the abort controller
   *
   * @returns {AbortController}
   */
  initAbortCtl: () => {
    return new AbortController();
  },
  /**
   * Login user
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  login: (success, error, payload) => {
    fetch(`${url}/api/login`, {
      method: "POST",
      headers: {
        ...Helper.getHeaders(),
        ...(window.localStorage.getItem("yhDeviceId")
          ? {
              "yh-device-id": window.localStorage.getItem("yhDeviceId"),
            }
          : {}),
      },
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.loginErrorCallback(error));
  },
  /**
   * Submit OTP
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  submitOTP: (success, error, payload) => {
    fetch(`${url}/api/otp`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.loginErrorCallback(error));
  },
  /**
   * Resend OTP
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  resendOTP: (success, error, payload) => {
    fetch(`${url}/api/otp/resend`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Request for reset password
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  requestResetPassword: (success, error, payload) => {
    fetch(`${url}/api/user/forgot_password/${payload.username}`, {
      method: "GET",
      headers: Helper.getHeaders(),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Validate reset password token
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  validateResetPasswordToken: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/user/reset_password/validate/${payload.token}`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Reset password
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  resetPassword: (success, error, payload) => {
    fetch(`${url}/api/user/reset_password`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Change password
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  changePassword: (success, error, payload) => {
    fetch(`${url}/api/user/change_password/own`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Set user settings
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  setUserSettings: (success, error, payload) => {
    fetch(`${url}/api/v1/internal/user_settings/update`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get user settings key-value by type
   *
   * @param {function} success
   * @param {function} error
   * @param {string} payload
   * @returns {AbortController} abortCtl
   */
  getUserSettingsKeyValueByType: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/user_settings/get/key_value/${payload}`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get search grid data
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  search: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/home/<USER>
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Download search table data
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  downloadSearchTable: (success, error, payload) => {
    fetch(`${url}/api/v1/home/<USER>/table/download`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Download table data in CSV format
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  downloadTableAsCSV: (success, error, payload) => {
    fetch(`${url}${payload.csv_download_url}`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Download MPR Raw Data
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  downloadMPRRawData: (success, error, payload) => {
    fetch(
      `${url}/api/v1/test_level/mpr/table/download/per_pin/${payload.tnum}`,
      {
        method: "POST",
        headers: Helper.getHeaders(),
        body: JSON.stringify(payload),
      },
    )
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get short url value for url parameters
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  getShortUrl: (success, error, payload) => {
    fetch(`${url}/api/v1/internal/short_url`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get long url parameters for short url value
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getLongUrlParams: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/long_url/${payload}`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  getDataloggedBinData: (success, error, payload) => {
    fetch(`${url}/api/v1/binning/table/bin`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  getDataloggedSummaryData: (success, error, payload) => {
    fetch(`${url}/api/v1/binning/table/summary`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  getParetoFailRateChartData: (success, error, payload) => {
    fetch(`${url}/api/v1/binning/chart/pareto/fail_rate`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  getParetoFromDatalogChartData: (success, error, payload) => {
    fetch(`${url}/api/v1/binning/chart/pareto/from_datalog`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  getParetoPerSiteChartData: (success, error, payload) => {
    fetch(`${url}/api/v1/binning/chart/pareto/per_site`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get data levels
   *
   * @param {function} success
   * @param {function} error
   * @returns {AbortController} abortCtl
   */
  getDataLevels: (success, error) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/blueprint/data_levels`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get analysis template components
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getAnalysisTemplateComponents: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(
      `${url}/api/v1/internal/blueprint/analysis_component_props${
        payload !== null && payload !== undefined ? `/${payload}` : ""
      }`,
      {
        method: "GET",
        headers: Helper.getHeaders(),
        signal: abortCtl.signal,
      },
    )
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get preset analysis templates
   *
   * @param {function} success
   * @param {function} error
   * @returns {AbortController} abortCtl
   */
  getPresetAnalysisTemplates: (success, error) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/preset_analysis_template`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get user analysis templates
   *
   * @param {function} success
   * @param {function} error
   * @returns {AbortController} abortCtl
   */
  getUserAnalysisTemplates: (success, error) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/user_analysis_template`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get user analysis template
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getUserAnalysisTemplate: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(
      `${url}/api/v1/internal/user_analysis_template/${payload.templateId}`,
      {
        method: "GET",
        headers: Helper.getHeaders(),
        signal: abortCtl.signal,
      },
    )
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Save user analysis template
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  saveUserAnalysisTemplate: (success, error, payload) => {
    fetch(`${url}/api/v1/internal/user_analysis_template`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Update user analysis template
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  updateUserAnalysisTemplate: (success, error, payload) => {
    fetch(`${url}/api/v1/internal/user_analysis_template/${payload.id}`, {
      method: "PUT",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Delete user analysis template
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  deleteUserAnalysisTemplate: (success, error, payload) => {
    fetch(`${url}/api/v1/internal/user_analysis_template/${payload.id}`, {
      method: "DELETE",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get preset analysis template
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getPresetAnalysisTemplate: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(
      `${url}/api/v1/internal/preset_analysis_template/${payload.templateId}`,
      {
        method: "GET",
        headers: Helper.getHeaders(),
        signal: abortCtl.signal,
      },
    )
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Common api function to get data
   *
   * @param {string} urlEndpoint
   * @param {string} method
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} bodyParams
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getData: (
    urlEndpoint,
    method,
    success,
    error,
    payload,
    bodyParams = {},
    cacheData = {},
  ) => {
    const abortCtl = Api.initAbortCtl();
    let apiUrl = `${url}${urlEndpoint}`;
    let requestParams = {
      method: method,
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    };

    payload = Helper.setRequiredApiParameters(payload, bodyParams);

    if (method === "get") {
      if (Object.keys(payload).length > 0) {
        const queryString = Helper.createQueryString(payload);
        apiUrl += `?${queryString}`;
      }
    } else {
      requestParams.body = JSON.stringify(payload);
    }

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Common api function to fetch data using React Query
   *
   * @param {string} urlEndpoint
   * @param {string} method
   * @param {object} payload
   * @param {object} bodyParams
   * @param {AbortSignal} signal
   * @returns {Promise}
   */
  fetchData: async (urlEndpoint, method, payload, bodyParams = {}, signal) => {
    let apiUrl = `${url}${urlEndpoint}`;
    let requestParams = {
      method: method,
      headers: Helper.getHeaders(),
      signal: signal,
    };

    payload = Helper.setRequiredApiParameters(payload, bodyParams);

    if (method === "get") {
      if (Object.keys(payload).length > 0) {
        const queryString = Helper.createQueryString(payload);
        apiUrl += `?${queryString}`;
      }
    } else {
      requestParams.body = JSON.stringify(payload);
    }

    const response = await fetch(apiUrl, requestParams);
    return response.json();
  },
  /**
   * Save user analysis template as draft
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  draftUserAnalysisTemplate: (success, error, payload) => {
    fetch(`${url}/api/v1/internal/draft_user_analysis_template`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get draft user analysis template
   *
   * @param {function} success
   * @param {function} error
   * @returns {AbortController} abortCtl
   */
  getDraftUserAnalysisTemplate: (success, error) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/draft_user_analysis_template`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get component blueprint by name
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getComponentBlueprint: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/blueprint/component_props/${payload.name}`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Validate/Invalidate datalog(s)
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  validateDatalog: (success, error, payload) => {
    fetch(`${url}/api/v1/internal/datalog_level/validation`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get manufacturing process options
   *
   * @param {function} success
   * @param {function} error
   * @returns {AbortController} abortCtl
   */
  getManufacturingProcessOptions: (success, error) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/options/list/mfg_process`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get select options
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  getSelectOptions: (success, error, payload) => {
    const apiUrl = payload.url
      ? `${url}/${payload.url}`
      : `${url}/api/v1/internal/options/list/${payload.field}`;
    fetch(apiUrl, {
      method: payload?.method ?? "POST",
      headers: Helper.getHeaders(),
      body:
        payload?.method === "GET" || payload?.method === "get"
          ? null
          : JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Save filter settings
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  saveFilterSettings: (success, error, payload) => {
    fetch(`${url}/api/v1/home/<USER>/filter/save`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get saved filter options
   *
   * @param {function} success
   * @param {function} error
   * @returns {AbortController} abortCtl
   */
  getSavedFilterOptions: (success, error) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/home/<USER>/filter/list`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Delete saved filter
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  deleteFilterSettings: (success, error, payload) => {
    fetch(`${url}/api/v1/home/<USER>/filter/delete/${payload.id}`, {
      method: "GET",
      headers: Helper.getHeaders(),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get saved filter
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  getFilterSettings: (success, error, payload) => {
    fetch(`${url}/api/v1/home/<USER>/filter/get/${payload.id}`, {
      method: "GET",
      headers: Helper.getHeaders(),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Save table settings
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  saveTableSettings: (success, error, payload) => {
    fetch(`${url}/api/v1/home/<USER>/table/props/save`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get saved table settings options
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getSavedTableOptions: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/home/<USER>/table/props/list/${payload.table_id}`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Delete saved table settings
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  deleteTableSettings: (success, error, payload) => {
    fetch(`${url}/api/v1/home/<USER>/table/props/delete/${payload.id}`, {
      method: "GET",
      headers: Helper.getHeaders(),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get saved table settings
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  getTableSettings: (success, error, payload) => {
    fetch(`${url}/api/v1/home/<USER>/table/props/state/get/${payload.id}`, {
      method: "GET",
      headers: Helper.getHeaders(),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get run type options
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getRunTypeOptions: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/lot_level/other_dlogs_perf_options`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get lot parametric test list options
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getLotParametricTestListOptions: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/lot_level/parametric/test_list`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get lot top failing test data
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getLotTopFailingTestData: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/lot_level/parametric/top_failed_test`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Get analysis type
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getAnalysisType: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/lot_level/parametric/analysis_type`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Get page meta
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getPageMeta: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/datalog_level/page_meta`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Update recipe auto trigger value
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  updateRecipeAutoTrigger: (success, error, payload) => {
    fetch(`${url}/api/v1/recipe/auto_trigger/update`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Duplicate recipe
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  duplicateRecipe: (success, error, payload) => {
    fetch(`${url}/api/v1/recipe/copy`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Delete recipe
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  deleteRecipe: (success, error, payload) => {
    fetch(`${url}/api/v1/recipe/delete`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Check if file already exist
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  checkDuplicateFile: (success, error, payload) => {
    fetch(`${url}/api/v1/file/check_duplicate`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get supported archive extension list
   *
   * @param {function} success
   * @param {function} error
   */
  getSupportedArchiveExtensions: (success, error) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/file/archive_list`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get supported file type list
   *
   * @param {function} success
   * @param {function} error
   */
  getSupportedFileTypes: (success, error) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/file/types`, {
      method: "POST",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Upload datalog/file
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {string} contentType
   * @param {string} uploadType
   */
  uploadFile: (success, error, payload, contentType, uploadType) => {
    fetch(`${url}/api/v1/${uploadType}/upload`, {
      method: "POST",
      headers: Helper.getHeaders(contentType),
      body: payload,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },

  /**
   * Get the list of metadata recipes
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getMetadataRecipeList: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/metadata_header/recipe/list`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Get the list of metadata recipe versions
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getMetadataRecipeVersion: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/metadata_header/recipe/version`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Get the info of metadata recipe
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getMetadataRecipeInfo: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/metadata_header/recipe/info`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Get the data of the recipe
   *
   * @param {string} recipeType
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getRecipeData: (recipeType, success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/${recipeType}/recipe/data`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Apply a metadata recipe to datalogs
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  applyMetadataRecipe: (success, error, payload) => {
    fetch(`${url}/api/v1/metadata_header/apply`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },

  /**
   * Execute metadata parsing step
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  executeMetadataParsingSteps: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();
    const apiUrl = `${url}/api/v1/metadata_header/execute_step`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Simulate metadata parsing steps
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  simulateMetadataParsingSteps: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();
    const apiUrl = `${url}/api/v1/metadata_header/simulate_steps`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Save a metadata recipe
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  saveMetadataRecipe: (success, error, payload) => {
    fetch(`${url}/api/v1/metadata_header/recipe/save`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },

  /**
   * Get the traceability tests table for recipe setup
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getTraceabilityTestTable: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/traceability/table`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Get the traceability configuration data
   *
   * @param {function} success
   * @param {function} error
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getTraceabilityConfig: (success, error, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/traceability/config`;
    const requestParams = {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Get total tests count
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getTotalTestsCount: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/traceability/tests_count`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Get recipe configurations
   *
   * @param {string} recipeType
   * @param {function} success
   * @param {function} error
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getRecipeConfig: (recipeType, success, error, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/${recipeType}/config`;
    const requestParams = {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Get predefined list of options
   *
   * @param {string} listType
   * @param {function} success
   * @param {function} error
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getPredefinedList: (listType, success, error, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/metadata_header/predefined_list?list_type=${listType}`;
    const requestParams = {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Get the traceability equation result
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getTraceabilityEquationResult: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/traceability/equation`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Save a traceability recipe
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  saveTraceabilityRecipe: (success, error, payload) => {
    fetch(`${url}/api/v1/traceability/recipe/save`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },

  /**
   * Get the list of traceability recipes
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getTraceabilityRecipes: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/traceability/recipe/list`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Get the list of traceability recipe versions
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getTraceabilityRecipeVersion: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/traceability/recipe/version`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Get the info of a traceability recipe
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getTraceabilityRecipeInfo: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/traceability/recipe/info`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Get the data of the recipe
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getTraceabilityRecipeData: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/traceability/recipe/data`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Apply a traceability recipe to datalogs
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  applyTraceabilityRecipe: (success, error, payload) => {
    fetch(`${url}/api/v1/traceability/apply`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },

  /**
   * Check if recipe name already exists
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  checkRecipeName: (success, error, payload = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/${payload.recipe_type}/recipe/check/${payload.recipe_name}/0`;
    const requestParams = {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error);

    return abortCtl;
  },

  /**
   * Save recipe
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  saveRecipe: (success, error, payload) => {
    fetch(`${url}/api/v1/${payload.recipe_type}/recipe/save`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },

  /**
   * Get the recipe info
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getRecipeInfo: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/${payload.recipe_type}/recipe/info`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },

  /**
   * Add user via admin
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  addUserViaAdmin: (success, error, payload) => {
    fetch(`${url}/api/user/add/via_admin`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get consolidation grouping field list
   *
   * @param {function} success
   * @param {function} error
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getConsolidationFieldOptions: (success, error, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/consolidation/fields`;
    const requestParams = {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Check if recipe name already exists
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  checkConsolidationRecipeName: (success, error, payload = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/consolidation/recipe/check/${payload.recipe_name}`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error);

    return abortCtl;
  },
  /**
   * Save a consolidation recipe
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  saveConsolidationRecipe: (success, error, payload) => {
    fetch(`${url}/api/v1/consolidation/recipe/save`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get the list of consolidation recipes
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getConsolidationRecipes: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/consolidation/recipe/list`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Validate selected datalogs for consolidation
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  validateConsolidationDatalogs: (success, error, payload) => {
    fetch(`${url}/api/v1/consolidation/apply/validation`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Consolidate selected datalogs
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  applyConsolidationRecipe: (success, error, payload) => {
    fetch(`${url}/api/v1/consolidation/apply/trigger`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get the info of a consolidation recipe
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getConsolidationRecipeInfo: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/consolidation/recipe/info`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Get the list of consolidation recipe versions
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getConsolidationRecipeVersion: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/consolidation/recipe/version`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Get the data of the consolidation recipe
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getConsolidationRecipeData: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/consolidation/recipe/data`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Get OEE options
   *
   * @param {function} success
   * @param {function} error
   * @returns {AbortController} abortCtl
   */
  getOeeOptions: (success, error) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/oee/options`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Method that checks the API for the system maintenance status
   *
   * @param {function} success
   * @param {function} error
   */
  checkMaintenance: (success, error) => {
    fetch(`${url}/api/maintenance/check`, {
      method: "GET",
      headers: Helper.getHeaders(),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Download datalog(s)
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  downloadDatalog: (success, error, payload) => {
    fetch(`${url}/api/v1/datalog_level/download`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Reprocess datalog(s)
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  reprocessDatalog: (success, error, payload) => {
    fetch(`${url}/api/v1/file/reprocess`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Delete datalog(s)
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  deleteDatalog: (success, error, payload) => {
    fetch(`${url}/api/v1/file/delete`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Update datalog visibility
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  updateDatalogVisibility: (success, error, payload) => {
    fetch(
      `${url}/api/v1/engineering_upload/visibility/${payload.dsk}/${payload.value}`,
      {
        method: "POST",
        headers: Helper.getHeaders(),
        body: JSON.stringify(payload),
      },
    )
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get user pdb storage info
   *
   * @param {function} success
   * @param {function} error
   */
  getPDBStorageInfo: (success, error) => {
    fetch(`${url}/api/v1/file/storage/personal`, {
      method: "GET",
      headers: Helper.getHeaders(),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Update table data
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  setTableData: (urlEndpoint, success, error, payload) => {
    fetch(`${url}${urlEndpoint}`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Save a chart settings
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  saveChartSettings: (success, error, payload) => {
    fetch(`${url}/api/v1/internal/user_settings/update`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Delete a saved chart settings
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  deleteChartSettings: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/user_settings/${payload.id}`, {
      method: "DELETE",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get the list of saved chart settings
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getSavedChartSettingsList: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/user_settings/get/key/${payload.type}`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get a saved chart settings
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getSavedChartSettings: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/user_settings/${payload.id}`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get test data
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getTestData: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/test_level/results_per_dlog`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Get limit values based on sigma
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getLimitsBasedOnSigma: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(
      `${url}/api/v1/test_level/sigma_limits/${payload.dsk}/${payload.tnum}/${payload.sigma ?? ""}`,
      {
        method: "GET",
        headers: Helper.getHeaders(),
        signal: abortCtl.signal,
      },
    )
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get the application version data
   *
   * @param {function} success
   * @param {function} error
   * @returns {AbortController} abortCtl
   */
  getAppVersion: (success, error) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/system/app/version`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get dsks that belongs to given lot(s)
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getLotDsks: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/lot_level/dsk_list`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Get datalog info
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {object} cacheData
   * @returns {AbortController} abortCtl
   */
  getDatalogInfo: (success, error, payload, cacheData = {}) => {
    const abortCtl = Api.initAbortCtl();

    const apiUrl = `${url}/api/v1/datalog_level/info`;
    const requestParams = {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    };

    getData(apiUrl, requestParams, success, error, cacheData);

    return abortCtl;
  },
  /**
   * Get the languages option list
   *
   * @param {function} success
   * @param {function} error
   * @returns {AbortController} abortCtl
   */
  getLanguages: (success, error) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/options/list/settings/languages`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get the timezones option list
   *
   * @param {function} success
   * @param {function} error
   * @returns {AbortController} abortCtl
   */
  getTimezones: (success, error) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/internal/options/list/settings/timezones`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Subscribe user to push notification
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  subscribeUserPush: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/system/push_notification/subscribe`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Unsubscribe user from push notification
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  unsubscribeUserPush: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/system/push_notification/unsubscribe`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get datalogs by group
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getGroupedDatalogs: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/test_level/group/breakdown`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Upload pin mapping file
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @param {string} contentType
   */
  uploadPinMappingFile: (success, error, payload, contentType) => {
    fetch(`${url}/api/v1/pin_level/pin_map/upload/${payload.save}`, {
      method: "POST",
      headers: Helper.getHeaders(contentType),
      body: payload.form_data,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
  /**
   * Get per pin map data
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getPerPinMapData: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/pin_level/pin_map/data`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Delete per pin map data
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  deletePerPinMapData: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/pin_level/pin_map/delete`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get breakdown of group
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getGroupBreakdown: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/test_level/group/breakdown`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get NPI data
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getNPIData: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/npi/report/get/${payload.report_key}`, {
      method: "GET",
      headers: Helper.getHeaders(),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Get NPI analysis set tests
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   * @returns {AbortController} abortCtl
   */
  getAnalysisSetTests: (success, error, payload) => {
    const abortCtl = Api.initAbortCtl();

    fetch(`${url}/api/v1/npi/analysis_set/tests/${payload.get_data ?? 0}`, {
      method: "POST",
      headers: Helper.getHeaders(),
      body: JSON.stringify(payload),
      signal: abortCtl.signal,
    })
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));

    return abortCtl;
  },
  /**
   * Update column value of NPI Test Summary table
   *
   * @param {function} success
   * @param {function} error
   * @param {object} payload
   */
  updateNpiTestSummaryTableColumnValue: (success, error, payload) => {
    fetch(
      `${url}/api/v1/npi/report/update/test_summary_table/${payload.grid_table_name}`,
      {
        method: "POST",
        headers: Helper.getHeaders(),
        body: JSON.stringify(payload),
      },
    )
      .then(Helper.responseCallback)
      .then(success)
      .catch(Helper.errorCallback(error));
  },
};

export default Api;
