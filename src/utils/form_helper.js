import { Checkbox, Form, Input, InputNumber, Switch } from "antd";

const { TextArea } = Input;

/**
 * Get input field
 *
 * @param {object} field
 * @param {string} fieldKey
 * @param {*} fieldValue
 * @returns {JSX.Element} inputField
 */
const getInputField = (field, fieldKey, fieldValue) => {
  const disabled = field.editable === false;
  let inputField;
  switch (field.type) {
    case "integer":
      inputField = (
        <InputNumber
          disabled={disabled}
          min={field.min !== undefined ? field.min : Number.MIN_SAFE_INTEGER}
        />
      );
      break;
    case "boolean":
      inputField = (
        <Switch
          defaultChecked={fieldValue ? fieldValue : field.default}
          disabled={disabled}
        />
      );
      break;
    case "checkbox":
      inputField = (
        <Checkbox checked={fieldValue ? fieldValue : false} disabled={disabled}>
          {field.label}
        </Checkbox>
      );
      break;
    case "text":
      inputField = <TextArea rows={2} />;
      break;
    default:
      inputField = <Input disabled={disabled} />;
  }

  return inputField;
};

/**
 * Form helper object
 */
const FormHelper = {
  /**
   * Get field form item
   *
   * @param {object} field
   * @param {string} fieldKey
   * @param {*} fieldValue
   * @param {string} fieldGroup
   * @param {int} labelColSpan
   * @param {int} wrapperColSpan
   * @param {JSX.Element} inputField
   * @returns {JSX.Element}
   */
  getFormItem: (
    field,
    fieldKey,
    fieldValue,
    fieldGroup = "",
    labelColSpan = 6,
    wrapperColSpan = 18,
    inputField = null,
  ) => {
    return (
      (!field || !field.exclude) && (
        <Form.Item
          key={`${fieldGroup}_${fieldKey}`}
          name={fieldGroup !== "" ? [fieldGroup, fieldKey] : fieldKey}
          label={
            field.type === "checkbox"
              ? null
              : field.label
                ? field.label
                : fieldKey
          }
          initialValue={fieldValue ? fieldValue : field.default}
          valuePropName={
            field.type === "boolean" || field.type === "checkbox"
              ? "checked"
              : "value"
          }
          rules={[
            {
              required: field ? field.required : false,
            },
          ]}
          labelCol={{
            span: labelColSpan,
          }}
          wrapperCol={{
            span: wrapperColSpan,
          }}
        >
          {inputField !== null
            ? inputField
            : getInputField(field, fieldKey, fieldValue)}
        </Form.Item>
      )
    );
  },
};

export default FormHelper;
