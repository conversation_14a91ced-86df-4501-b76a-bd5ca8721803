"use client";

import { useState, useEffect } from "react";
import { App, notification } from "antd";
import Api from "./api";

/**
 * Convert base64 encoded string to an array of 8-bit unsigned integer
 *
 * @param {string} base64String
 * @returns {Uint8Array|null}
 */
function urlBase64ToUint8Array(base64String) {
  try {
    const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, "+")
      .replace(/_/g, "/");
    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);
    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  } catch (e) {
    console.error("Invalid Base64 string:", e);
    return null;
  }
}

/**
 * Push Notification manager function
 *
 * NOTE: The display part of this function is only temporary.
 * It will be adjusted once the Notification settings is ready
 * to be implemented in the UI.
 *
 * @returns {JSX.Element}
 */
export function PushNotificationManager() {
  const [isSupported, setIsSupported] = useState(false);
  const [subscription, setSubscription] = useState(null);

  useEffect(() => {
    if ("serviceWorker" in navigator && "PushManager" in window) {
      setIsSupported(true);
      registerServiceWorker();
    }
  }, []);

  /**
   * Register the service worker
   */
  async function registerServiceWorker() {
    const registration = await navigator.serviceWorker.register("/sw.js", {
      scope: "/",
      updateViaCache: "none",
    });
    const sub = await registration.pushManager.getSubscription();
    setSubscription(sub);
  }

  /**
   * Subscribe to a Push Notification
   */
  async function subscribeToPush() {
    try {
      const registration = await navigator.serviceWorker.ready;
      const sub = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(
          process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
        ),
      });
      setSubscription(sub);
      // console.log("subscribeToPush", sub);
      Api.subscribeUserPush(
        (res) => {
          if (res.success) {
            notification.success({
              description: res.message,
            });
          } else {
            notification.warning({
              description: res.message,
            });
          }
        },
        (err) => {
          notification.error({
            description: err,
          });
        },
        sub,
      );
    } catch ({ name, message }) {
      notification.error({
        description: `${name}: ${message}`,
      });
    }
  }

  /**
   * Unsubscribe from a Push Notification
   */
  async function unsubscribeFromPush() {
    try {
      Api.unsubscribeUserPush(
        async (res) => {
          if (res.success) {
            await subscription?.unsubscribe();
            setSubscription(null);
            notification.success({
              description: res.message,
            });
          } else {
            notification.warning({
              description: res.message,
            });
          }
        },
        (err) => {
          notification.warning({
            description: err,
          });
        },
        subscription,
      );
    } catch ({ name, message }) {
      notification.warning({
        description: `${name}: ${message}`,
      });
    }
  }

  if (!isSupported) {
    return <p>Push notifications are not supported in this browser.</p>;
  }

  return (
    <App>
      <div>
        <h3>Push Notifications</h3>
        {subscription ? (
          <>
            <p>You are subscribed to push notifications.</p>
            <button onClick={unsubscribeFromPush}>Unsubscribe</button>
          </>
        ) : (
          <>
            <p>You are not subscribed to push notifications.</p>
            <button onClick={subscribeToPush}>Subscribe</button>
          </>
        )}
      </div>
    </App>
  );
}
