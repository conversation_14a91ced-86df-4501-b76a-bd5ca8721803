"use client";

import { ColorPicker, Form } from "antd";
import { blue, green, presetPalettes, red } from "@ant-design/colors";
import { useState } from "react";

/**
 * Generate preset colors
 *
 * @param {object} presets
 * @returns {array}
 */
const genPresetColors = (presets = presetPalettes) =>
  Object.entries(presets).map(([label, colors]) => ({
    label,
    colors,
  }));

const presets = genPresetColors({
  blue,
  red,
  green,
});

/**
 * Color picker component for highlight option
 *
 * @param {string} inputName
 * @returns {JSX.Element}
 */
const HighlightOptionColorPicker = ({ inputName, disabled }) => {
  const [colorValue, setColorValue] = useState("#ffffff");

  return (
    <Form.Item
      name={inputName}
      getValueFromEvent={(color) => color.toHexString()}
    >
      <ColorPicker
        className="ml-1"
        onChangeComplete={(color) => {
          setColorValue(color.toHexString());
        }}
        value={colorValue}
        presets={presets}
        trigger="hover"
        disabled={disabled}
      ></ColorPicker>
    </Form.Item>
  );
};

export default HighlightOptionColorPicker;
