import {
  App,
  <PERSON>ffix,
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
  Space,
  Typography,
} from "antd";
import React, { useState, useEffect } from "react";
import { useBoundStore } from "../../store/store";
import Helper from "../helper";
import Api from "../../../src/utils/api";
import { useEffectApiFetch } from "../../../src/hooks";
import { ComponentNameMapper } from "../../../src/utils/grid/component_name_mapper";

const { Text } = Typography;
const { RangePicker } = DatePicker;

const periodOptions = [
  {
    label: "Yesterday",
    value: "yesterday",
  },
  {
    label: "Today",
    value: "today",
  },
  {
    label: "This Week",
    value: "current_week",
  },
  {
    label: "This Month",
    value: "current_month",
  },
  {
    label: "This Year",
    value: "current_year",
  },
];

const categoryValueMapper = {
  tester_node: "tester",
  part_typ: "eng_product",
  program: "program",
  dib_id: "dib",
  handler_id: "handler",
  card_id: "context_id/card_id",
  load_id: "context_id/load_id",
  cabl_id: "context_id/cabl_id",
  cont_id: "context_id/cont_id",
  lasr_id: "context_id/lasr_id",
  extr_id: "context_id/extr_id",
  eng_id: "context_id/eng_id",
  proc_id: "context_id/proc_id",
  flow_id: "context_id/flow_id",
  floor_id: "context_id/floor_id",
  facility_id: "context_id/facility_id",
  setup_id: "context_id/setup_id",
};

/**
 * Create hour range options from `00:00-01:00` to `23:00-00:00`
 *
 * @returns {array} options
 */
const generateHourRangeOptions = () => {
  let options = [];
  let interval;
  for (let i = 0; i < 24; i++) {
    interval = i === 23 ? -23 : 1;
    options.push({
      value: `${i}-${i + interval}`,
      label: `${("0" + i).slice(-2)}:00-${("0" + (i + interval)).slice(-2)}:00`,
    });
  }

  return options;
};

/**
 * OEE filter
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function OEEFilter({ pageKey }) {
  const { message } = App.useApp();
  const [isFormChanged, setIsFormChanged] = useState(false);
  const mainContentRef = useBoundStore((state) => state.mainContentRef);
  const OEEFilters = useBoundStore((state) => state.OEEFilters);
  const setOEEFilters = useBoundStore((state) => state.setOEEFilters);
  const reloadGridFilters = useBoundStore((state) => state.reloadGridFilters);
  const setReloadGrid = useBoundStore((state) => state.setReloadGrid);
  const setReloadGrids = useBoundStore((state) => state.setReloadGrids);
  const setReloadGridFilters = useBoundStore(
    (state) => state.setReloadGridFilters,
  );
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const setReloadChart = useBoundStore((state) => state.setReloadChart);
  const setReloadChartFilters = useBoundStore(
    (state) => state.setReloadChartFilters,
  );
  const chartKeys = useBoundStore((state) => state.chartKeys);
  const [oeeFilterForm] = Form.useForm();
  const [oeeOptions, setOeeOptions] = useState({});
  const [categoryValueOptions, setCategoryValueOptions] = useState([]);
  const [manufacturingProcessOptions, setManufacturingProcessOptions] =
    useState([]);
  const [subconOptions, setSubconOptions] = useState([]);
  const OEETrendGridKey = ComponentNameMapper.oee_historical_trend_table;
  const OEEBreakdownGridKey = ComponentNameMapper.oee_breakdown_table;
  const [chartKey, setChartKey] = useState("");

  useEffect(() => {
    if (reloadGridFilters[OEETrendGridKey] === undefined) {
      reloadGridFilters[OEETrendGridKey] = {};
    }
    if (reloadGridFilters[OEEBreakdownGridKey] === undefined) {
      reloadGridFilters[OEEBreakdownGridKey] = {};
    }
  }, []);

  useEffect(() => {
    if (
      chartKeys[pageKey] &&
      Object.keys(chartKeys[pageKey]).length > 0 &&
      chartKeys[pageKey][ComponentNameMapper.oee_historical_trend]?.length > 0
    ) {
      setChartKey(
        chartKeys[pageKey][ComponentNameMapper.oee_historical_trend][0],
      );

      if (chartKey && reloadChartFilters[chartKey] === undefined) {
        reloadChartFilters[chartKey] = {};
      }
    }
  }, [chartKeys[pageKey]]);

  useEffectApiFetch(
    () => {
      return getOeeOptions();
    },
    () => {
      setOeeOptions();
    },
  );

  useEffectApiFetch(
    () => {
      return getCategoryValueOptions(OEEFilters.category);
    },
    () => {
      setCategoryValueOptions();
    },
  );

  useEffectApiFetch(
    () => {
      return Helper.getManufacturingProcessOptions(
        setManufacturingProcessOptions,
      );
    },
    () => {
      setManufacturingProcessOptions([]);
    },
  );

  useEffectApiFetch(
    () => {
      if (manufacturingProcessOptions.length > 0) return getSubconOptions();
    },
    () => {
      setSubconOptions();
    },
    [manufacturingProcessOptions],
  );

  /**
   * Get and set subcon options
   *
   * @returns {AbortController} abortCtl
   */
  const getSubconOptions = () => {
    const abortCtl = Api.getSelectOptions(
      (res) => {
        if (res.success) {
          setSubconOptions(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        field: "subcon",
        source: "oee",
        mfg_process: manufacturingProcessOptions
          .map((mfgProcess) => {
            return mfgProcess.value;
          })
          .join(),
      },
    );

    return abortCtl;
  };

  /**
   * Get and set datalog processing updates grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getOeeOptions = () => {
    const abortCtl = Api.getOeeOptions(
      (res) => {
        if (res.success) {
          setOeeOptions(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
    );

    return abortCtl;
  };

  /**
   * Set OEE period
   *
   * @param {Event} e
   */
  const setPeriod = (e) => {
    setPeriodLabel(e.target.value);
    oeeFilterForm.setFieldValue("custom_period");
  };

  /**
   * Set period label
   *
   * @param {string} period
   */
  const setPeriodLabel = (period) => {
    const label = periodOptions.filter((option) => {
      return option.value === period;
    })[0].label;

    oeeFilterForm.setFieldValue("period_label", label);
  };

  /**
   * Set custom period value and label
   *
   * @param {array} value
   */
  const setCustomPeriod = (value) => {
    oeeFilterForm.setFieldValue("period");
    const label = Helper.formatPeriodLabel(value);
    oeeFilterForm.setFieldValue("period_label", label);
  };

  /**
   * Create OEE Report based on applied filters
   *
   * @param {object} values
   */
  const generateOEEReport = (values) => {
    const filters = { ...OEEFilters, ...values };
    setOEEFilters(filters);
    setIsFormChanged(false);
    Helper.reloadOEEGridAndChartComponents(
      reloadGridFilters,
      setReloadGrid,
      setReloadGrids,
      setReloadGridFilters,
      reloadChartFilters,
      setReloadChart,
      setReloadChartFilters,
      OEETrendGridKey,
      OEEBreakdownGridKey,
      chartKey,
      filters,
    );
  };

  /**
   * Callback function when form values change
   *
   * @param {object} changedValues
   */
  const handleValuesChange = (changedValues) => {
    // Compare the current values with the initial values
    const isChanged = Object.keys(changedValues).some(
      (field) => changedValues[field] !== oeeFilterForm.getFieldValue("field"),
    );

    setIsFormChanged(isChanged);
  };

  /**
   * Handler when select category is clicked
   *
   * @param {string} value
   * @returns {AbortController} abortCtl
   */
  const getCategoryValueOptions = (value) => {
    setCategoryValueOptions([]);
    oeeFilterForm.setFieldsValue({
      category_value: undefined,
    });
    const abortCtl = Api.getSelectOptions(
      (res) => {
        if (res.success) {
          setCategoryValueOptions(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        field: categoryValueMapper[value],
        source: "oee",
      },
    );

    return abortCtl;
  };

  return (
    <Affix target={() => mainContentRef} className="w-full">
      <Row className="py-2 px-2 bg-white">
        <Col span={24}>
          <Form
            form={oeeFilterForm}
            layout="inline"
            initialValues={OEEFilters}
            onFinish={generateOEEReport}
            onValuesChange={handleValuesChange}
          >
            <Form.Item name="category" label="Context">
              <Select
                placeholder="-Select-"
                defaultValue={{
                  label: "Tester",
                  value: "tester_node",
                }}
                className="w-36!"
                popupMatchSelectWidth={false}
                onSelect={getCategoryValueOptions}
                options={oeeOptions && oeeOptions.category}
              />
            </Form.Item>
            <Form.Item name="category_value">
              <Select
                mode="multiple"
                placeholder="-Select-"
                className="w-36!"
                popupMatchSelectWidth={false}
                maxTagCount="responsive"
                allowClear
                options={categoryValueOptions}
              />
            </Form.Item>
            <Form.Item name="subcon">
              <Select
                mode="multiple"
                placeholder="-Select-"
                className="w-40!"
                popupMatchSelectWidth={false}
                maxTagCount="responsive"
                allowClear
                options={subconOptions}
              />
            </Form.Item>
            <Space>
              <Form.Item name="period">
                <Radio.Group
                  optionType="button"
                  buttonStyle="solid"
                  options={periodOptions}
                  onChange={setPeriod}
                />
              </Form.Item>
              <Text className="mr-2">or</Text>
              <Form.Item name="custom_period">
                <RangePicker
                  disabledDate={Helper.disabledFutureDate}
                  onChange={setCustomPeriod}
                />
              </Form.Item>
              <Form.Item name="period_label" noStyle>
                <Input type="hidden" />
              </Form.Item>
            </Space>
            <Form.Item name="limit" label="Limit">
              <InputNumber className="w-12" />
            </Form.Item>
            <Form.Item name="exclude_hours">
              <Select
                mode="multiple"
                placeholder="Exclude Hours"
                className="w-44!"
                popupMatchSelectWidth={false}
                maxTagCount={1}
                allowClear
                options={generateHourRangeOptions()}
              />
            </Form.Item>
            <Form.Item>
              <Button
                type="primary"
                onClick={() => oeeFilterForm.submit()}
                disabled={!isFormChanged}
              >
                Generate Report
              </Button>
            </Form.Item>
            {/* <Form.Item>
              <Button>Save Setup</Button>
            </Form.Item> */}
          </Form>
        </Col>
      </Row>
    </Affix>
  );
}
