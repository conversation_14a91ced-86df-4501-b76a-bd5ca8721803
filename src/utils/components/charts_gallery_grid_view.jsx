import { Col, Row, Typography, message } from "antd";
import React, { useEffect, useState } from "react";
import Helper from "../helper";
import { GalleryChart } from "./gallery_chart";

/**
 * Charts gallery in grid view
 *
 * @param {object} chartsData
 * @param {object} chartCustomData
 * @param {int} chartsPerRow
 * @param {object} galleryCharts
 * @param {function} setGalleryCharts
 * @param {string} galleryView
 * @returns {JSX.Element}
 */
export default function ChartsGalleryGridView({
  chartsData,
  chartCustomData,
  chartsPerRow,
  galleryCharts,
  setGalleryCharts,
  galleryView,
}) {
  const [galleryChartIndex, setGalleryChartIndex] = useState(0);
  const [gridCharts, setGridCharts] = useState({});
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    const chartKeys = Object.keys(chartsData);
    const chartKey = chartKeys[galleryChartIndex];
    if (chartKey) {
      messageApi.open({
        key: "generating_charts_msg",
        type: "loading",
        content: `Generating ${galleryChartIndex + 1} of ${chartKeys.length} charts...`,
        duration: 0,
      });

      const gridChartsCopy = Helper.cloneObject(gridCharts);
      gridChartsCopy[chartKey] = chartsData[chartKey];
      gridChartsCopy[chartKey].galleryChartIndex = galleryChartIndex;
      gridChartsCopy[chartKey].setGalleryChartIndex = setGalleryChartIndex;

      setGridCharts(gridChartsCopy);
    }
    if (galleryChartIndex === chartKeys.length) {
      messageApi.destroy("generating_charts_msg");
    }
  }, [galleryChartIndex]);

  return (
    <>
      {contextHolder}
      <Row gutter={[16, 16]}>
        {Object.keys(gridCharts).map(function (chartKey) {
          return (
            <Col
              key={`gallery_chart_${chartKey}_wrapper`}
              style={{
                width: `${100 / chartsPerRow}%`,
              }}
            >
              <Typography.Title level={5}>
                {gridCharts[chartKey].label}
              </Typography.Title>
              <div>
                {React.createElement(GalleryChart, {
                  ...gridCharts[chartKey],
                  chartCustomData: chartCustomData,
                  galleryCharts: galleryCharts,
                  setGalleryCharts: setGalleryCharts,
                  galleryView: galleryView,
                })}
              </div>
            </Col>
          );
        })}
      </Row>
    </>
  );
}
