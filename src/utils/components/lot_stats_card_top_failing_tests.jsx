import { LoadingOutlined } from "@ant-design/icons";
import {
  Badge,
  Col,
  Empty,
  notification,
  Row,
  Space,
  Table,
  Typography,
} from "antd";
import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useBoundStore } from "../../store/store";
import Api from "../api";
import Helper from "../helper";
import { QueryKeys } from "../query_keys";

const { Text } = Typography;
const colors = ["#FF4D4F", "#FA8C16", "#FADB14"];
const columns = [
  {
    title: "",
    dataIndex: "key",
    key: "key",
    render: (_, record, index) =>
      record.test_name.toString().toLowerCase() !== "others" && (
        <Badge count={index + 1} color={colors[index]} />
      ),
  },
  {
    title: "Test Name",
    dataIndex: "test_name",
    key: "test_name",
    render: (tname) => Helper.truncateString(tname, 30, "middle"),
  },
  {
    title: "Test #",
    dataIndex: "test_number",
    key: "test_number",
    align: "right",
    render: (tnum) => tnum && `#${tnum}`,
  },
  {
    title: "Execution",
    dataIndex: "execs",
    key: "execs",
    align: "right",
    render: (value) => Helper.numberFormat(value, 0),
  },
  {
    title: "Failures",
    dataIndex: "fails",
    key: "fails",
    align: "right",
    render: (value) => Helper.numberFormat(value, 0),
  },
];

/**
 * Card component that displays top failing tests
 *
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function LotStatsCardTopFailingTests({
  component,
  filters,
  pageKey,
}) {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState({
    indicator: <LoadingOutlined spin />,
  });
  const reloadQuantityData = useBoundStore((state) => state.reloadQuantityData);
  const params = component.props.params;

  /**
   * Get statistic data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise}
   */
  const getData = async ({ signal }) => {
    setLoading({
      indicator: <LoadingOutlined spin />,
    });
    if (filters[pageKey].exclude_dsk_data !== undefined) {
      filters[pageKey].exclude_dsk = filters[pageKey].exclude_dsk_data;
      delete filters[pageKey].exclude_dsk_data;
    }
    if (filters[pageKey].exclude_dsk === "") {
      delete filters[pageKey].exclude_dsk;
    }
    const requestParams = Helper.filterObjectByKeys(
      filters[pageKey],
      Object.keys(params.body_params),
    );
    const url = Helper.parseUrlEndpoint(params.url_endpoint, filters[pageKey]);

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Query to fetch statistics data
   */
  const dataQuery = useQuery({
    queryKey: QueryKeys.lot_stats(pageKey),
    queryFn: getData,
    enabled: true,
  });

  useEffect(() => {
    setData();
    dataQuery.refetch();
  }, [reloadQuantityData]);

  useEffect(() => {
    if (dataQuery.isSuccess) {
      const response = dataQuery.data;
      if (response.success) {
        setData(response.data.top_failing_tests);
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      setLoading(false);
    }
  }, [dataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (dataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: dataQuery.error,
      });
    }
  }, [dataQuery.isError]);

  return (
    <Row>
      <Col span={24}>
        <Row>
          <Col span={24}>
            <Space direction="vertical">
              <div className="mb-4">
                <Text type="secondary" className="text-sm">
                  {component.display_name}
                </Text>
              </div>

              {Array.isArray(data) && data.length === 0 ? (
                <Empty description="No failed test"></Empty>
              ) : (
                <Table
                  columns={columns}
                  dataSource={data}
                  pagination={false}
                  loading={loading}
                />
              )}
            </Space>
          </Col>
        </Row>
      </Col>
    </Row>
  );
}
