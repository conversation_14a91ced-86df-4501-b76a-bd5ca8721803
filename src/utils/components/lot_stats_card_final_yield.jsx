import {
  LoadingOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
} from "@ant-design/icons";
import {
  Col,
  Flex,
  notification,
  Row,
  Space,
  Spin,
  theme,
  Typography,
} from "antd";
import { useCallback, useEffect, useRef, useState } from "react";
import dynamic from "next/dynamic";
import { useQuery } from "@tanstack/react-query";
import { useBoundStore } from "../../store/store";
import Api from "../api";
import Helper from "../helper";
const Pie = dynamic(() => import("../charts/pie"), {
  ssr: false,
});
import { QueryKeys } from "../query_keys";

const { Text, Title } = Typography;
const { useToken } = theme;
const upColor = "#52C41A";
const downColor = "#FF4D4F";

/**
 * Card component that displays final yield percentage data
 *
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function LotStatsCardFinalYield({
  component,
  filters,
  pageKey,
}) {
  const [data, setData] = useState({});
  const [shouldLoadChart, setShouldLoadChart] = useState(false);
  const reloadQuantityData = useBoundStore((state) => state.reloadQuantityData);
  const chartRef = useRef();
  const { token } = useToken();
  const chartKey = `${pageKey}_${component.component}_${component.id}`;
  const params = component.props.params;

  /**
   * Get statistic data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise}
   */
  const getData = async ({ signal }) => {
    if (filters[pageKey].exclude_dsk_data !== undefined) {
      filters[pageKey].exclude_dsk = filters[pageKey].exclude_dsk_data;
      delete filters[pageKey].exclude_dsk_data;
    }
    if (filters[pageKey].exclude_dsk === "") {
      delete filters[pageKey].exclude_dsk;
    }
    const requestParams = Helper.filterObjectByKeys(
      filters[pageKey],
      Object.keys(params.body_params),
    );
    const url = Helper.parseUrlEndpoint(params.url_endpoint, filters[pageKey]);

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Query to fetch statistics data
   */
  const dataQuery = useQuery({
    queryKey: QueryKeys.lot_stats(pageKey),
    queryFn: getData,
    enabled: true,
  });

  useEffect(() => {
    setData({});
    setShouldLoadChart(false);
    dataQuery.refetch();
  }, [reloadQuantityData]);

  useEffect(() => {
    if (data.datalogged_yield !== undefined) {
      component.chart.component.data = generatePieData(data.datalogged_yield);
      setShouldLoadChart(true);
    }
  }, [data]);

  useEffect(() => {
    if (dataQuery.isSuccess) {
      const response = dataQuery.data;
      if (response.success) {
        setData(response.data);
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
    }
  }, [dataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (dataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: dataQuery.error,
      });
    }
  }, [dataQuery.isError]);

  /**
   * Create pie data
   *
   * @param {number} value
   * @returns {array} data
   */
  const generatePieData = (value) => {
    const data = [
      {
        y: parseFloat(value),
        color: upColor,
      },
      {
        y: 100 - parseFloat(value),
        color: downColor,
      },
    ];

    return data;
  };

  /**
   * Get color based on yield values
   */
  const getColor = useCallback(() => {
    const color =
      data.datalogged_yield > data.yield_limit_percentage
        ? upColor
        : data.datalogged_yield < data.yield_limit_percentage
          ? downColor
          : token.colorText;

    return color;
  }, [data]);

  return (
    <div className="flex grow flex-col h-full">
      <Flex className="h-full" vertical justify="space-between">
        <Row className="w-full">
          <Col span={12}>
            <Space direction="vertical">
              <div className="mb-1">
                <Text type="secondary" className="text-sm">
                  {component.display_name}
                </Text>
              </div>
              {data.datalogged_yield !== undefined ? (
                <Title
                  level={2}
                  style={{
                    color: getColor(),
                  }}
                >
                  {Helper.numberFormat(data.datalogged_yield, 2)}%
                </Title>
              ) : (
                <Spin size="large" indicator={<LoadingOutlined spin />} />
              )}
            </Space>
          </Col>
          {shouldLoadChart && (
            <Col span={12}>
              <Pie
                chartRef={chartRef}
                component={component.chart.component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
              />
            </Col>
          )}
        </Row>
        <Row className="w-full">
          <Col span={24}>
            <Text className="text-base inline-block mb-2">
              Yield Limit:{" "}
              {data.yield_limit_percentage !== undefined ? (
                <>
                  {Helper.numberFormat(data.yield_limit_percentage, 2)}%{" "}
                  {data.datalogged_yield > data.yield_limit_percentage ? (
                    <CaretUpOutlined style={{ color: upColor }} />
                  ) : data.datalogged_yield < data.yield_limit_percentage ? (
                    <CaretDownOutlined style={{ color: downColor }} />
                  ) : (
                    ""
                  )}
                </>
              ) : (
                <Spin
                  size="small"
                  indicator={<LoadingOutlined spin />}
                  className="ml-2"
                />
              )}
            </Text>
          </Col>
        </Row>
      </Flex>
    </div>
  );
}
