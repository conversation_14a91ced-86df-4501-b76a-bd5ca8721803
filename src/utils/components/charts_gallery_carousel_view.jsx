import React, { useEffect, useRef, useState } from "react";
import {
  LeftOutlined,
  RightOutlined,
  UpOutlined,
  DownOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { Carousel, Button, Flex, Image, Spin, theme, Typography } from "antd";
import { useBoundStore } from "../../store/store";
import ChartHelper from "../charts/chart_helper";
import Helper from "../helper";
import { GalleryChart } from "./gallery_chart";

const { useToken } = theme;
const { Text } = Typography;

const chartSliderSettings = {
  dots: false,
  infinite: false,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
};

const chartImageWidth = 128;
const chartImageHeight = 112;
const chartImageMargin = 16;
const chartInfoHeight = 44;
const navSliderPadding = 100;
const chartSliderPadding = 100;

/**
 * Charts gallery in carousel view
 *
 * @param {string} pageKey
 * @param {object} chartsData
 * @param {object} chartCustomData
 * @param {object} galleryCharts
 * @param {function} setGalleryCharts
 * @param {React.Ref} galleryOptionsRef
 * @param {string} galleryView
 * @returns {JSX.Element}
 */
export default function ChartsGalleryCarouselView({
  pageKey,
  chartsData,
  chartCustomData,
  galleryCharts,
  setGalleryCharts,
  galleryOptionsRef,
  galleryView,
}) {
  const [chartsImageData, setChartsImageData] = useState({});
  const [currentChartKey, setCurrentChartKey] = useState();
  const [currentChartSlideIndex, setCurrentChartSlideIndex] = useState(0);
  const [currentNavSlideIndex, setCurrentNavSlideIndex] = useState(0);
  const [maxNavSlide, setMaxNavSlide] = useState();
  const [navSliderHeight, setNavSliderHeight] = useState(0);
  const [navSliderWidth, setNavSliderWidth] = useState(0);
  const [chartSliderHeight, setChartSliderHeight] = useState();
  const [galleryChartIndex, setGalleryChartIndex] = useState(0);
  const [carouselCharts, setCarouselCharts] = useState({});
  const galleryChartsUpdateTime = useBoundStore(
    (state) => state.galleryChartsUpdateTime,
  );
  const mainContentRef = useBoundStore((state) => state.mainContentRef);
  const chartSlider = useRef();
  const navSliderWrapper = useRef();
  const navSlider = useRef();
  const navSliderBtnRef = useRef();
  const { token } = useToken();

  const navSliderSettings = {
    dots: false,
    infinite: false,
    speed: 500,
    vertical: true,
    verticalSwiping: true,
    slidesToShow:
      Object.keys(chartsData).length < maxNavSlide
        ? Object.keys(chartsData).length
        : maxNavSlide,
    slidesToScroll:
      Object.keys(chartsData).length < maxNavSlide
        ? Object.keys(chartsData).length
        : maxNavSlide,
  };

  useEffect(() => {
    initChartsImageData(chartsData);
    calculateNavSliderSize();
    calculateChartSliderHeight();
  }, []);

  useEffect(() => {
    if (galleryCharts?.[pageKey]?.[galleryView]) {
      const [initialChartKey] = Object.keys(
        galleryCharts[pageKey][galleryView],
      );
      setCurrentChartKey(initialChartKey);
      generateChartImages(galleryCharts[pageKey][galleryView]);
    }
  }, [galleryChartsUpdateTime]);

  useEffect(() => {
    setNavSlidesToShow();
  }, [navSliderHeight]);

  useEffect(() => {
    const chartKeys = Object.keys(chartsData);
    const chartKey = chartKeys[galleryChartIndex];
    if (chartKey) {
      const carouselChartsCopy = Helper.cloneObject(carouselCharts);
      carouselChartsCopy[chartKey] = chartsData[chartKey];
      carouselChartsCopy[chartKey].galleryChartIndex = galleryChartIndex;
      carouselChartsCopy[chartKey].setGalleryChartIndex = setGalleryChartIndex;

      setCarouselCharts(carouselChartsCopy);
    }
  }, [galleryChartIndex]);

  /**
   * Calculate navigation slider height based on remaining available height
   * Set navigation slider width
   */
  const calculateNavSliderSize = () => {
    setNavSliderHeight(
      mainContentRef.offsetHeight -
        galleryOptionsRef.current.offsetHeight -
        navSliderBtnRef.current.offsetHeight * 2 -
        navSliderPadding,
    );
    if (navSliderWrapper.current) {
      setNavSliderWidth(navSliderWrapper.current.offsetWidth);
    }
  };

  /**
   * Calculate chart slider height based on remaining available height
   */
  const calculateChartSliderHeight = () => {
    setChartSliderHeight(
      mainContentRef.offsetHeight -
        galleryOptionsRef.current.offsetHeight -
        chartSliderPadding,
    );
  };

  /**
   * Set number of navigation slides to show based on container height
   */
  const setNavSlidesToShow = () => {
    const slideItemHeight =
      chartImageHeight + chartImageMargin + chartInfoHeight;
    const slidesToShow = Math.floor(navSliderHeight / slideItemHeight);
    setMaxNavSlide(slidesToShow);
  };

  /**
   * Populate charts images data initially
   *
   * @param {object} chartsData
   */
  const initChartsImageData = (chartsData) => {
    let imagesData = {};
    Object.values(chartsData).forEach((data) => {
      imagesData[data.chartKey] = null;
    });
    setChartsImageData(imagesData);
  };

  /**
   * Generate chart image from rendered chart svg
   *
   * @param {object} charts
   */
  const generateChartImages = (charts) => {
    Object.keys(charts).forEach((chartKey) => {
      if (!chartsImageData[chartKey]) {
        ChartHelper.getHighchartsImageData(
          charts[chartKey],
          updateChartsImageData,
          chartKey,
        );
      }
    });
  };

  /**
   * Update charts image data
   *
   * @param {string} imageData
   * @param {string} chartKey
   */
  const updateChartsImageData = (imageData, chartKey) => {
    let chartsImageDataCopy = Helper.cloneObject(chartsImageData);
    chartsImageDataCopy[chartKey] = imageData;
    setChartsImageData(chartsImageDataCopy);
    chartsImageData[chartKey] = chartsImageDataCopy[chartKey];
  };

  /**
   * Handle slide change event on chart slider
   *
   * @param {int} currentSlideIndex
   */
  const onChangeChartSlider = (currentSlideIndex) => {
    const chartKey = Object.keys(galleryCharts[pageKey][galleryView])[
      currentSlideIndex
    ];
    setCurrentChartKey(chartKey);
    setCurrentChartSlideIndex(currentSlideIndex);
  };

  /**
   * Handle slide change event on navigation slider
   *
   * @param {int} currentSlideIndex
   */
  const onChangeNavSlider = (currentSlideIndex) => {
    setCurrentNavSlideIndex(currentSlideIndex);
  };

  /**
   * Move to next slide item
   *
   * @param {JSX.Element} slider
   */
  const nextSlide = (slider) => {
    slider.current.next();
  };

  /**
   * Move to previous slide item
   *
   * @param {JSX.Element} slider
   */
  const previousSlide = (slider) => {
    slider.current.prev();
  };

  /**
   * Go to slide based on chart key
   *
   * @param {JSX.Element} slider
   * @param {string} chartKey
   */
  const goToChartSlide = (slider, chartKey) => {
    const slideIndex = Object.keys(galleryCharts[pageKey][galleryView]).indexOf(
      chartKey,
    );
    slider.current.goTo(slideIndex);
    setCurrentChartKey(chartKey);
  };

  /**
   * Truncate navigation content item label at the middle
   *
   * @param {string} label
   * @param {int} width
   * @returns {string} formattedLabel
   */
  const formatNavContentItemLabel = (label, width) => {
    const maxLength = Helper.getStringMaxLength(width);
    const formattedLabel = Helper.truncateString(label, maxLength, "middle");

    return formattedLabel;
  };

  /**
   * Generate navigation content
   *
   * @returns {JSX.Element}
   */
  const createNavContent = () => {
    return Object.keys(chartsImageData).map((chartKey) => {
      return (
        <div key={`chart_img_${chartKey}_wrapper`}>
          <h3
            className={`cursor-pointer text-center p-2 ${
              chartKey === currentChartKey
                ? "border border-solid border-indigo-600"
                : ""
            }`}
            style={{
              height: `${chartImageHeight + chartInfoHeight}px`,
              margin: `${chartImageMargin}px auto`,
            }}
            onClick={() => goToChartSlide(chartSlider, chartKey)}
          >
            {chartsImageData[chartKey] ? (
              <Flex justify="center" align="center" gap="small" vertical>
                <Image
                  key={`chart_img_${chartKey}`}
                  src={chartsImageData[chartKey]}
                  width={`${chartImageWidth}px`}
                  height={`${chartImageHeight}px`}
                  preview={false}
                />
                <Text ellipsis>
                  {navSliderWrapper.current &&
                    formatNavContentItemLabel(
                      chartsData[chartKey].label,
                      navSliderWrapper.current.offsetWidth,
                    )}
                </Text>
              </Flex>
            ) : (
              <Flex className="h-full" justify="center" align="center">
                <Spin
                  indicator={
                    <LoadingOutlined
                      style={{
                        fontSize: 24,
                      }}
                      spin
                    />
                  }
                />
              </Flex>
            )}
          </h3>
        </div>
      );
    });
  };

  return (
    <div>
      <Flex className="flex-row!">
        <div
          ref={navSliderWrapper}
          className="flex-1 flex-col"
          style={{
            background: `${token.yhLightBlue}`,
          }}
        >
          <Flex justify="center" align="center" className="flex-1 flex-col">
            <div>
              <Button
                ref={navSliderBtnRef}
                type="link"
                icon={
                  <UpOutlined
                    style={{
                      fontSize: 16,
                    }}
                  />
                }
                disabled={currentNavSlideIndex === 0}
                onClick={() => previousSlide(navSlider)}
              />
            </div>
            <div
              style={{
                height: `${navSliderHeight}px`,
                width: `${navSliderWidth}px`,
              }}
            >
              {Object.keys(chartsImageData).length > 0 &&
                maxNavSlide &&
                (maxNavSlide < Object.keys(chartsData).length ? (
                  <Carousel
                    ref={navSlider}
                    className="h-full"
                    {...navSliderSettings}
                    afterChange={onChangeNavSlider}
                  >
                    {createNavContent()}
                  </Carousel>
                ) : (
                  <Flex vertical>{createNavContent()}</Flex>
                ))}
            </div>
            <div>
              <Button
                type="link"
                icon={
                  <DownOutlined
                    style={{
                      fontSize: 16,
                    }}
                  />
                }
                disabled={
                  currentNavSlideIndex >
                  Object.keys(chartsData).length - 1 - maxNavSlide
                }
                onClick={() => nextSlide(navSlider)}
              />
            </div>
          </Flex>
        </div>
        <div
          style={{
            flex: 6,
          }}
        >
          <Flex
            gap="large"
            justify="center"
            align="center"
            className="flex-row!"
          >
            <div>
              <Button
                type="link"
                disabled={currentChartSlideIndex === 0}
                onClick={() => previousSlide(chartSlider)}
              >
                <h3>
                  <LeftOutlined
                    style={{
                      fontSize: 24,
                    }}
                  />
                </h3>
              </Button>
            </div>
            <div>
              {chartSliderHeight && (
                <Carousel
                  ref={chartSlider}
                  afterChange={onChangeChartSlider}
                  {...chartSliderSettings}
                  style={{
                    width: `${chartSliderHeight}px`,
                    height: `${chartSliderHeight}px`,
                  }}
                >
                  {Object.keys(carouselCharts).map(function (chartKey) {
                    return (
                      <div key={`gallery_chart_${chartKey}_wrapper_carousel`}>
                        <h3>
                          <div className="m-auto">
                            {React.createElement(GalleryChart, {
                              ...carouselCharts[chartKey],
                              chartCustomData: chartCustomData,
                              galleryCharts: galleryCharts,
                              setGalleryCharts: setGalleryCharts,
                              galleryView: galleryView,
                            })}
                          </div>
                        </h3>
                      </div>
                    );
                  })}
                </Carousel>
              )}
            </div>
            <div>
              <Button
                type="link"
                icon={
                  <RightOutlined
                    style={{
                      fontSize: 24,
                    }}
                  />
                }
                disabled={
                  currentChartSlideIndex === Object.keys(chartsData).length - 1
                }
                onClick={() => nextSlide(chartSlider)}
              />
            </div>
          </Flex>
        </div>
      </Flex>
    </div>
  );
}
