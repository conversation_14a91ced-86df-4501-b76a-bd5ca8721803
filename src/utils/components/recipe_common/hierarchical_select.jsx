import React, { useState, useEffect } from "react";
import { Form, Select } from "antd";

/**
 * Get filtered options based on the selected values
 *
 * @param {object} data - Group field data object
 * @param {string[]} selectedValues - Currently selected values
 * @returns {string[][]} - Array of options for each level
 */
const getFilteredOptions = (data, selectedValues) => {
  const levelOptions = [];

  // Loop through the first two levels to get options for each level
  for (let level = 0; level <= 1; level++) {
    const currentLevelOptions = new Set();

    for (const key in data) {
      const parts = key.split("-");
      if (
        parts.slice(0, level).every((part, idx) => part === selectedValues[idx])
      ) {
        currentLevelOptions.add(parts[level]);
      }
    }

    levelOptions.push(Array.from(currentLevelOptions));
  }

  // Get remaining options for the third level based on the first two selected values
  const remainingOptions = new Set();
  for (const key in data) {
    const parts = key.split("-");
    if (parts.slice(0, 2).every((part, idx) => part === selectedValues[idx])) {
      const remaining = parts.slice(2).join("-");
      remainingOptions.add(remaining);
    }
  }
  levelOptions.push(Array.from(remainingOptions));

  return levelOptions;
};

/**
 *
 * This component provides a series of dependent Select components to allow hierarchical selection of options.
 *
 * @param {FormInstance} form
 * @param {object} data
 * @param {string} initialValue
 * @param {boolean} shouldClearFormValues
 * @param {function} setShouldClearFormValues
 * @returns {JSX.Element}
 */
const HierarchicalSelect = ({
  form,
  data,
  initialValue,
  shouldClearFormValues,
  setShouldClearFormValues,
}) => {
  const initialSelectedValues = initialValue
    ? [
        ...initialValue.split("-").slice(0, 2),
        initialValue.split("-").slice(2).join("-"),
      ]
    : [];
  const [selectedValues, setSelectedValues] = useState(initialSelectedValues);
  const [filteredOptions, setFilteredOptions] = useState(
    getFilteredOptions(data, []),
  );

  // Effect to handle when initialValue is set or not
  useEffect(() => {
    if (initialValue) {
      setSelectedValues(initialSelectedValues);
      setFilteredOptions(getFilteredOptions(data, initialSelectedValues));

      const initialFormValues = {};
      initialSelectedValues.forEach((value, index) => {
        initialFormValues[`grouping_fields_level_${index}`] = value;
      });
      initialFormValues.grouping_fields = initialValue;
      form.setFieldsValue(initialFormValues);
    } else {
      setSelectedValues([]);
      setFilteredOptions(getFilteredOptions(data, []));
    }
  }, [initialValue, data]);

  // Effect to handle form reset
  useEffect(() => {
    if (shouldClearFormValues) {
      setSelectedValues([]);
      setFilteredOptions(getFilteredOptions(data, []));
      setShouldClearFormValues(false);
    }
  }, [shouldClearFormValues]);

  /**
   * Generate label mappings based on the data prop
   *
   * @returns {object}
   */
  const generateLabelMappings = () => {
    const labelMappings = {};
    for (const key in data) {
      key.split("-").forEach((part, index) => {
        if (!labelMappings[part])
          labelMappings[part] = data[key].split(" - ")[index];
      });
    }
    return labelMappings;
  };

  const labelMappings = generateLabelMappings();

  /**
   * Get label and map it according to labelMappings
   *
   * @param {string} key
   * @returns {string}
   */
  const getLabel = (key) => {
    return key.includes("-")
      ? key
          .split("-")
          .map((part) => labelMappings[part] || part)
          .join(" - ")
      : labelMappings[key] || key;
  };

  /**
   * Handle change event for the Select component
   *
   * @param {string} value - Selected value
   * @param {number} level - The level of the Select component
   */
  const handleChange = (value, level) => {
    // Update selected values and filtered options
    const newSelectedValues = [...selectedValues.slice(0, level), value];
    setSelectedValues(newSelectedValues);
    setFilteredOptions(getFilteredOptions(data, newSelectedValues));

    // Update form values based on new selections
    const newFormValues = { ...form.getFieldsValue() };
    for (let i = level + 1; i < 3; i++) {
      newFormValues[`grouping_fields_level_${i}`] = undefined;
    }
    const concatenatedValues = newSelectedValues.filter(Boolean).join("-");
    newFormValues.grouping_fields = concatenatedValues;
    form.setFieldsValue(newFormValues);
  };

  return (
    <>
      <Form.Item
        name="grouping_fields"
        label="Select Grouping Field"
        rules={[
          {
            required: true,
            message: "Please select grouping field",
          },
        ]}
        tooltip="Grouping field is the unique meta data of the datalogs as basis to determine what to consolidate"
      >
        <div className="w-full flex gap-2">
          {filteredOptions.map((options, level) => (
            <Form.Item
              key={level}
              name={`grouping_fields_level_${level}`}
              validateTrigger={["onChange", "onClick", "onBlur"]}
              rules={[
                {
                  required:
                    (level === 2 &&
                      (selectedValues[0] !== "lot_id" ||
                        selectedValues[1] !== "program")) ||
                    selectedValues[0] === "" ||
                    selectedValues[0] === undefined ||
                    selectedValues[1] === "" ||
                    selectedValues[1] === undefined,
                  message: level === 2 && "Please complete the grouping field.",
                },
              ]}
              noStyle
            >
              <Select
                onChange={(value) => handleChange(value, level)}
                disabled={level > 0 && !selectedValues[level - 1]}
                popupMatchSelectWidth={false}
                allowClear
                className="w-full"
                options={options.map((option) => ({
                  value: option,
                  label: getLabel(option || ""),
                }))}
              />
            </Form.Item>
          ))}
        </div>
      </Form.Item>
    </>
  );
};

export default HierarchicalSelect;
