import React, { useState, useEffect } from "react";
import { Select, Button, Space, Tooltip, Typography, Flex } from "antd";
import {
  QuestionCircleOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined,
} from "@ant-design/icons";
import DebounceSelect from "./debounce_select";

const GroupSelect = ({
  options = [],
  pageFilters,
  required = true,
  groupFieldName,
  groupFieldLabel,
  tooltipMessage,
  initialValues,
  shouldClearFormValues,
  setShouldClearFormValues,
  ...props
}) => {
  const defaultRowsValue = [
    { id: 1, selected: null, urlEndpoint: null, urlMethod: null },
  ];
  const [rows, setRows] = useState(defaultRowsValue);

  // Effect to handle setting of initial value
  useEffect(() => {
    if (initialValues && options.length > 0) {
      setInitialRows(initialValues);
    }
  }, [initialValues, options]);

  // Effect to handle form reset
  useEffect(() => {
    if (shouldClearFormValues) {
      setRows(defaultRowsValue);
      setShouldClearFormValues(false);
    }
  }, [shouldClearFormValues]);

  /**
   * Initializes rows based on the provided initial values
   *
   * @param {object} initialValues
   */
  const setInitialRows = (initialValues) => {
    const newRows = [];

    Object.entries(initialValues).forEach(([key, values]) => {
      const methodOption = options.find(
        (option) => option.value === key && values,
      );
      if (methodOption) {
        newRows.push({
          id: newRows.length + 1,
          selected: key,
          urlEndpoint: methodOption.url_endpoint,
          urlMethod: methodOption.url_method,
        });
      }
    });

    setRows(newRows);
  };

  /**
   * Adds a new empty row to the rows array
   */
  const addRow = () => {
    setRows([...rows, { id: rows.length + 1, selected: null }]);
  };

  /**
   * Removes a row from the rows array by its id
   *
   * @param {number} id
   */
  const removeRow = (id) => {
    setRows(rows.filter((row) => row.id !== id));
  };

  /**
   * Updates the selected value, URL endpoint, and URL method for a specific row
   *
   * @param {number} id
   * @param {string} value
   * @param {string} urlEndpoint
   * @param {string} urlMethod
   */
  const handleSelectChange = (id, value, urlEndpoint, urlMethod) => {
    const updatedRows = rows.map((row) =>
      row.id === id
        ? {
            ...row,
            selected: value,
            urlEndpoint: urlEndpoint,
            urlMethod: urlMethod,
          }
        : row,
    );

    setRows(updatedRows);
  };

  /**
   * Retrieves available options that haven't been selected in other rows
   *
   * @param {string|null} selected
   * @returns {array}
   */
  const getAvailableOptions = (selected) => {
    const selectedValues = rows
      .filter((row) => row.selected && row.selected !== selected)
      .map((row) => row.selected);

    return options.filter((method) => !selectedValues.includes(method.value));
  };

  return (
    <>
      <Space className="mt-2.5 mb-1.5">
        <Typography.Text className={required && "required-label"}>
          {groupFieldLabel}
        </Typography.Text>
        <Tooltip title={tooltipMessage}>
          <QuestionCircleOutlined />
        </Tooltip>
      </Space>
      {rows?.map((row, index) => (
        <Flex key={row.id} className="gap-2">
          <div className="w-44 flex items-start gap-2">
            <span className="text-xl text-red-600">
              <MinusCircleOutlined
                onClick={() => removeRow(row.id)}
                className={index === 0 && "invisible"}
              />
            </span>

            <Select
              popupMatchSelectWidth={false}
              value={row.selected}
              onChange={(value, option) =>
                handleSelectChange(
                  row.id,
                  value,
                  option.urlEndpoint,
                  option.urlMethod,
                )
              }
              options={getAvailableOptions(row.selected).map((method) => ({
                label: method.label,
                value: method.value,
                urlEndpoint: method.url_endpoint,
                urlMethod: method.url_method,
              }))}
              className="min-w-28 mx-2"
            />
          </div>

          <DebounceSelect
            mode="tags"
            urlEndpoint={row.urlEndpoint}
            urlMethod={row.urlMethod}
            groupFieldName={groupFieldName}
            fieldName={row.selected}
            pageFilters={pageFilters}
            disabled={!row.selected}
            required={required}
            popupMatchSelectWidth={false}
            {...props}
          ></DebounceSelect>
        </Flex>
      ))}

      <div className="flex justify-center items-center">
        <Button
          onClick={addRow}
          className="w-28 flex justify-center items-center"
          icon={<PlusCircleOutlined className=" text-green-500 text-xl" />}
          disabled={rows.length === options.length}
        >
          Add Another
        </Button>
      </div>
    </>
  );
};

export default GroupSelect;
