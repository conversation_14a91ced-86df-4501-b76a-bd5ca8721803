"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";

/**
 * Step buttons component
 *
 * @param {object} steps
 * @param {int} currentStep
 * @param {function} setCurrentStep
 * @param {function} simulationHandler
 * @param {function} saveRecipeHandler
 * @param {function} updateConditionHandler
 * @param {function} updateConditionWithCallbackHandler
 * @param {boolean} disableSkipToSimulateBtn
 * @param {boolean} disableNextBtn
 * @returns {JSX.Element}
 */
const ProgressStepButtons = ({
  pageFilters,
  steps,
  currentStep,
  setCurrentStep,
  simulation<PERSON>andler,
  saveRecipeHandler,
  updateConditionHandler,
  updateConditionWithCallbackHandler,
  disableSkipToSimulateBtn,
  disableNextBtn,
}) => {
  /**
   * Update npi conditions
   */
  const updateCondition = () => {
    if (currentStep === 0 && typeof updateConditionHandler === "function") {
      updateConditionHandler();
    }
  };

  /**
   * Next button handler
   */
  const next = () => {
    if (typeof updateCondition === "function") {
      updateCondition();
    }
    setCurrentStep(currentStep + 1);
  };

  /**
   * Previous button handler
   */
  const prev = () => {
    setCurrentStep(currentStep - 1);
  };

  /**
   * Handler when the simulation button is clicked
   */
  const handleSimulation = () => {
    if (typeof saveRecipeHandler === "function") {
      saveRecipeHandler();
    }
    if (typeof simulationHandler === "function") {
      simulationHandler();
    }
  };

  return (
    <Flex justify="flex-end" gap="small" className="mt-6">
      {currentStep > 0 && <Button onClick={prev}>Previous</Button>}
      {currentStep < steps.length - 1 && (
        <>
          {pageFilters?.show_simulate_buttons && (
            <Button
              onClick={() => {
                // Will redirect to simulation page after updating the condition
                if (
                  currentStep === 0 &&
                  typeof updateConditionWithCallbackHandler === "function"
                ) {
                  updateConditionWithCallbackHandler();
                } else if (typeof simulationHandler === "function") {
                  simulationHandler();
                }
              }}
              disabled={disableSkipToSimulateBtn}
            >
              Skip to Simulate
            </Button>
          )}
          <Button type="primary" onClick={next} disabled={disableNextBtn}>
            Next
          </Button>
        </>
      )}
      {pageFilters?.show_simulate_buttons &&
        currentStep === steps.length - 1 && (
          <>
            <Button onClick={saveRecipeHandler}>Save Recipe</Button>
            <Button type="primary" onClick={handleSimulation}>
              Simulate
            </Button>
          </>
        )}
    </Flex>
  );
};

export default ProgressStepButtons;
