"use client";

import { Col, Row, Typography } from "antd";
const { Text } = Typography;

/**
 * Recipe info component
 *
 * @param {array} recipeInfo
 * @returns {JSX.Element}
 */
const RecipeInfo = ({ recipeInfo }) => {
  return (
    <>
      {recipeInfo.map((info, key) => {
        return (
          <Row key={`recipe_info_${key}`} className="pb-0">
            <Col span={8}>
              <Text strong>{info.label}</Text>
            </Col>
            <Col span={14}>
              <Text>{info.value}</Text>
            </Col>
          </Row>
        );
      })}
    </>
  );
};

export default RecipeInfo;
