"use client";

import { useState, useRef, useEffect } from "react";
import { Button, Checkbox, Flex, Form, List, Modal, Typography } from "antd";
import {
  InfoCircleOutlined,
  UpOutlined,
  DownOutlined,
} from "@ant-design/icons";

const { Text } = Typography;

/**
 * Recipe processing modal component
 *
 * @param {boolean} isModalOpen
 * @param {function} setIsModalOpen
 * @param {array} selectedDatalogs
 * @param {string} recipeName
 * @param {boolean} showNote
 * @returns {JSX.Element}
 */
const RecipeProcessingModal = ({
  isModalOpen,
  setIsModalOpen,
  selectedDatalogs,
  recipeName,
  showNote = false,
}) => {
  const [form] = Form.useForm();
  const [showAll, setShowAll] = useState(false);
  const [expandedItems, setExpandedItems] = useState(new Set());
  const [overflowingItems, setOverflowingItems] = useState(new Set());
  const textRefs = useRef({});
  const initialDisplayCount = 5;
  const maxVisibleItems = 10; // Maximum items before scrolling

  /**
   * Return the style for the list container
   *
   * If the displayedDatalogs is longer than maxVisibleItems, set a fixed height
   * and enable vertical scrolling.
   *
   * @returns {string} className string
   */
  const getListContainerStyle = () =>
    displayedDatalogs.length > maxVisibleItems
      ? "max-h-[300px] overflow-y-auto mb-4"
      : "";

  /**
   * Close the recipe processing modal
   */
  const closeModal = () => {
    setIsModalOpen(false);
  };

  /**
   * Toggle the showAll state and reset the textRefs object.
   *
   * Called when the user clicks the "Show all" button.
   */
  const toggleShowAll = () => {
    setShowAll((prev) => !prev);
    textRefs.current = {};
  };

  /**
   * Toggle the expansion of a single datalog in the list.
   *
   * @param {string} datalog datalog to toggle
   */
  const toggleItemExpansion = (datalog) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(datalog)) {
        newSet.delete(datalog);
      } else {
        newSet.add(datalog);
      }
      return newSet;
    });
  };

  /**
   * Check if any of the datalog texts are overflowing and update the set of
   * overflowing items.
   *
   * This function is called when the modal is opened and when the window is
   * resized. It checks if any of the datalog texts are overflowing by comparing
   * their scrollWidth and clientWidth properties. If a text is overflowing, it
   * adds the corresponding datalog to the set of overflowing items.
   */
  const checkOverflow = () => {
    const newOverflowingItems = new Set();
    Object.entries(textRefs.current).forEach(([datalog, ref]) => {
      if (ref && ref.scrollWidth > ref.clientWidth) {
        newOverflowingItems.add(datalog);
      }
    });
    setOverflowingItems(newOverflowingItems);
  };

  // Effect to check for text overflow and handle window resize
  useEffect(() => {
    if (isModalOpen) {
      // Delay the initial overflow check to ensure the DOM is fully rendered
      const overflowCheckTimeout = setTimeout(checkOverflow, 100);

      // Add event listener for window resize to recheck overflow
      const handleResize = () => checkOverflow();
      window.addEventListener("resize", handleResize);

      // Cleanup function to remove event listener and clear timeout
      return () => {
        window.removeEventListener("resize", handleResize);
        clearTimeout(overflowCheckTimeout);
      };
    }
  }, [isModalOpen, selectedDatalogs, showAll]);

  /**
   * Render a single datalog item in the list.
   *
   * If the item is expanded, it renders the text as a single line with an
   * UpOutlined icon at the end. If the item is not expanded but is overflowing,
   * it renders the text as a single line with an ellipsis at the end and a
   * DownOutlined icon at the end. If the item is not overflowing, it renders
   * the text as a single line without any icons.
   *
   * @param {string} datalog datalog to render
   * @returns {JSX.Element} rendered datalog item
   */
  const renderDatalogItem = (datalog) => {
    const isExpanded = expandedItems.has(datalog);
    const isOverflowing = overflowingItems.has(datalog);

    if (isExpanded) {
      return (
        <Text>
          • {datalog}
          <Button
            type="link"
            size="small"
            onClick={() => toggleItemExpansion(datalog)}
            style={{ padding: "0 4px" }}
            icon={<UpOutlined />}
          />
        </Text>
      );
    }

    return (
      <div style={{ display: "flex", alignItems: "center", width: "100%" }}>
        <Text
          ref={(el) => (textRefs.current[datalog] = el)}
          style={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            maxWidth: isOverflowing ? "calc(100% - 40px)" : "100%",
          }}
        >
          • {datalog}
        </Text>
        {isOverflowing && (
          <Button
            type="link"
            size="small"
            onClick={() => toggleItemExpansion(datalog)}
            style={{ padding: "0 4px", flexShrink: 0 }}
            icon={<DownOutlined />}
          />
        )}
      </div>
    );
  };

  const displayedDatalogs = showAll
    ? selectedDatalogs
    : selectedDatalogs.slice(0, initialDisplayCount);

  const hasMoreItems = selectedDatalogs.length > initialDisplayCount;

  return (
    <Modal
      title=""
      open={isModalOpen}
      onOk={closeModal}
      onCancel={closeModal}
      width="50vw"
      destroyOnClose
      footer={[
        <Button key="submit" type="primary" onClick={closeModal}>
          Ok
        </Button>,
      ]}
    >
      <Form form={form} initialValues={{ notify_email: true }}>
        <Flex align="center" className="mb-4!">
          <Typography.Title level={5} className="m-0!">
            <InfoCircleOutlined className="text-amber-500! mr-2!" />
            Recipe is currently processing
          </Typography.Title>
        </Flex>

        <Flex vertical gap="middle">
          <Text>
            Your selected recipe: <Text strong>{recipeName}</Text> has been
            applied to these datalogs:
          </Text>

          <div className={getListContainerStyle()}>
            <List
              size="small"
              dataSource={displayedDatalogs}
              renderItem={(datalog) => (
                <List.Item className="py-1! px-0!">
                  {renderDatalogItem(datalog)}
                </List.Item>
              )}
            />
          </div>

          {hasMoreItems && (
            <Button
              type="link"
              onClick={toggleShowAll}
              icon={showAll ? <UpOutlined /> : <DownOutlined />}
              className="p-0!"
            >
              {showAll
                ? "Show less"
                : `Show all ${selectedDatalogs.length} datalogs`}
            </Button>
          )}

          <Text>
            You will be notified once the processing is done or if an error
            occurs.
          </Text>
        </Flex>

        <Form.Item name="notify_email" valuePropName="checked" className="mt-4">
          <Checkbox disabled>Notify me via email</Checkbox>
        </Form.Item>

        {showNote && (
          <Text type="secondary" className="block">
            Note: Metadata recipe changes are not directly applied to
            consolidated or combined datalogs. These changes are inherited from
            the original files during the consolidation or combine process.
          </Text>
        )}
      </Form>
    </Modal>
  );
};

export default RecipeProcessingModal;
