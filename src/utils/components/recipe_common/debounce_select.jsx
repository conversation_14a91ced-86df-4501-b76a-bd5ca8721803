"use client";

import { Select, Form, Spin, Tooltip } from "antd";
import { useMemo, useRef, useState, useEffect } from "react";
import { debounce } from "lodash";
import Helper from "../../helper";

const url = process.env.NEXT_PUBLIC_APP_API_URL || "";

/**
 * A custom select that fetches options from server
 *
 * @param {object} fetchOptions
 * @param {int} debounceTimeout
 * @param {object} props
 * @param {string} fieldName
 * @returns {React.Component}
 */
const DebounceSelect = ({
  debounceTimeout = 800,
  groupFieldName,
  fieldName,
  pageFilters,
  urlEndpoint,
  urlMethod,
  required,
  ...props
}) => {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState([]);
  const fetchRef = useRef(0);

  useEffect(() => {
    setOptions([]);
  }, [fieldName]);

  /**
   * Fetch the options of the select asynchronously
   *
   * @param {string} value The user input
   * @returns {object} The options of the select
   */
  async function fetchOptions(value) {
    // On focus event, the value is the event object so we just equate the q to empty string to fetch the options
    let q = value;
    if (typeof value === "object") {
      q = "";
    }
    // GET doesn't have a body so we put the parameters in the url
    const queryString = new URLSearchParams({
      ...pageFilters,
      ...{ limit: 10, q: q },
    }).toString();
    return fetch(`${url}${urlEndpoint}?${queryString}`, {
      method: urlMethod,
      headers: Helper.getHeaders(),
    })
      .then((response) => response.json())
      .then((body) => body.data);
  }

  /**
   *  Fetches options with delay
   */
  const debounceFetcher = useMemo(() => {
    const loadOptions = (value) => {
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      setOptions([]);
      setFetching(true);
      fetchOptions(value).then((newOptions) => {
        if (fetchId !== fetchRef.current) {
          // for fetch callback order
          return;
        }
        setOptions(newOptions);
        setFetching(false);
      });
    };
    return debounce(loadOptions, debounceTimeout);
  }, [fetchOptions, debounceTimeout]);

  return (
    <Form.Item
      name={[groupFieldName, `${fieldName}`]}
      rules={[
        {
          required: required,
          message: `Please input ${fieldName ?? ""} value`,
        },
      ]}
      className="w-full truncate"
    >
      <Select
        filterOption={false}
        onSearch={debounceFetcher}
        // Fetch the options automatically even without typing a keyword
        onFocus={debounceFetcher}
        notFoundContent={fetching ? <Spin size="small" /> : null}
        options={options}
        maxTagPlaceholder={(omittedValues) => {
          return (
            <Tooltip
              title={
                <div>
                  {omittedValues?.map((item) => (
                    <div key={item.value}>{item.label}</div>
                  ))}
                </div>
              }
            >
              <span>+{omittedValues?.length} more</span>
            </Tooltip>
          );
        }}
        {...props}
      />
    </Form.Item>
  );
};

export default DebounceSelect;
