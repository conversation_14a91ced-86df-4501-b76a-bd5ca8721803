"use client";

import { App } from "antd";
import { useEffect, useRef, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import YHGrid from "../../grid/yh_grid";
import Api from "../../api";
import { QueryKeys } from "../../query_keys";

const gridId = "recipe_info_table";

/**
 * Recipe info grid component
 *
 * @param {string} pageKey
 * @param {string} recipeName
 * @param {string} recipeType
 * @param {string} recipeVersion
 * @returns {JSX.Element}
 */
const RecipeInfoGrid = ({ pageKey, recipeName, recipeType, recipeVersion }) => {
  const [gridComponent, setGridComponent] = useState();
  const [gridFilters, setGridFilters] = useState({});
  const gridRef = useRef();
  const { message } = App.useApp();

  useEffect(() => {
    if (recipeName !== "") {
      const filters = {};
      filters[pageKey] = {
        recipe_name: recipeName,
        recipe_category: recipeType,
        recipe_version: recipeVersion,
      };
      setGridFilters(filters);
    }
  }, [recipeName, recipeVersion]);

  /**
   * Get the recipe info grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: gridId,
      },
    );

    return abortCtl;
  };

  useQuery({
    queryKey: [...QueryKeys.grid(pageKey, gridId), recipeName, recipeVersion],
    queryFn: getGridComponent,
  });

  return (
    <div className="h-32">
      {gridComponent && (
        <YHGrid
          gridRef={gridRef}
          gridId={gridId}
          component={gridComponent}
          pageKey={pageKey}
          filters={gridFilters}
          wrapperClassName="flex grow flex-col h-full"
        />
      )}
    </div>
  );
};

export default RecipeInfoGrid;
