"use_client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Divide<PERSON>, <PERSON>, Modal, Row, Select } from "antd";
import { useEffect, useState } from "react";
import Helper from "../../helper";
import RecipeInfo from "./recipe_info";
import { recipeTypeOptions } from "./data";
import RecipeProcessingModal from "./recipe_processing_modal";

// These parameters should not be included so that the list will not be filtered
const paramsToExclude = ["recipe_name", "recipe_version"];
const noRecipeSelected = [
  {
    value: "No recipe selected",
    label: "",
  },
];

/**
 * Apply recipe modal component
 *
 * @param {boolean} isModalOpen
 * @param {boolean} setIsModalOpen
 * @param {object} selectedParams
 * @param {function} getRecipeListApi
 * @param {function} getRecipeInfoApi
 * @param {function} applyRecipeApi
 * @param {object} searchGridRef
 * @param {boolean} requiresRowDataUpdate
 * @param {string} recipeType
 * @param {string} activeRecipeName
 * @param {FormInstance} form
 * @param {function} handleApplyRecipe
 * @param {boolean} showNote
 * @returns {JSX.Element}
 */
const ApplyRecipeModal = ({
  isModalOpen,
  setIsModalOpen,
  selectedParams = {},
  getRecipeListApi,
  getRecipeInfoApi,
  applyRecipeApi,
  searchGridRef,
  requiresRowDataUpdate,
  recipeType,
  activeRecipeName,
  form: formProp,
  handleApplyRecipe: handleApplyRecipeProp,
  showNote = false,
}) => {
  const { message } = App.useApp();
  const [internalForm] = Form.useForm(); // Internal form instance
  const form = formProp || internalForm; // Use the passed form instance if provided, otherwise use the internal one
  const [recipeOptions, setRecipeOptions] = useState([]);
  const [pageFilters, setPageFilters] = useState([]);
  const [selectedDatalogs, setSelectedDatalogs] = useState([]);
  const [isProcessingRecipeModalOpen, setIsProcessingRecipeModalOpen] =
    useState(false);
  const [recipeInfo, setRecipeInfo] = useState(noRecipeSelected);

  /**
   * Construct filters based from the selected parameters of the search table
   *
   * @returns {object} filters
   */
  const getPageFilters = () => {
    const filters = {};
    Object.keys(selectedParams)
      .filter(
        (key) => selectedParams[key].length && !paramsToExclude.includes(key),
      )
      .forEach((key) => {
        filters[key] = Array.isArray(selectedParams[key])
          ? selectedParams[key].join(",")
          : selectedParams[key];
      });

    return filters;
  };

  /**
   * Setup the filters
   */
  useEffect(() => {
    setPageFilters(getPageFilters());
  }, [selectedParams]);

  useEffect(() => {
    if (isModalOpen === true) {
      getRecipeList(getPageFilters());
    }
    if (activeRecipeName) {
      form.setFieldsValue({ recipe_name: activeRecipeName });
    }
    if (form.getFieldValue("recipe_name")) {
      // get latest info of previously selected recipe name
      handleRecipeSelection();
    }
  }, [isModalOpen]);

  /**
   * Get the list of recipes
   *
   * @param {object} filters
   */
  const getRecipeList = (filters) => {
    getRecipeListApi(
      (res) => {
        if (res.success) {
          setRecipeOptions(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        ...(filters ?? pageFilters),
        list_category: form.getFieldValue("list_category"),
      },
    );
  };

  /**
   * When user selects a recipe category
   */
  const handleRecipeTypeSelection = () => {
    getRecipeList();
    form.setFieldsValue({ recipe_name: null });
    setRecipeInfo(noRecipeSelected);
  };

  /**
   * Event handler when user selects a recipe
   */
  const handleRecipeSelection = () => {
    getRecipeInfo();
  };

  /**
   * Get the recipe info
   */
  const getRecipeInfo = () => {
    getRecipeInfoApi(
      (res) => {
        if (res.success) {
          setRecipeInfo(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        recipe_name: form.getFieldValue("recipe_name"),
      },
    );
  };

  /**
   * Update the row data if the search table if applicable
   *
   * @param {object} rowData
   */
  const updateSearchGridRowData = (rowData) => {
    if (requiresRowDataUpdate && searchGridRef.current) {
      searchGridRef.current.api.getSelectedNodes().forEach((node) => {
        node.updateData({ ...node.data, ...rowData[node.data.data_struc_key] });
      });
    }
  };

  /**
   * Apply recipe to current datalog
   *
   * @param {object} payload
   */
  const applyRecipe = (payload) => {
    applyRecipeApi(
      (res) => {
        if (res.success) {
          setIsProcessingRecipeModalOpen(true);
          const fileNames = Array.isArray(res.data)
            ? res.data.map((data) => data.file_name)
            : Object.values(res.data).map((data) => data.file_name);
          setSelectedDatalogs(fileNames);
          updateSearchGridRowData(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      payload,
    );
  };

  /**
   * Handles the application of selected recipe
   */
  const handleApplyRecipe = () => {
    form
      .validateFields()
      .then((values) => {
        applyRecipe({
          dsk: selectedParams.dsk,
          recipe_name: values.recipe_name,
        });
        setIsModalOpen(false);
      })
      .catch((error) => {
        message.error(error.errorFields[0].errors[0], 5);
      });
  };

  /**
   * Event handler when users cancels the load recipe modal
   */
  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <RecipeProcessingModal
        isModalOpen={isProcessingRecipeModalOpen}
        setIsModalOpen={setIsProcessingRecipeModalOpen}
        selectedDatalogs={selectedDatalogs}
        recipeName={form.getFieldValue("recipe_name")}
        showNote={showNote}
      ></RecipeProcessingModal>
      <Modal
        title={`Apply ${Helper.titlelize(recipeType)} Recipe`}
        open={isModalOpen}
        onOk={handleApplyRecipeProp || handleApplyRecipe}
        onCancel={closeModal}
        width={"40vw"}
        footer={[
          <Button key="back" onClick={closeModal}>
            Close
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleApplyRecipeProp || handleApplyRecipe}
            disabled={!form.getFieldValue("recipe_name")}
          >
            Apply Recipe
          </Button>,
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ list_category: "dlog_related" }}
        >
          <Row gutter={[16, 24]}>
            <Col span={10}>
              <Form.Item name="list_category" label="Select Recipes From">
                <Select
                  placeholder="-"
                  options={recipeTypeOptions}
                  onSelect={handleRecipeTypeSelection}
                ></Select>
              </Form.Item>
            </Col>
            <Col span={14}>
              <Form.Item
                name="recipe_name"
                label="Recipe Name"
                rules={[
                  {
                    required: true,
                    message: "Please select a recipe.",
                  },
                ]}
              >
                <Select
                  placeholder="Please select a recipe"
                  options={recipeOptions}
                  onSelect={handleRecipeSelection}
                  showSearch
                ></Select>
              </Form.Item>
            </Col>
          </Row>
          <Divider orientation="left">Recipe Information</Divider>
          <RecipeInfo recipeInfo={recipeInfo} />
        </Form>
      </Modal>
    </>
  );
};

export default ApplyRecipeModal;
