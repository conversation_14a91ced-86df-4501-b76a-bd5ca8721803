"use client";

import { App, <PERSON>, Row } from "antd";
import { useEffect, useRef, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import YHGrid from "../../grid/yh_grid";
import Api from "../../api";
import { QueryKeys } from "../../query_keys";

/**
 * Datalog info component for recipe
 *
 * @param {string} pageKey
 * @param {object} pageFilters
 * @returns {JSX.Element}
 */
const DatalogInfoGrid = ({ pageKey, pageFilters, gridId }) => {
  const [gridComponent, setGridComponent] = useState();
  const [gridFilters, setGridFilters] = useState({});
  const gridRef = useRef();
  const { message } = App.useApp();

  useEffect(() => {
    const filters = {};
    filters[pageKey] = pageFilters;
    setGridFilters(filters);
  }, [pageFilters]);

  /**
   * Gets the datalog info grid definition
   *
   * @returns {AbortController} abortCtl
   */
  const getGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          // Pagination is of no use since this will only have one datalog
          res.data.props.settings.pagination = false;
          setGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: "datalog_info",
      },
    );

    return abortCtl;
  };

  useQuery({
    queryKey: QueryKeys.grid(pageKey, gridId),
    queryFn: getGridComponent,
  });

  return (
    <Row>
      <Col span={24} className="h-36">
        {gridComponent && (
          <YHGrid
            gridRef={gridRef}
            gridId={gridId}
            component={gridComponent}
            pageKey={pageKey}
            filters={gridFilters}
            wrapperClassName="flex grow flex-col h-full"
          />
        )}
      </Col>
    </Row>
  );
};

export default DatalogInfoGrid;
