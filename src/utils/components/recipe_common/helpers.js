import { Tooltip } from "antd";
import Helper from "../../helper";

/**
 * Generates a hash of the date now
 *
 * @returns {string}
 */
export const generateDateNowHash = () => {
  const date = new Date();
  return `${date.getFullYear()}${date.getMonth()}${date.getDay()}${date.getHours()}${date.getMinutes()}`;
};

/**
 * Placeholder when tags exceeds the width of the dropdown
 *
 * @param {array} omittedValues
 * @returns
 */
export const maxTagPlaceholder = (omittedValues) => {
  return (
    <Tooltip title={omittedValues.map(({ label }) => label).join(", ")}>
      <span>+{omittedValues?.length} more</span>
    </Tooltip>
  );
};

/**
 * Extract analysis set filters
 *
 * @param {object} formValues
 * @return {object} filters
 */
export const getAnalysisSetFilters = (formValues) => {
  const filters = { filters: {} };
  const filterKeys = ["include_data", "exclude_data", "stats_type", "iqr_n"];
  filterKeys.forEach((key) => {
    if (formValues?.[key]) {
      filters.filters[key] = formValues[key];
    }
  });
  if (formValues?.condition_sets) {
    filters.conditions = formValues.condition_sets.join(",");
    filters.layout = formValues.layout;
  }

  return filters;
};

/**
 * Get unique string from form values
 *
 * @param {object} values
 * @returns {string}
 */
export const getGroupKey = (values) => {
  const options = { ...values };
  ["append_new_items", "charts", "tables"].forEach((key) => {
    if (options[key]) {
      delete options[key];
    }
  });

  return Object.values(options)
    .filter((value) => typeof value !== "undefined" && value.length)
    .map((value) => {
      return Array.isArray(value) ? value.join("") : value;
    })
    .join("_");
};

/**
 * Get unique page parameters
 *
 * @param {object} values
 * @returns {string}
 */
export const getPageParams = () => {
  const pageFilters = Helper.getUrlParameters();
  const pageKey = window.history.state.pageUrl;
  const gridFilters = {};
  gridFilters[pageKey] = pageFilters;

  return { pageKey, pageFilters, gridFilters };
};
