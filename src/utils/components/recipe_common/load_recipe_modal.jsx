"use_client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Divide<PERSON>, <PERSON>, Modal, Row, Select } from "antd";
import { useEffect, useState } from "react";
import Helper from "../../helper";
import RecipeInfo from "./recipe_info";
import { recipeTypeOptions } from "./data";

const noRecipeSelected = [
  {
    value: "No recipe selected",
    label: "",
  },
];

/**
 * Load recipe modal component
 *
 * @param {boolean} isModalOpen
 * @param {boolean} setIsModalOpen
 * @param {object} selectedParams
 * @param {function} renderRecipePage
 * @param {boolean} fromExternalPage
 * @param {function} getRecipeListApi
 * @param {function} getRecipeVersionApi
 * @param {function} getRecipeInfoApi
 * @param {function} setRecipeData
 * @param {function} getRecipeDataApi
 * @param {string} recipeType
 * @param {function} setActiveRecipeName
 * @param {function} setRecipeVersion
 * @returns {JSX.Element}
 */
const LoadRecipeModal = ({
  isModalOpen,
  setIsModalOpen,
  selectedParams = {},
  renderRecipePage,
  fromExternalPage = true,
  getRecipeListApi,
  getRecipeVersionApi,
  getRecipeInfoApi,
  setRecipeData = {},
  getRecipeDataApi,
  recipeType,
  setActiveRecipeName,
  setRecipeVersion,
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [recipeOptions, setRecipeOptions] = useState([]);
  const [recipeVersionOptions, setRecipeVersionOptions] = useState([]);
  const [pageFilters, setPageFilters] = useState([]);
  const [recipeInfo, setRecipeInfo] = useState(noRecipeSelected);

  /**
   * Construct filters based from the selected parameters of the search table
   *
   * @returns {object} filters
   */
  const getPageFilters = () => {
    const filters = {};
    Object.keys(selectedParams)
      .filter((key) => selectedParams[key].length)
      .forEach((key) => {
        filters[key] = Array.isArray(selectedParams[key])
          ? selectedParams[key].join(",")
          : selectedParams[key];
      });
    if (!fromExternalPage && typeof filters.recipe_name !== "undefined") {
      delete filters.recipe_name;
    }
    form.setFieldValue("list_category", filters.dsk ? "dlog_related" : "all");

    return filters;
  };

  /**
   * Setup the filters
   */
  useEffect(() => {
    setPageFilters(getPageFilters());
  }, [selectedParams]);

  useEffect(() => {
    if (isModalOpen === true) {
      getRecipeList(getPageFilters());
      if (form.getFieldValue("recipe_name")) {
        // get latest version options and latest info of previously selected recipe name
        handleRecipeSelection();
      }
    }
  }, [isModalOpen]);

  /**
   * Get the list of recipes
   *
   * @param {object} filters
   */
  const getRecipeList = (filters) => {
    getRecipeListApi(
      (res) => {
        if (res.success) {
          setRecipeOptions(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        ...(filters ?? pageFilters),
        list_category: form.getFieldValue("list_category"),
      },
    );
  };

  /**
   * When user selects a recipe category
   */
  const handleRecipeTypeSelection = () => {
    getRecipeList();
    form.setFieldsValue({ recipe_name: null, recipe_version: null });
    setRecipeInfo(noRecipeSelected);
  };

  /**
   * Get the list of recipe versions
   */
  const getRecipeVersion = () => {
    getRecipeVersionApi(
      (res) => {
        if (res.success) {
          setRecipeVersionOptions(res.data);
          form.setFieldsValue({ recipe_version: res.data[0].value });
          getRecipeInfo(); // fetch recipe info once the latest version options is set to reflect the latest version of the recipe
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        recipe_name: form.getFieldValue("recipe_name"),
      },
    );
  };

  /**
   * Event handler when user selects a recipe
   */
  const handleRecipeSelection = () => {
    getRecipeVersion();
  };

  /**
   * Get the recipe info
   */
  const getRecipeInfo = () => {
    const recipeName = form.getFieldValue("recipe_name");
    if (!recipeName) return;

    getRecipeInfoApi(
      (res) => {
        if (res.success) {
          setRecipeInfo(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      form.getFieldsValue(true),
    );
  };

  /**
   * Event handler when user selects a recipe version
   */
  const handleRecipeVersionSelection = () => {
    getRecipeInfo();
  };

  /**
   * Get the recipe data
   */
  const getRecipeData = () => {
    getRecipeDataApi(
      recipeType,
      (res) => {
        if (res.success) {
          setRecipeData(res.data);
          if (typeof setActiveRecipeName === "function") {
            setActiveRecipeName(res.data.recipe_name);
          }
          if (typeof setRecipeVersion === "function") {
            setRecipeVersion(res.data.version);
          }
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      form.getFieldsValue(true),
    );
  };

  /**
   * Handles the loading of selected recipe
   */
  const handleLoadRecipe = () => {
    form
      .validateFields()
      .then((values) => {
        if (fromExternalPage) {
          renderRecipePage({
            ...selectedParams,
            ...values,
          });
        } else {
          getRecipeData();
        }
        setIsModalOpen(false);
      })
      .catch((error) => {
        message.error(error.errorFields[0].errors[0], 5);
      });
  };

  /**
   * Event handler when users cancels the load recipe modal
   */
  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal
      title={`Load ${Helper.titlelize(recipeType)} Recipe`}
      open={isModalOpen}
      onOk={handleLoadRecipe}
      onCancel={closeModal}
      width={"40vw"}
      footer={[
        <Button key="back" onClick={closeModal}>
          Close
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleLoadRecipe}
          disabled={!form.getFieldValue("recipe_name")}
        >
          Load Recipe
        </Button>,
      ]}
    >
      <Form form={form} name="load_recipe" layout="vertical">
        <Row gutter={[16, 24]}>
          <Col span={8}>
            <Form.Item name="list_category" label="Select Recipes From">
              <Select
                placeholder="-"
                options={recipeTypeOptions}
                onSelect={handleRecipeTypeSelection}
              ></Select>
            </Form.Item>
          </Col>
          <Col span={13}>
            <Form.Item
              name="recipe_name"
              label="Recipe Name"
              rules={[
                {
                  required: true,
                  message: "Please select a recipe.",
                },
              ]}
            >
              <Select
                placeholder="Please select a recipe"
                options={recipeOptions}
                onSelect={handleRecipeSelection}
                showSearch
              ></Select>
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item name="recipe_version" label="Version">
              <Select
                placeholder="-"
                options={recipeVersionOptions}
                onSelect={handleRecipeVersionSelection}
              ></Select>
            </Form.Item>
          </Col>
        </Row>
        <Divider orientation="left">Recipe Information</Divider>
        <RecipeInfo recipeInfo={recipeInfo} />
      </Form>
    </Modal>
  );
};

export default LoadRecipeModal;
