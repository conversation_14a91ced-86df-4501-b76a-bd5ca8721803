"use client";

import { Collapse, List } from "antd";
import {
  MinusSquareFilled,
  CloseCircleFilled,
  PlusCircleFilled,
} from "@ant-design/icons";

const actionIcon = {
  "+": <PlusCircleFilled />,
  "-": <CloseCircleFilled />,
  "=": <MinusSquareFilled />,
};

/**
 * Displays recipe updates in a collapsible list with color-coded actions.
 *
 * @param {object} updatesMade
 * @returns {JSX.Element}
 */
const RecipeUpdatesMade = ({ updatesMade }) => {
  /**
   * Renders updates as list items, with recursion for nested data.
   *
   * @param {string} field - field
   * @param {array|object} data - Update data.
   * @param {number} level - Indentation level.
   * @returns {JSX.Element} Rendered list.
   */
  const renderUpdates = (field, data, level = 0) => {
    let element = <></>;
    if (Array.isArray(data)) {
      element = data.map((update, idx) => (
        <List.Item key={idx} style={{ marginLeft: `${level * 20}px` }}>
          <div
            className={
              update.action === "+"
                ? "text-[#389E0D]"
                : update.action === "-"
                  ? "text-[#FF4D4F]"
                  : "text-black"
            }
          >
            {actionIcon[update.action]}
            <span className="ml-1">{update.value}</span>
          </div>
        </List.Item>
      ));
    } else if (typeof data === "object") {
      if (field === "Checkboxes") {
        element = renderCheckboxUpdates(data);
      } else {
        element = Object.keys(data).map((key, idx) => (
          <div key={idx}>
            <List.Item className="font-bold">{key}:</List.Item>
            {renderUpdates(field, data[key], level + 1)}
          </div>
        ));
      }
    }
    return element;
  };

  /**
   * Handles checkbox fields render updates
   *
   * @param {array} data - Update data.
   * @returns {JSX.Element}
   */
  const renderCheckboxUpdates = (data) => {
    const categories = {
      retained: [],
      deselected: [],
      selected: [],
    };

    Object.keys(data).forEach((key) => {
      const checkboxUpdates = data[key];
      if (Array.isArray(checkboxUpdates)) {
        if (
          checkboxUpdates[0].action === "=" &&
          checkboxUpdates[0].value === "On"
        ) {
          categories.retained.push(key);
        } else if (
          checkboxUpdates[1]?.value === "On" &&
          checkboxUpdates[0].value === "Off"
        ) {
          categories.deselected.push(key);
        } else if (checkboxUpdates[0].value === "On") {
          categories.selected.push(key);
        }
      }
    });

    const hasInputs = Object.values(categories).some(
      (fields) => fields.length > 0,
    );

    return hasInputs ? (
      Object.entries(categories).map(
        ([key, fields]) =>
          fields.length > 0 && (
            <span key={key}>
              <List.Item className="font-bold">
                <div>
                  <span
                    className={`${key === "deselected" ? "text-[#FF4D4F]" : key === "selected" ? "text-[#389E0D]" : "text-black"}`}
                  >
                    {
                      actionIcon[
                        key === "retained"
                          ? "="
                          : key === "deselected"
                            ? "-"
                            : "+"
                      ]
                    }
                  </span>

                  <span className="ml-1">
                    {key.charAt(0).toUpperCase() + key.slice(1)}:
                  </span>
                </div>
              </List.Item>
              {fields.map((field, idx) => (
                <List.Item key={idx}>
                  <span
                    className={`ml-10 ${key === "deselected" ? "text-[#FF4D4F]" : key === "selected" ? "text-[#389E0D]" : "text-black"}`}
                  >
                    {field}
                  </span>
                </List.Item>
              ))}
            </span>
          ),
      )
    ) : (
      <div className="ml-4">No input provided.</div>
    );
  };

  // Create collapse items
  const collapseItems = Object.keys(updatesMade).map((field, index) => ({
    key: `${index}`,
    label: field,
    children: (
      <List size="small">{renderUpdates(field, updatesMade[field])}</List>
    ),
    updates: updatesMade[field],
  }));

  /**
   * Determines which collapse panels should be expanded.
   *
   * @returns {array} Expanded panel keys.
   */
  const activeKeys = collapseItems
    .filter((item) => {
      const updates = Array.isArray(item.updates)
        ? item.updates
        : Object.values(item.updates).flat();

      return updates.some(
        (update) => update.action === "+" || update.action === "-",
      );
    })
    .map((item) => item.key);

  return (
    <div className="max-h-[70vh] overflow-y-auto">
      <Collapse items={collapseItems} defaultActiveKey={activeKeys} />
    </div>
  );
};

export default RecipeUpdatesMade;
