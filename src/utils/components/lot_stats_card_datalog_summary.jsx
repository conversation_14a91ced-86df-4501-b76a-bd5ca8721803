import { LoadingOutlined } from "@ant-design/icons";
import { Col, notification, Row, Space, Table, Typography } from "antd";
import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useBoundStore } from "../../store/store";
import Api from "../api";
import Helper from "../helper";
import { QueryKeys } from "../query_keys";

const { Text } = Typography;
const goodColor = "#52C41A";
const failColor = "#FF4D4F";
const columns = [
  {
    title: "",
    dataIndex: "label",
    key: "label",
  },
  {
    title: "",
    dataIndex: "value",
    key: "value",
    align: "right",
    render: (value, _, index) => {
      let element = value;
      switch (index) {
        case 1:
          element = <Text style={{ color: goodColor }}>{value}</Text>;
          break;
        case 2:
          element = <Text style={{ color: failColor }}>{value}</Text>;
          break;
      }
      return element;
    },
  },
];

/**
 * Card component that displays datalog summary
 *
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function LotStatsCardDatalogSummary({
  component,
  filters,
  pageKey,
}) {
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState({
    indicator: <LoadingOutlined spin />,
  });
  const reloadQuantityData = useBoundStore((state) => state.reloadQuantityData);
  const params = component.props.params;

  /**
   * Get statistic data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise}
   */
  const getData = async ({ signal }) => {
    setLoading({
      indicator: <LoadingOutlined spin />,
    });
    if (filters[pageKey].exclude_dsk_data !== undefined) {
      filters[pageKey].exclude_dsk = filters[pageKey].exclude_dsk_data;
      delete filters[pageKey].exclude_dsk_data;
    }
    if (filters[pageKey].exclude_dsk === "") {
      delete filters[pageKey].exclude_dsk;
    }
    const requestParams = Helper.filterObjectByKeys(
      filters[pageKey],
      Object.keys(params.body_params),
    );
    const url = Helper.parseUrlEndpoint(params.url_endpoint, filters[pageKey]);

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Query to fetch statistics data
   */
  const dataQuery = useQuery({
    queryKey: QueryKeys.lot_stats(pageKey),
    queryFn: getData,
    enabled: true,
  });

  useEffect(() => {
    setDataSource([]);
    dataQuery.refetch();
  }, [reloadQuantityData]);

  useEffect(() => {
    if (dataQuery.isSuccess) {
      const response = dataQuery.data;
      if (response.success) {
        let rowData = [
          {
            key: 1,
            label: "Total Quantity",
            value: Helper.numberFormat(response.data.datalogged_parts, 0),
          },
          {
            key: 2,
            label: "Total Good Quantity",
            value: Helper.numberFormat(response.data.datalogged_good, 0),
          },
          {
            key: 3,
            label: "Total Fail Quantity",
            value: Helper.numberFormat(response.data.datalogged_fail, 0),
          },
          {
            key: 4,
            label: "Yield",
            value: `${Helper.numberFormat(response.data.datalogged_yield, 2)}%`,
          },
          {
            key: 5,
            label: "Site Yield Delta",
            value: `${Helper.numberFormat(response.data.site_yield_delta, 2)}%`,
          },
        ];
        setDataSource(rowData);
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      setLoading(false);
    }
  }, [dataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (dataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: dataQuery.error,
      });
    }
  }, [dataQuery.isError]);

  return (
    <Row>
      <Col span={24}>
        <Space direction="vertical">
          <div className="mb-4">
            <Text type="secondary" className="text-sm">
              {component.display_name}
            </Text>
          </div>
          <Table
            columns={columns}
            dataSource={dataSource}
            pagination={false}
            loading={loading}
            showHeader={false}
          />
        </Space>
      </Col>
    </Row>
  );
}
