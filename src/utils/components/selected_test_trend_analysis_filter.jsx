import { FileAddOutlined } from "@ant-design/icons";
import { Button, Form, Modal, Space } from "antd";
import { useState } from "react";
import SelectedTestTrendAnalysisFilterForm from "../forms/selected_test_trend_analysis_filter_form";

/**
 * Section to apply filters on trend analysis tab
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
const SelectedTestTrendAnalysisFilter = ({ pageKey }) => {
  const [
    isSelectedTestAnalysisFilterOpen,
    setIsSelectedTestAnalysisFilterOpen,
  ] = useState(false);
  const [selectedTestTrendAnalysisFilterForm] = Form.useForm();

  return (
    <Space size="middle" className="w-full my-2">
      <Modal
        width="40%"
        open={isSelectedTestAnalysisFilterOpen}
        onCancel={() => setIsSelectedTestAnalysisFilterOpen(false)}
        footer={null}
        centered
        closable
        forceRender
      >
        <SelectedTestTrendAnalysisFilterForm
          selectedTestTrendAnalysisFilterForm={
            selectedTestTrendAnalysisFilterForm
          }
          pageKey={pageKey}
          setIsSelectedTestAnalysisFilterOpen={
            setIsSelectedTestAnalysisFilterOpen
          }
        />
      </Modal>
      <Button
        type="primary"
        icon={<FileAddOutlined />}
        onClick={() => setIsSelectedTestAnalysisFilterOpen(true)}
      >
        New Trend Analysis
      </Button>
    </Space>
  );
};

export default SelectedTestTrendAnalysisFilter;
