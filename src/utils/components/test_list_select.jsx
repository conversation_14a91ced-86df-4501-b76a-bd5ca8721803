import { Space, Typography } from "antd";
import Api from "../api";
import SearchInput from "../forms/fields/search_input";

const { Text } = Typography;
const separator = " - ";

/**
 * Test list select component
 *
 * @param {object} apiParams
 * @param {string} searchValue
 * @param {boolean} shouldClearTestList
 * @param {function} setShouldClearTestList
 * @param {function} onChange
 * @param {function} onChangeTestListOptions
 * @param {object} props
 * @returns {JSX.Element}
 */
const TestListSelect = ({
  apiParams,
  searchValue,
  shouldClearTestList,
  setShouldClearTestList,
  onChange,
  onChangeTestListOptions,
  ...props
}) => {
  /**
   * Create test list option
   *
   * @param {object} option
   * @returns {JSX.Element}
   */
  const generateTestListOption = (option) => {
    const labelArr = option.data.label.split(separator);
    const program = labelArr.splice(0, 1)[0];
    const label = labelArr.join(separator);

    return (
      <Space.Compact direction="vertical">
        <Text>{label}</Text>
        <Text type="secondary">{program}</Text>
      </Space.Compact>
    );
  };

  /**
   * Generate API parameters
   *
   * @param {object} apiParams
   * @returns {object} params
   */
  const generateApiParameters = (apiParams) => {
    let params = {
      src_type: apiParams.src_type,
      src_value: Array.isArray(apiParams.src_value)
        ? apiParams.src_value.join(",")
        : apiParams.src_value,
      mfg_process: Array.isArray(apiParams.mfg_process)
        ? apiParams.mfg_process.join()
        : apiParams.mfg_process,
      test_type: apiParams.test_type,
      tnum_exclude: apiParams?.tnum_exclude ?? "",
      program: apiParams?.program ?? "",
    };
    if (apiParams.sort_by) {
      params.sort_by = apiParams.sort_by;
    }

    return params;
  };

  /**
   * Handles the onChange event
   */
  const handleOnChange = (value) => {
    if (typeof onChange === "function") {
      onChange(value);
    }
  };

  return (
    <SearchInput
      placeholder="-Select Test-"
      mode={props.mode ?? "single"}
      searchValue={searchValue}
      optionRender={(option) => generateTestListOption(option)}
      shouldClearOptions={shouldClearTestList}
      setShouldClearOptions={setShouldClearTestList}
      onChange={handleOnChange}
      onOptionsChange={onChangeTestListOptions}
      apiFunction={Api.getLotParametricTestListOptions}
      apiParams={generateApiParameters(apiParams)}
      {...props}
    />
  );
};

export default TestListSelect;
