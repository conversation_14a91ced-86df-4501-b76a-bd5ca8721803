import { LoadingOutlined } from "@ant-design/icons";
import { Col, notification, Row, Space, Spin, Typography } from "antd";
import { useEffect, useRef, useState } from "react";
import dynamic from "next/dynamic";
import { useQuery } from "@tanstack/react-query";
import { useBoundStore } from "../../store/store";
import Api from "../api";
import Helper from "../helper";
const Pie = dynamic(() => import("../charts/pie"), {
  ssr: false,
});
import { QueryKeys } from "../query_keys";

const { Text, Title } = Typography;

/**
 * Component names that will be affected by exclude_dsk_data filter
 * exclude_dsk_data filter will be set as exclude_dsk filter
 */
const excludeDskDataComponents = [
  "lot_stats_card_parts",
  "lot_stats_card_good",
  "lot_stats_card_yield",
  "lot_stats_card_fail",
];

/**
 * Card component that displays statistics data
 *
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function StatisticCard({ component, filters, pageKey }) {
  const [data, setData] = useState();
  const reloadQuantityData = useBoundStore((state) => state.reloadQuantityData);
  const chartRef = useRef();
  const chartKey = `${pageKey}_${component.component}_${component.id}`;
  const params = component.props.params;

  /**
   * Get statistic data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise}
   */
  const getData = async ({ signal }) => {
    if (
      filters[pageKey].exclude_dsk_data !== undefined &&
      excludeDskDataComponents.indexOf(component.name) !== -1
    ) {
      filters[pageKey].exclude_dsk = filters[pageKey].exclude_dsk_data;
      delete filters[pageKey].exclude_dsk_data;
    }
    if (filters[pageKey].exclude_dsk === "") {
      delete filters[pageKey].exclude_dsk;
    }
    const requestParams = Helper.filterObjectByKeys(
      filters[pageKey],
      Object.keys(params.body_params),
    );
    const url = Helper.parseUrlEndpoint(params.url_endpoint, filters[pageKey]);

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Query to fetch statistics data
   */
  const dataQuery = useQuery({
    queryKey: QueryKeys.lot_stats(pageKey),
    queryFn: getData,
    enabled: true,
  });

  useEffect(() => {
    setData();
    dataQuery.refetch();
  }, [reloadQuantityData]);

  useEffect(() => {
    if (dataQuery.isSuccess) {
      const response = dataQuery.data;
      if (response.success) {
        const value = formatValue(response.data[component.data_key]);
        if (component.chart && component.chart.type === "pie") {
          component.chart.component.data = generatePieData(
            response.data[component.data_key],
          );
        }
        setData(value);
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
    }
  }, [dataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (dataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: dataQuery.error,
      });
    }
  }, [dataQuery.isError]);

  /**
   * Create pie data
   *
   * @param {number} value
   * @returns {array} data
   */
  const generatePieData = (value) => {
    const data = [
      {
        y: parseFloat(value),
      },
      {
        y: 100 - parseFloat(value),
        color: "#FF0000",
      },
    ];

    return data;
  };

  /**
   * Format value based on data type
   *
   * @param {*} value
   * @returns {string} formattedValue
   */
  const formatValue = (value) => {
    let formattedValue = value;
    switch (component.data_type) {
      case "numeric":
        formattedValue = Helper.numberFormat(value, 0);
        break;
      case "percentage":
        formattedValue = Helper.percentFormat(value, 2);
        break;
    }

    return formattedValue;
  };

  return (
    <Row>
      <Col span={24}>
        <Row>
          <Col span={component.chart ? 18 : 24}>
            <Space direction="vertical">
              <Text type="secondary">{component.display_name}</Text>
              {data ? (
                <Title
                  level={3}
                  type={component.content_type ? component.content_type : null}
                >
                  {data}
                </Title>
              ) : (
                <Spin size="large" indicator={<LoadingOutlined spin />} />
              )}
            </Space>
          </Col>
          {component.chart &&
            component.chart.component.data &&
            component.chart.type === "pie" && (
              <Col span={6}>
                <Pie
                  chartRef={chartRef}
                  component={component.chart.component}
                  filters={filters}
                  pageKey={pageKey}
                  chartKey={chartKey}
                />
              </Col>
            )}
        </Row>
      </Col>
    </Row>
  );
}
