import { App, Col, Row, Typography } from "antd";
import React, { useEffect, useState } from "react";
import Api from "../api";
import Helper from "../helper";
import { ComponentNameMapper } from "../grid/component_name_mapper";
import { useEffectApiFetch } from "../../hooks";
import ChartHelper from "../charts/chart_helper";
import { useBoundStore } from "../../store/store";
import { ChartspaceChart } from "./chartspace_chart";

/**
 * Display of per datalog scatter charts
 *
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {object} chartCustomData
 * @param {function} testStatsInfoHandler
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default function PerDatalogScatterCharts({
  component,
  filters,
  pageKey,
  chartCustomData,
  testStatsInfoHandler,
  prerenderData = {},
}) {
  const [datalogs, setDatalogs] = useState([]);
  const [chartComponentBlueprint, setChartComponentBlueprint] = useState();
  const [perDatalogScatterCharts, setPerDatalogScatterCharts] = useState([]);
  const chartKeys = useBoundStore((state) => state.chartKeys);
  const { message } = App.useApp();
  const { Title } = Typography;

  useEffectApiFetch(
    () => {
      if (chartComponentBlueprint) {
        return Api.getDatalogInfo(
          (res) => {
            if (res.success) {
              setDatalogs(res.data);
            } else {
              message.warning("Failed in getting datalog info.");
            }
          },
          (err) => {
            message.error(`Error in getting datalog info. ${err}`);
          },
          {
            dsk: filters[pageKey].src_value,
          },
        );
      }
    },
    () => {
      setDatalogs([]);
    },
    [chartComponentBlueprint],
  );

  useEffectApiFetch(
    () => {
      return Api.getComponentBlueprint(
        (res) => {
          if (res.success) {
            setChartComponentBlueprint(res.data);
          } else {
            message.warning(res.message, 5);
          }
        },
        (err) => {
          message.error(err, 5);
        },
        {
          name: ComponentNameMapper.lot_parametric_scatter,
        },
      );
    },
    () => {
      setChartComponentBlueprint();
    },
  );

  useEffect(() => {
    if (chartComponentBlueprint) {
      renderPerDatalogScatterCharts(chartComponentBlueprint, datalogs);
    }
  }, [datalogs]);

  /**
   * Add chart object to per datalog scatter charts
   *
   * @param {object} chartComponentBlueprint
   * @param {array} datalogs
   */
  const renderPerDatalogScatterCharts = (chartComponentBlueprint, datalogs) => {
    let charts = {};
    const prerenderDataStr = JSON.stringify(prerenderData);
    datalogs.forEach((datalog) => {
      const chartKey = `${component.name}_${chartComponentBlueprint.name}_${datalog.data_struc_key}_${prerenderDataStr}`;
      const chartComponentBlueprintCopy = Helper.cloneObject(
        chartComponentBlueprint,
      );
      chartComponentBlueprintCopy.id = chartKey;
      chartComponentBlueprintCopy.props.params.body_params.test_number =
        prerenderData.tnum;
      charts[chartKey] = {
        key: chartKey,
        chartType: "scatter",
        chartKey: chartKey,
        chartCustomData: chartCustomData,
        filters: filters,
        pageKey: pageKey,
        component: chartComponentBlueprintCopy,
        chartFilters: {
          src_type: "dsk",
          src_value: datalog.data_struc_key?.toString(),
        },
        testStatsInfoHandler: testStatsInfoHandler,
        title: `${datalog.lot_id} # ${datalog.wir_wafer_id}`,
        prerenderData: prerenderData,
        showBoostWarning: true,
      };

      ChartHelper.storeChartKey(chartKey, chartKeys, pageKey, component.name);
    });

    setPerDatalogScatterCharts(charts);
  };

  return (
    <div>
      <div className="p-2">
        {Object.keys(perDatalogScatterCharts).map((chartKey, i) => {
          return (
            <Row
              key={`per_datalog_scatter_chart_${chartKey}_wrapper`}
              className={i > 0 ? "mt-4" : ""}
              gutter={[16, 16]}
            >
              <Col className="text-center" span={24}>
                <Title level={3}>
                  {perDatalogScatterCharts[chartKey].title}
                </Title>
                <div
                  id={`boosted_warning_msg_wrapper_${perDatalogScatterCharts[chartKey].component.id}`}
                ></div>
                <div className="border border-solid border-black/[.25]">
                  {React.createElement(
                    ChartspaceChart,
                    perDatalogScatterCharts[chartKey],
                  )}
                </div>
              </Col>
            </Row>
          );
        })}
      </div>
    </div>
  );
}
