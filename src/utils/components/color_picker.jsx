import { <PERSON><PERSON>, <PERSON>, Pop<PERSON>, Row, theme, Tooltip } from "antd";

const { useToken } = theme;
const defaultColors = [
  "#0054A6",
  "#AEBBD8",
  "#004180",
  "#2092D8",
  "#B5D6F0",
  "#19699C",
  "#00A1AF",
  "#B4DCE1",
  "#00717B",
  "#3FA535",
  "#BDDDB5",
  "#2E7726",
  "#8FBB1C",
  "#D5E5AF",
  "#6B8B14",
  "#BB941C",
  "#E6D7AE",
  "#917316",
  "#FFC700",
  "#FFEAAB",
  "#C79C01",
  "#EB660D",
  "#F9C6A7",
  "#C6560B",
  "#CE2903",
  "#F1AF9F",
  "#A02002",
  "#A03058",
  "#DDAEC2",
  "#73233F",
  "#A91D9E",
  "#E1A2DB",
  "#7E177C",
  "#4E138C",
  "#C1A1D4",
  "#390E67",
  "#929288",
  "#D6D6D2",
  "#6B6B63",
];

/**
 * Color picker component
 *
 * @param {string} color
 * @param {array} presetColors
 * @param {function} onClickColor
 * @param {string} swatchTooltip
 * @param {string} colorPickerTitle
 * @returns {JSX.Element}
 */
export default function ColorPicker({
  color,
  presetColors = defaultColors,
  onClickColor,
  swatchTooltip = "",
  colorPickerTitle,
}) {
  const { token } = useToken();

  /**
   * Color picker selection
   *
   * @returns {JSX.Element}
   */
  const colorPicker = () => {
    return (
      <Row className="w-52">
        {presetColors.map((presetColor) => {
          return (
            <Col key={`col_${presetColor}`} span={4}>
              <Button
                key={`color_${presetColor}`}
                className="picker-swatch inline-block"
                style={{
                  background: presetColor,
                }}
                onClick={() => onClickColor(presetColor)}
              />
            </Col>
          );
        })}
      </Row>
    );
  };

  return (
    <Popover content={colorPicker} title={colorPickerTitle} trigger="click">
      <div className="w-max">
        <Tooltip title={swatchTooltip}>
          <div
            key="color_picker_swatch"
            className="picker-swatch"
            style={{
              backgroundColor: color,
              width: token.controlHeightSM,
              height: token.controlHeightSM,
            }}
          />
        </Tooltip>
      </div>
    </Popover>
  );
}
