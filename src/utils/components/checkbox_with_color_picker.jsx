"use client";

import { Checkbox, ColorPicker, Form, Flex } from "antd";
import { blue, green, presetPalettes, red } from "@ant-design/colors";
import { useState } from "react";

/**
 * Generate preset colors
 *
 * @param {object} presets
 * @returns {array}
 */
const genPresetColors = (presets = presetPalettes) =>
  Object.entries(presets).map(([label, colors]) => ({
    label,
    colors,
  }));

const presets = genPresetColors({
  blue,
  red,
  green,
});

/**
 * Checkbox with color picker component
 *
 * @param {string} inputName
 * @param {string} inputLabel
 * @returns {JSX.Element}
 */
const CheckboxWithColorPicker = ({ inputName, inputLabel, ...props }) => {
  const [colorValue, setColorValue] = useState("#ffffff");
  const [isColorPickerDisabled, setIsColorPickerDisabled] = useState(true);

  return (
    <Flex align="center" {...props}>
      <Form.Item name={inputName} valuePropName="checked" noStyle>
        <Checkbox
          onChange={(e) => {
            setIsColorPickerDisabled(!e.target.checked);
          }}
        >
          {inputLabel}
        </Checkbox>
      </Form.Item>
      <Form.Item
        name={`${inputName}_color`}
        getValueFromEvent={(color) => color.toHexString()}
      >
        <ColorPicker
          className="ml-1"
          onChangeComplete={(color) => {
            setColorValue(color.toHexString());
          }}
          value={colorValue}
          presets={presets}
          trigger="hover"
          disabled={isColorPickerDisabled}
        ></ColorPicker>
      </Form.Item>
    </Flex>
  );
};

export default CheckboxWithColorPicker;
