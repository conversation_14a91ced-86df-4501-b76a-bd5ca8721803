import { FileAddOutlined } from "@ant-design/icons";
import { Button, Form, Modal, Space } from "antd";
import { useState } from "react";
import SelectedTestAnalysisFilterForm from "../forms/selected_test_analysis_filter_form";

/**
 * Section to apply filters on all tests table
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
const SelectedTestAnalysisFilter = ({ pageKey }) => {
  const [
    isSelectedTestAnalysisFilterOpen,
    setIsSelectedTestAnalysisFilterOpen,
  ] = useState(false);
  const [selectedTestAnalysisFilterForm] = Form.useForm();
  const [allTestsFilter] = Form.useForm();

  return (
    <>
      <Form
        form={allTestsFilter}
        className="flex flex-col h-full"
        name="all_tests_filter"
        layout="vertical"
      >
        <Space size="middle" className="w-full my-2">
          <Modal
            width="40%"
            open={isSelectedTestAnalysisFilterOpen}
            onCancel={() => setIsSelectedTestAnalysisFilterOpen(false)}
            footer={[]}
            centered
            closable
            forceRender
          >
            <SelectedTestAnalysisFilterForm
              selectedTestAnalysisFilterForm={selectedTestAnalysisFilterForm}
              pageKey={pageKey}
              setIsSelectedTestAnalysisFilterOpen={
                setIsSelectedTestAnalysisFilterOpen
              }
            />
          </Modal>
          <Button
            type="primary"
            icon={<FileAddOutlined />}
            onClick={() => setIsSelectedTestAnalysisFilterOpen(true)}
          >
            New Analysis
          </Button>
        </Space>
      </Form>
    </>
  );
};

export default SelectedTestAnalysisFilter;
