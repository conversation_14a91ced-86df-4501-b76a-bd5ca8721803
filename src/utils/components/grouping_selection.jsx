"use client";

import { <PERSON><PERSON>, Form } from "antd";
import { useEffect, useState } from "react";
import { useBoundStore } from "../../store/store";
import FilterSelect from "../grid/components/filter_select";
import Helper from "../helper";

/**
 * Grouping selection component
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function GroupingSelection({ pageKey }) {
  const [groupingOptions, setGroupingOptions] = useState([]);
  const urlParams = useBoundStore((state) => state.urlParams);
  const [groupingSelectionForm] = Form.useForm();

  useEffect(() => {
    const grouping = urlParams[pageKey].group_by ?? "";
    const groupingOption = Helper.filterArrayAndFind(
      groupingOptions,
      "value",
      grouping,
    );
    groupingSelectionForm.setFieldValue("group_by", {
      value: grouping,
      label: groupingOption?.label ?? "",
    });
  }, [groupingOptions]);

  return (
    <Form
      form={groupingSelectionForm}
      layout="inline"
      name={`${pageKey}_grouping_selection_form`}
    >
      <Form.Item name="group_by" label="Grouped by">
        <FilterSelect
          componentKey="select_aggregate_options"
          placeholder="-Select-"
          labelInValue={true}
          setOptions={setGroupingOptions}
          params={{
            api: {
              url: "api/v1/internal/options/list/aggregate_options",
              mfg_process: urlParams[pageKey].mfg_process,
              cache_it: 0,
            },
            allOption: {
              label: "Grouped as one",
              value: "",
            },
          }}
        />
      </Form.Item>
      <Form.Item>
        <Button type="primary" htmlType="submit">
          Apply
        </Button>
      </Form.Item>
    </Form>
  );
}
