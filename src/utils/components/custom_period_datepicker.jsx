import { DatePicker, Flex, Form, Input, Radio, Space } from "antd";
import { useEffect, useState } from "react";
import Helper from "../helper";

const { RangePicker } = DatePicker;

/**
 * Custom period date picker component
 *
 * @param {Form} customPeriodForm
 * @param {function} setDisableCustomPeriodUpdate
 * @returns {JSX.Element}
 */
export default function CustomPeriodDatePicker({
  customPeriodForm,
  setDisableCustomPeriodUpdate,
}) {
  const [periodType, setPeriodType] = useState("date");

  useEffect(() => {
    toggleCustomPeriodUpdate();
  }, [periodType]);

  /**
   * Toggle custom period apply button disabled state
   */
  const toggleCustomPeriodUpdate = () => {
    const disabled =
      (periodType === "date" && !customPeriodForm.getFieldValue("date")) ||
      (periodType === "range" && !customPeriodForm.getFieldValue("range"));
    setDisableCustomPeriodUpdate(disabled);
  };

  return (
    <Form form={customPeriodForm}>
      <Flex justify="center">
        <Flex vertical={true}>
          <Form.Item name="period_type" initialValue={"date"}>
            <Radio.Group
              onChange={(e) => setPeriodType(e.target.value)}
              value={periodType}
            >
              <Space>
                <Radio value="date">Specific Date</Radio>
                <Radio value="range">Date Range</Radio>
              </Space>
            </Radio.Group>
          </Form.Item>
          {periodType === "date" ? (
            <Form.Item name="date">
              <DatePicker
                className="w-60"
                disabledDate={Helper.disabledFutureDate}
                onChange={() => toggleCustomPeriodUpdate()}
              />
            </Form.Item>
          ) : (
            <Form.Item name="range">
              <RangePicker
                className="w-60"
                disabledDate={Helper.disabledFutureDate}
                onChange={() => toggleCustomPeriodUpdate()}
              />
            </Form.Item>
          )}
          <Form.Item name="period_type" noStyle>
            <Input type="hidden" value={periodType} />
          </Form.Item>
        </Flex>
      </Flex>
    </Form>
  );
}
