import { LoadingOutlined } from "@ant-design/icons";
import { App, Empty, Spin, Tabs, Typography } from "antd";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useBoundStore } from "../../store/store";
import Api from "../api";
import Helper from "../helper";
import { useEffectApiFetch } from "../../hooks";
import { ComponentNameMapper } from "../grid/component_name_mapper";
import WaferMapGalleryOptions from "./wafer_map_gallery_options";
import ChartsGalleryCarouselView from "./charts_gallery_carousel_view";
import ChartsGalleryGridView from "./charts_gallery_grid_view";

const { Title } = Typography;

/**
 * Display of wafer map gallery
 *
 * @param {object} filters
 * @param {string} pageKey
 * @param {object} chartCustomData
 * @param {function} testStatsInfoHandler
 * @returns {JSX.Element}
 */
export default function WaferMapGallery({
  filters,
  pageKey,
  chartCustomData,
  testStatsInfoHandler,
}) {
  const [chartComponent, setChartComponent] = useState();
  const [galleryChartsData, setGalleryChartsData] = useState([]);
  const [loadingCharts, setLoadingCharts] = useState(true);
  const [galleryView, setGalleryView] = useState("grid");
  const [galleryTitle, setGalleryTitle] = useState("");
  const [groups, setGroups] = useState([]);
  const [galleryOptions, setGalleryOptions] = useState([]);
  const urlParams = useBoundStore((state) => state.urlParams);
  const galleryCharts = useBoundStore((state) => state.galleryCharts);
  const setGalleryCharts = useBoundStore((state) => state.setGalleryCharts);
  const { message } = App.useApp();
  const galleryOptionsRef = useRef();
  const binsToHighlight = useRef([]);
  const highligthedBins = useRef({});
  const chartsPerRow = 3;

  const galleryTabItems = useMemo(() => {
    return [
      {
        key: "grid",
        label: "",
        children: (
          <ChartsGalleryGridView
            chartsData={galleryChartsData.grid}
            chartCustomData={chartCustomData}
            chartsPerRow={chartsPerRow}
            galleryCharts={galleryCharts}
            setGalleryCharts={setGalleryCharts}
            galleryView="grid"
          />
        ),
      },
      {
        key: "carousel",
        label: "",
        children: (
          <ChartsGalleryCarouselView
            pageKey={pageKey}
            chartsData={galleryChartsData.carousel}
            chartCustomData={chartCustomData}
            galleryCharts={galleryCharts}
            setGalleryCharts={setGalleryCharts}
            galleryOptionsRef={galleryOptionsRef}
            galleryView="carousel"
          />
        ),
      },
    ];
  }, [galleryChartsData]);

  /**
   * Get wafer map groups
   */
  const getGroups = async () => {
    const params = {
      mfg_process: urlParams[pageKey].mfg_process,
      src_type: urlParams[pageKey].src_type,
      src_value: urlParams[pageKey].src_value,
    };
    if (urlParams[pageKey].group_by) {
      params.group_by = urlParams[pageKey].group_by;
    }

    Api.getGroupBreakdown(
      (res) => {
        if (res.success) {
          setGroups(res.data);
        } else {
          message.warning("Failed in getting group breakdown data.");
          setLoadingCharts(false);
        }
      },
      (err) => {
        message.error(`Error in getting group breakdown data. ${err}`);
      },
      params,
    );
  };

  useEffectApiFetch(
    () => {
      getChartComponent();
    },
    () => {
      setChartComponent();
    },
    [],
  );

  useEffect(() => {
    if (chartComponent) {
      switch (urlParams[pageKey].component_name) {
        case ComponentNameMapper.lot_bin_map:
          setGalleryTitle("Bin Map");
          setGalleryOptions(["highlightBins"]);
          break;
        case ComponentNameMapper.parametric_mpr_pin_yield_map:
          setGalleryTitle("Yield Map");
          break;
        case ComponentNameMapper.parametric_mpr_mean_pin_substrate_map:
          setGalleryTitle("Mean Parametric Pin Substrate Map");
          break;
      }
      getGroups();
    }
  }, [chartComponent]);

  useEffect(() => {
    renderGalleryCharts(groups);
  }, [groups]);

  useEffect(() => {
    if (!galleryCharts[pageKey]) {
      galleryCharts[pageKey] = {};
      highligthedBins.current[pageKey] = {};
    }
    if (!galleryCharts[pageKey][galleryView]) {
      galleryCharts[pageKey][galleryView] = {};
      highligthedBins.current[pageKey][galleryView] = [];
    }
    setGalleryCharts(galleryCharts);
  }, [galleryView]);

  /**
   * Get and set chart component
   *
   * @returns {AbortController} abortCtl
   */
  const getChartComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setChartComponent(res.data);
        } else {
          message.warning(res.message, 5);
          setLoadingCharts(false);
        }
      },
      (err) => {
        message.error(err, 5);
        setLoadingCharts(false);
      },
      {
        name: urlParams[pageKey].component_name,
      },
    );

    return abortCtl;
  };

  /**
   * Render charts to gallery
   *
   * @param {object} allChartData
   */
  const renderGalleryCharts = (groups) => {
    let galleryChartsDataCopy = {};
    let chartComponentCopy;
    let chartKey;
    const chartControls = [{ key: "tooltip" }, { key: "orientationSettings" }];
    const galleryViews = galleryTabItems.map((item) => {
      return item.key;
    });

    Object.values(groups).forEach((group) => {
      const dsklist = Array.isArray(group.dsk)
        ? group.dsk.join(",")
        : group.dsk;
      chartComponentCopy = Helper.cloneObject(chartComponent);
      chartComponentCopy.props.chart_controls = chartControls;
      let chartFilters = {};
      if (
        urlParams[pageKey].component_name === ComponentNameMapper.lot_bin_map
      ) {
        chartFilters.wafer_id_selection = { label: "", value: dsklist };
      }

      galleryViews.forEach((view) => {
        if (!galleryChartsDataCopy[view]) {
          galleryChartsDataCopy[view] = {};
        }
        chartKey = generateGalleryChartKey(view, dsklist);
        galleryChartsDataCopy[view][chartKey] = {
          dsk: dsklist,
          key: chartKey,
          chartKey: chartKey,
          filters: filters,
          pageKey: pageKey,
          component: chartComponentCopy,
          chartFilters: chartFilters,
          label: group.label ?? chartFilters.wafer_id_selection?.label ?? "",
          testStatsInfoHandler: testStatsInfoHandler,
        };
      });
    });
    setGalleryChartsData(galleryChartsDataCopy);
    setLoadingCharts(false);
  };

  /**
   * Generate gallery chart key
   *
   * @param {string} view
   * @param {string} dsk
   * @returns {string} chartKey
   */
  const generateGalleryChartKey = (view, dsk) => {
    const chartKey = `gallery_chart_${view}_${dsk}`;

    return chartKey;
  };

  /**
   * Highlight selected bins in charts
   *
   * @param {array} selectedBins
   */
  const highlightBins = (selectedBins) => {
    Object.values(galleryCharts[pageKey][galleryView]).forEach((chart) => {
      chart.legend.allItems.forEach((item) => {
        item.setVisible(
          selectedBins.includes(item?.userOptions?.custom?.bin?.toString()) ||
            selectedBins.length === 0,
          false,
        );
      });
      chart.redraw();
    });
    binsToHighlight.current = selectedBins;
    highligthedBins.current[pageKey][galleryView] = selectedBins;
  };

  /**
   * Render custom tab bar
   *
   * @param {object} props
   * @param {React.ReactElement} DefaultTabBar
   * @returns {React.ReactElement}
   */
  const renderTabBar = (props, DefaultTabBar) => (
    <DefaultTabBar {...props} className="hidden!" />
  );

  return (
    <div>
      <div ref={galleryOptionsRef}>
        <WaferMapGalleryOptions
          pageKey={pageKey}
          highlightBins={highlightBins}
          highlightedBins={highligthedBins.current[pageKey]}
          setGalleryView={setGalleryView}
          galleryView={galleryView}
          galleryOptions={galleryOptions}
        />
        <Title level={4} className="text-center mb-4!">
          {galleryView === "carousel"
            ? `${galleryTitle} Gallery View`
            : `${galleryTitle} Grid View`}
        </Title>
      </div>
      {loadingCharts ? (
        <Spin
          size="large"
          indicator={<LoadingOutlined spin />}
          className=" w-full text-center"
        />
      ) : Object.keys(galleryChartsData[galleryView] ?? {}).length > 0 ? (
        <Tabs
          renderTabBar={renderTabBar}
          items={galleryTabItems}
          activeKey={galleryView}
        />
      ) : (
        <Empty description="No chart to display." />
      )}
    </div>
  );
}
