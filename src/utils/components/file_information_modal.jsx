import { App, Col, <PERSON>, Space, Typography, Button, Modal } from "antd";
import { useRef, useState } from "react";
import { useEffectApiFetch } from "../../hooks";
import Api from "../api";
import YHGrid from "../grid/yh_grid";
import Helper from "../helper";

const { Text } = Typography;

/**
 * File information modal
 *
 * @param {boolean} isFileInformationModalOpen
 * @param {function} setIsFileInformationModalOpen
 * @param {object} fileInfo
 * @returns {JSX.Element}
 */
const FileInformationModal = ({
  isFileInformationModalOpen,
  setIsFileInformationModalOpen,
  fileInfo,
}) => {
  const {
    fileName,
    lotId,
    dataloggedParts,
    dataloggedGood,
    mainOperation,
    destinationDir,
    dataStrucKey,
    datalogType,
  } = fileInfo;
  const datalogFileHistoryGridRef = useRef();
  const [datalogFileHistoryGridComponent, setDatalogFileHistoryGridComponent] =
    useState();
  const { message } = App.useApp();

  useEffectApiFetch(
    () => {
      return getDatalogFileHistoryGridComponent();
    },
    () => {
      setDatalogFileHistoryGridComponent();
    },
  );

  /**
   * Get and set datalog file history grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getDatalogFileHistoryGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setDatalogFileHistoryGridComponent(res.data);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      {
        name: "file_event_history",
      },
    );

    return abortCtl;
  };

  return (
    <Modal
      title={<Typography.Title level={4}>File Information</Typography.Title>}
      width={"45%"}
      open={isFileInformationModalOpen}
      destroyOnHidden
      onCancel={() => {
        setIsFileInformationModalOpen(false);
      }}
      footer={[
        <Button
          key="back"
          onClick={() => {
            setIsFileInformationModalOpen(false);
          }}
        >
          Close
        </Button>,
      ]}
    >
      {" "}
      <>
        <Row>
          <Col span={5}>
            <Space className="flex justify-end">
              <Text className="mr-3" strong>
                Filename:
              </Text>
            </Space>
          </Col>
          <Col span={16}>
            <Space>
              <Text>{fileName}</Text>
            </Space>
          </Col>
        </Row>
        {datalogType !== "non_datalog" && (
          <>
            <Row>
              <Col span={5}>
                <Space className="flex justify-end">
                  <Text className="mr-3" strong>
                    Lot ID:
                  </Text>
                </Space>
              </Col>
              <Col span={16}>
                <Space>
                  <Text>{lotId ?? ""}</Text>
                </Space>
              </Col>
            </Row>
            <Row>
              <Col span={5}>
                <Space className="flex justify-end">
                  <Text className="mr-3" strong>
                    Quantity In:
                  </Text>
                </Space>
              </Col>
              <Col span={16}>
                <Space>
                  <Text>{dataloggedParts ?? ""}</Text>
                </Space>
              </Col>
            </Row>
            <Row>
              <Col span={5}>
                <Space className="flex justify-end">
                  <Text className="mr-3" strong>
                    Quantity Out:
                  </Text>
                </Space>
              </Col>
              <Col span={16}>
                <Space>
                  <Text>{dataloggedGood ?? ""}</Text>
                </Space>
              </Col>
            </Row>
          </>
        )}
        <Row>
          <Col span={5}>
            <Space className="flex justify-end">
              <Text className="mr-3" strong>
                Latest File Status:
              </Text>
            </Space>
          </Col>
          <Col span={16}>
            <Space>
              <Text>{mainOperation}</Text>
            </Space>
          </Col>
        </Row>
        <Row>
          <Col span={5}>
            <Space className="flex justify-end">
              <Text className="mr-3" strong>
                File Location:
              </Text>
            </Space>
          </Col>
          <Col span={16}>
            <Space>
              <Text>{destinationDir}</Text>
            </Space>
          </Col>
        </Row>
        <Row>
          <Col span={5} className="flex justify-end">
            <Space className="self-start">
              <Text className="mr-3" strong>
                Download Option:
              </Text>
            </Space>
          </Col>
          <Col span={16}>
            <Space direction="vertical" style={{ gap: 0 }}>
              {/* <Button
              className="p-0 mt-[-3px]"
              type="link"
              onClick={() => {
                Helper.downloadDatalog(fileName);
              }}
            >
              <u>Download datalog in .ZIP format</u>
            </Button> */}
              <Button
                className="p-0 mt-[-4px]"
                type="link"
                onClick={() => {
                  Helper.downloadDatalog(
                    dataStrucKey.toString(),
                    "raw",
                    fileName,
                  );
                }}
              >
                <u>Download datalog in .GZ format</u>
              </Button>
            </Space>
          </Col>
        </Row>
        {/* Row height is based from fixed component.props.settings.height + footer button height */}
        <Row className="h-[350px] mt-3">
          <Col span={24}>
            {datalogFileHistoryGridComponent && (
              <YHGrid
                gridRef={datalogFileHistoryGridRef}
                gridId={datalogFileHistoryGridComponent.name}
                component={datalogFileHistoryGridComponent}
                filters={{}}
                initialGridFilters={{ file_name: fileName }}
                rowGroups={[]}
                wrapperClassName="flex grow flex-col h-full"
              />
            )}
          </Col>
        </Row>
      </>
    </Modal>
  );
};

export default FileInformationModal;
