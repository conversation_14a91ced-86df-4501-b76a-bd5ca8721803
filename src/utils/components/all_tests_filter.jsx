import { FilterOutlined } from "@ant-design/icons";
import { App, Button, Form, Modal, Space, Tag, theme, Typography } from "antd";
import { useEffect, useState } from "react";
import { useBoundStore } from "../../store/store";
import Helper from "../helper";
import AllTestsFilterForm from "../forms/all_tests_filter_form";
import { AllTestsFilterFieldsMapper } from "../forms/mappers/all_tests_filter_fields_mapper";

const { Text } = Typography;
const { useToken } = theme;

/**
 * Section to apply filters on all tests table
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
const AllTestsFilter = ({ pageKey }) => {
  const [isAllTestsFilterOpen, setIsAllTestsFilterOpen] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState([]);
  const [displayedAppliedFilters, setDisplayedAppliedFilters] = useState([]);
  const [siteListOptions, setSiteListOptions] = useState([]);
  const setReloadGrids = useBoundStore((state) => state.setReloadGrids);
  const setReloadGrid = useBoundStore((state) => state.setReloadGrid);
  const setReloadGridFilters = useBoundStore(
    (state) => state.setReloadGridFilters,
  );
  const reloadGridFilters = useBoundStore((state) => state.reloadGridFilters);
  const [allTestsFilterForm] = Form.useForm();
  const [allTestsFilter] = Form.useForm();
  const { message } = App.useApp();
  const { token } = useToken();
  const reloadGridKey = "lot_all_tests";

  const allTestsFilterInitialValues = {
    stats_type: "rp",
    site: "255",
  };

  const optionsList = {
    siteListOptions,
  };

  useEffect(() => {
    if (reloadGridFilters[reloadGridKey] === undefined) {
      reloadGridFilters[reloadGridKey] = {};
    }
    updateAppliedFilters(reloadGridFilters[reloadGridKey]);
  }, [reloadGridFilters[reloadGridKey]]);

  useEffect(() => {
    loadFiltersToForm(reloadGridFilters);
    setDisplayedAppliedFilters(appliedFilters);
  }, [appliedFilters]);

  /**
   * Update applied filters display
   *
   * @param {object} gridFilters
   */
  const updateAppliedFilters = (gridFilters) => {
    let appliedFiltersArr = [];
    let values = [];
    Object.keys(gridFilters).forEach((key) => {
      let options = [];
      if (AllTestsFilterFieldsMapper[key]) {
        switch (AllTestsFilterFieldsMapper[key].type) {
          case "checkbox":
            appliedFiltersArr.push({
              key: key,
              fieldKey: key,
              value: gridFilters[key],
              valueText: AllTestsFilterFieldsMapper[key].label,
            });
            break;
          case "checkbox.group":
            gridFilters[key].split(",").forEach((value) => {
              appliedFiltersArr.push({
                key: value,
                fieldKey: key,
                value: value,
                valueText: AllTestsFilterFieldsMapper[value]
                  ? AllTestsFilterFieldsMapper[value].label
                  : value,
              });
            });
            break;
          case "select.multiple":
            values = gridFilters[key]
              .split(",")
              .map((value) => {
                return typeof value === "object" ? value.label : value;
              })
              .join(", ");

            if (AllTestsFilterFieldsMapper[key].optionsList) {
              options =
                optionsList[AllTestsFilterFieldsMapper[key].optionsList];
              const valuesArr = values.split(", ");
              if (
                valuesArr.length === options.length &&
                AllTestsFilterFieldsMapper[key].selectedAllLabel
              ) {
                values = AllTestsFilterFieldsMapper[key].selectedAllLabel;
              } else {
                values = valuesArr
                  .map((value) => {
                    const option = Helper.filterArrayAndFind(
                      options,
                      "value",
                      value,
                    );
                    return option && option.label ? option.label : value;
                  })
                  .join(", ");
              }
            }

            appliedFiltersArr.push({
              key: key,
              fieldKey: key,
              label: AllTestsFilterFieldsMapper[key].label,
              value: gridFilters[key],
              valueText: values,
            });
            break;
          default:
            appliedFiltersArr.push({
              key: key,
              fieldKey: key,
              label: AllTestsFilterFieldsMapper[key].label,
              value: gridFilters[key],
              valueText: AllTestsFilterFieldsMapper[gridFilters[key]]
                ? AllTestsFilterFieldsMapper[gridFilters[key]].label
                : gridFilters[key],
              required: AllTestsFilterFieldsMapper[key].required === true,
            });
        }
      }
    });
    setAppliedFilters(appliedFiltersArr);
  };

  /**
   * Load filters to form fields
   *
   * @param {object} reloadGridFilters
   */
  const loadFiltersToForm = (reloadGridFilters) => {
    let reloadGridFiltersCopy = Helper.cloneObject(reloadGridFilters);
    let fieldValue;
    let shouldSetFilters = false;
    Object.keys(AllTestsFilterFieldsMapper).forEach((fieldKey) => {
      switch (AllTestsFilterFieldsMapper[fieldKey].type) {
        case "select.multiple":
        case "checkbox.group":
          fieldValue =
            reloadGridFiltersCopy[reloadGridKey][fieldKey] !== undefined
              ? reloadGridFiltersCopy[reloadGridKey][fieldKey].split(",")
              : [];
          allTestsFilterForm.setFieldValue(fieldKey, fieldValue);
          if (fieldValue.length === 0) {
            fieldValue = allTestsFilterInitialValues[fieldKey];
            if (Array.isArray(fieldValue) && fieldValue.length > 0) {
              reloadGridFiltersCopy[reloadGridKey][fieldKey] =
                fieldValue.join();
            }
          }
          break;
        default:
          fieldValue =
            reloadGridFiltersCopy[reloadGridKey][fieldKey] !== undefined
              ? reloadGridFiltersCopy[reloadGridKey][fieldKey]
              : allTestsFilterInitialValues[fieldKey];
          allTestsFilterForm.setFieldValue(fieldKey, fieldValue);
          if (fieldValue !== undefined) {
            reloadGridFiltersCopy[reloadGridKey][fieldKey] = fieldValue;
          }
      }
      if (
        reloadGridFiltersCopy[reloadGridKey][fieldKey] !==
        reloadGridFilters[reloadGridKey][fieldKey]
      ) {
        shouldSetFilters = true;
      }
    });

    if (shouldSetFilters) {
      setReloadGridFilters(reloadGridFiltersCopy);
    }
  };

  /**
   * Apply filters to search data
   *
   * @param {object} values
   */
  const applyFilters = (values) => {
    const gridFilters = getFilterValues(values);
    updateAppliedFilters(gridFilters);

    let reloadGridFiltersCopy = Helper.cloneObject(reloadGridFilters);
    reloadGridFiltersCopy[reloadGridKey] = gridFilters;
    setReloadGridFilters(reloadGridFiltersCopy);
    setReloadGrids([reloadGridKey]);
    setReloadGrid(Date.now());

    setIsAllTestsFilterOpen(false);
  };

  /**
   * Get search filter values
   *
   * @param {object} allTestsFilterFormValues
   * @returns {object} filterValues
   */
  const getFilterValues = (allTestsFilterFormValues) => {
    const rawFilters = Object.keys(allTestsFilterFormValues)
      .filter((key) => {
        return (
          (!Array.isArray(allTestsFilterFormValues[key]) &&
            allTestsFilterFormValues[key] !== undefined) ||
          (Array.isArray(allTestsFilterFormValues[key]) &&
            allTestsFilterFormValues[key].length > 0)
        );
      })
      .reduce((obj, key) => {
        obj[key] = allTestsFilterFormValues[key];
        return obj;
      }, {});

    const filterValues = Object.keys(rawFilters).reduce((obj, key) => {
      if (Array.isArray(rawFilters[key])) {
        obj[key] = rawFilters[key]
          .map((value) => {
            return typeof value === "object" ? value.value : value;
          })
          .join();
      } else {
        obj[key] = rawFilters[key];
      }
      return obj;
    }, {});

    return filterValues;
  };

  /**
   * Clear filters form
   */
  const clearFilters = () => {
    allTestsFilterForm.resetFields();
    message.success("Search filters successfully cleared");
  };

  /**
   * Remove applied filter
   *
   * @param {object} removedFilter
   */
  const removeFilter = (removedFilter) => {
    const fieldKey = removedFilter.fieldKey;
    const filterValue = removedFilter.value;
    const gridFilters = reloadGridFilters[reloadGridKey];
    const appliedFilterValue = gridFilters[fieldKey];
    const appliedFilterArr =
      typeof appliedFilterValue === "string"
        ? appliedFilterValue.split(",").filter((value) => {
            return value !== filterValue;
          })
        : [];
    const reloadGridFiltersCopy = Helper.cloneObject(reloadGridFilters);
    switch (AllTestsFilterFieldsMapper[fieldKey].type) {
      case "checkbox":
      case "select":
      case "select.multiple":
        delete reloadGridFiltersCopy[reloadGridKey][fieldKey];
        break;
      case "checkbox.group":
        if (appliedFilterArr.length > 0) {
          reloadGridFiltersCopy[reloadGridKey][fieldKey] =
            appliedFilterArr.join();
        } else {
          delete reloadGridFiltersCopy[reloadGridKey][fieldKey];
        }
        break;
      default:
        delete reloadGridFiltersCopy[reloadGridKey][fieldKey];
    }
    setReloadGridFilters(reloadGridFiltersCopy);
    setReloadGrids([reloadGridKey]);
    setReloadGrid(Date.now());
  };

  return (
    <>
      <Form
        form={allTestsFilter}
        className="flex flex-col h-full"
        name="all_tests_filter"
        layout="vertical"
      >
        <Space size="middle" className="w-full my-2">
          <Modal
            width="60%"
            open={isAllTestsFilterOpen}
            onCancel={() => setIsAllTestsFilterOpen(false)}
            footer={[
              <Button
                key="cancel"
                onClick={() => setIsAllTestsFilterOpen(false)}
              >
                Cancel
              </Button>,
              <Button key="clear_all" onClick={() => clearFilters()}>
                Clear All
              </Button>,
              <Button
                key="apply"
                type="primary"
                onClick={() => allTestsFilterForm.submit()}
              >
                Apply
              </Button>,
            ]}
            centered
            closable
            forceRender
          >
            <AllTestsFilterForm
              allTestsFilterForm={allTestsFilterForm}
              onFinish={applyFilters}
              pageKey={pageKey}
              setSiteListOptions={setSiteListOptions}
            />
          </Modal>
          <Button
            type="primary"
            icon={<FilterOutlined />}
            onClick={() => setIsAllTestsFilterOpen(true)}
          >
            Table Filter
          </Button>
          <Space className="w-full" wrap>
            {displayedAppliedFilters.length > 0 && <Text strong>Filters:</Text>}
            {displayedAppliedFilters.map((filter) => {
              return (
                <Tag
                  key={filter.key}
                  color={token.yhColorLightOrange}
                  style={{
                    color: token.yhGrey,
                  }}
                  closable={filter.required !== true}
                  onClose={() => removeFilter(filter)}
                >
                  <Text className="m-0!">
                    {filter.label && <Text strong>{filter.label}: </Text>}
                    {filter.valueText}
                  </Text>
                </Tag>
              );
            })}
          </Space>
        </Space>
      </Form>
    </>
  );
};

export default AllTestsFilter;
