import { Select } from "antd";
import { useEffect, useState } from "react";
import { useEffectApiFetch } from "../../hooks";
import { useBoundStore } from "../../store/store";
import Helper from "../helper";

/**
 * Templates dropdown selection
 *
 * @param {int} selectedTemplateId
 * @param {function} onChange
 * @param {string} placeholder
 * @returns {JSX.Element}
 */
export default function TemplatesDropdown({
  selectedTemplateId,
  onChange,
  placeholder,
}) {
  const [presetAnalysisTemplates, setPresetAnalysisTemplates] = useState([]);
  const [templateOptions, setTemplateOptions] = useState([]);
  const userAnalysisTemplates = useBoundStore(
    (state) => state.userAnalysisTemplates,
  );
  const setUserAnalysisTemplates = useBoundStore(
    (state) => state.setUserAnalysisTemplates,
  );
  const rerenderTemplateDropdown = useBoundStore(
    (state) => state.rerenderTemplateDropdown,
  );

  useEffectApiFetch(
    () => {
      return Helper.getPresetAnalysisTemplates(setPresetAnalysisTemplates);
    },
    () => {
      setPresetAnalysisTemplates([]);
    },
    [rerenderTemplateDropdown],
  );

  useEffectApiFetch(
    () => {
      return Helper.getUserAnalysisTemplates(setUserAnalysisTemplates);
    },
    () => {
      setUserAnalysisTemplates([]);
    },
    [rerenderTemplateDropdown],
  );

  useEffect(() => {
    generateTemplateOptions();
  }, [userAnalysisTemplates, presetAnalysisTemplates]);

  /**
   * Create template options
   */
  const generateTemplateOptions = () => {
    let optionGroup = [];
    optionGroup.push(
      {
        label: "Preset",
        options: [],
      },
      {
        label: "User",
        options: [],
      },
    );
    presetAnalysisTemplates.map((template) => {
      optionGroup[0].options.push({
        label: template.name,
        value: `preset_${template.id}`,
      });
    });
    userAnalysisTemplates.map((template) => {
      optionGroup[1].options.push({
        label: template.name,
        value: `user_${template.id}`,
      });
    });

    setTemplateOptions(optionGroup);
  };

  return (
    <div>
      <Select
        className="w-32"
        showSearch
        allowClear
        placeholder={placeholder ? placeholder : "Select a template"}
        optionFilterProp="children"
        value={selectedTemplateId}
        popupMatchSelectWidth={false}
        onChange={onChange}
        filterOption={(input, option) =>
          (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
        }
        options={templateOptions}
      />
    </div>
  );
}
