import { useEffect, useRef, useState } from "react";
import { useFullScreenHandle } from "react-full-screen";
import dynamic from "next/dynamic";
const WaferMap = dynamic(() => import("../charts/wafer_map"), {
  ssr: false,
});
import ChartWrapper from "../charts/chart_wrapper";
import { useBoundStore } from "../../store/store";
import ChartHelper from "../charts/chart_helper";

/**
 * Gallery chart component
 *
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {object} filters
 * @param {string} pageKey
 * @param {object} component
 * @param {object} chartFilters
 * @param {object} galleryCharts
 * @param {function} setGalleryCharts
 * @param {int} galleryChartIndex
 * @param {function} setGalleryChartIndex
 * @param {string} galleryView
 * @param {object} prerenderData
 * @param {boolean} showBoostWarning
 * @returns {JSX.Element}
 */
export const GalleryChart = ({
  chartKey,
  chartCustomData,
  filters,
  pageKey,
  component,
  chartFilters,
  galleryCharts,
  setGalleryCharts,
  galleryChartIndex,
  setGalleryChartIndex,
  galleryView,
  prerenderData,
  showBoostWarning = false,
}) => {
  const [chartComponent, setChartComponent] = useState();
  const [highchartsChart, setHighchartsChart] = useState();
  const [hasChartData, setHasChartData] = useState(true);
  const [isChartBoosted, setIsChartBoosted] = useState(false);
  const setGalleryChartsUpdateTime = useBoundStore(
    (state) => state.setGalleryChartsUpdateTime,
  );
  const chartRef = useRef();
  const componentWrapperRef = useRef();
  const fullScreenHandle = useFullScreenHandle();

  // initialize chart custom data
  ChartHelper.initChartCustomData(chartCustomData, chartKey);

  useEffect(() => {
    renderChart();
  }, []);

  useEffect(() => {
    if (
      typeof galleryCharts === "object" &&
      typeof setGalleryCharts === "function" &&
      highchartsChart &&
      highchartsChart.options
    ) {
      galleryCharts[pageKey][galleryView][chartKey] = highchartsChart;
      setGalleryChartsUpdateTime(Date.now());
    }
  }, [highchartsChart]);

  /**
   * Set chart component to be rendered on page
   *
   */
  const renderChart = () => {
    let chart;
    switch (component.component) {
      case "yield_wafer_map":
      case "composite_wafer_map":
      case "parametric_wafer_map":
        chart = (
          <WaferMap
            ref={componentWrapperRef}
            chartRef={chartRef}
            component={component}
            filters={filters}
            pageKey={pageKey}
            chartKey={chartKey}
            chartCustomData={chartCustomData}
            chartFilters={chartFilters}
            setHighchartsChart={setHighchartsChart}
            setIsChartBoosted={setIsChartBoosted}
            setHasChartData={setHasChartData}
            galleryChartIndex={galleryChartIndex}
            setGalleryChartIndex={setGalleryChartIndex}
            fullScreenHandle={fullScreenHandle}
            prerenderData={prerenderData}
          />
        );
        break;
    }
    setChartComponent(chart);
  };

  return (
    <ChartWrapper
      key={chartKey}
      chartComponent={chartComponent}
      chartRef={chartRef}
      component={component}
      pageKey={pageKey}
      waferIdOptions={[]}
      chartCustomData={chartCustomData}
      chartOptionModalContainer={() => document.body}
      chartKey={chartKey}
      chartFilters={chartFilters}
      hasChartData={hasChartData}
      isChartBoosted={isChartBoosted}
      showBoostWarning={showBoostWarning}
      fullScreenHandle={fullScreenHandle}
    />
  );
};
