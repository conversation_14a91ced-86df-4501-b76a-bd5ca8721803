import { Col, Row, Typography, notification } from "antd";
import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import Helper from "../helper";
import { QueryKeys } from "../query_keys";
import Api from "../../../src/utils/api";

const { Text } = Typography;

/**
 * Component that displays reprobe analysis
 *
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function ReprobeAnalysis({ component, filters, pageKey }) {
  const [data, setData] = useState();
  const params = component.props.params;

  const getData = async ({ signal }) => {
    const requestParams = Helper.filterObjectByKeys(
      filters[pageKey],
      Object.keys(params.body_params),
    );
    const url = Helper.parseUrlEndpoint(params.url_endpoint, filters[pageKey]);

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Query to fetch reprobe analysis data
   */
  const dataQuery = useQuery({
    queryKey: QueryKeys.reprobe_analysis(pageKey),
    queryFn: getData,
  });

  useEffect(() => {
    if (dataQuery.isSuccess) {
      const response = dataQuery.data;
      if (response.success) {
        setData(response.data);
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
    }
  }, [dataQuery.dataUpdatedAt]);

  return (
    <>
      {data && (
        <>
          <Row>
            <Col span={24}>
              <Text strong>Total Dice: </Text>
              <Text type="secondary">{data.total_dice}</Text>
            </Col>
          </Row>
          <Row>
            <Col span={4}>
              <Text strong>Dice Re-tested: </Text>
              <Text type="secondary">
                {data.dice_retested}
                {` (${Helper.descriptiveFormatting(data.retest_pct, 2, 2)}%)`}
              </Text>
            </Col>
            <Col span={4}>
              <Text strong>Reprobed Pass Bins Passing: </Text>
              <Text type="secondary">{data.reprobed_pass_bins_passing}</Text>
            </Col>
            <Col span={4}>
              <Text strong>Reprobed Dice not Recovered: </Text>
              <Text type="danger">{data.not_recovered}</Text>
            </Col>
          </Row>
          <Row>
            <Col span={4}>
              <Text strong>Recovery Rate: </Text>
              <Text type="success">
                {Helper.descriptiveFormatting(data.recovery_rate, 2, 2)}%
              </Text>
            </Col>
            <Col span={4}>
              <Text strong>Reprobed Dice Recovered: </Text>
              <Text type="success">{data.recovered}</Text>
            </Col>
            <Col span={4}>
              <Text strong>Reprobed Pass Bins Failing: </Text>
              <Text type="danger">{data.reprobed_pass_bins_failed}</Text>
            </Col>
          </Row>
        </>
      )}
    </>
  );
}
