import { LoadingOutlined } from "@ant-design/icons";
import {
  Col,
  Flex,
  Form,
  Modal,
  notification,
  Row,
  Select,
  Space,
  Spin,
  Typography,
} from "antd";
import { useEffect, useRef, useState } from "react";
import dynamic from "next/dynamic";
import { useBoundStore } from "../../store/store";
import Api from "../api";
import Helper from "../helper";
const SolidGauge = dynamic(() => import("../charts/solid_gauge"), {
  ssr: false,
});
import { ComponentNameMapper } from "../../../src/utils/grid/component_name_mapper";
import CustomPeriodDatePicker from "./custom_period_datepicker";

const { Text, Title } = Typography;
const upColor = "#52C41A";
const downColor = "#FF4D4F";
const comparisonPeriodOptions = [
  {
    value: "yesterday",
    label: "Yesterday",
  },
  {
    value: "today",
    label: "Today",
  },
  {
    value: "current_week",
    label: "This Week",
  },
  {
    value: "current_month",
    label: "This Month",
  },
  {
    value: "current_year",
    label: "This Year",
  },
  {
    value: "previous_week",
    label: "Last Week",
  },
  {
    value: "previous_month",
    label: "Last Month",
  },
  {
    value: "previous_year",
    label: "Last Year",
  },
  {
    value: "custom_period",
    label: "Custom",
  },
];

/**
 * Card component that displays OEE statistics data
 *
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function OEEStatisticCard({ component, filters, pageKey }) {
  const [data, setData] = useState();
  const [chartCustomData] = useState({});
  const [disableCustomPeriodUpdate, setDisableCustomPeriodUpdate] =
    useState(true);
  const [comparisonPeriodValue, setComparisonPeriodValue] = useState({
    value: "yesterday",
    label: "Yesterday",
  });
  const [isCustomComparisonModalOpen, setIsCustomComparisonModalOpen] =
    useState(false);
  const [customComparisonModalPosition, setCustomComparisonModalPosition] =
    useState({ top: 0, left: 0 });
  const OEEFilters = useBoundStore((state) => state.OEEFilters);
  const setOEEFilters = useBoundStore((state) => state.setOEEFilters);
  const cacheData = useBoundStore((state) => state.cacheData);
  const reloadGridFilters = useBoundStore((state) => state.reloadGridFilters);
  const setReloadGrid = useBoundStore((state) => state.setReloadGrid);
  const setReloadGrids = useBoundStore((state) => state.setReloadGrids);
  const setReloadGridFilters = useBoundStore(
    (state) => state.setReloadGridFilters,
  );
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const setReloadChart = useBoundStore((state) => state.setReloadChart);
  const setReloadChartFilters = useBoundStore(
    (state) => state.setReloadChartFilters,
  );
  const chartKeys = useBoundStore((state) => state.chartKeys);
  const [customPeriodForm] = Form.useForm();
  const comparisonPeriodWrapperRef = useRef();
  const chartRef = useRef();
  const chartKey = `${pageKey}_${component.component}_${component.id}`;
  const params = component.props.params;
  const settings = component.props.settings;
  const OEETrendGridKey = ComponentNameMapper.oee_historical_trend_table;
  const OEEBreakdownGridKey = ComponentNameMapper.oee_breakdown_table;
  const [OEETrendChartKey, setOEETrendChartKey] = useState("");

  if (!chartCustomData[chartKey]) {
    chartCustomData[chartKey] = {};
  }

  useEffect(() => {
    if (reloadGridFilters[OEETrendGridKey] === undefined) {
      reloadGridFilters[OEETrendGridKey] = {};
    }
    if (reloadGridFilters[OEEBreakdownGridKey] === undefined) {
      reloadGridFilters[OEEBreakdownGridKey] = {};
    }
  }, []);

  useEffect(() => {
    if (
      chartKeys[pageKey] &&
      Object.keys(chartKeys[pageKey]).length > 0 &&
      chartKeys[pageKey][ComponentNameMapper.oee_historical_trend]?.length > 0
    ) {
      setOEETrendChartKey(
        chartKeys[pageKey][ComponentNameMapper.oee_historical_trend][0],
      );

      if (
        OEETrendChartKey &&
        reloadChartFilters[OEETrendChartKey] === undefined
      ) {
        reloadChartFilters[OEETrendChartKey] = {};
      }
    }
  }, [chartKeys[pageKey]]);

  useEffect(() => {
    setData();
    getData(params);
  }, [OEEFilters]);

  /**
   * Get statistic data
   *
   * @param {object} params
   * @returns {AbortController}
   */
  const getData = (params) => {
    const requestParams = Helper.filterObjectByKeys(
      filters[pageKey],
      Object.keys(params.body_params),
    );
    const url = Helper.parseUrlEndpoint(params.url_endpoint, filters[pageKey]);

    return Api.getData(
      url,
      params.method,
      (res) => {
        if (res.success) {
          const data = res.data;
          const dataKey = component.data_key;
          let chartData = {};
          if (component.chart && component.chart.type === "solidgauge") {
            chartData.value = data[dataKey];
            chartData.compareValue = data[`${dataKey}_comparison_value`];
            chartData.compareValueDiff = (
              chartData.value - chartData.compareValue
            ).toFixed(1);
            chartData.valueColor =
              chartData.value < OEEFilters.limit ? downColor : upColor;
            chartData.compareValueColor =
              chartData.compareValue < OEEFilters.limit ? downColor : upColor;

            chartData.value = Math.floor(chartData.value * 10) / 10;
            chartData.compareValue =
              Math.floor(chartData.compareValue * 10) / 10;

            if (chartData.compareValueDiff > 0) {
              chartData.compareValueDiffDirection = "up";
              chartData.compareValueDiffColor = upColor;
            } else if (chartData.compareValueDiff < 0) {
              chartData.compareValueDiffDirection = "down";
              chartData.compareValueDiffColor = downColor;
            }

            if (
              component.name === "stats_card_performance" ||
              component.name === "stats_card_quality"
            ) {
              chartData.parts = data.parts;
            }
            if (component.name === "stats_card_availability") {
              chartData.hours = Math.floor(data.hours * 10) / 10;
            }
            component.chart.component.data = chartData;
          }
          setData(chartData);
        } else {
          notification.warning({
            message: component.display_name,
            description: res.message,
          });
        }
      },
      (err) => {
        notification.error({
          message: component.display_name,
          description: err,
        });
      },
      {
        ...requestParams,
        ...OEEFilters,
      },
      params.body_params,
      cacheData,
    );
  };

  /**
   * Handle comparison period change
   *
   * @param {object} valueObj
   */
  const handleComparisonPeriodChange = (valueObj) => {
    if (valueObj.value === "custom_period") {
      const rect = comparisonPeriodWrapperRef.current.getBoundingClientRect();
      setCustomComparisonModalPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
      });
      setIsCustomComparisonModalOpen(true);
    } else {
      updateComparisonPeriod(valueObj);
    }
  };

  /**
   * Set custom comparison period value and label
   */
  const setCustomComparisonPeriod = () => {
    const values = customPeriodForm.getFieldsValue();
    let label = "";
    if (values.period_type === "date" && values.date) {
      label = Helper.formatPeriodLabel(values.date);
    } else {
      label = Helper.formatPeriodLabel(values.range);
    }

    const valueObj = {
      value: null,
      label: label,
    };
    updateComparisonPeriod(valueObj);
    setIsCustomComparisonModalOpen(false);
  };

  /**
   * Update comparison period value and label
   *
   * @param {object} valueObj
   */
  const updateComparisonPeriod = (valueObj) => {
    let OEEFiltersCopy = Helper.cloneObject(OEEFilters);
    OEEFiltersCopy.comparison_period = valueObj.value;
    OEEFiltersCopy.comparison_period_label = valueObj.label;
    setOEEFilters(OEEFiltersCopy);
    setComparisonPeriodValue(valueObj);
    Helper.reloadOEEGridAndChartComponents(
      reloadGridFilters,
      setReloadGrid,
      setReloadGrids,
      setReloadGridFilters,
      reloadChartFilters,
      setReloadChart,
      setReloadChartFilters,
      OEETrendGridKey,
      OEEBreakdownGridKey,
      OEETrendChartKey,
      OEEFiltersCopy,
    );
  };

  return (
    <>
      <Row>
        <Col span={24}>
          <Space direction="vertical">
            <Flex align="center" gap="middle">
              <Title level={2} className="mb-1!">
                {component.display_name}
              </Title>
              <Text type="secondary">{OEEFilters.period_label}</Text>
            </Flex>
          </Space>
        </Col>
      </Row>
      <Row>
        <Col span={16} offset={4}>
          {data && component.chart && component.chart.type === "solidgauge" ? (
            <SolidGauge
              chartRef={chartRef}
              component={component.chart.component}
              filters={filters}
              pageKey={pageKey}
              chartKey={chartKey}
              chartCustomData={chartCustomData}
            />
          ) : (
            <Spin
              className="p-24"
              size="large"
              indicator={<LoadingOutlined spin />}
            />
          )}
        </Col>
      </Row>
      <Row>
        <Col>
          <Space size="middle">
            {settings.has_comparison_period_selection ? (
              <>
                <Text>Compared to: </Text>
                <div ref={comparisonPeriodWrapperRef}>
                  <Select
                    name="comparison_period"
                    defaultValue={OEEFilters.comparison_period}
                    value={comparisonPeriodValue}
                    onChange={handleComparisonPeriodChange}
                    options={comparisonPeriodOptions}
                    popupMatchSelectWidth={false}
                    labelInValue
                  ></Select>
                </div>
                <Modal
                  title="Custom Comparison"
                  open={isCustomComparisonModalOpen}
                  okText="Apply"
                  cancelText="Close"
                  onOk={() => setCustomComparisonPeriod()}
                  onCancel={() => setIsCustomComparisonModalOpen(false)}
                  okButtonProps={{
                    disabled: disableCustomPeriodUpdate,
                  }}
                  style={{
                    top: customComparisonModalPosition.top,
                    left: customComparisonModalPosition.left,
                    margin: 0,
                  }}
                  mask={false}
                  width={300}
                >
                  <CustomPeriodDatePicker
                    customPeriodForm={customPeriodForm}
                    setDisableCustomPeriodUpdate={setDisableCustomPeriodUpdate}
                  />
                </Modal>
              </>
            ) : (
              <Text>
                Compared to{" "}
                <Text strong>{OEEFilters.comparison_period_label}</Text>
              </Text>
            )}

            {data && data.compareValue !== undefined ? (
              <Title
                className="mb-0! ml-2"
                level={1}
                style={{
                  color: data.compareValueColor,
                }}
              >
                {data.compareValue}%
              </Title>
            ) : (
              <Spin indicator={<LoadingOutlined spin />} />
            )}
          </Space>
        </Col>
      </Row>
    </>
  );
}
