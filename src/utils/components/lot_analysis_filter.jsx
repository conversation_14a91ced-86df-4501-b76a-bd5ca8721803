import { Space, Typography } from "antd";

const { Text } = Typography;

/**
 * Section to apply filters and display info on Lot Analysis
 *
 * @returns {JSX.Element}
 */
const LotAnalysisFilter = ({ pageKey, filters }) => {
  return (
    <Space className="ml-1" align="center">
      <Text className="text-base" strong>
        Lot ID:{" "}
      </Text>
      <Text type="secondary" className="text-sm">
        {filters[pageKey] && filters[pageKey].lot_id}
      </Text>
    </Space>
  );
};

export default LotAnalysisFilter;
