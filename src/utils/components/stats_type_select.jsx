"use client";

import { Flex, Form, InputNumber, Select } from "antd";
import { useState } from "react";
import { OptionsList } from "../../../src/utils/forms/options_list";

/**
 *  Stats type select component
 *
 * @param {array} selectOptions
 * @param {string} selectName
 * @param {string} selectLabel
 * @param {string} selectClassName
 * @param {string} iqrNClassName
 * @param {string} selectPlaceholder
 * @returns {JSX.Element}
 */
const StatsTypeSelect = ({
  selectOptions,
  selectName,
  selectLabel,
  selectClassName,
  iqrNClassName,
  selectPlaceholder,
  ...props
}) => {
  const [isIqrNDisabled, setIsIqrNDisabled] = useState(true);

  return (
    <Flex {...props}>
      <Form.Item
        name={selectName ?? "stats_type"}
        label={selectLabel ?? "Stats Type"}
        className={selectClassName}
      >
        <Select
          showSearch
          className="w-56"
          placeholder={selectPlaceholder ?? "Select Stats Type"}
          popupMatchSelectWidth={false}
          options={selectOptions ?? OptionsList.npi_stats_types}
          onChange={(value) => {
            setIsIqrNDisabled(!value.includes("iqr"));
          }}
        />
      </Form.Item>
      <Form.Item name="iqr_n" label="Robust Data N" className={iqrNClassName}>
        <InputNumber disabled={isIqrNDisabled} className="w-16"></InputNumber>
      </Form.Item>
    </Flex>
  );
};

export default StatsTypeSelect;
