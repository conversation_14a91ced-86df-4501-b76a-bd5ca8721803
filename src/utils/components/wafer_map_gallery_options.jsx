import { <PERSON><PERSON>, <PERSON><PERSON>, Col, Flex, Form, Row, Space, theme } from "antd";
import {
  TableOutlined,
  TabletOutlined,
  // DownloadOutlined,
} from "@ant-design/icons";
import React, { useEffect } from "react";
import { debounce } from "lodash";
import FilterSelect from "../grid/components/filter_select";
import { useBoundStore } from "../../store/store";

const { useToken } = theme;

/**
 * Wafer map gallery options
 *
 * @param {string} pageKey
 * @param {function} highlightBins
 * @param {function} setGalleryView
 * @param {string} galleryView
 * @param {object} highlightedBins
 * @param {array} galleryOptions
 * @returns {JSX.Element}
 */
export default function WaferMapGalleryOptions({
  pageKey,
  highlightBins,
  setGalleryView,
  galleryView,
  highlightedBins,
  galleryOptions,
}) {
  const urlParams = useBoundStore((state) => state.urlParams);
  const [chartsGalleryFilterForm] = Form.useForm();
  const { token } = useToken();

  useEffect(() => {
    chartsGalleryFilterForm.setFieldValue(
      "bins",
      highlightedBins?.[galleryView] ? highlightedBins[galleryView] : [],
    );
  }, [galleryView]);

  return (
    <Row className="px-2 bg-white">
      <Col span={24}>
        {galleryOptions.indexOf("highlightBins") !== -1 && (
          <Alert
            className="mb-2!"
            message="This will apply to all wafer maps in this page."
            type="info"
            closable
          />
        )}
        <Form form={chartsGalleryFilterForm} layout="inline">
          <Flex justify="space-between" flex={1}>
            {galleryOptions.indexOf("highlightBins") !== -1 ? (
              <Space>
                <Form.Item name="bins">
                  <FilterSelect
                    className="w-52!"
                    componentKey="highlight_bins_options"
                    placeholder="Select Bins to Highlight"
                    mode="multiple"
                    maxTagCount={2}
                    onChange={debounce(highlightBins, 1000)}
                    params={{
                      api: {
                        url: "api/v1/internal/options/list/bin_numbers",
                        mfg_process: urlParams[pageKey].mfg_process,
                        src_type: urlParams[pageKey].src_type,
                        src_value: urlParams[pageKey].src_value,
                        cache_it: 0,
                      },
                    }}
                  />
                </Form.Item>
                <Form.Item>
                  <Button
                    onClick={() => {
                      chartsGalleryFilterForm.setFieldValue("bins", []);
                      highlightBins([]);
                    }}
                  >
                    Revert to All Bins
                  </Button>
                </Form.Item>
              </Space>
            ) : (
              <div></div>
            )}
            <Space>
              <Button
                type="link"
                icon={
                  <TableOutlined
                    style={{
                      color: `${galleryView === "grid" ? token.yhColorSafetyOrange : token.colorPrimary}`,
                    }}
                  />
                }
                onClick={() => setGalleryView("grid")}
              />
              <Button
                type="link"
                icon={
                  <TabletOutlined
                    style={{
                      color: `${galleryView === "carousel" ? token.yhColorSafetyOrange : token.colorPrimary}`,
                    }}
                  />
                }
                onClick={() => setGalleryView("carousel")}
              />
              {/* <DownloadOutlined /> */}
            </Space>
          </Flex>
        </Form>
      </Col>
    </Row>
  );
}
