import React, { useEffect, useState } from "react";
import Helper from "../helper";

/**
 * Display of applied filters
 *
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default function FiltersDisplay({
  component,
  filters,
  pageKey,
  prerenderData = {},
}) {
  const [filtersDisplay, setFiltersDisplay] = useState("");

  useEffect(() => {
    let allFilters = { ...filters[pageKey], ...prerenderData };
    let info = Helper.generateLabelByData(component.template, allFilters);
    setFiltersDisplay(info);
  }, [filters[pageKey], prerenderData]);

  return <div dangerouslySetInnerHTML={{ __html: filtersDisplay }} />;
}
