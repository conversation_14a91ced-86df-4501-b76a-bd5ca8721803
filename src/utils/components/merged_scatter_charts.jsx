import { Col, Row, Typography, message } from "antd";
import React, { useEffect, useState } from "react";
import { useBoundStore } from "../../store/store";
import Api from "../api";
import Helper from "../helper";
import { useEffectApiFetch } from "../../hooks";
import ChartHelper from "../charts/chart_helper";
import { ChartspaceChart } from "./chartspace_chart";

/**
 * Display of merged scatter charts
 *
 * @param {object} component
 * @param {string} componentName
 * @param {object} filters
 * @param {string} pageKey
 * @param {object} chartCustomData
 * @param {function} testStatsInfoHandler
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default function MergedScatterCharts({
  component,
  componentName,
  filters,
  pageKey,
  chartCustomData,
  testStatsInfoHandler,
  prerenderData = {},
}) {
  const [groupedDatalogs, setGroupedDatalogs] = useState([]);
  const [chartComponentBlueprint, setChartComponentBlueprint] = useState();
  const [mergedScatterCharts, setMergedScatterCharts] = useState([]);
  const urlParams = useBoundStore((state) => state.urlParams);
  const chartKeys = useBoundStore((state) => state.chartKeys);
  const [messageApi, contextHolder] = message.useMessage();
  const { Title } = Typography;

  useEffectApiFetch(
    () => {
      if (chartComponentBlueprint) {
        return Helper.getGroupedDatalogs(
          {
            mfg_process: urlParams[pageKey].mfg_process,
            group_by: prerenderData.group_by ?? urlParams[pageKey].group_by,
            src_type: urlParams[pageKey].src_type,
            src_value: urlParams[pageKey].src_value,
            tnum: prerenderData.tnum,
          },
          setGroupedDatalogs,
          messageApi,
        );
      }
    },
    () => {
      setGroupedDatalogs([]);
    },
    [chartComponentBlueprint],
  );

  useEffectApiFetch(
    () => {
      return Api.getComponentBlueprint(
        (res) => {
          if (res.success) {
            setChartComponentBlueprint(res.data);
          } else {
            messageApi.warning(res.message, 5);
          }
        },
        (err) => {
          messageApi.error(err, 5);
        },
        {
          name: componentName,
        },
      );
    },
    () => {
      setChartComponentBlueprint();
    },
  );

  useEffect(() => {
    if (chartComponentBlueprint) {
      renderMergedScatterCharts(chartComponentBlueprint, groupedDatalogs);
    }
  }, [groupedDatalogs]);

  /**
   * Add chart object to per group merged scatter charts
   *
   * @param {object} chartComponentBlueprint
   * @param {object} groupedDatalogs
   */
  const renderMergedScatterCharts = (
    chartComponentBlueprint,
    groupedDatalogs,
  ) => {
    let charts = {};
    const prerenderDataStr = JSON.stringify(prerenderData);
    groupedDatalogs.forEach((group) => {
      const dsklist = group.dsk.join(",");
      const chartKey = `${component.name}_${chartComponentBlueprint.name}_${group.dsk_hash ?? ""}_${prerenderDataStr}`;
      const chartComponentBlueprintCopy = Helper.cloneObject(
        chartComponentBlueprint,
      );
      chartComponentBlueprintCopy.id = chartKey;
      chartComponentBlueprintCopy.props.params.body_params.test_number =
        prerenderData.tnum;
      charts[chartKey] = {
        key: chartKey,
        chartType: "scatter",
        chartKey: chartKey,
        chartCustomData: chartCustomData,
        filters: filters,
        pageKey: pageKey,
        component: chartComponentBlueprintCopy,
        chartFilters: {
          src_type: "dsk",
          src_value: dsklist,
        },
        testStatsInfoHandler: testStatsInfoHandler,
        title: group.label,
        prerenderData: prerenderData,
      };

      ChartHelper.storeChartKey(
        chartKey,
        chartKeys,
        pageKey,
        chartComponentBlueprintCopy.name,
      );
    });

    setMergedScatterCharts(charts);
  };

  return (
    <div>
      {contextHolder}
      <div className="p-2">
        {Object.keys(mergedScatterCharts).map((chartKey, i) => {
          return (
            <Row
              key={`merged_scatter_chart_${chartKey}_wrapper`}
              className={i > 0 ? "mt-4" : ""}
              gutter={[16, 16]}
            >
              <Col className="text-center" span={24}>
                <Title level={3}>{mergedScatterCharts[chartKey].title}</Title>
                <div
                  id={`boosted_warning_msg_wrapper_${mergedScatterCharts[chartKey].component.id}`}
                ></div>
                <div className="border border-solid border-black/[.25]">
                  {React.createElement(
                    ChartspaceChart,
                    mergedScatterCharts[chartKey],
                  )}
                </div>
              </Col>
            </Row>
          );
        })}
      </div>
    </div>
  );
}
