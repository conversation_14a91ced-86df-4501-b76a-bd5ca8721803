import { Checkbox, Col, Divider, Form, Input, Row, Typography } from "antd";

/*
 * Filters section component
 *
 * @param {string} sectionTitle
 * @param {Array} textFieldsProps
 * @param {object} checkboxFieldsProps
 * @returns {JSX.Element}
 * */
export default function FiltersSection({
  sectionTitle,
  textFieldsProps,
  checkboxFieldsProps,
}) {
  return (
    <>
      <Row>
        <Col span={24}>
          <Divider orientation="center">
            <Typography.Text>{sectionTitle}</Typography.Text>
          </Divider>
        </Col>
      </Row>
      <Row gutter={16}>
        {Array.isArray(textFieldsProps)
          ? textFieldsProps.map((item) => {
              return (
                <Col
                  key={item.key}
                  span={Math.floor(24 / textFieldsProps.length)}
                >
                  <Form.Item key={item.key} {...item.formItemProps}>
                    <Input key={item.key} {...item.inputProps} />
                  </Form.Item>
                </Col>
              );
            })
          : null}
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item {...checkboxFieldsProps.formItemProps}>
            <Checkbox.Group {...checkboxFieldsProps.inputProps} />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
}
