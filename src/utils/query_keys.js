/**
 * React Query keys factory
 */
export const QueryKeys = {
  page: (pageKey) => ["page", pageKey],
  charts: (pageKey) => [...QueryKeys.page(pageKey), "chart"],
  chart: (pageKey, chartKey, filters) => [
    ...QueryKeys.charts(pageKey),
    chartKey,
    filters,
  ],
  gallery_charts: (pageKey) => [...QueryKeys.charts(pageKey), "gallery"],
  lot_stats: (pageKey) => [...QueryKeys.page(pageKey), "lot_stats"],
  reprobe_analysis: (pageKey) => [
    ...QueryKeys.page(pageKey),
    "reprobe_analysis",
  ],
  grids: (pageKey) => [...QueryKeys.page(pageKey), "grid"],
  grid: (pageKey, gridId) => [...QueryKeys.grids(pageKey), gridId],
  recipe_list: (pageKey) => [...QueryKeys.page(pageKey), "recipe_list"],
  recipe_info: (pageKey) => [...QueryKeys.page(pageKey), "recipe_info"],
  recipe_version: (pageKey) => [...QueryKeys.page(pageKey), "recipe_version"],
  recipe_data: (pageKey) => [...QueryKeys.page(pageKey), "recipe_data"],
  consolidation_grouping: (pageKey) => [
    ...QueryKeys.page(pageKey),
    "consolidation_grouping",
  ],
  user_settings: () => ["user", "settings"],
};
