const reportNameMaxLength = 150;
export const alphanumericRule = {
  pattern: /^[a-zA-Z0-9\s._-]*$/,
  message:
    "Only letters (a-z), numbers (0-9), dash (-), underscore (_), period (.) and space are allowed.",
};

export const recipeNameRules = [
  {
    required: true,
    message: "Please enter recipe name.",
  },
  {
    pattern: /^[a-zA-Z_-]+[a-zA-Z0-9_-]*$/,
    message:
      "Can only include numbers 0-9, letters a-z, - and _ and should not start with a number.",
  },
  {
    max: reportNameMaxLength,
    message: "Recipe name should not exceed 150 characters.",
  },
];

export const reportNameRules = [
  {
    required: true,
    message: "Please enter valid name.",
  },
  {
    pattern: /^[a-zA-Z_-]+[a-zA-Z0-9_-]*$/,
    message:
      "Can only include numbers 0-9, letters a-z, - and _ and should not start with a number.",
  },
  {
    max: reportNameMaxLength,
    message: `Recipe name should not exceed ${reportNameMaxLength} characters.`,
  },
];

export const reportNameSelectRules = [
  {
    required: true,
    message: "Please input valid report name.",
  },
  {
    validator: (_, value) => {
      if (!/^[a-zA-Z_-]+[a-zA-Z0-9_-]*$/.test(value[0])) {
        return Promise.reject();
      } else {
        return Promise.resolve();
      }
    },
    message:
      "Can only include numbers 0-9, letters a-z, - and _ and should not start with a number.",
  },
  {
    validator: (_, value) => {
      if (value?.[0]?.length > reportNameMaxLength) {
        return Promise.reject();
      } else {
        return Promise.resolve();
      }
    },
    message: `Recipe name should not exceed ${reportNameMaxLength} characters.`,
  },
];
