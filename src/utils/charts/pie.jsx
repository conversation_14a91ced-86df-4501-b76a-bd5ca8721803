"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import Api from "../api";
import Helper from "../helper";
import { QueryKeys } from "../query_keys";
import ChartLoading from "./loading";

/**
 * Pie chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default function Pie({
  chartRef,
  component,
  filters,
  pageKey,
  chartKey,
  prerenderData = {},
}) {
  const [chartOptions, setChartOptions] = useState();
  const [shouldFetchChartData, setShouldFetchChartData] = useState(false);
  const { message } = App.useApp();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;

  const options = {
    chart: {
      renderTo: "container",
      plotBackgroundColor: null,
      plotBorderWidth: null,
      plotShadow: false,
      type: "pie",
      width: settings.width ? settings.width : null,
      height: settings.height ? settings.height : "auto",
      margin: settings.margin !== undefined ? settings.margin : null,
    },
    title: {
      text: "",
    },
    tooltip: {
      pointFormat: "{series.name}: <b>{point.percentage:.2f}%</b>",
    },
    accessibility: {
      point: {
        valueSuffix: "%",
      },
    },
    exporting: {
      enabled:
        settings.exporting && settings.exporting.enabled !== undefined
          ? settings.exporting.enabled
          : true,
    },
    plotOptions: {
      pie: {
        size: settings.size ? settings.size : null,
        allowPointSelect: true,
        cursor: "pointer",
        dataLabels: {
          enabled:
            settings.dataLabels && settings.dataLabels.enabled !== undefined
              ? settings.dataLabels.enabled
              : true,
          format: "<b>{point.name}</b>: {point.percentage:.2f} %",
        },
        enableMouseTracking:
          settings.enableMouseTracking !== undefined
            ? settings.enableMouseTracking
            : true,
      },
    },
    series: [
      {
        name: settings.title,
        colorByPoint: true,
        data: [],
      },
    ],
  };

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  useEffect(() => {
    getChartData(params);
  }, []);

  useEffect(() => {
    if (component.data) {
      getChartData(params);
    }
  }, [component.data]);

  /**
   * Set chart size to equal width and height
   *
   * @param {object} chart
   */
  const setChartSize = (chart) => {
    const width = chart.chartWidth;
    chart.setSize(width, width, false);
  };

  /**
   * Get chart data
   *
   * @returns {AbortController}
   */
  const getChartData = () => {
    chartRef.current?.chart.showLoading();

    if (component.data !== undefined) {
      options.series[0].data = component.data;
      setChartOptions(options);
      chartRef.current?.chart.hideLoading();
    } else {
      setShouldFetchChartData(true);
    }
  };

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    const requestParams = Helper.filterObjectByKeys(
      { ...filters[pageKey], ...prerenderData },
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      params.url_endpoint,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
    enabled: shouldFetchChartData,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        // TBD: To set data from api endpoint
        setChartOptions(options);
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    setChartSize(chart);
  };

  return (
    <div>
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
            callback={onChartLoaded}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
}
