"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/modules/pareto";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import "highcharts/modules/boost";
import React, { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Api from "../api";
import Helper from "../helper";
import { useBoundStore } from "../../store/store";
import { QueryKeys } from "../query_keys";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import ChartHelper from "./chart_helper";
import ChartLoading from "./loading";
import { redrawWhenBoosted } from "./chart_common";

/**
 * Pareto with line chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {function} setIsChartBoosted
 * @param {function} setHasChartData
 * @param {object} fullScreenHandle
 * @param {object} prerenderData
 * @param {React.Ref} ref
 * @returns {JSX.Element}
 */
export default React.forwardRef(function ParetoWithLine(
  {
    chartRef,
    component,
    filters,
    pageKey,
    chartKey,
    chartCustomData,
    setIsChartBoosted,
    setHasChartData,
    fullScreenHandle,
    prerenderData = {},
  },
  ref,
) {
  const [chartOptions, setChartOptions] = useState();
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const { message } = App.useApp();
  const [notificationApi, contextHolder] = notification.useNotification();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const chartType = component.name;

  const options = merge(ChartHelper.getChartDefaultSettings(), {
    chart: {
      renderTo: "container",
      type: "column",
      width: settings.width ? settings.width : null,
      height: settings.height ? settings.height : "auto",
      marginRight: 80,
      zoomType: "xy",
      events: {
        render: (event) => {
          const chart = event.target;
          if (typeof setIsChartBoosted === "function") {
            setIsChartBoosted(chart.boosted);
          }
        },
      },
    },
    title: {
      text: ChartHelper.generateChartTitle(
        settings.title ?? "",
        merge({}, filters[pageKey], prerenderData),
      ),
    },
    tooltip: {
      shared: true,
    },
    xAxis: [
      {
        categories: [],
        crosshair: true,
      },
    ],
    yAxis: [
      {
        title: {
          text: settings.y.title,
        },
        max: settings.y.max ?? null,
        min: settings.y.min ?? null,
        labels: {
          format: `{value}${settings.y.value_suffix ?? ""}`,
        },
      },
      {
        title: {
          text: settings.y2.title,
        },
        minPadding: 0,
        maxPadding: 0,
        max: settings.y2.max ?? null,
        min: settings.y2.min ?? null,
        opposite: true,
        labels: {
          format: `{value}${settings.y2.value_suffix ?? ""}`,
        },
        gridLineWidth: 0,
      },
    ],
    series: [
      {
        type: "line",
        name: settings.line.title,
        yAxis: settings.line.y_axis ?? 1,
        zIndex: 10,
        tooltip: {
          valueDecimals: settings.line.value_decimals ?? 2,
          valueSuffix: settings.line.value_suffix ?? "",
        },
        data: [],
      },
      {
        name: settings.bar.title,
        type: "column",
        color: "#FF0000",
        yAxis: settings.bar.y_axis ?? 0,
        zIndex: 2,
        tooltip: {
          valueDecimals: settings.bar.value_decimals ?? 2,
          valueSuffix: settings.bar.value_suffix ?? "",
        },
        data: [],
      },
    ],
    plotOptions: {
      series: {
        custom: {
          settings: settings,
        },
      },
    },
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      default: settings,
    },
  });

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }
    chartComponentRefs[chartKey] = ref;

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  React.useImperativeHandle(ref, () => ({
    reloadChart: () => {
      if (reloadChartFilters[chartKey].chart_bin_type !== undefined) {
        filters[pageKey].bin_type = reloadChartFilters[chartKey].chart_bin_type;
        delete reloadChartFilters[chartKey].chart_bin_type;
        updateChartTitle();
        chartDataQuery.refetch();
      }
    },
  }));

  /**
   * Update chart title based on bin type
   */
  const updateChartTitle = () => {
    // TODO: Use data returned in API response when getting chart data in updating chart title
    if (filters[pageKey].bin_type) {
      settings.title =
        filters[pageKey].bin_type === "soft"
          ? settings.title.replace("Hardware", "Software")
          : settings.title.replace("Software", "Hardware");
      options.title.text = settings.title;
    }
  };

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart?.hideNoData();
    chartRef.current?.chart?.showLoading();

    const allFilters = { ...filters[pageKey], ...prerenderData };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
    enabled: defaultChartOptions.isSuccess,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        setHasChartData(
          Array.isArray(response.data.y_data) &&
            response.data.y_data.length > 0,
        );
        if (response.data?.max_y) {
          options.yAxis[0].max = response.data.max_y;
        }
        if (response.data?.max_y2) {
          options.yAxis[1].max = response.data.max_y2;
        }
        options.xAxis[0].categories = response.data.x_categories;
        options.series[0].data = response.data.line_data;
        options.series[1].data = response.data.y_data;
        if (settings.bar2) {
          options.series.push({
            name: settings?.bar2?.title,
            type: "column",
            color: "#FF6BF8",
            yAxis: settings?.bar2?.y_axis ?? 0,
            zIndex: 2,
            tooltip: {
              valueDecimals: settings?.bar2?.value_decimals ?? 2,
              valueSuffix: settings?.bar2?.value_suffix ?? "",
              pointFormatter: function () {
                return ` 
                  ${this.series.name}: <b>${Helper.numberFormat(this.y, options.series[2].tooltip.valueDecimals)}${options.series[2].tooltip.valueSuffix}</b><br/>
                  ${settings.bar.tooltip.label ? `${settings.bar.tooltip.label}: <b>${response.data.bar_data[this.index]}</b><br/>` : ""}
                  ${settings.bar2.tooltip.label ? `${settings.bar2.tooltip.label}: <b>${response.data.bar2_data[this.index]}</b><br/>` : ""}
                `;
              },
            },
            data: response.data.y2_data,
          });
        }
        options.series[0].tooltip.pointFormatter = function () {
          return `
            ${this.series.name}: <b>${Helper.numberFormat(this.y, options.series[0].tooltip.valueDecimals)}${options.series[0].tooltip.valueSuffix}</b><br/>
            ${response.data.count ? `Fails: <b>${Helper.numberFormat(response.data.count[this.index])}</b><br/>` : ""}
            ${response.data.rejects_prct ? `Fail % of Line: <b>${response.data.rejects_prct[this.index]}%</b>` : ""}
          `;
        };
        options.series[1].tooltip.pointFormatter = function () {
          return `
            ${this.series.name}: <b>${Helper.numberFormat(this.y, options.series[1].tooltip.valueDecimals)}${options.series[1].tooltip.valueSuffix}</b><br/>
          `;
        };

        const chartOptions = defaultChartOptions.data?.data?.value
          ? merge(
              options,
              JSON.parse(defaultChartOptions.data.data.value),
              retainedChartOptions[chartKey] ?? {},
            )
          : options;
        setChartOptions(chartOptions);
      } else {
        notificationApi.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notificationApi.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    ChartHelper.updateExportingMenu(
      chart,
      setIsChartOptionsOpen,
      setCurrentChart,
      chartCustomData[chartKey],
      fullScreenHandle,
    );

    redrawWhenBoosted(chart);
  };

  return (
    <div>
      {contextHolder}
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
});
