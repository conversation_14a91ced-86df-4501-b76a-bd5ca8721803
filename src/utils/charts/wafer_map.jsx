"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/modules/heatmap";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import "highcharts/modules/boost";
import React, { useEffect, useRef, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Helper from "../helper";
import Api from "../api";
import { useBoundStore } from "../../store/store";
import { useEffectApiFetch } from "../../hooks";
import { QueryKeys } from "../query_keys";
import { UserSettingsKeys } from "../user_settings_keys";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import { ComponentNameMapper } from "../grid/component_name_mapper";
import ChartHelper from "./chart_helper";
import drawNotch from "./utils/drawNotch";
import ChartLoading from "./loading";
import { redrawWhenBoosted } from "./chart_common";

if (typeof Highcharts === "object") {
  // plugin code to set inactive series color to gray instead of hiding the series
  (function (H) {
    H.wrap(
      H.Series.prototype,
      "setVisible",
      function (proceed, visible, redraw) {
        const series = this,
          chart = series.chart,
          inactiveColor = "#a8a8a8";

        if (chart.userOptions.chart.disableHiddenSeries) {
          series.visible =
            visible !== undefined
              ? visible
              : series.userOptions.custom?.fakeVisible === undefined
                ? false
                : !series.userOptions.custom?.fakeVisible;

          if (series.legendItem) {
            chart.legend.colorizeItem(series);
          }
          series.update(
            {
              color: series.visible
                ? series.userOptions.activeColor
                : inactiveColor,
              custom: merge(series.userOptions?.custom, {
                fakeVisible: series.visible,
              }),
            },
            false,
          );

          if (redraw !== false) {
            chart.redraw();
          }
        } else {
          proceed.apply(this);
        }
      },
    );

    H.wrap(H.Legend.prototype, "colorizeItem", function (_, item, visible) {
      visible = item.userOptions?.custom?.fakeVisible ?? visible;
      const { area, group, label, line, symbol } = item.legendItem || {};
      group?.[visible ? "removeClass" : "addClass"](
        "highcharts-legend-item-hidden",
      );
      if (!this.chart.styledMode) {
        const { itemHiddenStyle = {} } = this,
          hiddenColor = itemHiddenStyle.color,
          { fillColor, fillOpacity, lineColor, marker } = item.options,
          colorizeHidden = (attr) => {
            if (!visible) {
              if (attr.fill) {
                attr.fill = hiddenColor;
              }
              if (attr.stroke) {
                attr.stroke = hiddenColor;
              }
            }
            return attr;
          };
        label?.css(H.merge(visible ? this.itemStyle : itemHiddenStyle));
        label?.css({ "text-decoration": visible ? "none" : "line-through" });
        line?.attr(colorizeHidden({ stroke: lineColor || item.color }));
        if (symbol) {
          // Apply marker options
          symbol.attr(
            colorizeHidden(
              marker && symbol.isMarker // #585
                ? item.pointAttribs()
                : { fill: item.color },
            ),
          );
        }
        area?.attr(
          colorizeHidden({
            fill: fillColor || item.color,
            "fill-opacity": fillColor ? 1 : (fillOpacity ?? 0.75),
          }),
        );
      }
      H.fireEvent(this, "afterColorizeItem", { item, visible });
    });
  })(Highcharts);
}

const yieldColors = [
  "#a50026",
  "#bf1a27",
  "#d6352c",
  "#e75438",
  "#f37446",
  "#f99556",
  "#fcb469",
  "#fecf7e",
  "#fee597",
  "#fcf4aa",
  "#f2f8aa",
  "#e0f297",
  "#c8e882",
  "#addc72",
  "#8ece68",
  "#6bbf62",
  "#46ad5b",
  "#259850",
  "#0f8144",
  "#006837",
];

/**
 * Wafer map chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {function} setWaferIdOptions
 * @param {object} chartCustomData
 * @param {string} chartKey
 * @param {object} chartFilters
 * @param {object} chartSettings
 * @param {function} setIsChartBoosted
 * @param {function} setHighchartsChart
 * @param {function} setHasChartData
 * @param {index} galleryChartIndex
 * @param {function} setGalleryChartIndex
 * @param {object} fullScreenHandle
 * @param {object} prerenderData
 * @param {object} requiredData
 * @param {React.Ref} ref
 * @returns {JSX.Element}
 */
export default React.forwardRef(function WaferMap(
  {
    chartRef,
    component,
    filters,
    pageKey,
    setWaferIdOptions,
    chartCustomData,
    chartKey,
    chartFilters = {},
    chartSettings = {},
    setIsChartBoosted,
    setHighchartsChart,
    setHasChartData,
    galleryChartIndex,
    setGalleryChartIndex,
    fullScreenHandle,
    prerenderData = {},
    requiredData,
  },
  ref,
) {
  const [chartOptions, setChartOptions] = useState();
  const [allWaferData, setAllWaferData] = useState({});
  const [topFailingTestData, setTopFailingTestData] = useState();
  const [shouldFetchChartData, setShouldFetchChartData] = useState(false);
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const urlParams = useBoundStore((state) => state.urlParams);
  const cacheData = useBoundStore((state) => state.cacheData);
  const pageMeta = useBoundStore((state) => state.pageMeta);
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const chartData = useRef();
  const { message } = App.useApp();
  const [notificationApi, contextHolder] = notification.useNotification();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const isDataLabelsEnabled =
    settings.dataLabels && settings.dataLabels.enabled !== undefined
      ? settings.dataLabels.enabled
      : true;
  const defaultSpacingTop = 20;
  const defaultSpacingBottom = 20;
  const defaultRotation = "90";
  const defaultFlip = "none";
  const chartType = component.name;
  const boost = settings.boost_mode ?? true;
  const boostThreshold = boost ? 1 : 5000;

  /**
   * Get wafer info width
   *
   * @param {object} chart
   * @returns {number} width
   */
  const getWaferInfoWidth = (chart) => {
    let width = 0;
    if (chart) {
      width = (chart.chartWidth ?? 0) - (chart.plotLeft ?? 0);
    }

    return width;
  };

  /**
   * Display of range labels on color axis
   *
   * @param {object} chart
   */
  const renderRangeLabelsToColorAxis = (chart) => {
    const renderer = chart.renderer;

    if (chartCustomData[chartKey].rangeLabels) {
      chartCustomData[chartKey].rangeLabels.forEach((rangeLabel) => {
        rangeLabel.element.remove();
      });
    }
    chartCustomData[chartKey].rangeLabels = [];

    const rangeLabelFontSize = 10;
    const defaultLegendMargin = 24;
    let rangeMaxWidth = defaultLegendMargin;
    chart.legend.allItems.forEach((item, itemIndex) => {
      var itemX = item.legendItem.group.translateX;
      var itemY = item.legendItem.group.translateY;
      const rangeLabel = renderer
        .text(
          item.to,
          itemX - rangeLabelFontSize / 2,
          itemY + rangeLabelFontSize / 2,
        )
        .attr({
          align: "right",
        })
        .css({
          fontSize: `${rangeLabelFontSize}px`,
        })
        .add(item.legendItem.group.parentGroup);

      const rangeLabelWidth = rangeLabel.getBBox().width;
      if (rangeLabelWidth > rangeMaxWidth) {
        rangeMaxWidth = rangeLabelWidth;
      }

      chartCustomData[chartKey].rangeLabels.push(rangeLabel);

      // render minimum range label
      if (itemIndex === chart.legend.allItems.length - 1) {
        const rangeLabel = renderer
          .text(
            item.from,
            itemX - rangeLabelFontSize / 2,
            itemY + item.itemHeight + rangeLabelFontSize / 2,
          )
          .attr({
            align: "right",
          })
          .css({
            fontSize: `${rangeLabelFontSize}px`,
          })
          .add(item.legendItem.group.parentGroup);

        const rangeLabelWidth = rangeLabel.getBBox().width;
        if (rangeLabelWidth > rangeMaxWidth) {
          rangeMaxWidth = rangeLabelWidth;
        }

        chartCustomData[chartKey].rangeLabels.push(rangeLabel);
      }
    });

    const newLegendMargin = rangeMaxWidth + defaultLegendMargin;
    if (chart.legend.options.margin !== newLegendMargin) {
      options.legend.margin = newLegendMargin;
      // add x offset to avoid navigation arrow being clipped if range labels are not wide enought
      options.legend.x = -10;
      const chartOptions = defaultChartOptions.data?.data?.value
        ? merge(options, JSON.parse(defaultChartOptions.data.data.value))
        : options;
      setChartOptions(chartOptions);
      chartCustomData[chartKey].shouldUpdateChart = true;
    }
  };

  /**
   * Display yield value on chart
   *
   * @param {object} chart
   * @param {string} yieldValue
   */
  const renderYieldValueToChart = (chart, yieldValue) => {
    // remove yield value if exists
    if (
      chartCustomData[chartKey].yieldValueElement &&
      chartCustomData[chartKey].shouldRemoveYieldValueElement !== false
    ) {
      chartCustomData[chartKey].yieldValueElement.remove();
    }
    chartCustomData[chartKey].yieldValue = yieldValue;
    const yieldValueElement = chart.renderer.g("yield-value").add();

    chart.renderer
      .text(`${Helper.numberFormat(yieldValue, 2)}%`, chart.chartWidth - 50, 80)
      .attr({
        align: "right",
      })
      .css({
        fontSize: "12px",
      })
      .add(yieldValueElement);

    // store rendered yield value element to be used when removing the displayed element to avoid duplicate rendering
    if (chartCustomData[chartKey].shouldRemoveYieldValueElement !== false) {
      chartCustomData[chartKey].yieldValueElement =
        chart.container.querySelectorAll(".highcharts-yield-value")[0];
    }
    chartCustomData[chartKey].shouldRemoveYieldValueElement = true;
  };

  /**
   * Display color axis label on chart
   *
   * @param {object} chart
   * @param {string} colorAxisLabel
   * @param {object} customData
   */
  const renderColorAxisLabelToChart = (chart, colorAxisLabel, customData) => {
    const legend = chart.legend;
    const legendCoordinates = {
      x: legend.group.translateX,
      y: legend.group.translateY,
    };

    if (customData.colorAxisLabelElement) {
      customData.colorAxisLabelElement.forEach((element) => {
        element.remove();
      });
    }

    const colorAxisLabelElement = chart.renderer.g("color-axis-label").add();

    chart.renderer
      .text(
        `${colorAxisLabel}`,
        legendCoordinates.x + 5,
        chart.chartHeight / 2 - legend.legendHeight / 2 - 10,
      )
      .attr({
        align: "left",
      })
      .css({
        fontSize: "12px",
      })
      .add(colorAxisLabelElement);

    customData.colorAxisLabelElement = chart.container.querySelectorAll(
      ".highcharts-color-axis-label",
    );
  };

  /**
   * Display arrows on chart
   *
   * @param {object} chart
   * @param {string} rotation
   * @param {string} flip
   */
  const renderArrowsToChart = (chart, rotation, flip) => {
    // remove arrows if exists
    if (
      chartCustomData[chartKey].arrowsElement &&
      chartCustomData[chartKey].shouldRemoveArrowsElement !== false
    ) {
      chartCustomData[chartKey].arrowsElement.remove();
    }
    const colors = Highcharts.getOptions().colors;
    const length = 50;

    // set arrows placement on chart
    let x = chart.plotLeft + 10;
    let y = chart.plotSizeY + chart.plotTop - 10;
    switch (true) {
      case rotation === "90" && flip === "none":
      case rotation === "180" && flip === "x":
      case rotation === "0" && flip === "y":
        y = chart.plotTop + 10;
        break;
      case rotation === "180" && flip === "none":
        x = chart.plotSizeX + chart.plotLeft - 10;
        y = chart.plotTop + 10;
        break;
      case rotation === "270" && flip === "none":
        x = chart.plotSizeX + chart.plotLeft + 10;
        y = chart.plotSizeY + chart.plotTop - 30;
        break;
      case rotation === "0" && flip === "x":
      case rotation === "180" && flip === "y":
        x = chart.plotSizeX + chart.plotLeft - 10;
        break;
      case rotation === "90" && flip === "x":
      case rotation === "270" && flip === "y":
        y = chart.plotSizeY + chart.plotTop - 20;
        break;
      case rotation === "270" && flip === "x":
      case rotation === "90" && flip === "y":
        x = chart.plotSizeX + chart.plotLeft + 10;
        y = chart.plotTop + 10;
        break;
    }

    // set arrow svg path
    const rightArrow = [
      "M",
      x,
      y,
      "L",
      x + length,
      y,
      "L",
      x + length - 5,
      y + 5,
      "M",
      x + length,
      y,
      "L",
      x + length - 5,
      y - 5,
    ];
    const leftArrow = [
      "M",
      x,
      y,
      "L",
      x - length,
      y,
      "L",
      x - length + 5,
      y + 5,
      "M",
      x - length,
      y,
      "L",
      x - length + 5,
      y - 5,
    ];
    const upArrow = [
      "M",
      x,
      y,
      "L",
      x,
      y - length,
      "L",
      x - 5,
      y - length + 5,
      "M",
      x,
      y - length,
      "L",
      x + 5,
      y - length + 5,
    ];
    const downArrow = [
      "M",
      x,
      y,
      "L",
      x,
      y + length,
      "L",
      x - 5,
      y + length - 5,
      "M",
      x,
      y + length,
      "L",
      x + 5,
      y + length - 5,
    ];
    const arrowsElement = chart.renderer.g("arrows").add();

    // set x and y arrows
    let xArrow = rightArrow;
    let yArrow = upArrow;
    switch (true) {
      case rotation === "90" && flip === "none":
        xArrow = downArrow;
        yArrow = rightArrow;
        break;
      case rotation === "180" && flip === "none":
        xArrow = leftArrow;
        yArrow = downArrow;
        break;
      case rotation === "270" && flip === "none":
        xArrow = upArrow;
        yArrow = leftArrow;
        break;
      case rotation === "0" && flip === "x":
        xArrow = leftArrow;
        break;
      case rotation === "90" && flip === "x":
        xArrow = upArrow;
        yArrow = rightArrow;
        break;
      case rotation === "180" && flip === "x":
        yArrow = downArrow;
        break;
      case rotation === "270" && flip === "x":
        xArrow = downArrow;
        yArrow = leftArrow;
        break;
      case rotation === "0" && flip === "y":
        yArrow = downArrow;
        break;
      case rotation === "90" && flip === "y":
        xArrow = downArrow;
        yArrow = leftArrow;
        break;
      case rotation === "180" && flip === "y":
        xArrow = leftArrow;
        break;
      case rotation === "270" && flip === "y":
        xArrow = upArrow;
        yArrow = rightArrow;
        break;
    }

    chart.renderer
      .path(xArrow)
      .attr({
        "stroke-width": 2,
        stroke: colors[3],
      })
      .add(arrowsElement);

    chart.renderer
      .path(yArrow)
      .attr({
        "stroke-width": 2,
        stroke: colors[1],
      })
      .add(arrowsElement);

    // store arrows element to be used when removing the displayed element to avoid duplicate rendering
    if (chartCustomData[chartKey].shouldRemoveArrowsElement !== false) {
      chartCustomData[chartKey].arrowsElement =
        chart.container.querySelectorAll(".highcharts-arrows")[0];
    }
    chartCustomData[chartKey].shouldRemoveArrowsElement = true;
  };

  const options = merge(ChartHelper.getChartDefaultSettings(false, boost), {
    chart: {
      type: "heatmap",
      renderTo: "container",
      title: {},
      spacingTop: defaultSpacingTop,
      spacingBottom: defaultSpacingBottom,
      zoomType: "xy",
      events: {
        fullscreenOpen: (chart) => {
          chartCustomData[chartKey].isFullScreen = true;

          if (component.component === "yield_wafer_map") {
            chart.shouldSetYieldPosition = true;
            chart.shouldSetWaferInfoPosition = true;
          }
          if (component.component === "composite_wafer_map") {
            chart.shouldSetWaferInfoPosition = true;
          }
          if (component.component === "parametric_wafer_map") {
            chartCustomData[chartKey].shouldUpdateParametricColorAxis = true;
            chart.shouldSetWaferInfoPosition = true;
          }
          if (settings.color_axis_label) {
            chartCustomData[chartKey].shouldSetColorAxisLabelPosition = true;
          }
        },
        fullscreenClose: (chart) => {
          chartCustomData[chartKey].isFullScreen = false;

          if (component.component === "yield_wafer_map") {
            chart.shouldSetYieldPosition = true;
            chart.shouldSetWaferInfoPosition = true;
          }
          if (component.component === "composite_wafer_map") {
            chart.shouldSetWaferInfoPosition = true;
          }
          if (component.component === "parametric_wafer_map") {
            chartCustomData[chartKey].shouldUpdateParametricColorAxis = true;
            chart.shouldSetWaferInfoPosition = true;
          }
          if (settings.color_axis_label) {
            chartCustomData[chartKey].shouldSetColorAxisLabelPosition = true;
          }
        },
        render: (event) => {
          const chart = event.target;

          if (component.component === "parametric_wafer_map") {
            renderRangeLabelsToColorAxis(chart);
          }

          if (chart.shouldSetChartSize) {
            chart.shouldSetChartSize = false;
            setChartSize(chart);
          }

          if (
            settings.color_axis_label &&
            chartCustomData[chartKey].shouldSetColorAxisLabelPosition
          ) {
            chartCustomData[chartKey].shouldSetColorAxisLabelPosition = false;
            renderColorAxisLabelToChart(
              chart,
              settings.color_axis_label,
              chartCustomData[chartKey],
            );
          }

          if (chart.shouldSetYieldPosition && chartData.current.pct_of_units) {
            chart.shouldSetYieldPosition = false;
            renderYieldValueToChart(chart, chartData.current.pct_of_units);
          }

          if (chart.shouldSetWaferInfoPosition) {
            chart.shouldSetWaferInfoPosition = false;
            if (
              settings.show_info ||
              (!chartSettings.hideWaferInfo &&
                component.name !== "lot_parametric_wafer_map_ftr")
            ) {
              ChartHelper.renderWaferInfoToChart(
                chart,
                chartCustomData[chartKey].waferInfo,
                options,
                chartCustomData[chartKey],
              );
            }
          }
          const userOrientation = getUserOrientation();
          if (
            (reloadChartFilters[chartKey] &&
              reloadChartFilters[chartKey].display_arrows === true) ||
            (Object.keys(reloadChartFilters[chartKey] ?? {}).length === 0 &&
              userOrientation.display_arrows === true)
          ) {
            renderArrowsToChart(
              chart,
              chartCustomData[chartKey].rotation,
              chartCustomData[chartKey].flip,
            );
            chartCustomData[chartKey].displayArrows = true;
          } else if (chartCustomData[chartKey].arrowsElement) {
            chartCustomData[chartKey].arrowsElement.remove();
            chartCustomData[chartKey].displayArrows = false;
          } else {
            chartCustomData[chartKey].displayArrows = false;
          }
          if (chartCustomData[chartKey].shouldUpdateParametricColorAxis) {
            chartCustomData[chartKey].shouldUpdateParametricColorAxis = false;
            updateParametricColorAxis(
              chartCustomData[chartKey].minScale
                ? chartCustomData[chartKey].minScale
                : chartCustomData[chartKey].minValue,
              chartCustomData[chartKey].maxScale
                ? chartCustomData[chartKey].maxScale
                : chartCustomData[chartKey].maxValue,
              chartCustomData[chartKey].values,
              chartCustomData[chartKey].selectedColorScale,
            );
          }
          if (chartCustomData[chartKey].shouldHighlightBins) {
            chartCustomData[chartKey].shouldHighlightBins = false;
            highlightBins(chart, chartCustomData[chartKey].binsToHighlight);
          }
          if (
            component.component === "composite_wafer_map" &&
            !ChartHelper.isChartBoosted(chart)
          ) {
            enableInactiveStateOnLegendHover(chart);
          }

          if (chartCustomData[chartKey].shouldUpdateChart) {
            chartCustomData[chartKey].shouldUpdateChart = false;
            chart.update(options);
          }
          if (typeof setIsChartBoosted === "function") {
            setIsChartBoosted(ChartHelper.isChartBoosted(chart));
          }
          if (typeof setHighchartsChart === "function") {
            setHighchartsChart(chart);
          }
          if (chartCustomData[chartKey]?.wafer_flat) {
            drawNotch(
              chart,
              chartCustomData[chartKey].wafer_flat,
              chartCustomData[chartKey],
            );
          }
        },
      },
    },
    exporting: {
      enabled: settings.exporting?.enabled ?? true,
      chartOptions: {
        chart: {
          events: {
            load: (event) => {
              const chart = event.target;
              if (component.component === "yield_wafer_map") {
                chart.shouldSetYieldPosition = true;
                chartCustomData[chartKey].shouldRemoveYieldValueElement = false;
                chartCustomData[chartKey].shouldRemoveArrowsElement = false;
              }
              if (
                component.component === "composite_wafer_map" ||
                component.component === "parametric_wafer_map"
              ) {
                chart.shouldSetWaferInfoPosition = true;
                chartCustomData[chartKey].shouldRemoveWaferInfoElement = false;
                chartCustomData[chartKey].shouldRemoveArrowsElement = false;
              }
              if (settings.color_axis_label) {
                chartCustomData[chartKey].shouldSetColorAxisLabelPosition =
                  true;
              }
            },
          },
        },
      },
    },
    plotOptions: {
      heatmap: {
        nullColor: "#A8A8A8",
        dataLabels: {
          enabled: false,
          style: {
            fontSize: "6px",
            fontWeight: "normal",
          },
        },
      },
    },
    title: {
      text: settings.show_title
        ? ChartHelper.generateChartTitle(
            settings?.title,
            merge({}, filters[pageKey], chartFilters, prerenderData),
          )
        : "",
    },
    xAxis: {
      title: {
        enabled: false,
        text: "X",
      },
      labels: {
        enabled: isDataLabelsEnabled,
      },
      gridLineWidth: 0,
      lineWidth: 0,
      type: "category",
      tickInterval: 5,
      startOnTick: false,
      endOnTick: false,
    },
    yAxis: {
      title: {
        enabled: false,
        text: "Y",
      },
      labels: {
        enabled: isDataLabelsEnabled,
      },
      gridLineWidth: 0,
      type: "category",
      tickInterval: 5,
      startOnTick: false,
      endOnTick: false,
    },
    legend: {
      align: "right",
      layout: "vertical",
      margin: 24,
      verticalAlign: "middle",
    },
    tooltip: {
      enabled: settings.tooltip?.enabled ?? true,
      formatter: function () {
        return `
        <div style="height: 8px;
        width: 8px;
        background-color: ${this.point.color};
        border-radius: 50%;
        display: inline-block;"></div> ${this.series.name}<br/>
        @(${this.point.x}, ${this.point.y})`;
      },
      useHTML: true,
      shadow: false,
    },
    series: [],
    disableOptions: {
      legend: true,
      xAxisTitle: true,
      yAxisTitle: true,
    },
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      noAxisSettings: true,
      noBarSettings: true,
      default: settings,
    },
  });

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    if (topFailingTestData !== undefined) {
      filters[pageKey].tNum = topFailingTestData.tnum;
    }
    let allFilters = { ...filters[pageKey], ...chartFilters, ...prerenderData };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
    enabled: shouldFetchChartData && defaultChartOptions.isSuccess,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        setAllWaferData(response.data);
        updateChartData(response.data);
      } else {
        notificationApi.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notificationApi.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  useEffectApiFetch(
    () => {
      if (component.component === "parametric_wafer_map") {
        getTopFailingTestData();
      }
    },
    () => {
      if (component.component === "parametric_wafer_map") {
        setTopFailingTestData();
      }
    },
    [],
  );

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    } else if (component.component !== "parametric_wafer_map") {
      getChartData(chartRef.current?.chart);
    }
    chartComponentRefs[chartKey] = ref;

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  useEffect(() => {
    if (topFailingTestData !== undefined) {
      getChartData(chartRef.current?.chart);
    }
  }, [topFailingTestData]);

  React.useImperativeHandle(ref, () => ({
    reloadChart: () => {
      getChartData(chartRef.current?.chart);
    },
  }));

  /**
   * Get user saved orientation settings
   *
   * @returns {object} userOrientation
   */
  const getUserOrientation = () => {
    const userOrientation =
      Helper.getUserSettings(
        UserSettingsKeys.wafer_map_orientation,
        "chart",
        component,
      ) ?? {};
    return userOrientation;
  };

  /**
   * Set chart size to equal width and height
   *
   * @param {object} chart
   */
  const setChartSize = (chart) => {
    const legendWidth = chart.legend.group.getBBox().width;
    const width = chart.chartWidth;
    const height = width - legendWidth;
    chart.renderTo.style.height = `${height}px`;
    chart.setSize(width, height, false);
  };

  /**
   * Get top failing test data
   */
  const getTopFailingTestData = () => {
    if (component.props.params.body_params.test_number !== undefined) {
      setTopFailingTestData({
        tnum: component.props.params.body_params.test_number,
      });
    } else if (component.component === "parametric_wafer_map") {
      Helper.getLotTopFailingTestData(
        urlParams[pageKey].src_type,
        urlParams[pageKey].src_value,
        urlParams[pageKey].mfg_process,
        setTopFailingTestData,
        message.warning,
        message.error,
        cacheData,
      );
    }
  };

  /**
   * Get chart data
   *
   * @param {object} chart
   * @returns {AbortController|void}
   */
  const getChartData = (chart) => {
    chart?.hideNoData();
    chart?.showLoading();
    if (
      reloadChartFilters[chartKey] &&
      (reloadChartFilters[chartKey].wafer_id_selection !== undefined ||
        reloadChartFilters[chartKey].rotation !== undefined ||
        reloadChartFilters[chartKey].flip !== undefined ||
        reloadChartFilters[chartKey].display_arrows !== undefined ||
        chartCustomData[chartKey].selectedColorScale !== undefined ||
        chartCustomData[chartKey].minScale ||
        chartCustomData[chartKey].maxScale) &&
      Object.keys(allWaferData).length > 0
    ) {
      updateChartData(allWaferData);
      chart?.hideLoading();
    } else {
      if (!shouldFetchChartData) {
        setShouldFetchChartData(true);
      } else {
        chartDataQuery.refetch();
      }
    }
  };

  /**
   * Update chart with provided data
   *
   * @param {object} waferData
   */
  const updateChartData = (waferData) => {
    let waferIdSelection;
    let waferMapData;
    chartData.current = waferData;

    if (waferData?.wafer_flat) {
      chartCustomData[chartKey].wafer_flat = waferData.wafer_flat;
    }

    switch (component.component) {
      case "composite_wafer_map":
        waferIdSelection =
          reloadChartFilters[chartKey] &&
          reloadChartFilters[chartKey].wafer_id_selection !== undefined
            ? reloadChartFilters[chartKey].wafer_id_selection
            : chartFilters && chartFilters.wafer_id_selection !== undefined
              ? chartFilters.wafer_id_selection
              : undefined;

        waferMapData =
          waferIdSelection !== undefined
            ? waferData.per_wafer[waferIdSelection.value]
            : waferData.composite;

        setHasChartData(
          waferMapData && Object.keys(waferMapData.data).length > 0,
        );
        generateCompositeWaferMapChartOptions(options, waferMapData, waferData);

        if (
          waferIdSelection !== undefined &&
          !chartSettings.hideWaferInfo &&
          component.name !== "lot_parametric_wafer_map_ftr"
        ) {
          chartCustomData[chartKey].waferInfo = getSelectedWaferInfo(
            waferIdSelection,
            waferData,
          );
          chartCustomData[chartKey].shouldSetWaferInfoPosition = true;
        } else if (chartCustomData[chartKey].waferInfoElement) {
          ChartHelper.removeWaferInfo(
            chartRef.current?.chart,
            options,
            chartCustomData[chartKey],
          );
        }
        if (settings.show_info && waferData.info) {
          chartCustomData[chartKey].waferInfo = getSelectedBinPatternWaferInfo(
            waferData.info,
          );
          chartCustomData[chartKey].shouldSetWaferInfoPosition = true;
        }
        if (settings.color_axis_label) {
          chartCustomData[chartKey].shouldSetColorAxisLabelPosition = true;
        }
        if (typeof setWaferIdOptions === "function") {
          setWaferIdOptions(ChartHelper.getWaferIdOptions(waferData));
        }
        break;
      case "yield_wafer_map":
        setHasChartData(
          Array.isArray(waferData.data) && waferData.data.length > 0,
        );
        generateYieldWaferMapChartOptions(options, waferData);
        if (chartRef.current?.chart && waferData.pct_of_units) {
          renderYieldValueToChart(
            chartRef.current.chart,
            waferData.pct_of_units,
          );
        }
        if (
          !chartSettings.hideWaferInfo &&
          component.name === ComponentNameMapper.parametric_mpr_pin_yield_map
        ) {
          chartCustomData[chartKey].waferInfo = getPerPinWaferInfo(
            waferData,
            "Yield Per Pin",
          );
          chartCustomData[chartKey].shouldSetWaferInfoPosition = true;
        }
        break;
      case "parametric_wafer_map":
        setHasChartData(
          Array.isArray(waferData.data) && waferData.data.length > 0,
        );
        generateParametricWaferMapChartOptions(options, waferData);
        if (!chartSettings.hideWaferInfo) {
          let hasWaferInfo = false;
          switch (component.name) {
            case ComponentNameMapper.lot_parametric_wafer_map_ftr:
              chartCustomData[chartKey].waferInfo =
                getParametricWaferInfo(waferData);
              hasWaferInfo = true;
              break;
            case ComponentNameMapper.parametric_mpr_mean_pin_substrate_map:
              chartCustomData[chartKey].waferInfo = getPerPinWaferInfo(
                waferData,
                "Mean Per Pin",
              );
              hasWaferInfo = true;
              break;
            case ComponentNameMapper.parametric_mpr_single_device_map:
              chartCustomData[chartKey].waferInfo = getPerPinWaferInfo(
                waferData,
                "Die Result Per Pin",
              );
              hasWaferInfo = true;
              break;
          }
          if (hasWaferInfo) {
            chartCustomData[chartKey].shouldSetWaferInfoPosition = true;
          }
        }
        break;
      default:
        generateCompositeWaferMapChartOptions(options, waferData);
    }

    if (chartRef.current?.chart) {
      chartRef.current.chart.shouldSetWaferInfoPosition =
        chartCustomData[chartKey].shouldSetWaferInfoPosition;
    }

    const chartOptions = defaultChartOptions.data?.data?.value
      ? merge(
          options,
          JSON.parse(defaultChartOptions.data.data.value),
          retainedChartOptions[chartKey] ?? {},
        )
      : options;

    setChartOptions(chartOptions);

    // increment gallery chart index to trigger create on next chart
    if (
      !isNaN(galleryChartIndex) &&
      typeof setGalleryChartIndex === "function"
    ) {
      setGalleryChartIndex(galleryChartIndex + 1);
    }

    // check for queue process
    Helper.checkQueueProcess(
      Helper.getQueueGroup(component),
      requiredData?.render_key
        ? Helper.generateTabKey(requiredData.render_key, prerenderData)
        : component.name,
    );
  };

  /**
   * Get selected wafer info
   *
   * @param {object} waferIdSelection
   * @param {object} waferData
   * @returns {string} waferInfo
   */
  const getSelectedWaferInfo = (waferIdSelection, waferData) => {
    const fileName =
      waferData?.composite?.qmap_header?.[waferIdSelection.value]?.file_name;
    const waferId =
      waferData?.composite?.qmap_header?.[waferIdSelection.value]?.wafer_id;
    const waferInfo = `Filename: ${fileName}<br/>Wafer: ${waferId}`;

    return waferInfo;
  };

  /**
   * Get parametric wafer info
   *
   * @param {object} waferData
   * @returns {string} waferInfo
   */
  const getParametricWaferInfo = (waferData) => {
    const chart = chartRef.current?.chart;
    const chartInfo = waferData.info;
    let waferInfo = "";

    if (pageMeta.analysis_type === "single" && chartInfo) {
      waferInfo = `
        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px 32px; width: ${getWaferInfoWidth(chart)}px; margin: 8px auto; box-sizing: border-box;">
          <div>${chartInfo.tname}</div>
          <div>Mean ${Helper.descriptiveFormatting(chartInfo.mean, 4, 4)}</div>
          <div>Test Number ${chartInfo.atnum}</div>
          <div>Stdev ${Helper.descriptiveFormatting(chartInfo.stdev, 4, 4)}</div>
          <div>Lot ${urlParams[pageKey].lot_id}</div>
          <div>Min ${Helper.descriptiveFormatting(chartInfo.min_result, 4, 4)}</div>
          <div>Wafer ${chartInfo.wafer_id}</div>
          <div>Max ${Helper.descriptiveFormatting(chartInfo.max_result, 4, 4)}</div>
          <div>${chartInfo.stats_type}</div>
          <div>Cp ${Helper.descriptiveFormatting(chartInfo.cp, 4, 4)}</div>
          <div>Site ${chartInfo.site_str}</div>
          <div>Cpk ${Helper.descriptiveFormatting(chartInfo.cpk, 4, 4)}</div>
        </div>
      `;
    } else if (chartInfo) {
      waferInfo = `${chartInfo.tname}<br/>Test Number ${chartInfo.atnum}<br/>${settings.title}`;
    }

    return waferInfo;
  };

  /**
   * Get mean parametric pin substrate wafer info
   *
   * @param {object} waferData
   * @param {string} perPinLabel
   * @returns {string} waferInfo
   */
  const getPerPinWaferInfo = (waferData, perPinLabel) => {
    const chart = chartRef.current?.chart;
    const chartInfo = waferData.info;
    let waferInfo = "";

    if (chartInfo) {
      waferInfo = `
        <div style="width: ${getWaferInfoWidth(chart)}px; margin: 8px auto; box-sizing: border-box;">
          <div>${chartInfo.tname}</div>
          <div>Test Number ${chartInfo.atnum}</div>
          <div>${perPinLabel}</div>
          <div>${chartInfo.stats_type}</div>
          <div>Site ${chartInfo.site_str}</div>
        </div>
      `;
    }

    return waferInfo;
  };

  /**
   * Get selected bin pattern info
   *
   * @param {object} info
   * @returns {string}
   */
  const getSelectedBinPatternWaferInfo = (info) => {
    return `Lot: ${info?.lot_ids?.[0]}<br/>Wafer: ${info?.wafer_ids?.[0]}<br/>${settings.has_reprobe_markings ? `Yield: ${Helper.numberFormat(info?.pct_of_units, 2)} %<br/>` : ""}`;
  };

  /**
   * Generate composite wafer map chart options
   *
   * @param {object} options
   * @param {object} waferMapData
   * @param {object} waferData
   */
  const generateCompositeWaferMapChartOptions = (
    options,
    waferMapData,
    waferData,
  ) => {
    let xCoords = [];
    let yCoords = [];
    waferMapData &&
      Object.values(waferMapData.data).forEach((binData) => {
        binData.forEach((data) => {
          xCoords.push(data[0]);
          yCoords.push(data[1]);
        });
      });

    const minXCoord = Helper.getArrayMin(
      Array.isArray(waferMapData?.x_categories)
        ? waferMapData.x_categories
        : xCoords,
    );
    const maxXCoord = Helper.getArrayMax(
      Array.isArray(waferMapData?.x_categories)
        ? waferMapData.x_categories
        : xCoords,
    );
    const minYCoord = Helper.getArrayMin(
      Array.isArray(waferMapData?.y_categories)
        ? waferMapData.y_categories
        : yCoords,
    );
    const maxYCoord = Helper.getArrayMax(
      Array.isArray(waferMapData?.y_categories)
        ? waferMapData.y_categories
        : yCoords,
    );

    const userOrientation = getUserOrientation();
    const orientation =
      reloadChartFilters[chartKey] &&
      reloadChartFilters[chartKey].rotation !== undefined &&
      reloadChartFilters[chartKey].flip !== undefined
        ? [
            reloadChartFilters[chartKey].rotation,
            reloadChartFilters[chartKey].flip,
          ]
        : userOrientation.rotation !== undefined &&
            userOrientation.flip !== undefined
          ? [userOrientation.rotation, userOrientation.flip]
          : [defaultRotation, defaultFlip];
    chartCustomData[chartKey].rotation = orientation[0];
    chartCustomData[chartKey].flip = orientation[1];
    ChartHelper.setWaferOrientation(
      chartCustomData[chartKey].rotation,
      chartCustomData[chartKey].flip,
      options,
    );

    options.xAxis.categories = Helper.arrayUnique(waferMapData?.x_categories);
    options.yAxis.categories = Helper.arrayUnique(waferMapData?.y_categories);
    options.xAxis.min = minXCoord;
    options.xAxis.max = maxXCoord;
    options.yAxis.min = minYCoord;
    options.yAxis.max = maxYCoord;
    options.chart.disableHiddenSeries = true;

    disableInactiveStateOnSeriesHover(options);

    // Remove all existing series from the chart to reset legend items
    while (chartRef.current?.chart.series.length > 0) {
      chartRef.current.chart.series[0].remove(false);
    }

    // Mapping of transparent data based on x and y values
    let allDataFromXY = [];
    waferMapData?.x_categories?.forEach((_, index) => {
      allDataFromXY.push([
        waferMapData.x_categories[index],
        waferMapData.y_categories[index],
        settings.has_color_axis ? 0 : "-",
      ]);
    });
    options.series.push({
      showInLegend: false,
      name: "All X/Y",
      color: "rgba(0, 0, 0, 0)",
      borderWidth: settings.border_width ?? 0,
      borderColor: "#cccccc",
      data: allDataFromXY,
      boostThreshold: boostThreshold,
    });

    if (settings.has_color_axis) {
      options.legend.itemMarginTop = 0;
      options.legend.itemMarginBottom = 0;
      options.legend.symbolRadius = 0;
      options.legend.symbolHeight = 14;
      options.legend.symbolWidth = 14;
      options.legend.padding = 0;
      options.colorAxis = {
        min: 0,
        max: waferData.max_count,
        dataClasses: generateCompositeDataClasses(
          waferData.max_count,
          waferData.series_colors,
        ),
      };
    }

    // Mapping of data based on composite.data values
    waferMapData &&
      Object.keys(waferMapData.data)
        .map(Number)
        .forEach((bin) => {
          options.series.push({
            showInLegend: settings.show_in_legend ?? true,
            name: `${waferData?.series_label ?? "Bin"} ${bin}`,
            color: waferData.series_colors?.[bin],
            borderWidth: settings.border_width ?? 0,
            borderColor: "#cccccc",
            data: waferMapData.data[bin],
            custom: {
              bin: bin,
            },
            activeColor: waferData.series_colors?.[bin],
            fakeVisible: true,
            boostThreshold: boostThreshold,
          });
        });

    // Mapping of reprobed data points, if available
    if (settings.has_reprobe_markings && waferMapData.reprobe_xy) {
      options.series.push({
        name: "Reprobed",
        color: "#4fd6f7",
        showInLegend: false,
        data: waferMapData.reprobe_xy,
        borderColor: "#000000",
        dataLabels: {
          enabled: true,
          formatter: function () {
            return "+";
          },
          style: {
            fontSize: "20px",
            color: "#000000",
            fontWeight: "bold",
          },
        },
        boostThreshold: boostThreshold,
      });
    }
  };

  /**
   * Do not fade inactive series when hovering chart
   *
   * @param {object} options
   */
  const disableInactiveStateOnSeriesHover = (options) => {
    options.plotOptions.series = {
      states: {
        inactive: {
          enabled: false,
        },
      },
      events: {
        mouseOver: (event) => {
          updateSeriesInactiveState(event.target.chart, false);
        },
      },
    };
  };

  /**
   * Enable inactive series state when hovering legend item
   *
   * @param {object} chart
   */
  const enableInactiveStateOnLegendHover = (chart) => {
    chart.legend.allItems.forEach((item) =>
      Highcharts.addEvent(item.legendItem.group.element, "mouseover", () => {
        updateSeriesInactiveState(chart, true);
      }),
    );
  };

  /**
   * Update of chart series inactive state
   *
   * @param {object} chart
   * @param {boolean} enabled
   */
  const updateSeriesInactiveState = (chart, enabled) => {
    let shouldRedraw = false;
    chart.series.forEach((series) => {
      if (
        series.options.states.inactive.enabled === undefined ||
        series.options.states.inactive.enabled === !enabled
      ) {
        series.update(
          {
            states: {
              inactive: {
                enabled: enabled,
              },
            },
          },
          false,
        );
        shouldRedraw = true;
      }
    });
    if (shouldRedraw) {
      chart.redraw();
    }
  };

  /**
   * Generate yield wafer map chart options
   *
   * @param {object} options
   * @param {object} waferData
   */
  const generateYieldWaferMapChartOptions = (options, waferData) => {
    let xCoords = [];
    let yCoords = [];
    waferData.data.forEach((data) => {
      xCoords.push(data[0]);
      yCoords.push(data[1]);
    });

    const minXCoord = Helper.getArrayMin(xCoords);
    const maxXCoord = Helper.getArrayMax(xCoords);
    const minYCoord = Helper.getArrayMin(yCoords);
    const maxYCoord = Helper.getArrayMax(yCoords);

    const userOrientation = getUserOrientation();
    const orientation =
      reloadChartFilters[chartKey] &&
      reloadChartFilters[chartKey].rotation !== undefined &&
      reloadChartFilters[chartKey].flip !== undefined
        ? [
            reloadChartFilters[chartKey].rotation,
            reloadChartFilters[chartKey].flip,
          ]
        : userOrientation.rotation !== undefined &&
            userOrientation.flip !== undefined
          ? [userOrientation.rotation, userOrientation.flip]
          : [defaultRotation, defaultFlip];
    chartCustomData[chartKey].rotation = orientation[0];
    chartCustomData[chartKey].flip = orientation[1];
    ChartHelper.setWaferOrientation(
      chartCustomData[chartKey].rotation,
      chartCustomData[chartKey].flip,
      options,
    );

    options.xAxis.min = minXCoord;
    options.xAxis.max = maxXCoord;
    options.yAxis.min = minYCoord;
    options.yAxis.max = maxYCoord;
    options.colorAxis = {
      height: "25%",
      startOnTick: false,
      endOnTick: false,
      reversed: false,
      min: 0,
      max: 100,
      labels: {
        format: "{value}%",
      },
      stops: generateYieldColorAxisStops(),
    };
    options.tooltip.formatter = function () {
      return `<strong>${this.series.name}: ${Helper.hasValue(this.point.value) ? `${Helper.numberFormat(this.point.value, 2)}%` : "-"}</strong><br/>
          @(${this.point.x}, ${this.point.y})`;
    };
    options.series.push({
      name: "Yield",
      borderWidth: 0,
      data: waferData.data,
      boostThreshold: boostThreshold,
    });
  };

  /**
   * Generate color axis stops based on predefined set of yield colors
   *
   * @returns {array} stops
   */
  const generateYieldColorAxisStops = () => {
    const stepCount = yieldColors.length - 1;
    const stops = yieldColors.map((color, i) => {
      return [(1 / stepCount) * i, color];
    });

    return stops;
  };

  /**
   * Generate parametric wafer map chart options
   *
   * @param {object} options
   * @param {object} waferData
   */
  const generateParametricWaferMapChartOptions = (options, waferData) => {
    let xCoords = [];
    let yCoords = [];
    let values = [];
    waferData.data?.forEach((data) => {
      xCoords.push(data[0]);
      yCoords.push(data[1]);
      values.push(data[2]);
    });
    const minXCoord = Helper.getArrayMin(xCoords);
    const maxXCoord = Helper.getArrayMax(xCoords);
    const minYCoord = Helper.getArrayMin(yCoords);
    const maxYCoord = Helper.getArrayMax(yCoords);
    const minValue = chartCustomData[chartKey].minScale
      ? chartCustomData[chartKey].minScale
      : Helper.getArrayMin(values);
    const maxValue = chartCustomData[chartKey].maxScale
      ? chartCustomData[chartKey].maxScale
      : Helper.getArrayMax(values);
    chartCustomData[chartKey].minValue = Helper.getArrayMin(values);
    chartCustomData[chartKey].maxValue = Helper.getArrayMax(values);
    chartCustomData[chartKey].values = values;

    const userOrientation = getUserOrientation();
    let orientation =
      reloadChartFilters[chartKey] &&
      reloadChartFilters[chartKey].rotation !== undefined &&
      reloadChartFilters[chartKey].flip !== undefined
        ? [
            reloadChartFilters[chartKey].rotation,
            reloadChartFilters[chartKey].flip,
          ]
        : userOrientation.rotation !== undefined &&
            userOrientation.flip !== undefined
          ? [userOrientation.rotation, userOrientation.flip]
          : [defaultRotation, defaultFlip];
    chartCustomData[chartKey].rotation = orientation[0];
    chartCustomData[chartKey].flip = orientation[1];
    ChartHelper.setWaferOrientation(
      chartCustomData[chartKey].rotation,
      chartCustomData[chartKey].flip,
      options,
    );

    let selectedColorScale = chartCustomData[chartKey].selectedColorScale
      ? chartCustomData[chartKey].selectedColorScale
      : [];

    options.xAxis.min = minXCoord;
    options.xAxis.max = maxXCoord;
    options.yAxis.min = minYCoord;
    options.yAxis.max = maxYCoord;
    options.plotOptions.heatmap.color = "#A8A8A8";
    options.legend.itemMarginTop = 0;
    options.legend.itemMarginBottom = 0;
    options.legend.symbolRadius = 0;
    options.legend.symbolHeight = 14;
    options.legend.symbolWidth = 14;
    options.legend.padding = 0;
    options.colorAxis = generateParametricColorAxis(
      minValue,
      maxValue,
      values,
      selectedColorScale,
    );
    options.tooltip.formatter = function () {
      return `
        ${waferData.tooltip_details ? `Part ID: ${waferData.tooltip_details[this.point.index][0]}<br>` : ""}
        ${this.series.name}: ${Helper.hasValue(this.point.value) ? Helper.numberFormat(this.point.value, 4) : "-"}<br>
        ${waferData.tooltip_details ? `Bin #: ${waferData.tooltip_details[this.point.index][1]}<br>` : ""}
        @(${this.point.x}, ${this.point.y})
      `;
    };
    options.series.push({
      name: "Value",
      borderWidth: 0,
      data: waferData.data,
      boostThreshold: boostThreshold,
    });
  };

  /**
   * Update parametric color axis with color scale options applied
   *
   * @param {number} minValue
   * @param {number} maxValue
   * @param {array} values
   * @param {array} selectedColorScale
   */
  const updateParametricColorAxis = (
    minValue,
    maxValue,
    values,
    selectedColorScale,
  ) => {
    options.colorAxis = generateParametricColorAxis(
      minValue,
      maxValue,
      values,
      selectedColorScale,
    );
    const chartOptions = defaultChartOptions.data?.data?.value
      ? merge(options, JSON.parse(defaultChartOptions.data.data.value))
      : options;
    setChartOptions(chartOptions);
    chartCustomData[chartKey].shouldUpdateChart = true;
  };

  /**
   * Highlight bins
   *
   * @param {object} chart
   * @param {array} bins
   */
  const highlightBins = (chart, bins) => {
    chart.legend.allItems.forEach((item) => {
      if (
        bins.indexOf(item.userOptions.custom.bin) !== -1 ||
        bins.length === 0
      ) {
        item.setVisible(true);
      } else {
        item.setVisible(false);
      }
    });
  };

  /**
   * Create color axis of parametric wafer map
   *
   * @param {number} minValue
   * @param {number} maxValue
   * @param {array} values
   * @param {array} selectedColorScale
   * @returns {object} colorAxis
   */
  const generateParametricColorAxis = (
    minValue,
    maxValue,
    values,
    selectedColorScale = [],
  ) => {
    const decimalPlaces =
      chartSettings.decimalPlaces !== undefined
        ? chartSettings.decimalPlaces
        : 4;
    const bandCount =
      chartCustomData[chartKey].bandCount !== undefined
        ? chartCustomData[chartKey].bandCount
        : 20;
    let colorAxis = {
      startOnTick: false,
      endOnTick: false,
      min: minValue,
      max: maxValue,
    };
    if (component.name === "lot_parametric_wafer_map_ftr") {
      colorAxis.reversed = false;
      colorAxis.height = "50%";
      colorAxis.stops = generateColorAxisStops(values);
    } else {
      colorAxis.reversed = true;
      if (Object.keys(values).length > 0) {
        colorAxis.dataClasses = ChartHelper.generateParametricColorScale(
          minValue,
          maxValue,
          values,
          bandCount,
          selectedColorScale,
          decimalPlaces,
        );
      }
    }
    chartCustomData[chartKey].bandCount = bandCount;

    return colorAxis;
  };

  /**
   * Create data classes for composite wafer map
   *
   * @param {number} maxValue
   * @param {array} series_colors
   * @returns {array} dataClasses
   */
  const generateCompositeDataClasses = (maxValue, series_colors) => {
    let dataClasses = [];

    for (let i = maxValue; i >= 0; i--) {
      const colorClass = {
        from: i,
        to: i,
        color: i !== 0 ? series_colors[i.toString()] : "#ffffff",
        name: i,
      };
      dataClasses.push(colorClass);
    }

    return dataClasses;
  };

  /**
   * Generate color axis stops based on count of unique values
   *
   * @param {array} values
   * @returns {array} stops
   */
  const generateColorAxisStops = (values) => {
    const stops = [
      [0, "#00BB00"],
      [0.25, "#FFCC00"],
      [0.5, "#FFAA00"],
      [0.75, "#FF8800"],
      [1.0, "#FF2200"],
    ];
    const uniqueValues = Helper.arrayUnique(values);
    if (stops.length > uniqueValues.length) {
      stops.splice(1, stops.length - uniqueValues.length);
    }

    return stops;
  };

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    chart.shouldSetWaferInfoPosition =
      chartCustomData[chartKey].shouldSetWaferInfoPosition;
    chart.shouldSetChartSize = true;
    if (component.component === "yield_wafer_map") {
      chart.shouldSetYieldPosition = true;
    }
    if (component.component === "composite_wafer_map") {
      chartCustomData[chartKey].isChartNavigated = false;
    }

    ChartHelper.updateExportingMenu(
      chart,
      setIsChartOptionsOpen,
      setCurrentChart,
      chartCustomData[chartKey],
      fullScreenHandle,
    );

    chart.update({
      legend: {
        maxHeight: chart.chartHeight / 2,
      },
    });

    redrawWhenBoosted(chart);
  };

  return (
    <div>
      {contextHolder}
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <div>
            <HighchartsReact
              ref={chartRef}
              highcharts={Highcharts}
              options={chartOptions}
              callback={onChartLoaded}
              containerProps={{
                style: {
                  // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                  height: chartRef.current
                    ? chartRef.current?.chart.chartHeight
                    : settings.height,
                },
              }}
            />
          </div>
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
});
