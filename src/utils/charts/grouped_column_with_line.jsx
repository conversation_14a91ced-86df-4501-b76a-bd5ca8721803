"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Api from "../api";
import Helper from "../helper";
import { useBoundStore } from "../../store/store";
import { QueryKeys } from "../query_keys";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import ChartHelper from "./chart_helper";
import ChartLoading from "./loading";

/**
 * Component names that will be affected by yield_trend_lot_count filter
 * yield_trend_num_lots filter will be set as lot_count filter
 */
const yieldTrendLotCountComponents = ["lot_yield_trend"];

/**
 * Grouped column with line chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {function} setHasChartData
 * @param {object} fullScreenHandle
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default function GroupedColumnWithLine({
  chartRef,
  component,
  filters,
  pageKey,
  chartKey,
  chartCustomData,
  setHasChartData,
  fullScreenHandle,
  prerenderData = {},
}) {
  const [chartOptions, setChartOptions] = useState();
  const reloadChart = useBoundStore((state) => state.reloadChart);
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const { message } = App.useApp();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const chartType = component.name;

  const options = merge(ChartHelper.getChartDefaultSettings(), {
    chart: {
      zoomType: "xy",
    },
    title: {
      text: settings.title,
      align: "left",
    },
    xAxis: [
      {
        categories: [],
      },
    ],
    yAxis: [
      {
        title: {
          text: settings.y.title,
        },
      },
      {
        title: {
          text: settings.y2.title,
        },
        minPadding: 0,
        maxPadding: 0,
        max: 100,
        min: 0,
        opposite: true,
        labels: {
          format: "{value}%",
        },
        gridLineWidth: 0,
      },
    ],
    series: [],
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      default: settings,
    },
  });

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  useEffect(() => {
    if (reloadChart[component.name]) {
      if (
        reloadChartFilters[chartKey].yield_trend_lot_count !== undefined &&
        yieldTrendLotCountComponents.indexOf(component.name) !== -1
      ) {
        filters[pageKey].lot_count =
          reloadChartFilters[chartKey].yield_trend_lot_count;
        delete reloadChartFilters[chartKey].yield_trend_lot_count;
      }
      chartDataQuery.refetch();
    }
  }, [reloadChart[component.name]]);

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart?.showLoading();

    const allFilters = { ...filters[pageKey], ...prerenderData };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
    enabled: defaultChartOptions.isSuccess,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        setHasChartData(
          Array.isArray(response.data.yData) && response.data.yData.length > 0,
        );
        options.xAxis[0].categories = response.data.xData;
        const groupedColumnSeries = generateGroupedColumnSeries(response.data);
        const lineSeries = generateLineSeries(response.data);
        options.series = [...groupedColumnSeries, ...lineSeries];
        const chartOptions = defaultChartOptions.data?.data?.value
          ? merge(
              options,
              JSON.parse(defaultChartOptions.data.data.value),
              retainedChartOptions[chartKey] ?? {},
            )
          : options;
        setChartOptions(chartOptions);
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * Generate grouped column series
   *
   * @param {object} data
   *
   * @returns {array} groupedColumnSeries
   */
  const generateGroupedColumnSeries = (data) => {
    const groups = data.barLabel?.[0] ?? [];
    let groupedColumnSeries = [];
    groups.forEach((group, groupIndex) => {
      const groupData = Object.values(data.yData).map((yData) => {
        return typeof yData[groupIndex] !== "undefined"
          ? yData[groupIndex]
          : null;
      });
      groupedColumnSeries.push({
        type: "column",
        name: `${group} ${options.yAxis[0].title.text}`,
        data: groupData,
      });
    });

    return groupedColumnSeries;
  };

  /**
   * Generate line series
   *
   * @param {object} data
   * @returns {array} lineSeries
   */
  const generateLineSeries = (data) => {
    const groups = data.barLabel?.[0] ?? [];
    let lineSeries = [];
    groups.forEach((group, groupIndex) => {
      const groupData = Object.values(data.lineData).map((lineData) => {
        return typeof lineData[groupIndex] !== "undefined"
          ? lineData[groupIndex]
          : null;
      });
      lineSeries.push({
        type: "line",
        name: `${group} ${options.yAxis[1].title.text}`,
        yAxis: 1,
        zIndex: 10,
        tooltip: {
          valueDecimals: 2,
          valueSuffix: "%",
        },
        data: groupData,
      });
    });

    return lineSeries;
  };

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    ChartHelper.updateExportingMenu(
      chart,
      setIsChartOptionsOpen,
      setCurrentChart,
      chartCustomData[chartKey],
      fullScreenHandle,
    );
  };

  return (
    <div>
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
}
