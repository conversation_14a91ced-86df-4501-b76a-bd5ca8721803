import { LoadingOutlined } from "@ant-design/icons";
import { Flex, Spin } from "antd";

/**
 * Chart loading component
 *
 * @returns {JSX.Element}
 */
export default function ChartLoading({ height = 400 }) {
  return (
    <Flex justify="center" align="center" style={{ height: `${height}px` }}>
      <div className="flex-1">
        <Spin
          className="w-full"
          size="large"
          indicator={<LoadingOutlined spin />}
          tip={
            <span className="text-black/88">Loading chart, please wait...</span>
          }
        >
          <></>
        </Spin>
      </div>
    </Flex>
  );
}
