"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/highcharts-more";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import "highcharts/modules/boost";
import React, { useEffect, useRef, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Api from "../api";
import Helper from "../helper";
import { QueryKeys } from "../query_keys";
import { useBoundStore } from "../../store/store";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import ChartHelper from "./chart_helper";
import {
  getLineSeries,
  getResultsSubtitle,
  redrawWhenBoosted,
  setAxisMinMax,
  setGroupedSeriesLegend,
} from "./chart_common";
import ChartLoading from "./loading";

/**
 * Line chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {object} chartFilters
 * @param {function} setIsChartBoosted
 * @param {function} setHasChartData
 * @param {function} setHighchartsChart
 * @param {object} fullScreenHandle
 * @param {object} localChartData
 * @param {boolean} boostMode
 * @param {object} prerenderData
 * @param {React.Ref} ref
 * @returns {JSX.Element}
 */
export default React.forwardRef(function Line(
  {
    chartRef,
    component,
    filters,
    pageKey,
    chartKey,
    chartCustomData,
    chartFilters = {},
    setIsChartBoosted,
    setHasChartData,
    setHighchartsChart,
    fullScreenHandle,
    localChartData,
    boostMode = false,
    prerenderData = {},
  },
  ref,
) {
  const [chartOptions, setChartOptions] = useState();
  const [topFailingTest, setTopFailingTest] = useState();
  const [refetchChartData, setRefetchChartData] = useState(false);
  const { message } = App.useApp();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const chartData = useRef();
  const chartType = component.name;
  const boost = settings.boost_mode ?? boostMode;
  const xAxisType = settings.x_axis_type ?? "datetime";

  const options = merge(ChartHelper.getChartDefaultSettings(true, boost), {
    chart: {
      zoomType: "xy",
      events: {
        render: (event) => {
          const chart = event.target;
          if (typeof setIsChartBoosted === "function") {
            setIsChartBoosted(chart.boosted);
          }
          if (
            chartCustomData?.[chartKey]?.shouldSetStatsInfoPosition ||
            chart.shouldRenderStatsInfo
          ) {
            chartCustomData[chartKey].shouldSetStatsInfoPosition = false;
            chart.shouldRenderStatsInfo = false;
            ChartHelper.renderStatsInfoToChart(
              chart,
              ChartHelper.getChartDefaultStats(),
              chartData.current,
              options,
              chartCustomData[chartKey],
            );
          }

          if (chart.shouldSetPlotLinesLabel === true) {
            chart.shouldSetPlotLinesLabel = false;
            ChartHelper.setPlotLinesLabel(chart);
          }

          if (settings.has_series_grouping) {
            setGroupedSeriesLegend(chart, chartCustomData);
          }
        },
      },
    },
    title: {
      text: settings.show_title ? settings.title : "",
      align: "left",
    },
    subtitle: {},
    xAxis: [
      {
        title: {
          text: ChartHelper.generateChartTitle(
            settings?.x?.title ?? "",
            merge({}, filters[pageKey], prerenderData),
          ),
        },
        type: xAxisType,
        events: {
          afterSetExtremes: (event) => {
            const chart = event.target.chart;
            if (chartCustomData[chartKey]?.highlightCategories) {
              ChartHelper.highlightCategories(
                chart,
                chart.series[0],
                chartCustomData[chartKey].categories,
                chartCustomData[chartKey].highlightCategories,
              );
            }
          },
        },
      },
    ],
    yAxis: [
      {
        title: {
          text: ChartHelper.generateChartTitle(
            settings?.y?.title ?? "",
            merge({}, filters[pageKey], prerenderData),
          ),
        },
      },
    ],
    series: [
      {
        name: settings.title,
        data: [],
      },
    ],
    legend: {
      enabled: settings.has_series_grouping !== true,
    },
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      resultsAxis: "y",
      default: settings,
    },
  });

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }
    chartComponentRefs[chartKey] = ref;

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  useEffect(() => {
    if (component.props.params.body_params.test_number !== undefined) {
      setTopFailingTest(component.props.params.body_params.test_number);
    }
  }, []);

  useEffect(() => {
    if (
      topFailingTest !== undefined ||
      settings.has_top_failing_tests === false
    ) {
      setRefetchChartData(true);
    }
  }, [topFailingTest]);

  React.useImperativeHandle(ref, () => ({
    reloadChart: () => {
      Object.assign(filters[pageKey], reloadChartFilters[chartKey]);
      chartDataQuery.refetch();
    },
  }));

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart?.showLoading();

    if (typeof topFailingTest === "undefined") {
      filters[pageKey].tNum = topFailingTest;
    }
    let allFilters = { ...filters[pageKey], ...chartFilters, ...prerenderData };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey, filters),
    queryFn: fetchChartData,
    enabled:
      refetchChartData &&
      defaultChartOptions.isSuccess &&
      typeof localChartData === "undefined",
  });

  /**
   * Set chart options after all required data are available
   *
   * @param {object} data
   */
  const setOptions = (data) => {
    const lineData =
      options.xAxis[0].type === "datetime" ? data.data : data.line_data;
    if (typeof setHasChartData === "function") {
      setHasChartData(Array.isArray(lineData) && lineData.length > 0);
    }
    chartData.current = data;

    options.subtitle.text = getResultsSubtitle(data, filters[pageKey]);
    if (data.x_categories) {
      options.xAxis[0].categories = data.x_categories;
      chartCustomData[chartKey].categories = data.x_categories;
      chartCustomData[chartKey].highlightCategories =
        data.highlight_x_categories;
    }
    options.series =
      options.xAxis[0].type === "datetime"
        ? generateLineSeries(data)
        : getLineSeries(data.line_data, data.legend);
    setAxisMinMax(
      options.yAxis[0],
      data,
      settings.should_use_limits_scaling ?? true,
    );
    if (settings?.y_axis?.show_plot_lines !== false) {
      options.yAxis[0].plotLines = [
        ...ChartHelper.getPlotLines(data),
        ...ChartHelper.getSigmaLines(data),
      ];
    }
    options.xAxis[0].plotLines = ChartHelper.getPlotLines(
      data,
      ChartHelper.filterPlotLines(["zone_info"], chartFilters),
      true,
      chartRef.current?.chart?.plotHeight - 16,
    );
    if (data.zone_labels && data.zone_info) {
      options.xAxis[0].plotBands = ChartHelper.getPlotBands(
        data.zone_info,
        data.zone_labels,
      );
    }
    // Append the test data to the user options
    options.testData = ChartHelper.setTestDataToUserOptions(data);
    ChartHelper.updateAxisTitleWithActualValue(data, options.yAxis[0]);
    const chartOptions = defaultChartOptions.data?.data?.value
      ? merge(
          options,
          JSON.parse(defaultChartOptions.data.data.value),
          retainedChartOptions[chartKey] ?? {},
        )
      : options;

    if (settings.plotarea_only) {
      ChartHelper.setPlotAreaOnlyOptions(chartOptions);
    }

    setChartOptions(chartOptions);
  };

  // If chart data is directly set
  useEffect(() => {
    if (localChartData && defaultChartOptions.isSuccess) {
      setOptions(localChartData);
    }
  }, [defaultChartOptions.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        setOptions(response.data);
        if (response.data.x_categories) {
          options.xAxis[0].categories = response.data.x_categories;
        }
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * Generate line series
   *
   * @param {object} data
   * @returns {array} lineSeries
   */
  const generateLineSeries = (data) => {
    let lineSeries = [];
    if (data.lineLabel) {
      const groups = data.lineLabel[0];
      if (groups && groups.length > 0) {
        groups.forEach((group, groupIndex) => {
          const groupData = data.lineData.map((lineData, index) => {
            let [year, month, day] = data.xData[index].split("-").map(Number);
            return [Date.UTC(year, month - 1, day), lineData[groupIndex]];
          });
          lineSeries.push({
            name: group,
            data: groupData,
            showInLegend: settings.has_series_grouping !== true,
          });
        });
      }
    } else {
      data.data.forEach((lineData, i) => {
        const seriesOptions = {
          name: data.chart_legend[i] ?? "",
          data: lineData,
          showInLegend: settings.has_series_grouping !== true,
        };
        if (settings.has_series_grouping) {
          const group = data.groups?.[i];
          const groupColor =
            data.group_colors?.[group] ?? Highcharts.getOptions().colors[i];

          seriesOptions.group = group;
          seriesOptions.groupColor = groupColor;
          seriesOptions.color = groupColor;
        }
        lineSeries.push(seriesOptions);
      });
    }

    return lineSeries;
  };

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    chart.shouldSetPlotLinesLabel = true;
    if (
      settings.show_stats_info !== false &&
      settings.has_stats_info !== false
    ) {
      chart.shouldRenderStatsInfo = true;
    }
    if (chartCustomData) {
      ChartHelper.updateExportingMenu(
        chart,
        setIsChartOptionsOpen,
        setCurrentChart,
        chartCustomData[chartKey],
        fullScreenHandle,
      );
    }
    if (typeof setHighchartsChart === "function") {
      setHighchartsChart(chart);
    }

    redrawWhenBoosted(chart);
  };

  return (
    <div>
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
});
