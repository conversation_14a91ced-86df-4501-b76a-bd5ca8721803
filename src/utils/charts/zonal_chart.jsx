"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import React, { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import Helper from "../helper";
import Api from "../api";
import { useBoundStore } from "../../store/store";
import { QueryKeys } from "../query_keys";
import ChartHelper from "./chart_helper";
import ChartLoading from "./loading";

const zonalDeltaLabels = {
  bulls_eye: "Bulls-Eye",
  clusters: "Clusters",
  hemispheres: "Horiz",
  left_right: "Left/Right",
  quadrants: "Quads",
};
const zonalDeltaTableColWidth = 80;
const zonalDeltaTableCellPadding = 4;
const defaultSpacingBottom = 20;

/**
 * Format label to 2 decimal places
 *
 * @param {float} label
 * @returns {float}
 */
const formatLabel = (label) => {
  return Helper.numberFormat(label, 2);
};

if (typeof Highcharts === "object") {
  /**
   * Create the delta table
   *
   * @param {object} chart
   * @param {object} chartZonalDelta
   * @param {object} options
   * @param {object} customData
   */
  Highcharts.drawTable = (chart, chartZonalDelta, options, customData) => {
    // remove delta table if exists
    if (chart.deltaTable) {
      chart.deltaTable.remove();
      // resize chart to its original height if not in full screen mode
      if (!customData.isFullScreen) {
        options.chart.height =
          customData.origChartHeight - customData.deltaTableHeight;
        chart.setSize(chart.chartWidth, options.chart.height, false);
      }
    }
    // draw delta table
    const renderer = chart.renderer;
    const deltaTable = renderer.g("deltatable").add();
    const columns = Object.keys(zonalDeltaLabels);
    const rowHeight = 20;
    const rowCount = 3;
    const deltaTableHeight =
      (zonalDeltaTableCellPadding + rowHeight) * rowCount;

    const extraSpacingBottom = customData.isFullScreen
      ? defaultSpacingBottom
      : 0;

    // save chart original height if in full screen mode, save stats info height otherwise
    if (customData.isFullScreen) {
      customData.origChartHeight =
        chart.fullscreen.origHeight ?? customData.fullscreen.origHeight;
    } else {
      customData.deltaTableHeight = deltaTableHeight;
    }

    // add space for delta table  display at the bottom of the chart
    options.chart.spacingBottom =
      deltaTableHeight + defaultSpacingBottom + extraSpacingBottom;
    options.chart.height = customData.isFullScreen
      ? chart.chartHeight
      : chart.chartHeight + deltaTableHeight;
    chart.renderTo.style.height = `${options.chart.height}px`;
    chart.setSize(chart.chartWidth, options.chart.height, false);

    const tableTop =
      options.chart.height -
      deltaTableHeight -
      defaultSpacingBottom -
      extraSpacingBottom;
    const tableLeft =
      (chart.chartWidth -
        (zonalDeltaTableCellPadding +
          zonalDeltaTableColWidth * columns.length)) /
      2;

    renderer
      .text(
        "Deltas Between Zones",
        tableLeft +
          zonalDeltaTableCellPadding +
          zonalDeltaTableColWidth * columns.length -
          (zonalDeltaTableCellPadding +
            zonalDeltaTableColWidth * columns.length) /
            2,
        tableTop + rowHeight,
      )
      .attr({
        align: "center",
      })
      .css({
        fontSize: "12px",
      })
      .add(deltaTable);

    columns.forEach(function (columnKey, i) {
      renderer
        .text(
          zonalDeltaLabels[columnKey],
          tableLeft +
            zonalDeltaTableCellPadding +
            zonalDeltaTableColWidth * (i + 1) -
            zonalDeltaTableColWidth / 2,
          tableTop + rowHeight * 2,
        )
        .attr({
          align: "center",
        })
        .css({
          fontSize: "12px",
          fontWeight:
            chartZonalDelta.predominant_zonal_issue === columnKey
              ? "bold"
              : "normal",
        })
        .add(deltaTable);
    });

    columns.forEach(function (columnKey, i) {
      renderer
        .text(
          formatLabel(chartZonalDelta[columnKey]),
          tableLeft +
            zonalDeltaTableCellPadding +
            zonalDeltaTableColWidth * (i + 1) -
            zonalDeltaTableColWidth / 2,
          tableTop + rowHeight * 3,
        )
        .attr({
          align: "center",
        })
        .css({
          fontSize: "12px",
          fontWeight:
            chartZonalDelta.predominant_zonal_issue === columnKey
              ? "bold"
              : "normal",
        })
        .add(deltaTable);
    });

    // horizontal lines
    for (let i = 0; i <= rowCount; i++) {
      Highcharts.tableLine(
        renderer,
        tableLeft,
        tableTop + zonalDeltaTableCellPadding + rowHeight * i,
        tableLeft +
          zonalDeltaTableCellPadding +
          zonalDeltaTableColWidth * columns.length,
        tableTop + zonalDeltaTableCellPadding + rowHeight * i,
        deltaTable,
      );
    }
    // left table border
    Highcharts.tableLine(
      renderer,
      tableLeft,
      tableTop + zonalDeltaTableCellPadding,
      tableLeft,
      tableTop + zonalDeltaTableCellPadding + rowHeight * rowCount,
      deltaTable,
    );
    // right table border
    Highcharts.tableLine(
      renderer,
      tableLeft +
        zonalDeltaTableCellPadding +
        zonalDeltaTableColWidth * columns.length,
      tableTop + zonalDeltaTableCellPadding,
      tableLeft +
        zonalDeltaTableCellPadding +
        zonalDeltaTableColWidth * columns.length,
      tableTop + zonalDeltaTableCellPadding + rowHeight * rowCount,
      deltaTable,
    );
    // vertical lines
    const columnCount = columns.length;
    for (let i = 1; i < columnCount; i++) {
      Highcharts.tableLine(
        renderer,
        tableLeft + zonalDeltaTableCellPadding + zonalDeltaTableColWidth * i,
        tableTop + zonalDeltaTableCellPadding + rowHeight * 1,
        tableLeft + zonalDeltaTableCellPadding + zonalDeltaTableColWidth * i,
        tableTop + zonalDeltaTableCellPadding + rowHeight * rowCount,
        deltaTable,
      );
    }

    // store rendered delta table to be used when removing the table to avoid duplicate rendering
    chart.deltaTable = chart.container.querySelectorAll(
      ".highcharts-deltatable",
    )[0];
  };

  /**
   * Draw a single line in the table
   */
  Highcharts.tableLine = function (renderer, x1, y1, x2, y2, table) {
    renderer
      .path(["M", x1, y1, "L", x2, y2])
      .attr({
        stroke: "#5B5B5B",
        "stroke-width": 1,
      })
      .add(table);
  };
}

/**
 * Get start and end angle based on orientation settings
 * @param {string} rotation
 * @param {string} flip
 * @returns {array} startEndAngle
 */
const getStartEndAngle = (rotation, flip) => {
  let startEndAngle = [];
  switch (true) {
    case rotation === "90" && flip === "none":
      startEndAngle = [90, 450];
      break;
    default:
      startEndAngle = [90, 450];
  }

  return startEndAngle;
};

/**
 * Zonal chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {function} setHasChartData
 * @param {object} fullScreenHandle
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default React.forwardRef(function ZonalChart(
  {
    chartRef,
    component,
    filters,
    pageKey,
    chartKey,
    chartCustomData,
    setHasChartData,
    fullScreenHandle,
    prerenderData = {},
  },
  ref,
) {
  const [chartOptions, setChartOptions] = useState();
  let [chartZonalDelta] = useState({});
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const { message } = App.useApp();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const defaultRotation = 90;
  const defaultFlip = "none";
  const [startAngle, endAngle] = getStartEndAngle(defaultRotation, defaultFlip);

  const options = {
    chart: {
      type: "pie",
      spacingBottom: defaultSpacingBottom,
      events: {
        fullscreenOpen: (chart) => {
          chartCustomData[chartKey].isFullScreen = true;
          chart.shouldSetCenterPosition = true;
        },
        fullscreenClose: (chart) => {
          chartCustomData[chartKey].isFullScreen = false;
          chart.shouldSetCenterPosition = true;
        },
        render: (event) => {
          const chart = event.target;
          if (chart.shouldSetCenterPosition) {
            chart.shouldSetCenterPosition = false;
            options.series = setZonalPositionAndSize(chart, options.series);
            Highcharts.drawTable(
              chart,
              chartZonalDelta,
              options,
              chartCustomData[chartKey],
            );
            setChartOptions(options);
            chart.update(options);
          }
        },
      },
    },
    exporting: {
      chartOptions: {
        chart: {
          events: {
            load: (event) => {
              const chart = event.target;
              options.series = setZonalPositionAndSize(chart, options.series);
              Highcharts.drawTable(
                chart,
                chartZonalDelta,
                options,
                chartCustomData[chartKey],
              );
              setChartOptions(options);
              chart.update(options);
            },
          },
        },
      },
      sourceWidth: 1600,
      sourceHeight: 400,
    },
    title: {
      text: "",
    },
    plotOptions: {
      pie: {
        shadow: false,
        center: ["50%", "50%"],
        startAngle: startAngle,
        endAngle: endAngle,
        borderRadius: 0,
      },
    },
    series: [
      {
        size: "40%",
        dataLabels: {
          color: "#ffffff",
        },
        borderWidth: 0,
      },
      {
        size: "13%",
        startAngle: 0,
        dataLabels: {
          color: "#ffffff",
        },
        borderWidth: 0,
      },
      {
        size: "26%",
        innerSize: "50%",
        startAngle: 0,
        dataLabels: {
          color: "#ffffff",
        },
        borderWidth: 0,
      },
      {
        size: "40%",
        innerSize: "66%",
        startAngle: 0,
        dataLabels: {
          color: "#ffffff",
        },
        borderWidth: 0,
      },
      {
        size: "40%",
        startAngle: 0 - startAngle,
        dataLabels: {
          color: "#ffffff",
        },
      },
      {
        size: "40%",
        startAngle: 90 + startAngle,
        endAngle: 90 + endAngle,
        dataLabels: {
          color: "#ffffff",
        },
      },
      {
        size: "40%",
        dataLabels: {
          color: "#ffffff",
        },
      },
      {
        size: "13%",
        dataLabels: {
          color: "#ffffff",
        },
      },
      {
        size: "26%",
        innerSize: "50%",
        dataLabels: {
          color: "#ffffff",
        },
      },
      {
        size: "40%",
        innerSize: "66%",
        dataLabels: {
          color: "#ffffff",
        },
      },
    ],
  };

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }
    chartComponentRefs[chartKey] = ref;

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  React.useImperativeHandle(ref, () => ({
    reloadChart: () => {
      chartDataQuery.refetch();
    },
  }));

  /**
   * Get zonal center position and size
   *
   * @param {object} chart
   * @param {int} seriesIndex
   * @returns {array} [center, size]
   */
  const getZonalPositionAndSize = (chart, seriesIndex) => {
    const plotWidth = chart.plotWidth;
    const plotHeight = chart.plotHeight;
    const pieSize = plotHeight * 0.4;
    const pieSizeWidthRatio = Math.ceil((pieSize / plotWidth) * 100) + 1;
    const pieCenterPercentageX = 50;
    let center;
    let size;

    switch (seriesIndex) {
      case 0:
        center = [`${pieCenterPercentageX - pieSizeWidthRatio}%`, "20%"];
        size = plotHeight * 0.4;
        break;
      case 1:
        center = [`${pieCenterPercentageX}%`, "20%"];
        size = plotHeight * 0.13;
        break;
      case 2:
        center = [`${pieCenterPercentageX}%`, "20%"];
        size = plotHeight * 0.26;
        break;
      case 3:
        center = [`${pieCenterPercentageX}%`, "20%"];
        size = plotHeight * 0.4;
        break;
      case 4:
        center = [`${pieCenterPercentageX + pieSizeWidthRatio}%`, "20%"];
        size = plotHeight * 0.4;
        break;
      case 5:
        center = [`${pieCenterPercentageX - pieSizeWidthRatio}%`, "71%"];
        size = plotHeight * 0.4;
        break;
      case 6:
        center = [`${pieCenterPercentageX}%`, "71%"];
        size = plotHeight * 0.4;
        break;
      case 7:
        center = [`${pieCenterPercentageX + pieSizeWidthRatio}%`, "71%"];
        size = plotHeight * 0.13;
        break;
      case 8:
        center = [`${pieCenterPercentageX + pieSizeWidthRatio}%`, "71%"];
        size = plotHeight * 0.26;
        break;
      case 9:
        center = [`${pieCenterPercentageX + pieSizeWidthRatio}%`, "71%"];
        size = plotHeight * 0.4;
        break;
    }

    return [center, size];
  };

  /**
   * Set zonal series center position and size
   *
   * @param {object} chart
   * @param {array} seriesOptions
   * @returns {array} seriesOptions
   */
  const setZonalPositionAndSize = (chart, seriesOptions) => {
    seriesOptions.forEach((seriesOption, seriesIndex) => {
      const [centerPosition, size] = getZonalPositionAndSize(
        chart,
        seriesIndex,
      );
      seriesOption.center = centerPosition;
      seriesOption.size = size;
      seriesOption.dataLabels.allowOverlap = true;
    });

    return seriesOptions;
  };

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart?.hideNoData();
    chartRef.current?.chart?.showLoading();

    const allFilters = { ...filters[pageKey], ...prerenderData };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
    enabled: true,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        const hasData = response.data.has_zonal_data !== false;
        setHasChartData(hasData);
        if (hasData) {
          const zoneChartData = response.data.zonal_data;
          const zonalDelta = response.data.zonal_delta;
          const tooltipLabels = response.data.tooltip_labels;
          const zonalType =
            reloadChartFilters[chartKey].chart_zonal_type !== undefined
              ? reloadChartFilters[chartKey].chart_zonal_type
              : component.data_key
                ? component.data_key
                : "percent_zonal";
          chartZonalDelta =
            zonalType === "number_zonal"
              ? zonalDelta.number
              : zonalDelta.percent;

          drawChart(zoneChartData, zonalType, tooltipLabels);
        }
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * Generate zonal chart
   *
   * @param {object} zoneChartData
   * @param {string} zonalType
   * @param {array} tooltipLabels
   */
  const drawChart = (zoneChartData, zonalType, tooltipLabels) => {
    options.series.forEach((seriesOption, seriesIndex) => {
      let zonalData = [];
      switch (seriesIndex) {
        case 0:
          zonalData.push({
            name:
              zonalType === "number_zonal"
                ? zoneChartData.zone1.extra_field_pass[0]
                : formatLabel(zoneChartData.zone1.label[0]),
            y: 100,
            color: zoneChartData.zone1.color[0],
            dataLabels: {
              distance: "-100%",
            },
            custom: {
              name: "Zone 1",
              yield: zoneChartData.zone1.label[0],
              good: zoneChartData.zone1.extra_field_pass[0],
              total: zoneChartData.zone1.extra_field[0],
            },
          });
          break;
        case 1:
          zonalData.push({
            name:
              zonalType === "number_zonal"
                ? zoneChartData.zone2.extra_field_pass[0]
                : formatLabel(zoneChartData.zone2.label[0]),
            y: 100,
            color: zoneChartData.zone2.color[0],
            dataLabels: {
              distance: "-100%",
            },
            custom: {
              name: "Zone 2 Radius 1",
              yield: zoneChartData.zone2.label[0],
              good: zoneChartData.zone2.extra_field_pass[0],
              total: zoneChartData.zone2.extra_field[0],
            },
          });
          break;
        case 2:
          zonalData.push({
            name:
              zonalType === "number_zonal"
                ? zoneChartData.zone2.extra_field_pass[1]
                : formatLabel(zoneChartData.zone2.label[1]),
            y: 100,
            color: zoneChartData.zone2.color[1],
            dataLabels: {
              distance: "-33%",
            },
            custom: {
              name: "Zone 2 Radius 2",
              yield: zoneChartData.zone2.label[1],
              good: zoneChartData.zone2.extra_field_pass[1],
              total: zoneChartData.zone2.extra_field[1],
            },
          });
          break;
        case 3:
          zonalData.push({
            name:
              zonalType === "number_zonal"
                ? zoneChartData.zone2.extra_field_pass[2]
                : formatLabel(zoneChartData.zone2.label[2]),
            y: 100,
            color: zoneChartData.zone2.color[2],
            dataLabels: {
              distance: "-17%",
            },
            custom: {
              name: "Zone 2 Radius 3",
              yield: zoneChartData.zone2.label[2],
              good: zoneChartData.zone2.extra_field_pass[2],
              total: zoneChartData.zone2.extra_field[2],
            },
          });
          break;
        case 4:
          zoneChartData.zone3.label.forEach((label, index) => {
            zonalData.push({
              name:
                zonalType === "number_zonal"
                  ? zoneChartData.zone3.extra_field_pass[index]
                  : formatLabel(label),
              y: 50,
              color: zoneChartData.zone3.color[index],
              dataLabels: {
                distance: "-50%",
              },
              custom: {
                name: `Zone 3 Sector ${index}`,
                yield: zoneChartData.zone3.label[index],
                good: zoneChartData.zone3.extra_field_pass[index],
                total: zoneChartData.zone3.extra_field[index],
              },
            });
          });
          break;
        case 5:
          zoneChartData.zone4.label.forEach((label, index) => {
            zonalData.push({
              name:
                zonalType === "number_zonal"
                  ? zoneChartData.zone4.extra_field_pass[index]
                  : formatLabel(label),
              y: 50,
              color: zoneChartData.zone4.color[index],
              dataLabels: {
                distance: "-50%",
              },
              custom: {
                name: `Zone 4 Sector ${index}`,
                yield: zoneChartData.zone4.label[index],
                good: zoneChartData.zone4.extra_field_pass[index],
                total: zoneChartData.zone4.extra_field[index],
              },
            });
          });
          break;
        case 6:
          zoneChartData.zone5.label.forEach((label, index) => {
            zonalData.push({
              name:
                zonalType === "number_zonal"
                  ? zoneChartData.zone5.extra_field_pass[index]
                  : formatLabel(label),
              y: 25,
              color: zoneChartData.zone5.color[index],
              dataLabels: {
                distance: "-50%",
              },
              custom: {
                name: `Zone 5 Sector ${index}`,
                yield: zoneChartData.zone5.label[index],
                good: zoneChartData.zone5.extra_field_pass[index],
                total: zoneChartData.zone5.extra_field[index],
              },
            });
          });
          break;
        case 7:
          zoneChartData.zone6.label1.forEach((label, index) => {
            zonalData.push({
              name:
                zonalType === "number_zonal"
                  ? zoneChartData.zone6.extra_field1_pass[index]
                  : formatLabel(label),
              y: 25,
              color: zoneChartData.zone6.color[index + 0],
              dataLabels: {
                distance: "-17%",
                x: 4 * (index === 0 || index === 3 ? 1 : -1),
                y: 4 * (index === 0 || index === 1 ? -1 : 1),
              },
              custom: {
                name: `Zone 6 Radius 3`,
                yield: zoneChartData.zone6.label1[index],
                good: zoneChartData.zone6.extra_field1_pass[index],
                total: zoneChartData.zone6.extra_field1[index],
              },
            });
          });
          break;
        case 8:
          zoneChartData.zone6.label2.forEach((label, index) => {
            zonalData.push({
              name:
                zonalType === "number_zonal"
                  ? zoneChartData.zone6.extra_field2_pass[index]
                  : formatLabel(label),
              y: 25,
              color: zoneChartData.zone6.color[index + 4],
              dataLabels: {
                distance: "-17%",
              },
              custom: {
                name: `Zone 6 Radius 2`,
                yield: zoneChartData.zone6.label2[index],
                good: zoneChartData.zone6.extra_field2_pass[index],
                total: zoneChartData.zone6.extra_field2[index],
              },
            });
          });
          break;
        case 9:
          zoneChartData.zone6.label3.forEach((label, index) => {
            zonalData.push({
              name:
                zonalType === "number_zonal"
                  ? zoneChartData.zone6.extra_field3_pass[index]
                  : formatLabel(label),
              y: 25,
              color: zoneChartData.zone6.color[index + 8],
              dataLabels: {
                distance: "-17%",
              },
              custom: {
                name: `Zone 6 Radius 1`,
                yield: zoneChartData.zone6.label3[index],
                good: zoneChartData.zone6.extra_field3_pass[index],
                total: zoneChartData.zone6.extra_field3[index],
              },
            });
          });
          break;
      }
      seriesOption.data = zonalData;
    });
    options.tooltip = {
      formatter: function () {
        return `<b>${this.point.custom.name}</b><br><br>
        ${tooltipLabels[0]}: <b>${Helper.numberFormat(this.point.custom.yield, 2)}%</b><br>
        ${tooltipLabels[1]}: <b>${Helper.numberFormat(this.point.custom.good)}</b><br>
        ${tooltipLabels[2]}: <b>${Helper.numberFormat(this.point.custom.total)}</b>`;
      },
    };
    setChartOptions(options);
    chartRef.current?.chart?.update(options);
  };

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    chart.shouldSetCenterPosition = true;
    ChartHelper.updateExportingMenu(
      chart,
      setIsChartOptionsOpen,
      setCurrentChart,
      chartCustomData[chartKey],
      fullScreenHandle,
    );
  };

  return (
    <div>
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
});
