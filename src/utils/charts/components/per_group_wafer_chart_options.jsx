"use client";

import React from "react";
import { Button, Divider, Form, Input, Space, Typography } from "antd";
import { useBoundStore } from "../../../store/store";
import Helper from "../../helper";

/**
 * Per Group Wafer Chart options
 *
 * @param {string} pageKey
 * @param {object} rowOptionData
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default function PerGroupWaferChartOptions({
  pageKey,
  rowOptionData,
  prerenderData,
}) {
  const pageMeta = useBoundStore((state) => state.pageMeta);
  const pageElements = useBoundStore((state) => state.pageElements);
  const [perGroupWaferChartOptionsForm] = Form.useForm();

  return (
    <div>
      <Divider orientation="left" orientationMargin="8" plain>
        <strong>More Wafer Chart Options</strong>
      </Divider>
      <Form
        form={perGroupWaferChartOptionsForm}
        layout="inline"
        name={`${pageKey}_per_group_wafer_chart_options_form`}
        className="my-3"
      >
        <Form.Item name="tab_key" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="component_name" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="group_name" hidden>
          <Input />
        </Form.Item>
        <Space>
          <Typography.Text strong className="mr-2">
            Click to View:
          </Typography.Text>
          <Space>
            {Array.isArray(rowOptionData?.items) &&
              rowOptionData.items.map((item) => {
                const itemKey = Helper.generateTabKey(item.key, prerenderData);
                const itemTabKey = Helper.generateTabKey(
                  item.tab_key,
                  prerenderData,
                );
                let shouldRender = true;
                if (item.render_conditions !== undefined) {
                  shouldRender = Helper.filterByRenderConditions(
                    item.render_conditions,
                    item.invert_render_conditions,
                    {
                      analysis_types: pageMeta.analysis_type,
                      has_xy: pageMeta.has_xy,
                    },
                  );
                }

                let rowOptionElement = null;
                if (shouldRender) {
                  const itemState = pageElements[itemKey] || {
                    disabled:
                      item.required_data?.key &&
                      prerenderData[item.required_data.key] === false,
                  };
                  rowOptionElement = (
                    <Button
                      key={`${itemTabKey}_${item.component_name}`}
                      onClick={() => {
                        perGroupWaferChartOptionsForm.resetFields();
                        perGroupWaferChartOptionsForm.setFieldsValue({
                          tab_key: item.tab_key,
                          component_name: item.component_name,
                          group_name: item.group_name,
                        });
                        perGroupWaferChartOptionsForm.submit();
                      }}
                      disabled={itemState.disabled}
                    >
                      {item.label}
                    </Button>
                  );
                }

                return rowOptionElement;
              })}
          </Space>
        </Space>
      </Form>
    </div>
  );
}
