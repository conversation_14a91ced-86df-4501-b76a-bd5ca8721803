"use client";

import { <PERSON>boltOutlined } from "@ant-design/icons";
import { Tooltip, Typography } from "antd";

/**
 * Display of warning message if chart is in boost mode
 *
 * @returns {JSX.Element}
 */
export default function BoostedChartWarningMessage() {
  return (
    <Typography.Text className="text-right w-full inline-block cursor-default">
      <Tooltip title="Faster chart loading with limited interactive features.">
        <ThunderboltOutlined /> Boosted Chart
      </Tooltip>
    </Typography.Text>
  );
}
