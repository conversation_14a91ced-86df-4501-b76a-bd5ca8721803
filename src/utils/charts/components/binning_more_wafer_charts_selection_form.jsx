"use client";

import React, { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  Col,
  Divider,
  Form,
  Input,
  Modal,
  Row,
  Space,
  Typography,
  Radio,
  message,
} from "antd";
import { useBoundStore } from "../../../store/store";
import { useEffectApiFetch } from "../../../hooks";
import YHGrid from "../../grid/yh_grid";
import Api from "../../api";

/**
 * Tab pattern component name mapper
 */
const TabPatternComponentMapper = {
  composite: "binning_select_composite_bin_patterns",
  per_wafer_pattern: "binning_per_wafer_patterns",
  per_group: "binning_per_group_patterns",
  binning_per_wafer_all_bin_patterns: "binning_per_wafer_all_bin_patterns",
};

/**
 * More Wafer Charts selection options
 *
 * @param {string} pageKey
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default function MoreWaferChartsOptions({ pageKey, prerenderData }) {
  const [isSelectBinPatternSiteModalOpen, setIsSelectBinPatternSiteModalOpen] =
    useState(false);
  const [selectBinPatternGridComponent, setSelectBinPatternGridComponent] =
    useState();
  const [
    selectBinPatternSiteGridComponent,
    setSelectBinPatternSiteGridComponent,
  ] = useState();
  const [binPatternOptions, setSelectBinPatternOptions] = useState([]);
  const filters = useBoundStore((state) => state.filters);
  const urlParams = useBoundStore((state) => state.urlParams);
  const gridSelectionData = useBoundStore((state) => state.gridSelectionData);
  const pageMeta = useBoundStore((state) => state.pageMeta);
  const [binPatternForm] = Form.useForm();
  const [messageApi, contextHolder] = message.useMessage();
  const selectBinPatternGridRef = useRef();
  const selectBinPatternSiteGridRef = useRef();

  useEffectApiFetch(
    () => {
      return getSelectBinPatternGridComponent();
    },
    () => {
      setSelectBinPatternGridComponent();
    },
  );

  useEffectApiFetch(
    () => {
      return getSelectBinPatternSiteGridComponent();
    },
    () => {
      setSelectBinPatternSiteGridComponent();
    },
  );

  useEffectApiFetch(
    () => {
      return getSelectBinPatternOptions();
    },
    () => {
      setSelectBinPatternOptions();
    },
  );

  /**
   * Get and set select bin pattern options
   *
   * @returns {AbortController} abortCtl
   */
  const getSelectBinPatternOptions = () => {
    const abortCtl = Api.getSelectOptions(
      (res) => {
        if (res.success) {
          const formattedOptions = Object.entries(res.data).map(
            ([key, label]) => ({
              label,
              value: key,
            }),
          );
          setSelectBinPatternOptions(formattedOptions);
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      {
        field: "bin_patterns/options",
        mfg_process: urlParams[pageKey].mfg_process,
        is_grouped: pageMeta.analysis_type === "grouped",
      },
    );

    return abortCtl;
  };

  /**
   * Get and set select bin grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getSelectBinPatternGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setSelectBinPatternGridComponent(res.data);
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      {
        name: "bin_patterns",
      },
    );

    return abortCtl;
  };

  /**
   * Get and set select bin by site grid component
   *
   * @returns {AbortController} abortCtl
   */
  const getSelectBinPatternSiteGridComponent = () => {
    const abortCtl = Api.getComponentBlueprint(
      (res) => {
        if (res.success) {
          setSelectBinPatternSiteGridComponent(res.data);
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      {
        name: "bin_patterns_sites",
      },
    );

    return abortCtl;
  };

  /**
   * Closes the bin pattern modal and submits the form.
   */
  const handleSelectBinPatternOk = () => {
    let selectedSites;
    let selectedSiteColors;
    let selectedBins = gridSelectionData[
      selectBinPatternGridComponent.selection_store_key
    ]
      .map((item) => item.bin_number)
      .sort((a, b) => a - b)
      .join();

    if (
      gridSelectionData[selectBinPatternSiteGridComponent.selection_store_key]
    ) {
      selectedSites = gridSelectionData[
        selectBinPatternSiteGridComponent.selection_store_key
      ]
        ?.map((item) => item.site_number)
        .sort((a, b) => a - b)
        .join();
      selectedSiteColors = JSON.stringify(
        Object.fromEntries(
          gridSelectionData[
            selectBinPatternSiteGridComponent.selection_store_key
          ]?.map(({ site_number, color }) => [site_number, color]),
        ),
      );
    }
    binPatternForm.setFieldsValue({
      bin_number: selectedBins,
      site_number: selectedSites,
      site_colors: selectedSiteColors,
    });
    binPatternForm.submit();
    resetGridSelection();
    setIsSelectBinPatternSiteModalOpen(false);
  };

  /**
   * Resets grid selection
   */
  const resetGridSelection = () => {
    gridSelectionData[selectBinPatternGridComponent?.selection_store_key] = [];
    gridSelectionData[selectBinPatternSiteGridComponent?.selection_store_key] =
      [];
  };

  /**
   * Set plot button disabled state
   *
   * @returns {boolean}
   */
  const isPlotButtonDisabled = () => {
    const gridSelectionKey1 =
      selectBinPatternGridComponent?.selection_store_key;
    const gridSelectionKey2 =
      selectBinPatternSiteGridComponent?.selection_store_key;
    const isGridSelectionKey1Empty =
      !gridSelectionData[gridSelectionKey1] ||
      gridSelectionData[gridSelectionKey1]?.length === 0;
    const isGridSelectionKey2Empty =
      binPatternForm.getFieldValue("tab_key") ===
      "bin_{bin_tab_key}_{site_tab_key}"
        ? !gridSelectionData[gridSelectionKey2] ||
          gridSelectionData[gridSelectionKey2]?.length === 0
        : false;

    return isGridSelectionKey1Empty || isGridSelectionKey2Empty;
  };

  /**
   * Handles bin pattern options change
   *
   */
  const handlebinPatternOptionsChange = (e) => {
    binPatternForm.setFieldsValue({
      tab_key: `${e.target.value}_{bin_tab_key}`,
      component_name: TabPatternComponentMapper[e.target.value],
    });
  };

  return (
    <Space>
      {contextHolder}
      <Form
        form={binPatternForm}
        layout="inline"
        name={`${pageKey}_binning_bin_pattern_form`}
      >
        <Form.Item name="tab_key" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="component_name" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="group_name" hidden>
          <Input />
        </Form.Item>
        <Space>
          <Typography.Text strong className="mr-2">
            Click to View:
          </Typography.Text>
          {pageMeta.analysis_type === "single" && (
            <Space>
              <Button
                onClick={() => {
                  binPatternForm.resetFields();
                  binPatternForm.setFieldsValue({
                    tab_key: "binPatterns",
                  });
                  binPatternForm.submit();
                }}
              >
                Bin Patterns
              </Button>
              <Button
                onClick={() => {
                  binPatternForm.resetFields();
                  binPatternForm.setFieldsValue({
                    tab_key: "bin_{bin_tab_key}",
                  });
                  setIsSelectBinPatternSiteModalOpen(true);
                }}
              >
                Select Bin Pattern
              </Button>
              <Button
                onClick={() => {
                  binPatternForm.resetFields();
                  binPatternForm.setFieldsValue({
                    tab_key: "bin_{bin_tab_key}_{site_tab_key}",
                  });
                  setIsSelectBinPatternSiteModalOpen(true);
                }}
              >
                Select Bin Pattern per Site
              </Button>
              <Button
                onClick={() => {
                  binPatternForm.resetFields();
                  binPatternForm.setFieldsValue({
                    tab_key: "reprobeAnalysis",
                  });
                  filters[pageKey].show_all_dice = true;
                  binPatternForm.submit();
                }}
              >
                Perform Reprobe
              </Button>
            </Space>
          )}

          {pageMeta.analysis_type === "multiple" && (
            <Space>
              <Button
                onClick={() => {
                  binPatternForm.resetFields();
                  binPatternForm.setFieldsValue({
                    tab_key: "binWaferBreakdown",
                  });
                  binPatternForm.submit();
                }}
              >
                Bin Wafer Breakdown
              </Button>
              <Button
                onClick={() => {
                  binPatternForm.resetFields();
                  binPatternForm.setFieldsValue({
                    tab_key: "compositeBinPatterns",
                  });
                  binPatternForm.submit();
                }}
              >
                Composite Bin Patterns
              </Button>
              <Button
                onClick={() => {
                  binPatternForm.resetFields();
                  binPatternForm.setFieldsValue({
                    tab_key: `composite_{bin_tab_key}`,
                  });
                  setIsSelectBinPatternSiteModalOpen(true);
                }}
              >
                Select Bin Pattern
              </Button>
              <Button
                onClick={() => {
                  binPatternForm.resetFields();
                  binPatternForm.setFieldsValue({
                    tab_key: "bin_{bin_tab_key}_{site_tab_key}",
                  });
                  setIsSelectBinPatternSiteModalOpen(true);
                }}
              >
                Select Bin Pattern per Site
              </Button>
            </Space>
          )}

          {pageMeta.analysis_type === "grouped" && (
            <Space>
              <Button
                onClick={() => {
                  binPatternForm.resetFields();
                  binPatternForm.setFieldsValue({
                    tab_key: "binWaferBreakdown",
                    component_name:
                      TabPatternComponentMapper.binning_per_wafer_all_bin_patterns,
                  });
                  binPatternForm.submit();
                }}
              >
                Bin Wafer Breakdown
              </Button>
              <Button
                onClick={() => {
                  binPatternForm.resetFields();
                  binPatternForm.setFieldsValue({
                    tab_key: `composite_{bin_tab_key}`,
                    component_name: TabPatternComponentMapper.composite,
                  });
                  setIsSelectBinPatternSiteModalOpen(true);
                }}
              >
                Select Bin Pattern
              </Button>
              <Button
                onClick={() => {
                  binPatternForm.resetFields();
                  binPatternForm.setFieldsValue({
                    tab_key: "bin_{bin_tab_key}_{site_tab_key}",
                  });
                  setIsSelectBinPatternSiteModalOpen(true);
                }}
              >
                Select Bin Pattern per Site
              </Button>
              <Button
                onClick={() => {
                  binPatternForm.resetFields();
                  binPatternForm.setFieldsValue({
                    tab_key: `compositeWafer`,
                    component_name: TabPatternComponentMapper.per_group,
                  });
                  binPatternForm.submit();
                }}
              >
                Composite Wafer
              </Button>
            </Space>
          )}
        </Space>

        <Modal
          title={
            binPatternForm.getFieldValue("tab_key") ===
            "bin_{bin_tab_key}_{site_tab_key}"
              ? "Select Bin Pattern by Site"
              : "Select Bin Pattern"
          }
          width={"35%"}
          open={isSelectBinPatternSiteModalOpen}
          okText="Plot"
          okButtonProps={{
            disabled: isPlotButtonDisabled(),
          }}
          onOk={handleSelectBinPatternOk}
          onCancel={() => {
            resetGridSelection();
            setIsSelectBinPatternSiteModalOpen(false);
          }}
        >
          <Row className="min-h-80">
            <Col span={24}>
              {selectBinPatternGridComponent && (
                <Form.Item name="bin_number" noStyle>
                  <YHGrid
                    gridRef={selectBinPatternGridRef}
                    gridId="bin_patterns_grid"
                    component={selectBinPatternGridComponent}
                    filters={filters}
                    rowGroups={[]}
                    pageKey={pageKey}
                    wrapperClassName="flex grow flex-col h-full"
                    prerenderData={prerenderData}
                  />
                </Form.Item>
              )}
            </Col>
          </Row>
          {binPatternForm.getFieldValue("tab_key") ===
          "bin_{bin_tab_key}_{site_tab_key}" ? (
            <>
              <Divider />
              <Row className="min-h-80">
                <Col span={24}>
                  {selectBinPatternSiteGridComponent && (
                    <>
                      <Form.Item name="site_number" noStyle>
                        <YHGrid
                          gridRef={selectBinPatternSiteGridRef}
                          gridId="bin_patterns_sites_grid"
                          component={selectBinPatternSiteGridComponent}
                          filters={filters}
                          rowGroups={[]}
                          pageKey={pageKey}
                          wrapperClassName="flex grow flex-col h-full"
                          prerenderData={prerenderData}
                        />
                      </Form.Item>
                      <Form.Item name="site_colors" hidden>
                        <Input />
                      </Form.Item>
                    </>
                  )}
                </Col>
              </Row>
            </>
          ) : (
            (pageMeta.analysis_type === "multiple" ||
              pageMeta.analysis_type === "grouped") && (
              <Row>
                <Col>
                  <Space>
                    <Typography.Text className="mr-2">
                      Select Pattern:
                    </Typography.Text>

                    <Form.Item name="bin_pattern_option" noStyle>
                      <Radio.Group
                        onChange={handlebinPatternOptionsChange}
                        defaultValue={"composite"}
                      >
                        {binPatternOptions?.map((option) => (
                          <Radio key={option.value} value={option.value}>
                            {option.label}
                          </Radio>
                        ))}
                      </Radio.Group>
                    </Form.Item>
                  </Space>
                </Col>
              </Row>
            )
          )}
        </Modal>
      </Form>
    </Space>
  );
}
