"use client";

import { useState } from "react";
import { Button, Form, Space, Typography, Select, Checkbox } from "antd";
import { useBoundStore } from "../../../store/store";
import FilterSelect from "../../grid/components/filter_select";
import Helper from "../../../../src/utils/helper";

/**
 * Grouped bin pareto charts form components
 *
 * @param {string} pageKey
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default function BinningParetoChartsGroupFilter({
  pageKey,
  prerenderData,
}) {
  const urlParams = useBoundStore((state) => state.urlParams);
  const [groupedBinParetoForm] = Form.useForm();
  const [groupBy, setGroupBy] = useState("use_top_failing_bins");
  const initialFilters = {
    group_by: "use_top_failing_bins",
    top_n_pareto: 5,
    bin_numbers: [{ value: "all", label: "Overall Bins" }],
    site: [{ value: 255, label: "Overall Sites" }],
    show_bin_count: false,
  };

  /**
   * Revert to default filter values
   */
  const resetToDefaultFilter = () => {
    groupedBinParetoForm.resetFields();
    setGroupBy("use_top_failing_bins");
  };

  return (
    <Space>
      <Form
        form={groupedBinParetoForm}
        layout="inline"
        initialValues={initialFilters}
        name={`${pageKey}_binning_pareto_group_filter_form`}
      >
        <Space>
          <Typography.Text strong className="mr-2">
            View:
          </Typography.Text>
          <Form.Item name="group_by">
            <Select
              className={"min-w-32"}
              options={[
                { value: "use_top_failing_bins", label: "Top Failing Bins" },
                { value: "select_bins", label: "Select Bins" },
                { value: "use_pass_bins", label: "Stack Pass Bins" },
              ]}
              onChange={(value) => {
                setGroupBy(value);
              }}
            />
          </Form.Item>
          {groupBy === "use_top_failing_bins" && (
            <Form.Item name="top_n_pareto">
              <Select
                className={"min-w-32"}
                disabled={groupBy === "use_pass_bins"}
                options={[
                  { value: 3, label: "Top 3" },
                  { value: 5, label: "Top 5" },
                  { value: 10, label: "Top 10" },
                  { value: 15, label: "Top 15" },
                  { value: 20, label: "Top 20" },
                ]}
              />
            </Form.Item>
          )}
          {groupBy === "select_bins" && (
            <Form.Item name="bin_numbers">
              <FilterSelect
                className={"min-w-32 max-w-64"}
                disabled={groupBy === "use_pass_bins"}
                componentKey="select_bin_numbers_options"
                placeholder="Select bin number"
                labelInValue
                allowClear={false}
                mode="multiple"
                onChange={(values) => {
                  Object.keys(values).length
                    ? Helper.handleOverallValue(values, "all")
                    : groupedBinParetoForm.resetFields(["bin_numbers"]);
                }}
                params={{
                  api: {
                    url: "api/v1/internal/options/list/bin_numbers",
                    mfg_process: urlParams[pageKey].mfg_process,
                    src_type: urlParams[pageKey].src_type,
                    src_value: urlParams[pageKey].src_value,
                    bin_type: prerenderData.bin_type,
                    cache_it: 1,
                  },
                  allOption: {
                    label: "Overall Bins",
                    value: "all",
                  },
                }}
              />
            </Form.Item>
          )}
          <Form.Item name="site">
            <FilterSelect
              className={"min-w-32 max-w-64"}
              componentKey="select_site_options"
              placeholder="Select site"
              labelInValue
              allowClear={false}
              mode="multiple"
              onChange={(values) => {
                Object.keys(values).length
                  ? Helper.handleOverallValue(values, 255)
                  : groupedBinParetoForm.resetFields(["site"]);
              }}
              params={{
                api: {
                  url: "api/v1/internal/options/list/sites",
                  mfg_process: urlParams[pageKey].mfg_process,
                  src_type: urlParams[pageKey].src_type,
                  src_value: urlParams[pageKey].src_value,
                  bin_type: prerenderData.bin_type,
                  cache_it: 1,
                },
              }}
            />
          </Form.Item>
          <Form.Item name="show_bin_count">
            <Checkbox
              className="ml-2"
              onChange={(e) => {
                groupedBinParetoForm.setFieldsValue({
                  show_bin_count: e.target.checked,
                });
              }}
            >
              Show Bin Count
            </Checkbox>
          </Form.Item>

          <Button onClick={resetToDefaultFilter}>Reset to Default</Button>
          <Button type="primary" htmlType="submit">
            Apply
          </Button>
        </Space>
      </Form>
    </Space>
  );
}
