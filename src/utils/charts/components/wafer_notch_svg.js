/**
 * Generates an SVG element for a wafer with a notch and orientation indicators.
 * The notch position is specified by the `notchPosition` parameter and the
 * orientation is specified by the `rotation` and `flip` parameters.
 *
 * This component renders a wafer with a notch and orientation indicators as
 * an SVG element. The notch is rendered as a circle with a curved edge on one
 * side, and the orientation indicators are rendered as arrows pointing to the
 * positive x and y axes. The notch position is specified by the `notchPosition`
 * parameter and the orientation is specified by the `rotation` and `flip`
 * parameters.
 *
 * @param {string} [notchPosition="T"] - The position of the notch (T, R, L, D)
 * @param {string} [rotation="0"] - The rotation angle in degrees (0, 90, 180, 270)
 * @param {string} [flip="none"] - The flip direction (none, x, y)
 *
 * @returns {JSX.Element} The rendered SVG element
 */
const WaferNotchSVG = ({ notchPosition, rotation = "0", flip = "none" }) => {
  // SVG dimensions
  const width = 200;
  const height = 200;
  const centerX = width / 2;
  const centerY = height / 2;
  const radius = 80;

  // Arrow configuration
  const arrowLength = radius * 0.25;
  const arrowHeadSize = 6;
  const arrowsX = 20;
  const arrowsY = height - 20;

  // Calculate transforms
  const { transform, arrowTransform } = calculateTransforms(
    rotation,
    flip,
    width,
    height,
    centerX,
    centerY,
  );

  // Calculate label positions
  const { xLabelX, xLabelY, yLabelX, yLabelY } = calculateLabelPositions(
    rotation,
    flip,
    arrowLength,
  );

  // Render wafer based on notch position
  return (
    <svg width={width} height={height} viewBox={`0 0 ${width} ${height}`}>
      {renderWafer(notchPosition, centerX, centerY, radius, transform)}
      {renderArrows(
        arrowsX,
        arrowsY,
        arrowLength,
        arrowHeadSize,
        arrowTransform,
        xLabelX,
        xLabelY,
        yLabelX,
        yLabelY,
      )}
    </svg>
  );
};

/**
 * Calculates the transformation strings for SVG elements based on rotation and flip parameters.
 *
 * @param {string} rotation - The rotation angle in degrees ("0", "90", "180", "270").
 * @param {string} flip - The flip direction ("none", "x", "y").
 * @param {number} width - The width of the SVG element.
 * @param {number} height - The height of the SVG element.
 * @param {number} centerX - The x-coordinate of the SVG element's center.
 * @param {number} centerY - The y-coordinate of the SVG element's center.
 * @returns {object} An object containing 'transform' and 'arrowTransform' strings for SVG transformations.
 */
const calculateTransforms = (
  rotation,
  flip,
  width,
  height,
  centerX,
  centerY,
) => {
  let transform = "";

  // Apply rotation first
  if (rotation && rotation !== "0") {
    transform += `rotate(${rotation} ${centerX} ${centerY}) `;
  }

  // Apply flip after rotation
  if (flip === "x") {
    if (rotation === "90" || rotation === "270") {
      transform += `scale(1, -1) translate(0, ${-height}) `;
    } else {
      transform += `scale(-1, 1) translate(${-width}, 0) `;
    }
  } else if (flip === "y") {
    if (rotation === "90" || rotation === "270") {
      transform += `scale(-1, 1) translate(${-width}, 0) `;
    } else {
      transform += `scale(1, -1) translate(0, ${-height}) `;
    }
  }

  // Create separate transform for arrows
  let arrowTransform = "";

  if (rotation && rotation !== "0") {
    arrowTransform += `rotate(${rotation}) `;
  }

  if (flip === "x") {
    arrowTransform += `scale(-1, 1) `;
  } else if (flip === "y") {
    arrowTransform += `scale(1, -1) `;
  }

  return { transform, arrowTransform };
};

/**
 * Calculates the positions of the X and Y axis labels based on the chart's
 * rotation and flip settings.
 *
 * @param {string} rotation - The rotation of the chart (0, 90, 180, 270)
 * @param {string} flip - The flip setting of the chart (none, x, y)
 * @param {number} arrowLength - The length of the arrows
 *
 * @returns {{xLabelX: number, xLabelY: number, yLabelX: number, yLabelY: number}}
 * An object containing the x and y coordinates of the X and Y axis labels
 */
const calculateLabelPositions = (rotation, flip, arrowLength) => {
  // Default positions
  let xLabelX = arrowLength / 2;
  let xLabelY = -8;
  let yLabelX = -8;
  let yLabelY = -arrowLength / 2;

  // Adjust based on rotation and flip
  switch (rotation) {
    case "90":
      if (flip === "none") {
        xLabelX = 8;
        xLabelY = arrowLength / 2;
        yLabelX = -arrowLength / 2;
        yLabelY = -8;
      } else if (flip === "x") {
        xLabelX = 8;
        xLabelY = -arrowLength / 2;
        yLabelX = -arrowLength / 2;
        yLabelY = -8;
      } else if (flip === "y") {
        xLabelX = -8;
        xLabelY = arrowLength / 2;
        yLabelX = arrowLength / 2;
        yLabelY = -8;
      }
      break;
    case "180":
      if (flip === "none") {
        xLabelX = -arrowLength / 2;
        xLabelY = 8;
        yLabelX = 8;
        yLabelY = arrowLength / 2;
      } else if (flip === "x") {
        xLabelX = arrowLength / 2;
        xLabelY = 8;
        yLabelX = 8;
        yLabelY = arrowLength / 2;
      } else if (flip === "y") {
        xLabelX = -arrowLength / 2;
        xLabelY = -8;
        yLabelX = 8;
        yLabelY = -arrowLength / 2;
      }
      break;
    case "270":
      if (flip === "none") {
        xLabelX = -8;
        xLabelY = -arrowLength / 2;
        yLabelX = arrowLength / 2;
        yLabelY = 8;
      } else if (flip === "x") {
        xLabelX = -8;
        xLabelY = arrowLength / 2;
        yLabelX = arrowLength / 2;
        yLabelY = 8;
      } else if (flip === "y") {
        xLabelX = 8;
        xLabelY = -arrowLength / 2;
        yLabelX = -arrowLength / 2;
        yLabelY = 8;
      }
      break;
    default: // "0"
      if (flip === "x") {
        xLabelX = -arrowLength / 2;
        xLabelY = -8;
      } else if (flip === "y") {
        yLabelY = arrowLength / 2;
      }
      break;
  }

  return { xLabelX, xLabelY, yLabelX, yLabelY };
};

/**
 * Renders a wafer with a notch at a specified position.
 *
 * @param {string} notchPosition - The notch position (T, R, L, D, or NONE)
 * @param {number} centerX - The x-coordinate of the wafer's center
 * @param {number} centerY - The y-coordinate of the wafer's center
 * @param {number} radius - The wafer's radius
 * @param {string} transform - An optional SVG transform string
 * @returns {JSX.Element} An SVG element representing the wafer
 */
const renderWafer = (notchPosition, centerX, centerY, radius, transform) => {
  if (!notchPosition || notchPosition === "NONE") {
    return (
      <circle
        cx={centerX}
        cy={centerY}
        r={radius}
        fill="#466CA6"
        stroke="#666"
        strokeWidth="2"
        transform={transform}
      />
    );
  } else {
    const path = createNotchPath(notchPosition, centerX, centerY, radius);
    return (
      <path
        d={path.join(" ")}
        fill="#466CA6"
        stroke="#666"
        strokeWidth="1"
        transform={transform}
      />
    );
  }
};

/**
 * Creates an SVG path for a wafer with a notch at the specified position
 *
 * @param {string} notchPosition - The notch position (T, R, L, D, or NONE)
 * @param {number} centerX - The x-coordinate of the wafer's center
 * @param {number} centerY - The y-coordinate of the wafer's center
 * @param {number} radius - The wafer's radius
 * @returns {array} An array of SVG path commands
 */
const createNotchPath = (notchPosition, centerX, centerY, radius) => {
  const path = [];

  // Notch dimensions
  const curveWidth = Math.min(radius * 0.3, 30);
  const curveDepth = radius * 0.05;

  // Calculate notch position
  let curveAngle;
  if (notchPosition === "T") {
    curveAngle = 270; // degrees (top)
  } else if (notchPosition === "R") {
    curveAngle = 0; // degrees (right)
  } else if (notchPosition === "D") {
    curveAngle = 90; // degrees (bottom)
  } else {
    // "L"
    curveAngle = 180; // degrees (left)
  }

  // Convert angle to radians
  const curveRadians = (curveAngle * Math.PI) / 180;

  // Calculate the center point of the curved edge
  const curveCenterX = centerX + radius * Math.cos(curveRadians);
  const curveCenterY = centerY + radius * Math.sin(curveRadians);

  // Calculate perpendicular direction
  const perpRadians = curveRadians + Math.PI / 2;
  const perpX = Math.cos(perpRadians);
  const perpY = Math.sin(perpRadians);

  // Calculate curve edge points
  const curveStartX = curveCenterX + perpX * curveWidth;
  const curveStartY = curveCenterY + perpY * curveWidth;
  const curveEndX = curveCenterX - perpX * curveWidth;
  const curveEndY = curveCenterY - perpY * curveWidth;

  // Calculate control point for the curve
  const controlX = curveCenterX - Math.cos(curveRadians) * curveDepth;
  const controlY = curveCenterY - Math.sin(curveRadians) * curveDepth;

  // Start the path at the first curve edge point
  path.push(`M ${curveStartX} ${curveStartY}`);

  // Draw the curved edge using a quadratic Bézier curve
  path.push(`Q ${controlX} ${controlY} ${curveEndX} ${curveEndY}`);

  // Calculate angles for the arc
  const startAngle = Math.atan2(curveEndY - centerY, curveEndX - centerX);
  let endAngle = Math.atan2(curveStartY - centerY, curveStartX - centerX);

  // Ensure we go the long way around
  if (endAngle < startAngle) {
    endAngle += 2 * Math.PI;
  }

  // Draw the arc from the end of the curved edge back to the start
  path.push(`A ${radius} ${radius} 0 1 0 ${curveStartX} ${curveStartY}`);

  return path;
};

/**
 * Renders two arrows (orange and blue) to represent the X and Y axes of a
 * wafer map chart. The arrows are positioned at the center of the chart and
 * point in opposite directions. The orange arrow points to the right and
 * represents the X axis, while the blue arrow points up and represents the
 * Y axis. The arrows are also labeled with the corresponding axis label.
 *
 * @param {number} arrowsX - The x-coordinate of the center of the chart
 * @param {number} arrowsY - The y-coordinate of the center of the chart
 * @param {number} arrowLength - The length of the arrow
 * @param {number} arrowHeadSize - The size of the arrow head
 * @param {string} arrowTransform - An optional SVG transform string
 * @param {number} xLabelX - The x-coordinate of the X axis label
 * @param {number} xLabelY - The y-coordinate of the X axis label
 * @param {number} yLabelX - The x-coordinate of the Y axis label
 * @param {number} yLabelY - The y-coordinate of the Y axis label
 * @returns {JSX.Element} An SVG element representing the two arrows and their labels
 */
const renderArrows = (
  arrowsX,
  arrowsY,
  arrowLength,
  arrowHeadSize,
  arrowTransform,
  xLabelX,
  xLabelY,
  yLabelX,
  yLabelY,
) => {
  return (
    <g transform={`translate(${arrowsX}, ${arrowsY})`}>
      <g transform={arrowTransform}>
        {/* X-axis arrow (orange) */}
        <line
          x1={0}
          y1={0}
          x2={arrowLength}
          y2={0}
          stroke="#FF9900"
          strokeWidth="2"
        />
        <polygon
          points={`${arrowLength},0 ${arrowLength - arrowHeadSize},-${arrowHeadSize / 2} ${arrowLength - arrowHeadSize},${arrowHeadSize / 2}`}
          fill="#FF9900"
        />

        {/* Y-axis arrow (blue) */}
        <line
          x1={0}
          y1={0}
          x2={0}
          y2={-arrowLength}
          stroke="#0066CC"
          strokeWidth="2"
        />
        <polygon
          points={`0,-${arrowLength} -${arrowHeadSize / 2},-${arrowLength - arrowHeadSize} ${arrowHeadSize / 2},-${arrowLength - arrowHeadSize}`}
          fill="#0066CC"
        />
      </g>

      {/* Text labels */}
      <text
        x={xLabelX}
        y={xLabelY}
        fill="#FF9900"
        textAnchor="middle"
        fontSize="10"
      >
        X
      </text>
      <text
        x={yLabelX}
        y={yLabelY}
        fill="#0066CC"
        textAnchor="middle"
        fontSize="10"
      >
        Y
      </text>
    </g>
  );
};

export default WaferNotchSVG;
