"use client";

import { useState } from "react";
import { Button, Form, message, Switch } from "antd";
import { useEffectApiFetch } from "../../../hooks";
import { useBoundStore } from "../../../store/store";
import Api from "../../api";
import FilterSelect from "../../../../src/utils/grid/components/filter_select";

/**
 * Trend Charts options
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function TrendChartsOptions({ pageKey }) {
  const urlParams = useBoundStore((state) => state.urlParams);
  const [programFilterList, setProgramFilterList] = useState([]);
  const [trendChartsOptionsForm] = Form.useForm();
  const [messageApi] = message.useMessage();

  useEffectApiFetch(
    () => {
      return getProgramFiltersList();
    },
    () => {
      setProgramFilterList([]);
    },
  );

  /**
   * Get program filter list
   */
  const getProgramFiltersList = () => {
    const abortCtl = Api.getSelectOptions(
      (res) => {
        if (res.success) {
          setProgramFilterList(res.data);
        } else {
          messageApi.warning(res.message, 5);
        }
      },
      (err) => {
        messageApi.error(err, 5);
      },
      {
        field: "program_filters",
        cache_it: 1,
      },
    );

    return abortCtl;
  };

  return (
    <div>
      <Form
        form={trendChartsOptionsForm}
        layout="inline"
        name={`${pageKey}_trend_analysis_chart_options_form`}
        initialValues={{ show_related: true }}
        className="my-3"
      >
        {programFilterList.includes("test_step") && (
          <Form.Item name="test_step" label="Test Step">
            <FilterSelect
              className="w-40!"
              params={{
                api: {
                  ...{
                    url: `api/v1/test_level/trend/list/filters/test_step`,
                    cache_it: 0,
                  },
                  ...urlParams[pageKey],
                },
              }}
            />
          </Form.Item>
        )}
        <Form.Item name="run_type" label="Run Type">
          <FilterSelect
            className="w-40!"
            params={{
              api: {
                ...{
                  url: `api/v1/test_level/trend/list/filters/run_type`,
                  cache_it: 0,
                },
                ...urlParams[pageKey],
              },
            }}
          />
        </Form.Item>
        <Form.Item
          name="show_related"
          valuePropName="checked"
          label="Show Related"
        >
          <Switch />
        </Form.Item>
        <Form.Item>
          <Button htmlType="submit">Apply to Chart</Button>
        </Form.Item>
      </Form>
    </div>
  );
}
