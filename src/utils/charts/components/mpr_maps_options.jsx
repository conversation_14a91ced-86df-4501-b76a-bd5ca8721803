"use client";

import React, { useState } from "react";
import { Button, Space, message } from "antd";
import PinMappingFileUploadModal from "../../forms/pin_mapping_file_upload_modal";
import Api from "../../api";
import Helper from "../../helper";

/**
 * MPR Map Options
 *
 * @param {string} pageKey
 * @param {object} filters
 * @param {function} setFilters
 * @param {object} prerenderData
 * @param {function} setPrerenderData
 * @param {function} setShouldGenerateRow
 * @returns {JSX.Element}
 */
export default function MPRMapsOptions({
  pageKey,
  filters,
  setFilters,
  prerenderData,
  setPrerenderData,
  setShouldGenerateRow,
}) {
  const [uploadFormDisabled, setUploadFormDisabled] = useState(false);
  const [uploadingIsComplete, setUploadingIsComplete] = useState(false);
  const [isUploadFormOpen, setIsUploadFormOpen] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();

  /**
   * Download MPR raw data
   */
  const downloadMPRRawData = () => {
    Api.downloadMPRRawData(
      (res) => {
        const filename = `mpr-raw-data-${+new Date()}.csv`;
        Helper.downloadBlobAsFile(res, filename);
      },
      (err) => {
        messageApi.error({
          message: "Download MPR Raw Data",
          description: err,
        });
      },
      {
        src_type: filters[pageKey].src_type,
        src_value: filters[pageKey].src_value,
        tnum: filters[pageKey].tnum,
      },
    );
  };

  return (
    <>
      {contextHolder}
      <Space size="middle">
        <Button onClick={downloadMPRRawData}>Download MPR Raw Data</Button>
        <Button
          disabled={uploadFormDisabled && !uploadingIsComplete}
          onClick={() => setIsUploadFormOpen(true)}
        >
          {uploadFormDisabled && !uploadingIsComplete
            ? "Uploading"
            : "Upload CSV Pin Mapping File"}
        </Button>
      </Space>
      <PinMappingFileUploadModal
        pageKey={pageKey}
        filters={filters}
        setFilters={setFilters}
        isUploadFormOpen={isUploadFormOpen}
        setIsUploadFormOpen={setIsUploadFormOpen}
        uploadFormDisabled={uploadFormDisabled}
        setUploadFormDisabled={setUploadFormDisabled}
        uploadingIsComplete={uploadingIsComplete}
        setUploadingIsComplete={setUploadingIsComplete}
        prerenderData={prerenderData}
        setPrerenderData={setPrerenderData}
        setShouldGenerateRow={setShouldGenerateRow}
      />
    </>
  );
}
