"use client";

import { PlusCircleOutlined } from "@ant-design/icons";
import { Button, Form } from "antd";
import { useState } from "react";
import { useBoundStore } from "../../../store/store";
import TestListSelect from "../../components/test_list_select";

/**
 * View more run type form component
 *
 * @param {string} pageKey
 * @returns {JSX.Element}
 */
export default function TopFailingTestSelectionForm({ pageKey }) {
  const [disableOpenTabBtn, setDisableOpenTabBtn] = useState(true);
  const urlParams = useBoundStore((state) => state.urlParams);

  /**
   * Triggers when test selection change
   * Disable open tab button when there is no test selected
   *
   * @param {object} value
   */
  const onChangeTest = (value) => {
    setDisableOpenTabBtn(value === undefined);
  };

  return (
    <div>
      <Form layout="inline" name={`${pageKey}_top_failing_test_selection_form`}>
        <Form.Item name="tnum" label="">
          <TestListSelect
            labelInValue
            className="min-w-[700px]!"
            apiParams={{ ...urlParams[pageKey], sort_by: "fails_overall" }}
            onChange={onChangeTest}
          ></TestListSelect>
        </Form.Item>
        <Form.Item>
          <Button
            htmlType="submit"
            icon={<PlusCircleOutlined />}
            disabled={disableOpenTabBtn}
          >
            Open Tab
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}
