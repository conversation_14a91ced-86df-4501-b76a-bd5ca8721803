"use client";

import { But<PERSON>, Checkbox, Form, Select } from "antd";
import { useBoundStore } from "../../../store/store";
import Api from "../../api";
import SearchInput from "../../forms/fields/search_input";

/**
 * View more run type form component
 *
 * @param {string} pageKey
 * @param {object} prerenderData
 * @param {object} rowOptionData
 * @returns {JSX.Element}
 */
export default function ScatterChartOptions({
  pageKey,
  prerenderData,
  rowOptionData,
}) {
  const pageMeta = useBoundStore((state) => state.pageMeta);
  const [mprScatterChartOptionsForm] = Form.useForm();

  return (
    <div>
      <Form
        form={mprScatterChartOptionsForm}
        layout="inline"
        name={`${pageKey}_scatter_chart_options_form`}
        initialValues={{
          sort_by: "start_date",
          sort_dir: "asc",
          x_axis_data: "all",
        }}
      >
        {rowOptionData?.has_pin_selection === true && (
          <Form.Item name="pin_numbers" label="Select Pins" className="!w-48">
            <SearchInput
              placeholder="All Pins"
              apiFunction={Api.getSelectOptions}
              apiParams={{
                field: "pin_numbers",
                src_type: "dsk",
                src_value: prerenderData.tnum_dsk?.toString(),
                tnum: prerenderData.tnum,
              }}
            />
          </Form.Item>
        )}
        {rowOptionData?.has_x_axis_data_option === true && (
          <Form.Item name="x_axis_data" label="X-Axis Data" className="!w-64">
            <Select
              className="w-32"
              popupMatchSelectWidth={false}
              options={[
                {
                  label: "Combined Unique Part Ids",
                  value: "all",
                },
                {
                  label: "Per Datalog Part Ids",
                  value: "per",
                },
              ]}
            />
          </Form.Item>
        )}
        {(pageMeta.analysis_type === "multiple" ||
          pageMeta.analysis_type === "grouped") && (
          <>
            {rowOptionData?.has_sorting_option === true && (
              <>
                <Form.Item name="sort_by" label="Sort By">
                  <Select
                    className="w-32"
                    popupMatchSelectWidth={false}
                    options={[
                      {
                        label: "Start Date",
                        value: "start_date",
                      },
                      {
                        label: "Filename",
                        value: "file_name",
                      },
                    ]}
                  />
                </Form.Item>
                <Form.Item name="sort_dir" label="Order">
                  <Select
                    className="w-32"
                    popupMatchSelectWidth={false}
                    options={[
                      {
                        label: "Ascending",
                        value: "asc",
                      },
                      {
                        label: "Descending",
                        value: "desc",
                      },
                    ]}
                  />
                </Form.Item>
              </>
            )}

            {rowOptionData?.has_show_filename_option === true && (
              <Form.Item name="show_datalog_lines" valuePropName="checked">
                <Checkbox>Show Datalog Filename</Checkbox>
              </Form.Item>
            )}
          </>
        )}
        <Form.Item>
          <Button type="primary" htmlType="submit">
            Apply
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}
