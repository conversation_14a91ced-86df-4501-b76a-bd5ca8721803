import React, { useEffect, useState } from "react";
import { Row, Col, message, Card, Button, Space, Form, Input } from "antd";
import Api from "../../api";
import { useEffectApiFetch } from "../../../hooks";
import Helper from "../../helper";
import { useBoundStore } from "../../../store/store";
import { GalleryChart } from "../../components/gallery_chart";
import FilterSelect from "../../grid/components/filter_select";
import { ComponentNameMapper } from "../../grid/component_name_mapper";
import SearchInput from "../../forms/fields/search_input";

/**
 * Per Die Wafer Charts
 *
 * @param {object} chartCustomData
 * @param {string} pageKey
 * @param {object} filters
 * @param {function} testStatsInfoHandler
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default function PerDieWaferCharts({
  chartCustomData,
  pageKey,
  filters,
  testStatsInfoHandler,
  prerenderData,
}) {
  const [galleryChartIndex, setGalleryChartIndex] = useState(0);
  const [gridCharts, setGridCharts] = useState({});
  const [chartComponentBlueprint, setChartComponentBlueprint] = useState();
  const [perDieWaferCharts, setPerDieWaferCharts] = useState({});
  const [selectedChartLayout] = useState(2);
  const [isPlotBtnDisabled, setIsPlotBtnDisabled] = useState(true);
  const [shouldClearPartIds, setShouldClearPartIds] = useState(false);
  const [isPartIdSelectionDisabled, setIsPartIdSelectionDisabled] =
    useState(false);
  const urlParams = useBoundStore((state) => state.urlParams);
  const galleryCharts = useBoundStore((state) => state.galleryCharts);
  const setGalleryCharts = useBoundStore((state) => state.setGalleryCharts);
  const pageMeta = useBoundStore((state) => state.pageMeta);
  const [messageApi, contextHolder] = message.useMessage();
  const [perDieWaferChartsOptionsForm] = Form.useForm();
  const galleryView = "grid";

  useEffect(() => {
    if (!galleryCharts[pageKey]) {
      galleryCharts[pageKey] = { [galleryView]: {} };
    }
    if (pageMeta.analysis_type !== "single") {
      setIsPartIdSelectionDisabled(true);
    }
  }, []);

  useEffectApiFetch(
    () => {
      return Api.getComponentBlueprint(
        (res) => {
          if (res.success) {
            setChartComponentBlueprint(res.data);
          } else {
            messageApi.warning(res.message, 5);
          }
        },
        (err) => {
          messageApi.error(err, 5);
        },
        {
          name: ComponentNameMapper.parametric_mpr_single_device_map,
        },
      );
    },
    () => {
      setChartComponentBlueprint();
    },
  );

  useEffect(() => {
    const chartKeys = Object.keys(perDieWaferCharts);
    const chartKey = chartKeys[galleryChartIndex];
    if (chartKey) {
      messageApi.open({
        key: "generating_charts_msg",
        type: "loading",
        content: `Generating ${galleryChartIndex + 1} of ${chartKeys.length} charts...`,
        duration: 0,
      });

      const gridChartsCopy = Helper.cloneObject(gridCharts);
      gridChartsCopy[chartKey] = perDieWaferCharts[chartKey];
      gridChartsCopy[chartKey].galleryChartIndex = galleryChartIndex;
      gridChartsCopy[chartKey].setGalleryChartIndex = setGalleryChartIndex;

      setGridCharts(gridChartsCopy);
    }
    if (galleryChartIndex === chartKeys.length) {
      messageApi.destroy("generating_charts_msg");
    }
  }, [galleryChartIndex, perDieWaferCharts]);

  /**
   * Add chart object to per die wafer charts
   *
   * @param {object} values
   */
  const plotPerDieWaferCharts = (values) => {
    setGalleryChartIndex(0);
    setGridCharts({});

    let charts = {};
    const prerenderDataStr = JSON.stringify(prerenderData);
    values.dsk.forEach((dskObj) => {
      const dsk = dskObj.value.split("|")[0];
      const mfgProcess = dskObj.value.split("|")[1];

      values.part_id.forEach((partId) => {
        const chartKey = `${chartComponentBlueprint.name}_${dskObj.value}_${partId}_${prerenderDataStr}`;
        const chartComponentBlueprintCopy = Helper.cloneObject(
          chartComponentBlueprint,
        );
        chartComponentBlueprintCopy.id = chartKey;
        chartComponentBlueprintCopy.props.params.body_params.test_number =
          prerenderData.tnum;
        charts[chartKey] = {
          key: chartKey,
          chartType: "wafer",
          chartKey: chartKey,
          filters: filters,
          pageKey: pageKey,
          component: chartComponentBlueprintCopy,
          chartFilters: {
            src_type: "dsk",
            src_value: dsk,
            mfg_process: mfgProcess,
            part_id_show_only: partId,
          },
          prerenderData: prerenderData,
          showBoostWarning: true,
          testStatsInfoHandler: testStatsInfoHandler,
          title: `${dskObj.label}<br/> Die/Serial Number ${partId}`,
        };
      });
    });

    setPerDieWaferCharts(charts);
  };

  /**
   * Get split value for each array item
   *
   * @param {array} arr
   * @param {int} index
   * @returns {string}
   */
  const getSplitValues = (arr, index) => {
    return arr.map((item) => item.value.split("|")[index]).join(",");
  };

  return (
    <div className="">
      {contextHolder}
      <Form
        form={perDieWaferChartsOptionsForm}
        layout="inline"
        name={`${pageKey}_per_die_wafer_charts_options_form`}
        onFinish={plotPerDieWaferCharts}
        onValuesChange={(_, values) => {
          const dsk = values.dsk;
          const partIds = values.part_id;
          setIsPlotBtnDisabled(!(partIds?.length > 0 && dsk?.length > 0));
        }}
      >
        <Space>
          <Form.Item
            name="dsk"
            label="Select Datalog"
            initialValue={
              pageMeta.analysis_type === "single"
                ? prerenderData.test_info?.dsk
                : undefined
            }
            noStyle={pageMeta.analysis_type === "single"}
          >
            {pageMeta.analysis_type === "single" ? (
              <Input type="hidden" />
            ) : (
              <FilterSelect
                className="!min-w-48 !max-w-96"
                componentKey="select_datalog_options"
                placeholder="Select Datalog"
                mode="multiple"
                labelInValue={true}
                onChange={(value) => {
                  perDieWaferChartsOptionsForm.setFieldValue("part_id", null);
                  setShouldClearPartIds(true);
                  setIsPartIdSelectionDisabled(
                    !Array.isArray(value) || value.length === 0,
                  );
                }}
                params={{
                  api: {
                    url: "api/v1/internal/options/list/file_names",
                    mfg_process: urlParams[pageKey].mfg_process,
                    src_type: urlParams[pageKey].src_type,
                    src_value: urlParams[pageKey].src_value,
                    cache_it: 0,
                  },
                }}
              />
            )}
          </Form.Item>
          <Form.Item name="part_id" label="Select Die">
            <SearchInput
              className="!w-48"
              placeholder="Please select Pins"
              mode="multiple"
              apiFunction={Api.getSelectOptions}
              apiParams={{
                field: "part_ids",
                mfg_process: getSplitValues(
                  perDieWaferChartsOptionsForm.getFieldValue("dsk") ?? [],
                  1,
                ),
                src_type: "dsk",
                src_value: getSplitValues(
                  perDieWaferChartsOptionsForm.getFieldValue("dsk") ?? [],
                  0,
                ),
              }}
              disabled={isPartIdSelectionDisabled}
              shouldClearOptions={shouldClearPartIds}
              setShouldClearOptions={setShouldClearPartIds}
            />
          </Form.Item>
          <Button
            type="primary"
            onClick={() => perDieWaferChartsOptionsForm.submit()}
            disabled={isPlotBtnDisabled}
          >
            Plot
          </Button>
        </Space>
      </Form>

      <Row className="mt-4" gutter={[16, 16]}>
        {Object.keys(gridCharts).map(function (chartKey) {
          return (
            <Col
              key={`per_group_wafer_chart_${chartKey}_wrapper`}
              style={{
                width: `${100 / selectedChartLayout}%`,
              }}
            >
              <Card
                title={
                  <span
                    dangerouslySetInnerHTML={{
                      __html: perDieWaferCharts[chartKey].title,
                    }}
                  />
                }
              >
                <div
                  className={`m-auto ${selectedChartLayout === 2 ? "w-3/4" : ""}`}
                >
                  <div
                    id={`boosted_warning_msg_wrapper_${gridCharts[chartKey].component.id}`}
                  ></div>
                  {React.createElement(GalleryChart, {
                    ...gridCharts[chartKey],
                    chartCustomData: chartCustomData,
                    galleryCharts: galleryCharts,
                    setGalleryCharts: setGalleryCharts,
                    galleryView: galleryView,
                  })}
                </div>
              </Card>
            </Col>
          );
        })}
      </Row>
    </div>
  );
}
