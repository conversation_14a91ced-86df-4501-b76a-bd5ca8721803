import React, { useEffect, useState } from "react";
import { Row, Col, Select, Typography, message, Card } from "antd";
import Api from "../../api";
import { useEffectApiFetch } from "../../../hooks";
import Helper from "../../helper";
import { UserSettingsKeys } from "../../user_settings_keys";
import { useBoundStore } from "../../../store/store";
import { GalleryChart } from "../../components/gallery_chart";

/**
 * Per Group Wafer Charts
 *
 * @param {object} chartCustomData
 * @param {string} pageKey
 * @param {object} filters
 * @param {function} testStatsInfoHandler
 * @param {object} prerenderData
 * @param {object} component
 * @returns {JSX.Element}
 */
export default function PerGroupWaferCharts({
  chartCustomData,
  pageKey,
  filters,
  testStatsInfoHandler,
  prerenderData,
  component,
}) {
  const [galleryChartIndex, setGalleryChartIndex] = useState(0);
  const [gridCharts, setGridCharts] = useState({});
  const [groupedDatalogs, setGroupedDatalogs] = useState([]);
  const [chartComponentBlueprint, setChartComponentBlueprint] = useState();
  const [perGroupWaferCharts, setPerGroupWaferCharts] = useState({});
  const [selectedChartLayout, setSelectedChartLayout] = useState(
    Helper.getUserSettings(UserSettingsKeys.more_wafer_chart_layout) ?? 2,
  );
  const urlParams = useBoundStore((state) => state.urlParams);
  const galleryCharts = useBoundStore((state) => state.galleryCharts);
  const setGalleryCharts = useBoundStore((state) => state.setGalleryCharts);
  const [messageApi, contextHolder] = message.useMessage();
  const galleryView = "grid";
  const settings = component?.props?.settings;
  const currentPageParams = urlParams[pageKey];

  useEffect(() => {
    if (!galleryCharts[pageKey]) {
      galleryCharts[pageKey] = { [galleryView]: {} };
    }
  }, []);

  useEffectApiFetch(
    () => {
      if (chartComponentBlueprint) {
        return Helper.getGroupedDatalogs(
          {
            mfg_process: currentPageParams.mfg_process,
            group_by:
              settings?.group_by ??
              prerenderData.group_by ??
              currentPageParams.group_by,
            src_type: currentPageParams.src_type,
            src_value: currentPageParams.src_value,
          },
          setGroupedDatalogs,
          messageApi,
        );
      }
    },
    () => {
      setGroupedDatalogs([]);
    },
    [chartComponentBlueprint],
  );

  useEffectApiFetch(
    () => {
      return Api.getComponentBlueprint(
        (res) => {
          if (res.success) {
            setChartComponentBlueprint(res.data);
          } else {
            messageApi.warning(res.message, 5);
          }
        },
        (err) => {
          messageApi.error(err, 5);
        },
        {
          name: settings?.component_name ?? prerenderData.component_name,
        },
      );
    },
    () => {
      setChartComponentBlueprint();
    },
  );

  useEffect(() => {
    if (chartComponentBlueprint) {
      renderPerGroupWaferCharts(chartComponentBlueprint, groupedDatalogs);
    }
  }, [groupedDatalogs]);

  useEffect(() => {
    const chartKeys = Object.keys(perGroupWaferCharts);
    const chartKey = chartKeys[galleryChartIndex];
    if (chartKey) {
      messageApi.open({
        key: "generating_charts_msg",
        type: "loading",
        content: `Generating ${galleryChartIndex + 1} of ${chartKeys.length} charts...`,
        duration: 0,
      });

      const gridChartsCopy = Helper.cloneObject(gridCharts);
      gridChartsCopy[chartKey] = perGroupWaferCharts[chartKey];
      gridChartsCopy[chartKey].galleryChartIndex = galleryChartIndex;
      gridChartsCopy[chartKey].setGalleryChartIndex = setGalleryChartIndex;

      setGridCharts(gridChartsCopy);
    }
    if (galleryChartIndex === chartKeys.length) {
      messageApi.destroy("generating_charts_msg");
    }
  }, [galleryChartIndex, perGroupWaferCharts]);

  useEffect(() => {
    Object.keys(perGroupWaferCharts).forEach((chartKey) => {
      const chart = galleryCharts[pageKey][galleryView][chartKey];
      if (chart) {
        chart.shouldSetWaferInfoPosition = true;
        chart.shouldSetChartSize = true;
        chart.setSize(chart.renderTo.clientWidth, null, false);
      }
    });
  }, [selectedChartLayout]);

  /**
   * Add chart object to per group wafer charts
   *
   * @param {object} chartComponentBlueprint
   * @param {object} groupedDatalogs
   */
  const renderPerGroupWaferCharts = (
    chartComponentBlueprint,
    groupedDatalogs,
  ) => {
    let charts = {};
    const prerenderDataStr = JSON.stringify(prerenderData);
    groupedDatalogs.forEach((group) => {
      const chartKey = `${chartComponentBlueprint.name}_${group?.dsk_hash || ""}_${prerenderDataStr}`;
      const chartComponentBlueprintCopy = Helper.cloneObject(
        chartComponentBlueprint,
      );
      chartComponentBlueprintCopy.id = chartKey;
      chartComponentBlueprintCopy.props.params.body_params.test_number =
        prerenderData.tnum;
      const groupName =
        group.group_name ??
        prerenderData.group_name ??
        filters[pageKey].group_by_label ??
        null;
      charts[chartKey] = {
        key: chartKey,
        chartType: "wafer",
        chartKey: chartKey,
        filters: filters,
        pageKey: pageKey,
        component: chartComponentBlueprintCopy,
        chartFilters: {
          src_type: "dsk",
          src_value: group.dsk.join(),
          has_open_selected_test_analysis_option: true,
          selected_test_analysis_params: {
            src_type: "dsk",
            src_value: group.dsk.join(),
            dsk: group.dsk,
            lot_id: group.lot_id,
            mfg_process: group.mfg_process,
            tnum: prerenderData.tnum,
            tnum_dsk: prerenderData.tnum_dsk,
          },
        },
        prerenderData: prerenderData,
        showBoostWarning: true,
        testStatsInfoHandler: testStatsInfoHandler,
        title: `${groupName ? `${groupName}: ` : ""}${group.label}`,
      };
    });

    setPerGroupWaferCharts(charts);
  };

  /**
   * Handles changes to the chart layout selection
   *
   * @param {int} value
   */
  const onChartLayoutChange = (value) => {
    Helper.setUserSettings(UserSettingsKeys.more_wafer_chart_layout, value);
    setSelectedChartLayout(value);
  };

  return (
    <div className="">
      {contextHolder}
      <Row>
        <Typography.Text className="flex items-center mr-1">
          Layout:
        </Typography.Text>
        <Select
          popupMatchSelectWidth
          value={selectedChartLayout}
          onChange={onChartLayoutChange}
          options={[
            { value: 2, label: "2 Charts per Row" },
            { value: 3, label: "3 Charts per Row" },
            { value: 4, label: "4 Charts per Row" },
          ]}
          placeholder="Select Layout"
        />
      </Row>
      <Row className="mt-4" gutter={[16, 16]}>
        {Object.keys(gridCharts).map(function (chartKey) {
          return (
            <Col
              key={`per_group_wafer_chart_${chartKey}_wrapper`}
              style={{
                width: `${100 / selectedChartLayout}%`,
              }}
            >
              <Card title={perGroupWaferCharts[chartKey].title}>
                <div
                  className={`m-auto ${selectedChartLayout === 2 ? "w-3/4" : ""}`}
                >
                  <div
                    id={`boosted_warning_msg_wrapper_${gridCharts[chartKey].component.id}`}
                  ></div>
                  {React.createElement(GalleryChart, {
                    ...gridCharts[chartKey],
                    chartCustomData: chartCustomData,
                    galleryCharts: galleryCharts,
                    setGalleryCharts: setGalleryCharts,
                    galleryView: galleryView,
                  })}
                </div>
              </Card>
            </Col>
          );
        })}
      </Row>
    </div>
  );
}
