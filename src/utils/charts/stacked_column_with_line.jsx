"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import React, { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Api from "../api";
import Helper from "../helper";
import { useBoundStore } from "../../store/store";
import { QueryKeys } from "../query_keys";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import ChartHelper from "./chart_helper";
import ChartLoading from "./loading";

/**
 * Component names that will be affected by yield trend lot_count filter
 * yield_trend_num_lots filter will be set as lot_count filter
 */
const yieldTrendLotCountComponents = ["lot_yield_trend_chart"];

/**
 * Stacked column with line chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {function} setHasChartData
 * @param {object} fullScreenHandle
 * @param {object} prerenderData
 * @param {React.Ref} ref
 * @returns {JSX.Element}
 */
export default React.forwardRef(function StackedColumnWithLine(
  {
    chartRef,
    component,
    filters,
    pageKey,
    chartKey,
    chartCustomData,
    setHasChartData,
    fullScreenHandle,
    prerenderData = {},
  },
  ref,
) {
  const [chartOptions, setChartOptions] = useState();
  const [fetchedChartData, setFetchedChartData] = useState({});
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const presetAnalysisTemplates = useBoundStore(
    (state) => state.presetAnalysisTemplates,
  );
  const urlParams = useBoundStore((state) => state.urlParams);
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const { message } = App.useApp();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const chartType = component.name;

  const options = merge(ChartHelper.getChartDefaultSettings(), {
    chart: {
      type: "column",
      zoomType: "xy",
      events: {
        render: (event) => {
          const chart = event.target;
          if (
            chart.shouldHighlightCategories ||
            chartCustomData[chartKey].shouldHighlightCategories
          ) {
            chart.shouldHighlightCategories = false;
            chartCustomData[chartKey].shouldHighlightCategories = false;
            options.xAxis[0].plotLines = ChartHelper.highlightCategories(
              chart,
              chart.series[chart.series.length - 1],
              chartCustomData[chartKey].categories,
              chartCustomData[chartKey].highlightCategories,
            );
          }
        },
      },
    },
    title: {
      text: settings.title,
      align: "left",
    },
    xAxis: [
      {
        categories: [],
      },
    ],
    yAxis: [
      {
        title: {
          text: settings.y.title,
        },
        minPadding: 0,
        maxPadding: 0,
        max: 100,
        min: 0,
        labels: {
          format: "{value}%",
        },
        gridLineWidth: 0,
      },
      {
        title: {
          text: settings.y2.title,
        },
        opposite: true,
        reversedStacks: false,
      },
    ],
    plotOptions: {
      column: {
        stacking: "normal",
      },
    },
    series: [],
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      default: settings,
    },
  });

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }
    chartComponentRefs[chartKey] = ref;

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  React.useImperativeHandle(ref, () => ({
    reloadChart: () => {
      if (
        reloadChartFilters[chartKey].lot_count !== undefined &&
        yieldTrendLotCountComponents.indexOf(component.name) !== -1
      ) {
        filters[pageKey].lot_count = reloadChartFilters[chartKey].lot_count;
        delete reloadChartFilters[chartKey].lot_count;
      }
      if (reloadChartFilters[chartKey].sorting !== undefined) {
        filters[pageKey].sorting = reloadChartFilters[chartKey].sorting;
      }
      generateChartData(
        filters[pageKey].lot_count,
        reloadChartFilters[chartKey].sorted_categories,
      );
    },
  }));

  /**
   * Generate chart data based on category count and sorting
   *
   * @param {int} categoryCount
   * @param {array} sortedCategories
   */
  const generateChartData = (categoryCount, sortedCategories) => {
    const categories = fetchedChartData?.x_categories;
    if (categories && (!categoryCount || categoryCount <= categories.length)) {
      if (!categoryCount) {
        categoryCount = categories.length;
      }
      if (!sortedCategories) {
        sortedCategories = categories;
      }
      if (
        categoryCount < sortedCategories.length &&
        fetchedChartData.highlight_x_categories[0]
      ) {
        const selectedCategory = fetchedChartData.highlight_x_categories[0];
        const selectedCategoryIndex = categories.indexOf(selectedCategory);
        sortedCategories = Helper.getArrayElementsAroundIndex(
          categories,
          selectedCategoryIndex,
          categoryCount,
        );
      }
      let sortedIndexes = [];
      sortedCategories.forEach((category) => {
        sortedIndexes.push(categories.indexOf(category));
      });
      const newChartData = Helper.cloneObject(fetchedChartData);
      newChartData.x_categories = sortedCategories;
      newChartData.line_data.forEach((data, i) => {
        newChartData.line_data[i] = Helper.rearrangeArray(data, sortedIndexes);
      });
      newChartData.y_data.forEach((data, i) => {
        newChartData.y_data[i] = Helper.rearrangeArray(data, sortedIndexes);
      });
      generateChart(newChartData);
    } else {
      chartDataQuery.refetch();
    }
  };

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart?.showLoading();

    const allFilters = { ...filters[pageKey], ...prerenderData };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
    enabled: defaultChartOptions.isSuccess,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        setFetchedChartData(response.data);
        generateChart(Helper.cloneObject(response.data));
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * Generate chart options
   *
   * @param {object} data
   */
  const generateChart = (data) => {
    setHasChartData(Array.isArray(data.y_data) && data.y_data.length > 0);

    chartCustomData[chartKey].categories = data.x_categories;
    chartCustomData[chartKey].highlightCategories = data.highlight_x_categories;
    chartCustomData[chartKey].shouldHighlightCategories = true;

    options.xAxis[0].categories = data.x_categories;

    const stackedColumnSeries = generateStackedSeries(
      data.y_data ?? [],
      data.bar_labels ?? [],
      data.bar_colors ?? [],
    );
    const lineSeries = generateLineSeries(
      data.line_data ?? [],
      data.line_labels ?? [],
      data.line_colors ?? [],
    );
    options.series = [...lineSeries, ...stackedColumnSeries];

    const chartOptions = defaultChartOptions.data?.data?.value
      ? merge(
          options,
          JSON.parse(defaultChartOptions.data.data.value),
          retainedChartOptions[chartKey] ?? {},
        )
      : options;
    setChartOptions(chartOptions);
  };

  /**
   * Open lot analysis in a new browser tab
   *
   * @param {string} lotId
   */
  const openLotAnalysis = (lotId) => {
    const page = "lot_analysis";
    const mfgProcess = urlParams[pageKey].mfg_process;
    const analysisInput = {
      lot_id: [lotId],
      mfg_process: [mfgProcess],
      src_type: "lotid",
      src_value: [lotId],
    };
    Helper.openAnalysis(
      page,
      analysisInput,
      presetAnalysisTemplates,
      queryClient,
      message,
      false,
      true,
    );
  };

  const eventActions = {
    openLotAnalysis,
  };

  /**
   * Generate stacked column series
   *
   * @param {array} barData
   * @param {array} labels
   * @param {array} colors
   * @returns {array} stackedSeries
   */
  const generateStackedSeries = (barData, labels, colors) => {
    let stackedSeries = [];
    barData.forEach((data, i) => {
      const series = {
        name: labels[i] ?? "",
        data: data,
        color: colors[i] ?? null,
        yAxis: 1,
        tooltip: {
          pointFormatter: function () {
            return `${this.series.name}: <b>${Helper.numberFormat(this.y, 0)}</b>`;
          },
        },
      };
      if (component.props.events) {
        series.events = {};
        Object.keys(component.props.events).forEach((event) => {
          switch (event) {
            case "click":
              series.events.click = (event) => {
                eventActions[component.props.events.click.action](
                  event.point.category,
                );
              };
              series.cursor = "pointer";
              break;
          }
        });
      }
      stackedSeries.push(series);
    });

    return stackedSeries;
  };

  /**
   * Generate line series
   *
   * @param {array} lineData
   * @param {array} labels
   * @param {array} colors
   * @returns {array} lineSeries
   */
  const generateLineSeries = (lineData, labels, colors) => {
    let lineSeries = [];

    lineData.forEach((data, i) => {
      lineSeries.push({
        type: "line",
        name: labels[i] ?? "",
        color: colors[i] ?? null,
        yAxis: 0,
        zIndex: 10,
        tooltip: {
          valueDecimals: 2,
          valueSuffix: "%",
        },
        data: data,
      });
    });

    return lineSeries;
  };

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    chart.shouldHighlightCategories = true;

    ChartHelper.updateExportingMenu(
      chart,
      setIsChartOptionsOpen,
      setCurrentChart,
      chartCustomData[chartKey],
      fullScreenHandle,
    );
  };

  return (
    <div>
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
});
