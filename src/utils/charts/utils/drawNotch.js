import { NOTCH_POSITIONS, DEFAULT_NOTCH_ATTRIBUTES } from "../constants";

/**
 * Calculates the center point and radius for the chart
 * @param {Object} chart - The chart object containing plot dimensions
 * @returns {Object} Object containing centerX, centerY, and radius
 */
const calculateChartDimensions = (chart) => {
  const centerX = chart.plotLeft + chart.plotWidth / 2;
  const centerY = chart.plotTop + chart.plotHeight / 2;
  const radius = Math.min(chart.plotWidth, chart.plotHeight) / 2 + 10;

  return { centerX, centerY, radius };
};

/**
 * Transforms the notch location based on the chart's rotation and flip settings
 * @param {string} notchLocation - The notch location to transform
 * @param {object} chartCustomData - The chart's custom data
 * @returns {string} The transformed notch location
 */
const getNotchLocationAfterTransform = (notchLocation, chartCustomData) => {
  const { rotation, flip } = chartCustomData;
  let transformedNotchLocation = notchLocation;

  const rotationMap = {
    0: {
      T: "T",
      R: "R",
      L: "L",
      D: "D",
    },
    90: {
      T: "R",
      R: "D",
      L: "T",
      D: "L",
    },
    180: {
      T: "D",
      R: "L",
      L: "R",
      D: "T",
    },
    270: {
      T: "L",
      R: "T",
      L: "D",
      D: "R",
    },
  };

  const flipMap = {
    // x flip (horizontal flip effect - swap L/R)
    x: {
      R: "L",
      L: "R",
    },
    // y flip (vertical flip effect - swap T/D)
    y: {
      T: "D",
      D: "T",
    },
  };

  // Apply rotation first
  if (rotation && rotationMap[rotation]) {
    transformedNotchLocation =
      rotationMap[rotation][transformedNotchLocation] ||
      transformedNotchLocation;
  }

  // Then apply flip if needed
  if (flip && flip !== "none" && flipMap[flip]) {
    transformedNotchLocation =
      flipMap[flip][transformedNotchLocation] || transformedNotchLocation;
  }

  return transformedNotchLocation;
};

/**
 * Draws a notch on a chart.
 *
 * @param {object} chart - The Highcharts chart object
 * @param {string} [notchLocation="T"] - The location of the notch (T, R, L, D)
 * @param {object} [chartCustomData] - The custom data for the chart
 */
const drawNotch = (chart, notchLocation = "T", chartCustomData) => {
  if (!NOTCH_POSITIONS[notchLocation]) {
    console.warn(
      `Invalid notch position: ${notchLocation}. Using 'T' instead.`,
    );
    notchLocation = "T";
  }

  // Remove previous circle and notch if they exist
  if (chart.notchCircle) {
    chart.notchCircle.destroy();
    chart.notchCircle = null; // Clear the reference
  }
  if (chart.notchPath) {
    chart.notchPath.destroy();
    chart.notchPath = null; // Clear the reference
  }

  const dimensions = calculateChartDimensions(chart);
  const { centerX, centerY, radius } = dimensions;

  // Apply transformation to notch location based on rotation and flip
  const transformedNotchLocation = getNotchLocationAfterTransform(
    notchLocation,
    chartCustomData,
  );

  // Use a larger notch width for more noticeable curved edge
  const curveWidth = Math.min(radius * 0.3, 30); // 30% of radius or max 30px
  // Curve depth - how much the curve dips inward (smaller value = more subtle curve)
  const curveDepth = radius * 0.05; // 5% of radius for subtle curve

  // Calculate the angle for the curved edge based on the transformed notch location
  let curveAngle;
  if (transformedNotchLocation === "T") {
    curveAngle = 270; // degrees (top)
  } else if (transformedNotchLocation === "R") {
    curveAngle = 0; // degrees (right)
  } else if (transformedNotchLocation === "D") {
    curveAngle = 90; // degrees (bottom)
  } else {
    // "L"
    curveAngle = 180; // degrees (left)
  }

  // Convert angle to radians
  const curveRadians = (curveAngle * Math.PI) / 180;

  // Calculate the center point of the curved edge
  const curveCenterX = centerX + radius * Math.cos(curveRadians);
  const curveCenterY = centerY + radius * Math.sin(curveRadians);

  // Calculate perpendicular direction
  const perpRadians = curveRadians + Math.PI / 2;
  const perpX = Math.cos(perpRadians);
  const perpY = Math.sin(perpRadians);

  // Calculate curve edge points
  const curveStartX = curveCenterX + perpX * curveWidth;
  const curveStartY = curveCenterY + perpY * curveWidth;
  const curveEndX = curveCenterX - perpX * curveWidth;
  const curveEndY = curveCenterY - perpY * curveWidth;

  // Calculate control point for the curve (slightly inward from the center point)
  const controlX = curveCenterX - Math.cos(curveRadians) * curveDepth;
  const controlY = curveCenterY - Math.sin(curveRadians) * curveDepth;

  // Create a path for a circle with a curved edge
  let path = [];

  // Start the path at the first curve edge point
  path.push("M", curveStartX, curveStartY);

  // Draw the curved edge using a quadratic Bézier curve
  path.push("Q", controlX, controlY, curveEndX, curveEndY);

  // Calculate angles for the arc
  const startAngle = Math.atan2(curveEndY - centerY, curveEndX - centerX);
  let endAngle = Math.atan2(curveStartY - centerY, curveStartX - centerX);

  // Ensure we go the long way around (more than 180 degrees)
  if (endAngle < startAngle) {
    endAngle += 2 * Math.PI;
  }

  // Draw the arc from the end of the curved edge back to the start
  path.push(
    "A", // Arc command
    radius,
    radius, // x radius, y radius
    0, // x-axis-rotation
    1, // large-arc-flag (1 means take the long way around)
    0, // sweep-flag (0 means draw the arc in a negative angle)
    curveStartX,
    curveStartY, // End point
  );

  // Close the path
  path.push("Z");

  // Create the circle with curved edge
  chart.notchCircle = chart.renderer
    .path(path)
    .attr(DEFAULT_NOTCH_ATTRIBUTES)
    .add();
};

export default drawNotch;
