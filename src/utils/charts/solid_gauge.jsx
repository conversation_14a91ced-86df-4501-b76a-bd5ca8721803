"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/modules/solid-gauge";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import Api from "../api";
import Helper from "../helper";
import { QueryKeys } from "../query_keys";
import ChartLoading from "./loading";

/**
 * Solid gauge chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default function SolidGauge({
  chartRef,
  component,
  filters,
  pageKey,
  chartKey,
  prerenderData = {},
}) {
  const [chartOptions, setChartOptions] = useState();
  const [shouldFetchChartData, setShouldFetchChartData] = useState(false);
  const { message } = App.useApp();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;

  const options = {
    chart: {
      type: "solidgauge",
      height: settings.height ? settings.height : "auto",
    },
    title: {
      text: "",
    },
    tooltip: {
      enabled: false,
    },
    exporting: {
      enabled:
        settings.exporting && settings.exporting.enabled !== undefined
          ? settings.exporting.enabled
          : true,
    },
    pane: {
      startAngle: 0,
      endAngle: 360,
      background: [
        {
          outerRadius: "100%",
          innerRadius: "80%",
          backgroundColor: "#F0F0F0",
          borderWidth: 0,
        },
      ],
    },
    yAxis: {
      min: 0,
      max: 100,
      lineWidth: 0,
      tickPositions: [],
    },
    plotOptions: {
      solidgauge: {
        dataLabels: {
          enabled:
            settings.dataLabels && settings.dataLabels.enabled !== undefined
              ? settings.dataLabels.enabled
              : true,
          verticalAlign: "center",
          formatter: function () {
            return formatDataLabel();
          },
          borderWidth: 0,
          y:
            component.data.parts !== undefined ||
            component.data.hours !== undefined
              ? -40
              : -30,
          useHTML: true,
        },
        linecap: "round",
        stickyTracking: false,
      },
    },
    series: [
      {
        name: "Gauge",
        data: [],
        borderRadius: "50%",
      },
    ],
  };

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    } else {
      setChartSize();
    }

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  useEffect(() => {
    getChartData(params);
  }, []);

  /**
   * Set chart size to equal width and height
   */
  const setChartSize = () => {
    const width = chartRef.current.chart.chartWidth;
    chartRef.current.chart.setSize(width, width, false);
  };

  /**
   * Get chart data
   *
   * @returns {AbortController}
   */
  const getChartData = () => {
    chartRef.current?.chart?.showLoading();

    if (component.data !== undefined) {
      options.series[0].data = generateGaugeData(component.data);
      setChartOptions(options);
      chartRef.current?.chart?.hideLoading();
    } else {
      setShouldFetchChartData(true);
    }
  };

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    const requestParams = Helper.filterObjectByKeys(
      { ...filters[pageKey], ...prerenderData },
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      params.url_endpoint,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
    enabled: shouldFetchChartData,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        // TBD: To set data from api endpoint
        setChartOptions(options);
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * Create gauge data
   *
   * @param {object} chartData
   * @returns {array} data
   */
  const generateGaugeData = (chartData) => {
    const data = [
      {
        name: component.name,
        color: chartData.valueColor,
        radius: "100%",
        innerRadius: "80%",
        y: chartData.value,
      },
    ];

    return data;
  };

  /**
   * Format data label based on value
   */
  const formatDataLabel = () => {
    let arrow = "";
    if (component.data.compareValueDiff > 0) {
      arrow = "&#9650;";
    } else if (component.data.compareValueDiff < 0) {
      arrow = "&#9660;";
    }
    let extraData = "";
    if (component.data.parts) {
      extraData = `(${component.data.parts} parts)`;
    } else if (component.data.hours) {
      extraData = `(${component.data.hours} hours)`;
    }

    return `<div style="width: 100%; margin: auto">
      <div style="font-size: 38px; text-align: center; color:${
        component.data.valueColor
      }">${component.data.value}%</div>
      <div style="font-size: 12px; font-weight: normal; text-align: center; color: #00000073;">${extraData}</div><br/>
      <div style="font-size: 14px; font-weight: normal; text-align: center;">
        <span>${Math.abs(component.data.compareValueDiff)}%</span> 
        <span style="color:${
          component.data.compareValueDiffColor
        }">${arrow}</span>
      </div>
    </div>`;
  };

  return (
    <div>
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
}
