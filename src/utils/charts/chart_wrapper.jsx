import {
  DownOutlined,
  LoadingOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
} from "@ant-design/icons";
import {
  Button,
  Dropdown,
  Empty,
  Flex,
  Form,
  InputNumber,
  message,
  Modal,
  Radio,
  Select,
  Space,
  Spin,
  Switch,
  theme,
  Tooltip,
  Typography,
} from "antd";
import { debounce } from "lodash";
import React, { useCallback, useEffect, useRef, useState } from "react";
import ReactDOM from "react-dom";
import { FullScreen } from "react-full-screen";
import { useQueryClient } from "@tanstack/react-query";
import { ChartControlsFields } from "../../../app/(main)/(full-width-content)/(pages)/template/template_content/component_options/form_fields/chart_controls_fields";
import { useBoundStore } from "../../store/store";
import ColorScaleForm from "../forms/color_scale_form";
import OrientationSettingsForm from "../forms/orientation_settings_form";
import Helper from "../helper";
import { UserSettingsKeys } from "../user_settings_keys";
import BoostedChartWarningMessage from "./components/boosted_chart_warning_message";
import ChartHelper from "./chart_helper";

const { Text } = Typography;
const { useToken } = theme;

/**
 * Wrapper component of chart
 *
 * @param {JSX.Element} chartComponent
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {array} waferIdOptions
 * @param {object} chartCustomData
 * @param {string} chartKey
 * @param {string} pageKey
 * @param {object} chartFilters
 * @param {boolean} displayChartControls
 * @param {*} chartOptionModalContainer
 * @param {boolean} hasChartData
 * @param {boolean} isChartBoosted
 * @param {boolean} showBoostWarning
 * @param {object} fullScreenHandle
 * @returns {JSX.Element}
 */
const ChartWrapper = ({
  chartComponent,
  chartRef,
  component,
  waferIdOptions,
  chartCustomData,
  chartKey,
  pageKey,
  chartFilters = {},
  displayChartControls = true,
  chartOptionModalContainer = () => document.body,
  hasChartData,
  isChartBoosted,
  showBoostWarning = true,
  fullScreenHandle,
}) => {
  const [loadingChart] = useState(false);
  const [controlsProps, setControlsProps] = useState({});
  const [currentWaferId, setCurrentWaferId] = useState();
  const [isFullScreen, setIsFullScreen] = useState();
  const [modalContainer, setModalContainer] = useState(
    chartOptionModalContainer,
  );
  const [isOrientationSettingsModalOpen, setIsOrientationSettingsModalOpen] =
    useState(false);
  const [isColorScaleModalOpen, setIsColorScaleModalOpen] = useState(false);
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const presetAnalysisTemplates = useBoundStore(
    (state) => state.presetAnalysisTemplates,
  );
  const urlParams = useBoundStore((state) => state.urlParams);
  const pageMeta = useBoundStore((state) => state.pageMeta);
  const [messageApi, contextHolder] = message.useMessage();
  const chartWrapperRef = useRef();
  const chartControlsTopRef = useRef();
  const chartControlsBottomRef = useRef();
  const { token } = useToken();
  const [orientationSettingsForm] = Form.useForm();
  const [colorScaleForm] = Form.useForm();
  const queryClient = useQueryClient();
  const boostedWarningMsgWrapper = document.getElementById(
    `boosted_warning_msg_wrapper_${component.id}`,
  );

  useEffect(() => {
    if (!reloadChartFilters[chartKey]) {
      reloadChartFilters[chartKey] = {};
    }
  }, []);

  useEffect(() => {
    updateWaferIdNavigationBtns(currentWaferId, waferIdOptions);
  }, [currentWaferId]);

  useEffect(() => {
    updateFullScreenBtn(isFullScreen);
    const container = isFullScreen ? false : () => document.body;
    setModalContainer(container);
  }, [isFullScreen]);

  /**
   * Set selected wafer id
   *
   * @param {object} selectedWafer
   */
  const setWaferIdSelection = (selectedWafer) => {
    setTimeout(() => {
      setCurrentWaferId(
        typeof selectedWafer === "object" ? selectedWafer.value : selectedWafer,
      );
      reloadChartFilters[chartKey].wafer_id_selection = selectedWafer;
      chartComponentRefs[chartKey].current.reloadChart();
    }, 300);
  };

  /**
   * Show previous wafer based on current displayed wafer
   */
  const showPreviousWaferId = () => {
    showWaferId(-1);
  };

  /**
   * Show next wafer based on current displayed wafer
   */
  const showNextWaferId = () => {
    showWaferId(1);
  };

  /**
   * Show wafer based on offset from current displayed wafer
   *
   * @param {int} offset
   */
  const showWaferId = (offset) => {
    const currentWaferId =
      reloadChartFilters[chartKey].wafer_id_selection.value;
    const currentOptionKey = getWaferIdOptionKey(
      currentWaferId,
      waferIdOptions,
    );
    if (currentOptionKey) {
      const newOptionKey = parseInt(currentOptionKey) + offset;
      if (waferIdOptions[newOptionKey]) {
        setWaferIdSelection(waferIdOptions[newOptionKey]);
      }
    }
  };

  /**
   * Get option key of wafer id
   *
   * @param {string} currentWaferId
   * @param {array} waferIdOptions
   * @returns {string|undefined} optionKey
   */
  const getWaferIdOptionKey = (waferId, waferIdOptions) => {
    let optionKey;
    const optionKeys = Object.keys(waferIdOptions).filter((key) => {
      return waferIdOptions[key]["value"] === waferId;
    });
    if (optionKeys.length > 0) {
      optionKey = optionKeys[0];
    }

    return optionKey;
  };

  /**
   * Update next and previous wafer id navigation buttons
   *
   * @param {string} currentWaferId
   * @param {array} waferIdOptions
   */
  const updateWaferIdNavigationBtns = (currentWaferId, waferIdOptions) => {
    updateWaferIdNavigationBtn(currentWaferId, waferIdOptions);
  };

  /**
   * Update wafer id navigation button
   *
   * @param {string} currentWaferId
   * @param {array} waferIdOptions
   */
  const updateWaferIdNavigationBtn = (currentWaferId, waferIdOptions) => {
    let controlsPropsCopy = Helper.cloneObject(controlsProps);
    if (!controlsPropsCopy["previousWaferId"]) {
      controlsPropsCopy["previousWaferId"] = {};
      controlsPropsCopy["nextWaferId"] = {};
    }
    const currentOptionKey = getWaferIdOptionKey(
      currentWaferId,
      waferIdOptions,
    );
    controlsPropsCopy["previousWaferId"].disabled =
      currentOptionKey === undefined || parseInt(currentOptionKey) === 0;
    controlsPropsCopy["nextWaferId"].disabled =
      currentOptionKey === undefined ||
      parseInt(currentOptionKey) === waferIdOptions.length - 1;
    controlsProps.previousWaferId = controlsPropsCopy.previousWaferId;
    controlsProps.nextWaferId = controlsPropsCopy.nextWaferId;
    setControlsProps(controlsPropsCopy);
  };

  /**
   * Update full screen button
   *
   * @param {boolean} isFullScreen
   */
  const updateFullScreenBtn = (isFullScreen) => {
    let controlsPropsCopy = Helper.cloneObject(controlsProps);
    if (!controlsPropsCopy["fullScreen"]) {
      controlsPropsCopy["fullScreen"] = {};
    }
    controlsPropsCopy["fullScreen"].label = isFullScreen
      ? "Exit Full Screen"
      : "Full Screen";
    controlsPropsCopy["fullScreen"].icon = isFullScreen
      ? "FullscreenExitOutlined"
      : "FullscreenOutlined";
    controlsProps.fullScreen = controlsPropsCopy.fullScreen;
    setControlsProps(controlsPropsCopy);
  };

  /**
   * Set chart bin type and reload the chart
   *
   * @param {object}
   */
  const toggleBinType = ({ target: { value } }) => {
    reloadChartFilters[chartKey].chart_bin_type = value;
    chartComponentRefs[chartKey].current.reloadChart();
  };

  /**
   * Set chart zonal type and reload the chart
   *
   * @param {object}
   */
  const toggleZonalType = ({ target: { value } }) => {
    reloadChartFilters[chartKey].chart_zonal_type = value;
    chartComponentRefs[chartKey].current.reloadChart();
  };

  /**
   * Enter/exit chart in full screen
   */
  const toggleFullScreen = () => {
    ChartHelper.toggleFullScreen(
      chartRef.current.chart,
      chartCustomData[chartKey],
      fullScreenHandle,
    );
  };

  /**
   * Event fired for every full screen mode change
   */
  const onFullScreenChange = useCallback(
    (state, handle) => {
      setIsFullScreen(state);
      chartCustomData[chartKey].isFullScreen = state;
      if (chartCustomData[chartKey].fullscreen) {
        chartCustomData[chartKey].fullscreen.state = state;
      }

      // prevent trigger actions on page load
      if (isFullScreen !== undefined) {
        const chart = chartRef.current?.chart;

        // set flag to set displayed yield value position for yield wafer map
        if (component.component === "yield_wafer_map") {
          chart.shouldSetYieldPosition = true;
          chart.shouldSetWaferInfoPosition = true;
        }
        // set flag to set displayed wafer info position for composite wafer map
        if (component.component === "composite_wafer_map") {
          chart.shouldSetWaferInfoPosition = true;
        }
        // set flag to set color axis label position
        if (component.props.settings.color_axis_label) {
          chartCustomData[chartKey].shouldSetColorAxisLabelPosition = true;
        }
        if (component.component === "parametric_wafer_map") {
          // set flag to update color axis for parametric wafer map
          chartCustomData[chartKey].shouldUpdateParametricColorAxis = true;
          // set flag to set displayed wafer info position for parametric wafer map
          chart.shouldSetWaferInfoPosition = true;
        }
        if (component.component === "zonal_chart") {
          chart.shouldSetCenterPosition = true;
        }
        if (component.component === "stacked_column_with_line") {
          chartCustomData[chartKey].shouldHighlightCategories = true;
        }
        if (chartCustomData[chartKey].statsInfoElement) {
          chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
        }

        if (chart) {
          if (state === true) {
            let height =
              (component.component.indexOf("wafer_map") !== -1
                ? handle.node.current.clientHeight
                : handle.node.current.clientHeight * 0.9) -
              (chartControlsTopRef.current?.clientHeight ?? 0) -
              (chartControlsBottomRef.current?.clientHeight ?? 0);
            let width =
              component.component.indexOf("wafer_map") !== -1
                ? handle.node.current.clientWidth
                : handle.node.current.clientWidth * 0.9;
            if (component.component.indexOf("wafer_map") !== -1) {
              const estimatedWaferLegendWidth = 100;
              width = height;
              height = height - estimatedWaferLegendWidth;
            }

            chart.renderTo.style.height = `${height}px`;
            chart.renderTo.style.width = `${width}px`;
            chart.renderTo.style.margin = "auto";
            setChartSize(width, height, false);
            ChartHelper.updateFullScreenViewLabel(chart, true);
          } else {
            chart.renderTo.style.height = `${chartCustomData[chartKey].fullscreen.containerHeight}px`;
            chart.renderTo.style.width = `${chartCustomData[chartKey].fullscreen.containerWidth}px`;
            setChartSize(null, null, false);
            ChartHelper.updateFullScreenViewLabel(chart, false);
          }
        }
      }
    },
    [fullScreenHandle],
  );

  /**
   * Set chart size
   *
   * @param {number} width
   * @param {number} height
   */
  const setChartSize = (width, height) => {
    chartRef.current.chart.setSize(width, height, false);
    chartRef.current.chart.reflow();
  };

  /**
   * Enable/Disable chart tooltip
   * Set timeout to avoid laggy experience when toggling
   *
   * @param {boolean} enabled
   */
  const toggleTooltip = (enabled) => {
    setTimeout(() => {
      chartRef.current.chart.update({
        tooltip: {
          enabled: enabled,
        },
      });
    }, 200);
  };

  /**
   * Display orientation settings
   */
  const showOrientationSettings = () => {
    setIsOrientationSettingsModalOpen(true);
  };

  /**
   * Display color scale options
   */
  const showColorScaleOptions = () => {
    setIsColorScaleModalOpen(true);
  };

  /**
   * Display of wafer map gallery
   */
  const viewWaferMapGallery = () => {
    let requestParams = Helper.cloneObject(urlParams[pageKey]);
    delete requestParams.template;
    delete requestParams.tNum;
    requestParams.template_key = "wafer_map_gallery";
    requestParams.component_name = component.name;
    const queryString = Helper.createQueryString(requestParams);
    Helper.renderPage(
      `#lot-analysis/wafer-map-gallery?${queryString}`,
      "wafer_map_gallery",
      `#lot-analysis/wafer-map-gallery?${queryString}`,
      queryClient,
      false,
      true,
    );
  };

  /**
   * Display of pin yield datalog breakdown
   */
  const viewPinYieldDatalogBreakdown = () => {
    let requestParams = Helper.cloneObject(urlParams[pageKey]);
    delete requestParams.template;
    delete requestParams.tNum;
    requestParams.template_key = "pin_yield_datalog_breakdown";
    requestParams.component_name = component.name;
    const queryString = Helper.createQueryString(requestParams);
    Helper.renderPage(
      `#selected-test/pin-yield-datalog-breakdown?${queryString}`,
      "pin_yield_datalog_breakdown",
      `#selected-test/pin-yield-datalog-breakdown?${queryString}`,
      queryClient,
      false,
      true,
    );
  };

  /**
   * Display of mean parametric pin substrate datalog breakdown
   */
  const viewMeanParametricPinSubstrateDatalogBreakdown = () => {
    let requestParams = Helper.cloneObject(urlParams[pageKey]);
    delete requestParams.template;
    delete requestParams.tNum;
    requestParams.template_key =
      "mean_parametric_pin_substrate_datalog_breakdown";
    requestParams.component_name = component.name;
    const queryString = Helper.createQueryString(requestParams);
    Helper.renderPage(
      `#selected-test/mean-parametric-pin-substrate-datalog-breakdown?${queryString}`,
      "mean_parametric_pin_substrate_datalog_breakdown",
      `#selected-test/mean-parametric-pin-substrate-datalog-breakdown?${queryString}`,
      queryClient,
      false,
      true,
    );
  };

  /**
   * Display of pin yield group breakdown
   */
  const viewPinYieldGroupBreakdown = () => {
    let requestParams = Helper.cloneObject(urlParams[pageKey]);
    delete requestParams.template;
    delete requestParams.tNum;
    requestParams.template_key = "pin_yield_group_breakdown";
    requestParams.component_name = component.name;
    const queryString = Helper.createQueryString(requestParams);
    Helper.renderPage(
      `#selected-test/pin-yield-group-breakdown?${queryString}`,
      "pin_yield_group_breakdown",
      `#selected-test/pin-yield-group-breakdown?${queryString}`,
      queryClient,
      false,
      true,
    );
  };

  /**
   * Display of mean parametric pin substrate group breakdown
   */
  const viewMeanParametricPinSubstrateGroupBreakdown = () => {
    let requestParams = Helper.cloneObject(urlParams[pageKey]);
    delete requestParams.template;
    delete requestParams.tNum;
    requestParams.template_key =
      "mean_parametric_pin_substrate_group_breakdown";
    requestParams.component_name = component.name;
    const queryString = Helper.createQueryString(requestParams);
    Helper.renderPage(
      `#selected-test/mean-parametric-pin-substrate-group-breakdown?${queryString}`,
      "mean_parametric_pin_substrate_group_breakdown",
      `#selected-test/mean-parametric-pin-substrate-group-breakdown?${queryString}`,
      queryClient,
      false,
      true,
    );
  };

  /**
   * Generate Selected Test Analysis in a new browser tab
   */
  const openSelectedTestAnalysis = () => {
    Helper.openAnalysis(
      "selected_test",
      chartFilters.selected_test_analysis_params,
      presetAnalysisTemplates,
      queryClient,
      messageApi,
      false,
      true,
    );
  };

  /**
   * Check if chart has open Selected Test Analysis option
   *
   * @returns {boolean}
   */
  const hasOpenSelectedTestAnalysisOption = () => {
    return chartFilters.has_open_selected_test_analysis_option === true;
  };

  /**
   * Check if chart has datalog breakdown
   *
   * @returns {boolean}
   */
  const hasDatalogBreakdown = () => {
    return pageMeta.analysis_type === "multiple";
  };

  /**
   * Check if chart has group breakdown
   *
   * @returns {boolean}
   */
  const hasGroupBreakdown = () => {
    return pageMeta.analysis_type === "grouped";
  };

  /**
   * Set chart orientation
   */
  const setOrientation = () => {
    const values = orientationSettingsForm.getFieldsValue();
    reloadChartFilters[chartKey].rotation = values.rotation;
    reloadChartFilters[chartKey].flip = values.flip;
    reloadChartFilters[chartKey].display_arrows = values.display_arrows;
    chartComponentRefs[chartKey].current.reloadChart();
    setIsOrientationSettingsModalOpen(false);

    Helper.setUserSettings(
      UserSettingsKeys.wafer_map_orientation,
      values,
      "chart",
      component,
    );
  };

  /**
   * Set color scale for color axis
   */
  const setColorScale = () => {
    const values = colorScaleForm.getFieldsValue();
    chartCustomData[chartKey].shouldUpdateParametricColorAxis = true;
    chartCustomData[chartKey].selectedColorScale = values.selected_color_scale;
    chartCustomData[chartKey].bandCount = values.band_count;
    chartCustomData[chartKey].minScale = values.min_scale;
    chartCustomData[chartKey].maxScale = values.max_scale;
    chartComponentRefs[chartKey].current.reloadChart();
    setIsColorScaleModalOpen(false);
  };

  /**
   * Icon mapper for chart controls
   */
  const controlsIcons = {
    FullscreenOutlined: <FullscreenOutlined />,
    FullscreenExitOutlined: <FullscreenExitOutlined />,
  };

  /**
   * Action mapper for chart controls
   */
  const controlsActions = {
    setWaferIdSelection,
    showPreviousWaferId,
    showNextWaferId,
    toggleBinType,
    toggleZonalType,
    toggleFullScreen,
    toggleTooltip,
    showOrientationSettings,
    showColorScaleOptions,
    viewWaferMapGallery,
    viewPinYieldDatalogBreakdown,
    viewMeanParametricPinSubstrateDatalogBreakdown,
    viewPinYieldGroupBreakdown,
    viewMeanParametricPinSubstrateGroupBreakdown,
    openSelectedTestAnalysis,
    hasOpenSelectedTestAnalysisOption,
    hasDatalogBreakdown,
    hasGroupBreakdown,
  };

  /**
   * Generate chart controls options menu based on input type
   *
   * @param {object} controls
   * @param {string} position
   * @returns {array} controlsOptions
   */
  const createControlsOptions = (controls, position = ["top", "left"]) => {
    let controlsOptions = [];
    if (controls) {
      controlsOptions = controls
        .filter((controlsOption) => {
          const option = ChartControlsFields[controlsOption.key];
          return (
            (!option.position &&
              JSON.stringify(position) === JSON.stringify(["top", "left"])) ||
            (JSON.stringify(option.position) === JSON.stringify(position) &&
              (!option.renderConditionAction ||
                controlsActions[option.renderConditionAction]() === true))
          );
        })
        .map((controlsOption) => {
          const option = ChartControlsFields[controlsOption.key];
          const controlsMenuItem = getControlsMenuItem(option, controlsOption);
          return option.tooltip ? (
            <Tooltip
              key={`tooltip_${controlsOption.key}`}
              title={option.tooltip}
            >
              <div>{controlsMenuItem}</div>
            </Tooltip>
          ) : (
            controlsMenuItem
          );
        });
    }

    return controlsOptions;
  };

  /**
   * Get controls menu item element based on input type
   *
   * @param {object} option
   * @param {object} controlsOption
   * @returns {JSX Element} item
   */
  const getControlsMenuItem = (option, controlsOption) => {
    let item = <div key={`${option.inputType}_${controlsOption.key}`}></div>;
    if (option && !(hasChartData === false && option.hideNoData !== false)) {
      switch (option.inputType) {
        case "string":
          item = (
            <div
              key={`string_${controlsOption.key}`}
              className={option.className ?? ""}
              style={{
                height: token.controlHeightSM,
              }}
            >
              {option.value}
            </div>
          );
          break;
        case "dropdown":
          item = (
            <Dropdown
              key={`dropdown_${controlsOption.key}`}
              className={option.className ?? ""}
              menu={{
                items: option.items,
                onClick: controlsActions[option.onClick],
              }}
              trigger="click"
              disabled={option.disabled}
            >
              <Button
                icon={option.icon ? option.icon : null}
                loading={option.hasLoading ? loadingChart : null}
              >
                <Space>
                  {option.label}
                  {option.hasDownArrow !== false && <DownOutlined />}
                </Space>
              </Button>
            </Dropdown>
          );
          break;
        case "inputNumber":
          item = (
            <Form
              key={`form_${controlsOption.key}`}
              className={option.className ?? ""}
            >
              <Form.Item label={option.label}>
                <InputNumber
                  key={`input_number_${controlsOption.key}`}
                  min={option.min}
                  max={option.max}
                  defaultValue={
                    option.defaultValue !== undefined
                      ? option.defaultValue
                      : Helper.getDefaultValueByComponent(
                          component,
                          option.dataKey,
                        )
                  }
                  onChange={debounce(controlsActions[option.onChange], 1000)}
                />
              </Form.Item>
            </Form>
          );
          break;
        case "radioButton":
          item = (
            <Radio.Group
              key={`radio_group_${controlsOption.key}`}
              className={option.className ?? ""}
              options={option.options}
              onChange={controlsActions[option.onChange]}
              defaultValue={
                option.defaultValue !== undefined
                  ? option.defaultValue
                  : Helper.getDefaultValueByComponent(component, option.dataKey)
              }
              optionType="button"
              buttonStyle="solid"
            />
          );
          break;
        case "select":
          item = (
            <Select
              key={`select_${controlsOption.key}_${chartKey}`}
              className={`w-32 ${option.className ?? ""}`}
              showSearch
              allowClear
              placeholder={option.placeholder}
              popupMatchSelectWidth={
                option.popupMatchSelectWidth !== undefined
                  ? option.popupMatchSelectWidth
                  : true
              }
              labelInValue={
                option.labelInValue !== undefined ? option.labelInValue : false
              }
              getPopupContainer={() =>
                document.getElementById(`chart_wrapper_${chartKey}`)
              }
              optionFilterProp="children"
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              value={
                controlsOption.key === "waferIdSelection" &&
                reloadChartFilters[chartKey]
                  ? reloadChartFilters[chartKey].wafer_id_selection
                  : null
              }
              options={
                controlsOption.key === "waferIdSelection"
                  ? waferIdOptions
                  : option.options
              }
              onChange={controlsActions[option.onChange]}
            />
          );
          break;
        case "switch":
          item = (
            <Space
              key={`switch_${controlsOption.key}_wrapper`}
              className={option.className ?? ""}
            >
              {option.label && (
                <Text className="text-nowrap">{option.label}</Text>
              )}
              <Switch
                checkedChildren={option.checkedChildren}
                unCheckedChildren={option.unCheckedChildren}
                defaultChecked={option.defaultChecked}
                onChange={controlsActions[option.onChange]}
              />
            </Space>
          );
          break;
        default:
          item = (
            <Button
              key={`btn_${controlsOption.key}`}
              className={option.className ?? ""}
              icon={
                controlsProps[controlsOption.key] &&
                controlsProps[controlsOption.key].icon
                  ? controlsIcons[controlsProps[controlsOption.key].icon]
                  : option.icon
                    ? option.icon
                    : null
              }
              onClick={controlsActions[option.onClick]}
              disabled={
                controlsProps[controlsOption.key] &&
                controlsProps[controlsOption.key].disabled
                  ? controlsProps[controlsOption.key].disabled
                  : option.disabled
                    ? option.disabled
                    : false
              }
              type={
                controlsProps[controlsOption.key] &&
                controlsProps[controlsOption.key].type
                  ? controlsProps[controlsOption.key].type
                  : "default"
              }
            >
              {controlsProps[controlsOption.key] &&
              controlsProps[controlsOption.key].label &&
              !controlsProps[controlsOption.key].disabled
                ? controlsProps[controlsOption.key].label
                : option.label}
            </Button>
          );
      }
    }

    return item;
  };

  return (
    <>
      {contextHolder}
      {isChartBoosted &&
        showBoostWarning &&
        boostedWarningMsgWrapper &&
        ReactDOM.createPortal(
          <BoostedChartWarningMessage chartRef={chartRef} />,
          boostedWarningMsgWrapper,
        )}
      <FullScreen handle={fullScreenHandle} onChange={onFullScreenChange}>
        <div ref={chartWrapperRef} id={`chart_wrapper_${chartKey}`}>
          {displayChartControls && component.props.chart_controls && (
            <Flex justify="space-between" align="flex-start">
              <Flex align="center" gap="small" wrap>
                {createControlsOptions(component.props.chart_controls, [
                  "top",
                  "left",
                ])}
              </Flex>
              <Flex justify="center" align="center" gap="small" flex={1}>
                {createControlsOptions(component.props.chart_controls, [
                  "top",
                  "center",
                ])}
              </Flex>
              <Flex justify="flex-end" align="center" gap="small" flex={1}>
                {createControlsOptions(component.props.chart_controls, [
                  "top",
                  "right",
                ])}
              </Flex>
            </Flex>
          )}
          {loadingChart ? (
            <Spin size="large" indicator={<LoadingOutlined spin />} />
          ) : hasChartData ? (
            chartComponent
          ) : (
            <Empty description="No chart data available"></Empty>
          )}
          {displayChartControls && component.props.chart_controls && (
            <div ref={chartControlsBottomRef}>
              {component.props.chart_controls.some(
                (option) => option.key === "tooltip",
              ) &&
                hasChartData !== false && (
                  <div className="mb-2">
                    <Text strong>Options:</Text>
                  </div>
                )}
              <Flex justify="space-between" align="flex-start">
                <Flex align="center" gap="small" wrap>
                  {createControlsOptions(component.props.chart_controls, [
                    "bottom",
                    "left",
                  ])}
                </Flex>
                <Flex justify="center" align="center" gap="small">
                  {createControlsOptions(component.props.chart_controls, [
                    "bottom",
                    "center",
                  ])}
                </Flex>
                <Flex justify="flex-end" align="center" gap="small" flex={1}>
                  {createControlsOptions(component.props.chart_controls, [
                    "bottom",
                    "right",
                  ])}
                </Flex>
              </Flex>
            </div>
          )}
          {displayChartControls &&
            component.props.chart_controls &&
            component.props.chart_controls.some(
              (option) => option.key === "orientationSettings",
            ) && (
              <Modal
                title="Wafer Map Orientation Settings"
                open={isOrientationSettingsModalOpen}
                okText="Apply Changes"
                cancelText="Cancel"
                onOk={() => setOrientation()}
                onCancel={() => setIsOrientationSettingsModalOpen(false)}
                getContainer={modalContainer}
              >
                <OrientationSettingsForm
                  form={orientationSettingsForm}
                  component={component}
                  chartKey={chartKey}
                  chartCustomData={chartCustomData}
                />
              </Modal>
            )}
          {displayChartControls &&
            component.props.chart_controls &&
            component.props.chart_controls.some(
              (option) => option.key === "colorScale",
            ) && (
              <Modal
                title="Change Color Scaling for Minimum Value per Die"
                open={isColorScaleModalOpen}
                onCancel={() => setIsColorScaleModalOpen(false)}
                getContainer={modalContainer}
                footer={[
                  <Button
                    key="cancel"
                    onClick={() => setIsColorScaleModalOpen(false)}
                  >
                    Cancel
                  </Button>,
                  <Button
                    key="reset"
                    onClick={() => colorScaleForm.resetFields()}
                  >
                    Reset
                  </Button>,
                  <Button key="update" type="primary" onClick={setColorScale}>
                    Update Map
                  </Button>,
                ]}
              >
                <ColorScaleForm
                  form={colorScaleForm}
                  component={component}
                  chartKey={chartKey}
                  chartCustomData={chartCustomData}
                />
              </Modal>
            )}
        </div>
      </FullScreen>
    </>
  );
};

export default ChartWrapper;
