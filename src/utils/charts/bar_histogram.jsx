"use client";

import { App, Empty, notification } from "antd";
import * as Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/highcharts-more";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/histogram-bellcurve";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import React, { useEffect, useRef, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Api from "../api";
import Helper from "../helper";
import { useBoundStore } from "../../store/store";
import { QueryKeys } from "../query_keys";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import {
  DEFAULT_BIN_COUNT,
  DEFAULT_BIN_WIDTH,
  DEFAULT_PLOTLINES,
} from "./constants";
import ChartHelper from "./chart_helper";
import { getResultsSubtitle, setAxisMinMax } from "./chart_common";
import ChartLoading from "./loading";

/**
 * Bar histogram chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {object} chartFilters
 * @param {function} setHasChartData
 * @param {function} setHighchartsChart
 * @param {object} fullScreenHandle
 * @param {boolean} hasTopFailingTests
 * @param {object} localChartData
 * @param {object} prerenderData
 * @param {React.Ref} ref
 * @returns {JSX.Element}
 */
export default React.forwardRef(function BarHistogram(
  {
    chartRef,
    component,
    filters,
    pageKey,
    chartKey,
    chartCustomData,
    chartFilters = {},
    setHasChartData,
    setHighchartsChart,
    fullScreenHandle,
    hasTopFailingTests: hasTopFailingTestsProp = true,
    localChartData,
    prerenderData = {},
  },
  ref,
) {
  const [chartOptions, setChartOptions] = useState();
  const [topFailingTestData, setTopFailingTestData] = useState();
  const [shouldFetchChartData, setShouldFetchChartData] = useState(false);
  const urlParams = useBoundStore((state) => state.urlParams);
  const cacheData = useBoundStore((state) => state.cacheData);
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const chartData = useRef();
  const { message } = App.useApp();
  const [notificationApi, contextHolder] = notification.useNotification();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const chartType = component.name;
  const hasTopFailingTests =
    settings.has_top_failing_tests ?? hasTopFailingTestsProp;

  const options = merge(ChartHelper.getChartDefaultSettings(true), {
    name: "bar_histogram",
    chart: {
      zoomType: "x",
      events: {
        fullscreenOpen: () => {
          chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
          chartCustomData[chartKey].isFullScreen = true;
        },
        fullscreenClose: () => {
          chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
          chartCustomData[chartKey].isFullScreen = false;
        },
        render: (event) => {
          const chart = event.target;
          if (
            chartCustomData?.[chartKey]?.shouldSetStatsInfoPosition ||
            chart.shouldRenderStatsInfo
          ) {
            chartCustomData[chartKey].shouldSetStatsInfoPosition = false;
            chart.shouldRenderStatsInfo = false;
            ChartHelper.renderStatsInfoToChart(
              chart,
              ChartHelper.getChartDefaultStats(),
              chartData.current,
              options,
              chartCustomData[chartKey],
            );
          }
        },
      },
    },
    boost: {
      enabled: false,
    },
    plotOptions: {
      column: {
        grouping: false,
      },
    },
    title: {
      text: settings.show_title ? (chartFilters.title ?? settings.title) : "",
      align: "left",
    },
    subtitle: {},
    xAxis: [
      {
        title: {
          text: settings.x.title,
        },
        alignTicks: false,
        labels: {
          formatter: function () {
            const label = !isNaN(Number(this.value))
              ? Helper.numberFormat(this.value, 4)
              : this.axis.defaultLabelFormatter.call(this);
            return label;
          },
        },
      },
    ],
    yAxis: [
      {
        title: {
          text:
            prerenderData.histogram_y_axis_as_percent === true
              ? "Percentage (%)"
              : settings.y.title,
        },
      },
    ],
    exporting: {
      chartOptions: {
        chart: {
          events: {
            load: () => {
              chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
              chartCustomData[chartKey].shouldRemoveStatsInfoElement = false;
            },
          },
        },
      },
    },
    series: [],
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      resultsAxis: "x",
      logScaleAxis: "y",
      hasStatsInfo: true,
      default: settings,
    },
  });

  React.useImperativeHandle(ref, () => ({
    getHistogramOptions: (userChartOptions) => {
      return ChartHelper.getHistogramOptions(
        settings,
        chartData.current,
        prerenderData,
        userChartOptions,
      );
    },
  }));

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }
    chartComponentRefs[chartKey] = ref;

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  useEffect(() => {
    if (hasTopFailingTests) {
      if (component.props.params.body_params.test_number !== undefined) {
        setTopFailingTestData({
          tnum: component.props.params.body_params.test_number,
          per_pin: component.props.params.body_params.per_pin,
          pin_index: component.props.params.body_params.pin_index,
        });
      } else {
        Helper.getLotTopFailingTestData(
          urlParams[pageKey].src_type,
          urlParams[pageKey].src_value,
          urlParams[pageKey].mfg_process,
          setTopFailingTestData,
          message.warning,
          message.error,
          cacheData,
        );
      }
    }
  }, []);

  useEffect(() => {
    if (hasTopFailingTests) {
      setShouldFetchChartData(topFailingTestData !== undefined);
    } else {
      setShouldFetchChartData(true);
    }
  }, [topFailingTestData]);

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart?.showLoading();

    if (typeof topFailingTestData?.tnum !== "undefined") {
      filters[pageKey].tNum = topFailingTestData.tnum;
    }
    if (topFailingTestData?.per_pin === true) {
      filters[pageKey].per_pin = topFailingTestData.per_pin;
      filters[pageKey].pin_index = topFailingTestData.pin_index;
    } else {
      delete filters[pageKey].per_pin;
      delete filters[pageKey].pin_index;
    }
    let allFilters = { ...filters[pageKey], ...chartFilters, ...prerenderData };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey, filters),
    queryFn: fetchChartData,
    enabled:
      shouldFetchChartData &&
      defaultChartOptions.isSuccess &&
      typeof localChartData === "undefined",
  });

  /**
   * Set chart options after all required data are available
   *
   * @param {object} data
   */
  const setOptions = (data) => {
    if (typeof setHasChartData === "function") {
      setHasChartData(Array.isArray(data.results) && data.results.length > 0);
    }
    chartData.current = data;

    options.subtitle.text = getResultsSubtitle(data, filters[pageKey]);

    if (prerenderData.histogram_y_axis_as_percent) {
      options.tooltip = {
        headerFormat: "",
        pointFormatter() {
          return `<span>${this.x}</span><br/><span style="color:${this.color}">\u25CF</span> ${this.series.name}: <b>${Helper.numberFormat(this.y, 2)}%</b><br/>`;
        },
      };
    }

    const histogramOptions = ChartHelper.getHistogramOptions(
      settings,
      data,
      prerenderData,
      {
        bin_count: DEFAULT_BIN_COUNT,
        bin_width: DEFAULT_BIN_WIDTH,
      },
    );
    options.series = histogramOptions.series ?? [];
    options.xAxis[0].tickPositions = histogramOptions.x_categories;

    setAxisMinMax(
      options.xAxis[0],
      data,
      settings.should_use_limits_scaling ?? true,
    );
    options.xAxis[0].plotLines = [
      ...ChartHelper.getPlotLines(
        data,
        ChartHelper.filterPlotLines(DEFAULT_PLOTLINES, chartFilters),
        true,
      ),
      ...ChartHelper.getSigmaLines(data, true),
    ];
    // Append the test data to the user options
    options.testData = ChartHelper.setTestDataToUserOptions(data);
    ChartHelper.updateAxisTitleWithActualValue(data, options.xAxis[0]);
    const chartOptions = defaultChartOptions.data?.data?.value
      ? merge(
          options,
          JSON.parse(defaultChartOptions.data.data.value),
          retainedChartOptions[chartKey] ?? {},
        )
      : options;

    if (settings.plotarea_only) {
      ChartHelper.setPlotAreaOnlyOptions(chartOptions);
    }

    setChartOptions(chartOptions);
  };

  // If chart data is directly set
  useEffect(() => {
    if (localChartData && defaultChartOptions.isSuccess) {
      setOptions(localChartData);
    }
  }, [defaultChartOptions.dataUpdatedAt]);

  // If data is being fetched remotely
  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        setOptions(response.data);
      } else {
        notificationApi.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notificationApi.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    chart.shouldRenderStatsInfo = settings.has_stats_info !== false;
    chart.shouldDrawPercentHistogram =
      prerenderData.histogram_y_axis_as_percent;

    if (chartCustomData) {
      ChartHelper.updateExportingMenu(
        chart,
        setIsChartOptionsOpen,
        setCurrentChart,
        chartCustomData[chartKey],
        fullScreenHandle,
      );
    }
    if (typeof setHighchartsChart === "function") {
      setHighchartsChart(chart);
    }
  };

  return (
    <div>
      {contextHolder}
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
});
