"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/highcharts-more";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import "highcharts/modules/boost";
import React, { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Api from "../api";
import Helper from "../helper";
import { useBoundStore } from "../../store/store";
import { QueryKeys } from "../query_keys";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import ChartHelper from "./chart_helper";
import ChartLoading from "./loading";
import { redrawWhenBoosted } from "./chart_common";

/**
 * Scatter with line chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {object} chartFilters
 * @param {function} setIsChartBoosted
 * @param {object} fullScreenHandle
 * @param {object} prerenderData
 * @param {React.Ref} ref
 * @returns {JSX.Element}
 */
export default React.forwardRef(function ScatterWithLine(
  {
    chartRef,
    component,
    filters,
    pageKey,
    chartKey,
    chartCustomData,
    chartFilters = {},
    setIsChartBoosted,
    fullScreenHandle,
    prerenderData = {},
  },
  ref,
) {
  const [chartOptions, setChartOptions] = useState();
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const { message } = App.useApp();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const chartType = component.name;

  const options = merge(ChartHelper.getChartDefaultSettings(), {
    chart: {
      zoomType: "xy",
      events: {
        fullscreenOpen: () => {
          chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
          chartCustomData[chartKey].isFullScreen = true;
        },
        fullscreenClose: () => {
          chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
          chartCustomData[chartKey].isFullScreen = false;
        },
        render: (event) => {
          const chart = event.target;
          if (typeof setIsChartBoosted === "function") {
            setIsChartBoosted(chart.boosted);
          }
        },
      },
    },
    title: {
      text: ChartHelper.generateChartTitle(
        settings.title,
        merge({}, filters[pageKey]),
      ),
    },
    subtitle: {},
    boost: {
      useGPUTranslations: true,
      usePreAllocated: true,
    },
    xAxis: [
      {
        title: {
          text: settings.x.title,
        },
        tickWidth: 0,
      },
    ],
    yAxis: [
      {
        minPadding: 0,
        maxPadding: 0,
        title: {
          text: settings.y.title,
        },
        max: settings.y.max ?? null,
        min: settings.y.min ?? null,
      },
      {
        minPadding: 0,
        maxPadding: 0,
        title: {
          text: settings.y2.title,
        },
        max: settings.y2.max ?? null,
        min: settings.y2.min ?? null,
        opposite: true,
        gridLineWidth: 1,
      },
    ],
    exporting: {
      chartOptions: {
        chart: {
          events: {
            load: () => {
              chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
              chartCustomData[chartKey].shouldRemoveStatsInfoElement = false;
            },
          },
        },
      },
    },
    series: [],
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      resultsAxis: "y",
      logScaleAxis: "y",
      hasStatsInfo: true,
      default: settings,
    },
  });

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }
    chartComponentRefs[chartKey] = ref;

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  React.useImperativeHandle(ref, () => ({
    reloadChart: () => {
      if (reloadChartFilters[chartKey].chart_bin_type) {
        filters[pageKey].bin_type = reloadChartFilters[chartKey].chart_bin_type;
        delete reloadChartFilters[chartKey].chart_bin_type;
      }
      chartDataQuery.refetch();
    },
  }));

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart?.showLoading();

    let allFilters = { ...filters[pageKey], ...chartFilters, ...prerenderData };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
    enabled: defaultChartOptions.isSuccess,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        const categories = response.data?.x_categories ?? [];
        options.xAxis[0].categories = categories;

        Object.keys(response.data.yield).forEach((siteYield) => {
          options.series.push({
            type: "spline",
            name: siteYield,
            data: response.data.yield[siteYield],
            color: response.data.series_colors[siteYield],
            yAxis: 1,
            connectNulls: true,
            lineWidth: 2,
            marker: {
              enabled: false,
            },
            boostThreshold: 2000,
          });
        });

        Object.keys(response.data.sites)
          .map(Number)
          .forEach((site) => {
            options.series.push({
              type: "scatter",
              name: `Site ${site}`,
              data: response.data.sites[site],
              color: response.data.series_colors[site],
              yAxis: 0,
              marker: {
                enabled: true,
                symbol: "circle",
                radius: 3,
              },
              tooltip: {
                followPointer: false,
                pointFormatter: function () {
                  return `${settings.x.title}: <strong>${
                    categories[this.x]
                  }</strong><br/>${settings.y.title}: <strong>${this.y}</strong>`;
                },
              },
              boostThreshold: 2000,
            });
          });

        const chartOptions = defaultChartOptions.data?.data?.value
          ? merge(
              options,
              JSON.parse(defaultChartOptions.data.data.value),
              retainedChartOptions[chartKey] ?? {},
            )
          : options;
        setChartOptions(chartOptions);
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    ChartHelper.updateExportingMenu(
      chart,
      setIsChartOptionsOpen,
      setCurrentChart,
      chartCustomData[chartKey],
      fullScreenHandle,
    );

    redrawWhenBoosted(chart);
  };

  return (
    <div>
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
});
