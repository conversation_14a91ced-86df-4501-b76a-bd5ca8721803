import { create } from "zustand";
import { createDataSlice } from "../slice/data_slice";
import { createHasBackRoute } from "../slice/has_back_route";
import { createLoginData } from "../slice/login_data";
import { createAnalysisTemplateData } from "../slice/analysis_template_data";
import { createGridData } from "../slice/grid_data";
import { createUrlParams } from "../slice/url_params";
import { createChartData } from "../slice/chart_data";
import { createUserData } from "../slice/user_data";
import { createPageData } from "../slice/page_data";
import { createNPIData } from "../slice/npi_data";

/**
 * Create global store
 */
export const useBoundStore = create((...a) => ({
  ...createDataSlice(...a),
  ...createHasBackRoute(...a),
  ...createLoginData(...a),
  ...createAnalysisTemplateData(...a),
  ...createGridData(...a),
  ...createUrlParams(...a),
  ...createChartData(...a),
  ...createUserData(...a),
  ...createPageData(...a),
  ...createNPIData(...a),
}));
