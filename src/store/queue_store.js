import { startTransition } from "react";
import { create } from "zustand";

/**
 * This is a queue-based store to support queue system
 * This can be used to start rendering a component after the other
 *
 * @param {function} set
 * @param {function} get
 * @returns {object}
 */
export const useQueueStore = create((set, get) => ({
  queues: {}, // { [group]: [{ key, task }] }
  processing: {}, // { [group]: true/false }
  currentKeys: {}, // { [group]: currentTaskKey }
  currentDones: {}, // { [group]: doneCallback }
  enqueue: (key, group, taskFn) => {
    const state = get();
    const queue = state.queues[group] || [];

    set({
      queues: {
        ...state.queues,
        [group]: [...queue, { key, task: taskFn }],
      },
    });

    if (!state.processing[group]) {
      get().processNext(group);
    }
  },
  processNext: (group) => {
    const state = get();
    const queue = state.queues[group] || [];
    if (queue.length === 0 || state.processing[group]) return;

    const next = queue[0];
    const { key, task } = next;

    const done = () => {
      const updatedQueue = get().queues[group].slice(1);

      set((state) => ({
        queues: {
          ...state.queues,
          [group]: updatedQueue,
        },
        processing: {
          ...state.processing,
          [group]: false,
        },
        currentDones: {
          ...state.currentDones,
          [group]: null,
        },
        currentKeys: {
          ...state.currentKeys,
          [group]: null,
        },
      }));

      startTransition(() => {
        get().processNext(group);
      });
    };

    set((state) => ({
      processing: {
        ...state.processing,
        [group]: true,
      },
      currentKeys: {
        ...state.currentKeys,
        [group]: key,
      },
      currentDones: {
        ...state.currentDones,
        [group]: done,
      },
    }));

    task();
  },
  triggerDone: (group) => {
    const done = get().currentDones[group];
    if (done) done();
  },
  getQueueByGroup: (group) => get().queues[group] || [],
  cancelGroup: (group) => {
    const state = get();
    set({
      queues: {
        ...state.queues,
        [group]: [],
      },
    });
  },
}));
