import { useEffect } from "react";

/**
 * Consume any pending local chart data
 * @param {boolean} options.isLocalDataOnly - whether or not the chart is local data only
 * @param {string} options.chartType - type of chart
 * @param {string} options.pageKey - key for the current page
 * @param {object} options.prerenderData - prerendered data for the current page
 * @param {(id: string) => any} options.popPendingLocalChartData - function to
 *   pop pending local chart data from the store
 * @param {(b: boolean) => void} options.setShouldFetchChartData - function to
 *   set whether or not to fetch chart data
 * @param {(data: any) => void} options.setExternalLocalData - function to set
 *   external local chart data
 */
export const useLocalBoxPlotData = ({
  isLocalDataOnly,
  chartType,
  pageKey,
  prerenderData,
  popPendingLocalChartData,
  setShouldFetchChartData,
  setExternalLocalData,
}) => {
  useEffect(() => {
    if (isLocalDataOnly) {
      try {
        const pendingId = `${pageKey}_${chartType}_${
          prerenderData?.tab_key ?? ""
        }`;
        const pending = popPendingLocalChartData?.(pendingId);
        if (pending) {
          setShouldFetchChartData(false);
          setExternalLocalData(pending);
        }
      } catch (err) {
        void err; // ignore
      }
    }
  }, [
    isLocalDataOnly,
    chartType,
    pageKey,
    prerenderData,
    popPendingLocalChartData,
    setShouldFetchChartData,
    setExternalLocalData,
  ]);
};
