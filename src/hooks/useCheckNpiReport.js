import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for checking if report already existed
 *
 * @param {object} filters
 * @returns {object}
 */
export function useCheckNpiReport(filters) {
  return useQuery({
    queryKey: ["check_npi_report", filters],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/npi/report/recipe/check/${filters.recipe_category}/${filters.recipe_name}/${filters.report_name}`,
        "GET",
      );
    },
    enabled: typeof filters.report_name !== "undefined",
  });
}
