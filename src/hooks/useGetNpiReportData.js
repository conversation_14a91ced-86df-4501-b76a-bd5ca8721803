import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for getting npi report data
 *
 * @param {object} filters
 * @returns {object}
 */
export function useGetNpiReportData(filters) {
  return useQuery({
    queryKey: ["npi_report_data", filters],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/npi/report/recipe/get/${filters.recipe_category}/${filters.recipe_name}/${filters.report_name}`,
        "GET",
      );
    },
    enabled: typeof filters.report_name !== "undefined",
  });
}
