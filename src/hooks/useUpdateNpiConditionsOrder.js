import { useMutation } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for updating npi conditions order
 *
 * @param {object} filters
 * @returns {object} mutation
 */
export function useUpdateNpiConditionsOrder(filters) {
  const mutation = useMutation({
    mutationFn: async (filters) => {
      return await Api.fetchData(
        "/api/v1/npi/conditions/reorder",
        "POST",
        filters,
      );
    },
    onSuccess: () => {
      if (typeof filters.success_callback === "function") {
        filters.success_callback();
      }
    },
  });

  return mutation;
}
