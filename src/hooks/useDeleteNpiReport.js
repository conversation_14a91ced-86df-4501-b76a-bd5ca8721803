import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for deleting npi report
 *
 * @param {int} reportKey
 * @returns {object}
 */
export function useDeleteNpiReport(reportKey) {
  return useQuery({
    queryKey: ["delete_npi_report", reportKey],
    queryFn: async () => {
      return await Api.fetchData("/api/v1/npi/report/delete", "POST", {
        report_key: reportKey,
      });
    },
    enabled: typeof reportKey !== "undefined",
  });
}
