import { useEffect, useRef } from "react";
import { useBoundStore } from "../store/store";

/**
 * Custom hook to generate tabs for selected part IDs.
 *
 * @param {object}   rowPrerenderData    – the prerender data object
 * @param {function} generateTabItem     – the function that actually creates the tab
 */
export function usePartIdTabs(rowPrerenderData, generateTabItem) {
  const selectedPartIdData = useBoundStore((state) => state.selectedPartIdData);
  // Keep the latest generateTabItem without retriggering the main effect
  const generateTabItemRef = useRef(generateTabItem);

  useEffect(() => {
    generateTabItemRef.current = generateTabItem;
  }, [generateTabItem]);

  useEffect(() => {
    if (selectedPartIdData && selectedPartIdData.length > 0) {
      const partIds = selectedPartIdData
        .map((row) => row.part_id)
        .filter((v) => v !== "" && v !== null && v !== undefined);

      // Get all other keys from the first row (excluding part_id)
      const otherKeys = Object.keys(selectedPartIdData[0]);
      const otherKeysData = {};

      otherKeys.forEach((key) => {
        const values = selectedPartIdData
          .map((row) => row[key])
          .filter(
            (value) => value !== "" && value !== null && value !== undefined,
          );
        otherKeysData[key] = values.join(",");
      });

      // Sort part ids for display and key stability
      const sortedPartIds = partIds
        .map((v) => `${v}`)
        .sort((a, b) =>
          a.localeCompare(b, undefined, { numeric: true, sensitivity: "base" }),
        );
      if (sortedPartIds.length > 0) {
        otherKeysData.part_id = sortedPartIds.join(",");
      }

      // Build a nicer label for subtitle: show incidence when a single part id is selected
      let partIdLabel = "Multiple";
      if (sortedPartIds.length === 1) {
        const singleId = sortedPartIds[0];
        const match = selectedPartIdData.find(
          (row) => `${row.part_id}` === singleId,
        );
        const incidenceVal = match?.incidence;
        partIdLabel = `${singleId}${
          incidenceVal !== undefined &&
          incidenceVal !== null &&
          incidenceVal !== ""
            ? ` (Incidence: ${incidenceVal})`
            : ""
        }`;
      }

      const partIdPrerenderData = {
        ...rowPrerenderData,
        ...otherKeysData,
        tab_title: `Part: ${otherKeysData.part_id}`,
        tab_key: `partids_${otherKeysData.part_id}`,
        part_id_label: partIdLabel,
      };
      // create the tab and mark it active (only when selection changes)
      generateTabItemRef.current(
        { tab_key: "part_id_tab" },
        partIdPrerenderData,
        true,
      );
    }
  }, [selectedPartIdData, rowPrerenderData]);
}
