import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook of getting the analysis set list
 *
 * @param {object} filters
 * @returns {object}
 */
export function useGetAnalysisSetList(filters) {
  return useQuery({
    queryKey: ["analysis_set_list", filters],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/npi/analysis_set/list`,
        "POST",
        filters,
      );
    },
    enabled: typeof filters.recipe_name !== "undefined",
    refetchOnWindowFocus: false,
  });
}
