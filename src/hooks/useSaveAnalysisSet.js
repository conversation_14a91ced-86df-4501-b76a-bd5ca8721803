import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { App } from "antd";
import Api from "../utils/api";

/**
 * Custom hook for saving analysis set
 *
 * @param {object} filters
 * @param {string} action
 * @param {function} successCbk
 * @param {boolean} all
 * @returns {object}
 */
export function useSaveAnalysisSet(
  filters,
  action = "save",
  successCbk,
  all = "",
) {
  const { message } = App.useApp();
  const { data, dataUpdatedAt } = useQuery({
    queryKey: ["save_analysis_set", filters, action, all],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/${filters.recipe_type}/analysis_set/${action}/${all}`,
        "POST",
        filters,
      );
    },
    enabled: filters?.recipe_type !== "",
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (data?.success) {
      if (typeof successCbk === "function") {
        successCbk(filters);
      } else {
        message.success("Analysis set was saved successfully.");
      }
    }
  }, [dataUpdatedAt]);

  return { success: data?.success };
}
