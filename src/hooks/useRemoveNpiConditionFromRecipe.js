import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for removing condition from npi recipe
 *
 * @param {object} filters
 * @returns {object} query
 */
export function useRemoveNpiConditionFromRecipe(filters) {
  return useQuery({
    queryKey: ["remove_condition_from_recipe", filters],
    queryFn: async () => {
      return await Api.fetchData(
        "/api/v1/npi/conditions/remove",
        "POST",
        filters,
      );
    },
    enabled: typeof filters.recipe_name !== "undefined",
    refetchOnWindowFocus: false,
  });
}
