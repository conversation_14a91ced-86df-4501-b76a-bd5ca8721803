import { useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { App } from "antd";
import Api from "../utils/api";

/**
 * Custom hook for saving recipe
 *
 * @param {object} filters
 * @param {boolean} enabled
 * @returns {object} query
 */
export function useSaveRecipe(filters, enabled) {
  const { message } = App.useApp();

  const query = useQuery({
    queryKey: ["save_recipe", filters],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/${filters.recipe_type}/recipe/save`,
        "POST",
        filters,
      );
    },
    enabled:
      typeof enabled !== "undefined" &&
      enabled &&
      typeof filters.recipe_name !== "undefined",
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (query.data?.success) {
      message.success("Recipe was saved successfully.");
    }
  }, [query.dataUpdatedAt]);

  return query;
}
