import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook of getting the npi recipe condition types
 *
 * @param {string} recipeCategory
 * @returns {object}
 */
export function useGetNpiConditionTypes(recipeCategory) {
  return useQuery({
    queryKey: ["npi_conditions", recipeCategory],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/npi/conditions/get/${recipeCategory}`,
        "GET",
      );
    },
    enabled: typeof recipeCategory !== "undefined",
  });
}
