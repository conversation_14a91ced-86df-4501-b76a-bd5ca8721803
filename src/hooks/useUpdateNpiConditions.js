import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for updating npi conditions
 *
 * @param {object} filters
 * @returns {object} query
 */
export function useUpdateNpiConditions(filters) {
  return useQuery({
    queryKey: ["update_npi_condition", filters],
    queryFn: async () => {
      return await Api.fetchData(
        "/api/v1/npi/conditions/update",
        "POST",
        filters,
      );
    },
    enabled: typeof filters.recipe_name !== "undefined",
    refetchOnWindowFocus: false,
  });
}
