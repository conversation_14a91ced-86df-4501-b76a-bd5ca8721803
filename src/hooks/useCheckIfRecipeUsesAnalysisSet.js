import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook of checking whether the recipe uses an analysis set
 *
 * @param {object} filters
 * @returns {object}
 */
export function useCheckIfRecipeUsesAnalsisSet(filters) {
  return useQuery({
    queryKey: ["check_if_recipe_uses_analysis_set", filters],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/npi/analysis_set/check`,
        "POST",
        filters,
      );
    },
    enabled: typeof filters.recipe_name !== "undefined",
    refetchOnWindowFocus: false,
  });
}
