import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for renaming a condition in npi
 *
 * @param {object} filters
 * @returns {object} query
 */
export function useRenameNpiCondition(filters) {
  return useQuery({
    queryKey: ["rename_npi_condition", filters],
    queryFn: async () => {
      return await Api.fetchData(
        "/api/v1/npi/conditions/rename",
        "POST",
        filters,
      );
    },
    enabled: typeof filters.recipe_name !== "undefined",
    refetchOnWindowFocus: false,
  });
}
