import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for getting recipe data
 *
 * @param {object} filters
 * @returns {object}
 */
export function useGetRecipeData(filters) {
  return useQuery({
    queryKey: ["recipe_data", filters],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/${filters.recipe_type}/recipe/data`,
        "POST",
        filters,
      );
    },
    enabled: typeof filters.recipe_name !== "undefined",
    refetchOnWindowFocus: false,
  });
}
