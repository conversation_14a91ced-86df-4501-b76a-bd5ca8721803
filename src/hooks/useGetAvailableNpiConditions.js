import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for getting the available npi conditions
 *
 * @param {object} filters
 * @param {int} isSelected
 * @returns {object}
 */
export function useGetAvailableNpiConditions(filters, isSelected = 0) {
  return useQuery({
    queryKey: ["available_npi_conditions", filters, isSelected],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/npi/conditions/available/${isSelected}`,
        "POST",
        filters,
      );
    },
    enabled: typeof filters.recipe_name !== "undefined",
  });
}
