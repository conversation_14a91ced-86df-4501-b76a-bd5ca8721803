import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for adding condition to npi recipe
 *
 * @param {object} filters
 * @returns {object} query
 */
export function useAddNpiConditionToRecipe(filters) {
  return useQuery({
    queryKey: ["add_condition_to_recipe", filters],
    queryFn: async () => {
      return await Api.fetchData("/api/v1/npi/conditions/add", "POST", filters);
    },
    enabled: typeof filters.recipe_name !== "undefined",
    refetchOnWindowFocus: false,
  });
}
