import { useEffect } from "react";

/**
 * Custom useEffect hook that performs an API fetch call
 * and then aborts (in the cleanup) the fetch
 * when component is unmounted
 *
 * @param {Function} effectCallback - This function is required to return an AbortController instance
 * @param {Function} cleanUpCallback
 * @param {Array} deps
 */
export function useEffectApiFetch(effectCallback, cleanUpCallback, deps = []) {
  useEffect(() => {
    let abortCtl = effectCallback();
    if (!(abortCtl instanceof AbortController)) {
      abortCtl = new AbortController();
    }

    return () => {
      abortCtl.abort();
      cleanUpCallback();
    };
  }, deps);
}
