import { useQuery } from "@tanstack/react-query";
import { QueryKeys } from "../utils/query_keys";
import Api from "../utils/api";

/**
 * Custom hook for getting grid blueprint
 *
 * @param {string} pageKey
 * @param {string} gridId
 * @returns {object}
 */
export function useGetGridBlueprint(pageKey, gridId) {
  return useQuery({
    queryKey: [...QueryKeys.grid(pageKey, gridId)],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/internal/blueprint/component_props/${gridId}`,
        "GET",
      );
    },
    enabled: typeof gridId !== "undefined",
    refetchOnWindowFocus: false,
  });
}
