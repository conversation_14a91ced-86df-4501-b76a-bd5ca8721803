import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for checking if analysis set already exists
 *
 * @param {object} filters
 * @returns {object}
 */
export function useCheckAnalysisSet(filters) {
  return useQuery({
    queryKey: ["check_analysis_set", filters],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/npi/analysis_set/check`,
        "POST",
        filters,
      );
    },
    enabled: typeof filters.recipe_type !== "undefined",
    refetchOnWindowFocus: false,
  });
}
