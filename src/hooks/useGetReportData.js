import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for getting report data
 *
 * @param {object} filters
 * @returns {object}
 */
export function useGetReportData(filters) {
  return useQuery({
    queryKey: ["report_data", filters],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/${filters.recipe_type}/report/get/${filters.report_key}`,
        "GET",
      );
    },
    enabled: typeof filters.report_key !== "undefined",
  });
}
