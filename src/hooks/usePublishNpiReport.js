import { App } from "antd";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import Api from "../utils/api";

/**
 * Custom hook of publishing npi report
 *
 * @params {string} reportKey
 * @returns {boolean}
 */
export function usePublishNpiReport(reportKey) {
  const { message } = App.useApp();
  const { data, dataUpdatedAt } = useQuery({
    queryKey: ["npi_publish_report", reportKey],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/npi/report/publish/${reportKey}`,
        "GET",
      );
    },
    enabled: typeof reportKey !== "undefined",
  });

  useEffect(() => {
    if (data?.success) {
      message.success("Report was published successfully.");
      window.location.reload();
    }
  }, [dataUpdatedAt]);

  return data?.success;
}
