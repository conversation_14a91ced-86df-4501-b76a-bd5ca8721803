import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook of getting the default chart options of a chart type
 *
 * @param {string} chartType
 * @returns {object}
 */
export function useChartDefaultOptions(chartType) {
  return useQuery({
    queryKey: [chartType],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/internal/user_settings/get/value/chart_option_defaults/${chartType}`,
        "GET",
      );
    },
  });
}
