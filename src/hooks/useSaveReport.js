import { useQuery } from "@tanstack/react-query";
import Api from "../utils/api";

/**
 * Custom hook for saving report
 *
 * @param {object} filters
 * @returns {object}
 */
export function useSaveReport(filters) {
  return useQuery({
    queryKey: ["save_report", filters],
    queryFn: async () => {
      return await Api.fetchData(
        `/api/v1/${filters.recipe_type}/report/save`,
        "POST",
        filters,
      );
    },
    enabled: filters?.recipe_type !== "",
    refetchOnWindowFocus: false,
  });
}
