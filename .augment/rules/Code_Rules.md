---
type: "always_apply"
---

Rule 1: NEVER disable or remove a feature to fix a bug or error.
Rule 2: NEVER fix an error or bug by hiding it.
Rule 3: NO silent fallbacks or silent failures, all problems should be loud and proud.
Rule 4: Always check online documentation of every package used and do everything the officially recommended way.
Rule 5: Clean up your mess. Remove any temporary and/or outdated files or scripts that were only meant to be used once and no longer serve a purpose.
Rule 6: Use functional patterns where possible
Rule 7: Use Single Responsibility, Keep it Simple (KISS), Don't Repeat Yourself (DRY), You Aren't Gonna Need It (YAGNI) and SOLID principles when possible.
